import os
import json

def create_vue_template(page_obj):
    page_name = page_obj["page"].strip("*").lower().replace(" ", "_")
    content = page_obj["content"]
    url_path = page_obj.get("URL路径", "").strip()

    base_directory = os.path.join('pages', page_name)
    os.makedirs(base_directory, exist_ok=True)
    
    index = 1
    while True:
        if index == 1:
            file_name = 'index.vue'
        else:
            file_name = f'index{index}.vue'
        
        file_path = os.path.join(base_directory, file_name)
        if not os.path.exists(file_path):
            break
        index += 1

    component_name = page_name.capitalize() if index == 1 else f"{page_name.capitalize()}{index}"
    
    template = f'''<template>
  <div class="{component_name.lower()}-page">
    <h1>{{ component_name }}</h1>
    <!-- 在此添加页面内容 -->
  </div>
</template>

<script>
import {{ ref, onMounted }} from 'vue'

export default {{
  name: '{component_name}Page',
  setup() {{
    /**
     * 页面需求：
     * {content.replace("*", "").replace("\n", "\n     * ")}
     * 
     * URL路径: {url_path}
     */
    
    // 组件逻辑
    const pageTitle = ref('{component_name}')

    onMounted(() => {{
      console.log('{component_name} 组件已挂载')
    }})

    return {{
      pageTitle
    }}
  }}
}}
</script>

<style lang="stylus" scoped>
.{component_name.lower()}-page
  // 在此添加样式
  h1
    font-size 24px
    color #333
</style>
'''
    
    with open(file_path, 'w', encoding='utf-8') as file:
        file.write(template)
    
    print(f"已创建页面: {file_path}")

if __name__ == "__main__":
    print("请输入页面对象数组（JSON格式），输入完成后按回车键两次：")
    json_input = ""
    while True:
        line = input()
        if line.strip() == "":
            break
        json_input += line

    try:
        page_objects = json.loads(json_input)
        for page_obj in page_objects:
            create_vue_template(page_obj)
    except json.JSONDecodeError:
        print("输入的 JSON 格式不正确，请检查后重试。")

print("程序已结束")