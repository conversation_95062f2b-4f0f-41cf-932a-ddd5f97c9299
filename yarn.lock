# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@achrinza/node-ipc@9.2.2":
  version "9.2.2"
  dependencies:
    "@node-ipc/js-queue" "2.0.3"
    event-pubsub "4.3.0"
    js-message "1.0.7"

"@ampproject/remapping@^2.2.0":
  version "2.3.0"
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.24"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.24.7", "@babel/code-frame@^7.25.9", "@babel/code-frame@^7.26.0", "@babel/code-frame@^7.26.2":
  version "7.26.2"
  dependencies:
    "@babel/helper-validator-identifier" "^7.25.9"
    js-tokens "^4.0.0"
    picocolors "^1.0.0"

"@babel/compat-data@^7.22.6", "@babel/compat-data@^7.25.9", "@babel/compat-data@^7.26.0":
  version "7.26.3"

"@babel/core@^7.0.0", "@babel/core@^7.0.0-0", "@babel/core@^7.0.0-0 || ^8.0.0-0 <8.0.0", "@babel/core@^7.11.0", "@babel/core@^7.12.0", "@babel/core@^7.13.0", "@babel/core@^7.4.0 || ^8.0.0-0 <8.0.0":
  version "7.26.0"
  dependencies:
    "@ampproject/remapping" "^2.2.0"
    "@babel/code-frame" "^7.26.0"
    "@babel/generator" "^7.26.0"
    "@babel/helper-compilation-targets" "^7.25.9"
    "@babel/helper-module-transforms" "^7.26.0"
    "@babel/helpers" "^7.26.0"
    "@babel/parser" "^7.26.0"
    "@babel/template" "^7.25.9"
    "@babel/traverse" "^7.25.9"
    "@babel/types" "^7.26.0"
    convert-source-map "^2.0.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.3"
    semver "^6.3.1"

"@babel/generator@^7.26.0", "@babel/generator@^7.26.3":
  version "7.26.3"
  dependencies:
    "@babel/parser" "^7.26.3"
    "@babel/types" "^7.26.3"
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"
    jsesc "^3.0.2"

"@babel/helper-annotate-as-pure@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/types" "^7.25.9"

"@babel/helper-compilation-targets@^7.22.6", "@babel/helper-compilation-targets@^7.25.9", "@babel/helper-compilation-targets@^7.9.6":
  version "7.25.9"
  dependencies:
    "@babel/compat-data" "^7.25.9"
    "@babel/helper-validator-option" "^7.25.9"
    browserslist "^4.24.0"
    lru-cache "^5.1.1"
    semver "^6.3.1"

"@babel/helper-create-class-features-plugin@^7.18.6", "@babel/helper-create-class-features-plugin@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.25.9"
    "@babel/helper-member-expression-to-functions" "^7.25.9"
    "@babel/helper-optimise-call-expression" "^7.25.9"
    "@babel/helper-replace-supers" "^7.25.9"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.25.9"
    "@babel/traverse" "^7.25.9"
    semver "^6.3.1"

"@babel/helper-create-regexp-features-plugin@^7.18.6", "@babel/helper-create-regexp-features-plugin@^7.25.9":
  version "7.26.3"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.25.9"
    regexpu-core "^6.2.0"
    semver "^6.3.1"

"@babel/helper-define-polyfill-provider@^0.6.2", "@babel/helper-define-polyfill-provider@^0.6.3":
  version "0.6.3"
  dependencies:
    "@babel/helper-compilation-targets" "^7.22.6"
    "@babel/helper-plugin-utils" "^7.22.5"
    debug "^4.1.1"
    lodash.debounce "^4.0.8"
    resolve "^1.14.2"

"@babel/helper-member-expression-to-functions@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/traverse" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/helper-module-imports@^7.0.0", "@babel/helper-module-imports@^7.24.7", "@babel/helper-module-imports@^7.25.9", "@babel/helper-module-imports@^7.8.3":
  version "7.25.9"
  dependencies:
    "@babel/traverse" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/helper-module-transforms@^7.25.9", "@babel/helper-module-transforms@^7.26.0":
  version "7.26.0"
  dependencies:
    "@babel/helper-module-imports" "^7.25.9"
    "@babel/helper-validator-identifier" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/helper-optimise-call-expression@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/types" "^7.25.9"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.18.6", "@babel/helper-plugin-utils@^7.22.5", "@babel/helper-plugin-utils@^7.24.8", "@babel/helper-plugin-utils@^7.25.9", "@babel/helper-plugin-utils@^7.8.0":
  version "7.25.9"

"@babel/helper-remap-async-to-generator@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.25.9"
    "@babel/helper-wrap-function" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/helper-replace-supers@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-member-expression-to-functions" "^7.25.9"
    "@babel/helper-optimise-call-expression" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/helper-skip-transparent-expression-wrappers@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/traverse" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/helper-string-parser@^7.25.9":
  version "7.25.9"

"@babel/helper-validator-identifier@^7.25.9":
  version "7.25.9"

"@babel/helper-validator-option@^7.25.9":
  version "7.25.9"

"@babel/helper-wrap-function@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/template" "^7.25.9"
    "@babel/traverse" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/helpers@^7.26.0":
  version "7.26.0"
  dependencies:
    "@babel/template" "^7.25.9"
    "@babel/types" "^7.26.0"

"@babel/parser@^7.25.3", "@babel/parser@^7.25.6", "@babel/parser@^7.25.9", "@babel/parser@^7.26.0", "@babel/parser@^7.26.3", "@babel/parser@^7.7.0":
  version "7.26.3"
  dependencies:
    "@babel/types" "^7.26.3"

"@babel/plugin-bugfix-firefox-class-in-computed-class-key@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/plugin-bugfix-safari-class-field-initializer-scope@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.25.9"
    "@babel/plugin-transform-optional-chaining" "^7.25.9"

"@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/plugin-proposal-class-properties@^7.8.3":
  version "7.18.6"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-proposal-decorators@^7.8.3":
  version "7.25.9"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/plugin-syntax-decorators" "^7.25.9"

"@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2":
  version "7.21.0-placeholder-for-preset-env.2"

"@babel/plugin-syntax-decorators@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-syntax-dynamic-import@^7.8.3":
  version "7.8.3"
  resolved "https://registry.nlark.com/@babel/plugin-syntax-dynamic-import/download/@babel/plugin-syntax-dynamic-import-7.8.3.tgz"
  integrity sha1-Yr+Ysto80h1iYVT8lu5bPLaOrLM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-import-assertions@^7.26.0":
  version "7.26.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-syntax-import-attributes@^7.26.0":
  version "7.26.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-syntax-jsx@^7.2.0", "@babel/plugin-syntax-jsx@^7.24.7", "@babel/plugin-syntax-jsx@^7.8.3":
  version "7.25.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-syntax-unicode-sets-regex@^7.18.6":
  version "7.18.6"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-arrow-functions@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-async-generator-functions@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-remap-async-to-generator" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/plugin-transform-async-to-generator@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-module-imports" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-remap-async-to-generator" "^7.25.9"

"@babel/plugin-transform-block-scoped-functions@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-block-scoping@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-class-properties@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-class-static-block@^7.26.0":
  version "7.26.0"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-classes@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.25.9"
    "@babel/helper-compilation-targets" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-replace-supers" "^7.25.9"
    "@babel/traverse" "^7.25.9"
    globals "^11.1.0"

"@babel/plugin-transform-computed-properties@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/template" "^7.25.9"

"@babel/plugin-transform-destructuring@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-dotall-regex@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-duplicate-keys@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-duplicate-named-capturing-groups-regex@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-dynamic-import@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-exponentiation-operator@^7.25.9":
  version "7.26.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-export-namespace-from@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-for-of@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.25.9"

"@babel/plugin-transform-function-name@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-compilation-targets" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/plugin-transform-json-strings@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-literals@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-logical-assignment-operators@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-member-expression-literals@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-modules-amd@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-module-transforms" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-modules-commonjs@^7.25.9":
  version "7.26.3"
  dependencies:
    "@babel/helper-module-transforms" "^7.26.0"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-modules-systemjs@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-module-transforms" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-validator-identifier" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/plugin-transform-modules-umd@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-module-transforms" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-named-capturing-groups-regex@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-new-target@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-nullish-coalescing-operator@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-numeric-separator@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-object-rest-spread@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-compilation-targets" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/plugin-transform-parameters" "^7.25.9"

"@babel/plugin-transform-object-super@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-replace-supers" "^7.25.9"

"@babel/plugin-transform-optional-catch-binding@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-optional-chaining@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.25.9"

"@babel/plugin-transform-parameters@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-private-methods@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-private-property-in-object@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.25.9"
    "@babel/helper-create-class-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-property-literals@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-regenerator@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    regenerator-transform "^0.15.2"

"@babel/plugin-transform-regexp-modifiers@^7.26.0":
  version "7.26.0"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-reserved-words@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-runtime@^7.11.0":
  version "7.25.9"
  dependencies:
    "@babel/helper-module-imports" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    babel-plugin-polyfill-corejs2 "^0.4.10"
    babel-plugin-polyfill-corejs3 "^0.10.6"
    babel-plugin-polyfill-regenerator "^0.6.1"
    semver "^6.3.1"

"@babel/plugin-transform-shorthand-properties@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-spread@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.25.9"

"@babel/plugin-transform-sticky-regex@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-template-literals@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-typeof-symbol@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-unicode-escapes@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-unicode-property-regex@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-unicode-regex@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-unicode-sets-regex@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/preset-env@^7.11.0":
  version "7.26.0"
  dependencies:
    "@babel/compat-data" "^7.26.0"
    "@babel/helper-compilation-targets" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-validator-option" "^7.25.9"
    "@babel/plugin-bugfix-firefox-class-in-computed-class-key" "^7.25.9"
    "@babel/plugin-bugfix-safari-class-field-initializer-scope" "^7.25.9"
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression" "^7.25.9"
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining" "^7.25.9"
    "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly" "^7.25.9"
    "@babel/plugin-proposal-private-property-in-object" "7.21.0-placeholder-for-preset-env.2"
    "@babel/plugin-syntax-import-assertions" "^7.26.0"
    "@babel/plugin-syntax-import-attributes" "^7.26.0"
    "@babel/plugin-syntax-unicode-sets-regex" "^7.18.6"
    "@babel/plugin-transform-arrow-functions" "^7.25.9"
    "@babel/plugin-transform-async-generator-functions" "^7.25.9"
    "@babel/plugin-transform-async-to-generator" "^7.25.9"
    "@babel/plugin-transform-block-scoped-functions" "^7.25.9"
    "@babel/plugin-transform-block-scoping" "^7.25.9"
    "@babel/plugin-transform-class-properties" "^7.25.9"
    "@babel/plugin-transform-class-static-block" "^7.26.0"
    "@babel/plugin-transform-classes" "^7.25.9"
    "@babel/plugin-transform-computed-properties" "^7.25.9"
    "@babel/plugin-transform-destructuring" "^7.25.9"
    "@babel/plugin-transform-dotall-regex" "^7.25.9"
    "@babel/plugin-transform-duplicate-keys" "^7.25.9"
    "@babel/plugin-transform-duplicate-named-capturing-groups-regex" "^7.25.9"
    "@babel/plugin-transform-dynamic-import" "^7.25.9"
    "@babel/plugin-transform-exponentiation-operator" "^7.25.9"
    "@babel/plugin-transform-export-namespace-from" "^7.25.9"
    "@babel/plugin-transform-for-of" "^7.25.9"
    "@babel/plugin-transform-function-name" "^7.25.9"
    "@babel/plugin-transform-json-strings" "^7.25.9"
    "@babel/plugin-transform-literals" "^7.25.9"
    "@babel/plugin-transform-logical-assignment-operators" "^7.25.9"
    "@babel/plugin-transform-member-expression-literals" "^7.25.9"
    "@babel/plugin-transform-modules-amd" "^7.25.9"
    "@babel/plugin-transform-modules-commonjs" "^7.25.9"
    "@babel/plugin-transform-modules-systemjs" "^7.25.9"
    "@babel/plugin-transform-modules-umd" "^7.25.9"
    "@babel/plugin-transform-named-capturing-groups-regex" "^7.25.9"
    "@babel/plugin-transform-new-target" "^7.25.9"
    "@babel/plugin-transform-nullish-coalescing-operator" "^7.25.9"
    "@babel/plugin-transform-numeric-separator" "^7.25.9"
    "@babel/plugin-transform-object-rest-spread" "^7.25.9"
    "@babel/plugin-transform-object-super" "^7.25.9"
    "@babel/plugin-transform-optional-catch-binding" "^7.25.9"
    "@babel/plugin-transform-optional-chaining" "^7.25.9"
    "@babel/plugin-transform-parameters" "^7.25.9"
    "@babel/plugin-transform-private-methods" "^7.25.9"
    "@babel/plugin-transform-private-property-in-object" "^7.25.9"
    "@babel/plugin-transform-property-literals" "^7.25.9"
    "@babel/plugin-transform-regenerator" "^7.25.9"
    "@babel/plugin-transform-regexp-modifiers" "^7.26.0"
    "@babel/plugin-transform-reserved-words" "^7.25.9"
    "@babel/plugin-transform-shorthand-properties" "^7.25.9"
    "@babel/plugin-transform-spread" "^7.25.9"
    "@babel/plugin-transform-sticky-regex" "^7.25.9"
    "@babel/plugin-transform-template-literals" "^7.25.9"
    "@babel/plugin-transform-typeof-symbol" "^7.25.9"
    "@babel/plugin-transform-unicode-escapes" "^7.25.9"
    "@babel/plugin-transform-unicode-property-regex" "^7.25.9"
    "@babel/plugin-transform-unicode-regex" "^7.25.9"
    "@babel/plugin-transform-unicode-sets-regex" "^7.25.9"
    "@babel/preset-modules" "0.1.6-no-external-plugins"
    babel-plugin-polyfill-corejs2 "^0.4.10"
    babel-plugin-polyfill-corejs3 "^0.10.6"
    babel-plugin-polyfill-regenerator "^0.6.1"
    core-js-compat "^3.38.1"
    semver "^6.3.1"

"@babel/preset-modules@0.1.6-no-external-plugins":
  version "0.1.6-no-external-plugins"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/types" "^7.4.4"
    esutils "^2.0.2"

"@babel/runtime@^7.0.0", "@babel/runtime@^7.11.0", "@babel/runtime@^7.15.4", "@babel/runtime@^7.21.0", "@babel/runtime@^7.3.4", "@babel/runtime@^7.8.4":
  version "7.26.0"
  dependencies:
    regenerator-runtime "^0.14.0"

"@babel/template@^7.25.0", "@babel/template@^7.25.9":
  version "7.25.9"
  dependencies:
    "@babel/code-frame" "^7.25.9"
    "@babel/parser" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/traverse@^7.25.6", "@babel/traverse@^7.25.9", "@babel/traverse@^7.7.0":
  version "7.26.4"
  dependencies:
    "@babel/code-frame" "^7.26.2"
    "@babel/generator" "^7.26.3"
    "@babel/parser" "^7.26.3"
    "@babel/template" "^7.25.9"
    "@babel/types" "^7.26.3"
    debug "^4.3.1"
    globals "^11.1.0"

"@babel/types@^7.25.6", "@babel/types@^7.25.9", "@babel/types@^7.26.0", "@babel/types@^7.26.3", "@babel/types@^7.4.4", "@babel/types@^7.7.0":
  version "7.26.3"
  dependencies:
    "@babel/helper-string-parser" "^7.25.9"
    "@babel/helper-validator-identifier" "^7.25.9"

"@hapi/address@2.x.x":
  version "2.1.4"
  resolved "https://registry.npmmirror.com/@hapi/address/download/@hapi/address-2.1.4.tgz"
  integrity sha1-XWftQ/P9QaadS5/3tW58DR0KgeU=

"@hapi/bourne@1.x.x":
  version "1.3.2"
  resolved "https://registry.npmmirror.com/@hapi/bourne/download/@hapi/bourne-1.3.2.tgz"
  integrity sha1-CnCVreoGckPOMoPhtWuKj0U7JCo=

"@hapi/hoek@^8.3.0", "@hapi/hoek@8.x.x":
  version "8.5.1"
  resolved "https://registry.npmmirror.com/@hapi/hoek/download/@hapi/hoek-8.5.1.tgz?cache=0&sync_timestamp=1632776440309&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40hapi%2Fhoek%2Fdownload%2F%40hapi%2Fhoek-8.5.1.tgz"
  integrity sha1-/elgZMpEbeyMVajC8TCVewcMbgY=

"@hapi/joi@^15.0.0", "@hapi/joi@^15.0.1":
  version "15.1.1"
  resolved "https://registry.npmmirror.com/@hapi/joi/download/@hapi/joi-15.1.1.tgz"
  integrity sha1-xnW4pxKW8Cgz+NbSQ7NMV7jOGdc=
  dependencies:
    "@hapi/address" "2.x.x"
    "@hapi/bourne" "1.x.x"
    "@hapi/hoek" "8.x.x"
    "@hapi/topo" "3.x.x"

"@hapi/topo@3.x.x":
  version "3.1.6"
  resolved "https://registry.nlark.com/@hapi/topo/download/@hapi/topo-3.1.6.tgz"
  integrity sha1-aNk1+j6uf91asNf5U/MgXYsr/Ck=
  dependencies:
    "@hapi/hoek" "^8.3.0"

"@intervolga/optimize-cssnano-plugin@^1.0.5":
  version "1.0.6"
  resolved "https://registry.nlark.com/@intervolga/optimize-cssnano-plugin/download/@intervolga/optimize-cssnano-plugin-1.0.6.tgz"
  integrity sha1-vnx4RhKLiPapsdEmGgrQbrXA/fg=
  dependencies:
    cssnano "^4.0.0"
    cssnano-preset-default "^4.0.0"
    postcss "^7.0.0"

"@jridgewell/gen-mapping@^0.3.5":
  version "0.3.8"
  dependencies:
    "@jridgewell/set-array" "^1.2.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/resolve-uri@^3.1.0":
  version "3.1.2"

"@jridgewell/set-array@^1.2.1":
  version "1.2.1"

"@jridgewell/source-map@^0.3.3":
  version "0.3.6"
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"

"@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@^1.4.14", "@jridgewell/sourcemap-codec@^1.5.0":
  version "1.5.0"

"@jridgewell/trace-mapping@^0.3.24", "@jridgewell/trace-mapping@^0.3.25":
  version "0.3.25"
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@mrmlnc/readdir-enhanced@^2.2.1":
  version "2.2.1"
  resolved "https://registry.nlark.com/@mrmlnc/readdir-enhanced/download/@mrmlnc/readdir-enhanced-2.2.1.tgz"
  integrity sha1-UkryQNGjYFJ7cwR17PoTRKpUDd4=
  dependencies:
    call-me-maybe "^1.0.1"
    glob-to-regexp "^0.3.0"

"@node-ipc/js-queue@2.0.3":
  version "2.0.3"
  dependencies:
    easy-stack "1.0.1"

"@nodelib/fs.stat@^1.1.2":
  version "1.1.3"
  resolved "https://registry.nlark.com/@nodelib/fs.stat/download/@nodelib/fs.stat-1.1.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40nodelib%2Ffs.stat%2Fdownload%2F%40nodelib%2Ffs.stat-1.1.3.tgz"
  integrity sha1-K1o6s/kYzKSKjHVMCBaOPwPrphs=

"@soda/friendly-errors-webpack-plugin@^1.7.1":
  version "1.8.1"
  resolved "https://registry.npmmirror.com/@soda/friendly-errors-webpack-plugin/download/@soda/friendly-errors-webpack-plugin-1.8.1.tgz"
  integrity sha512-h2ooWqP8XuFqTXT+NyAFbrArzfQA7R6HTezADrvD9Re8fxMLTPPniLdqVTdDaO0eIoLaAwKT+d6w+5GeTk7Vbg==
  dependencies:
    chalk "^3.0.0"
    error-stack-parser "^2.0.6"
    string-width "^4.2.3"
    strip-ansi "^6.0.1"

"@soda/get-current-script@^1.0.0":
  version "1.0.2"
  resolved "https://registry.nlark.com/@soda/get-current-script/download/@soda/get-current-script-1.0.2.tgz"
  integrity sha1-pTUV2yXYA4N0OBtzryC7Ty5QjYc=

"@types/body-parser@*":
  version "1.19.5"
  dependencies:
    "@types/connect" "*"
    "@types/node" "*"

"@types/connect-history-api-fallback@*":
  version "1.5.4"
  dependencies:
    "@types/express-serve-static-core" "*"
    "@types/node" "*"

"@types/connect@*":
  version "3.4.38"
  dependencies:
    "@types/node" "*"

"@types/eslint-scope@^3.7.7":
  version "3.7.7"
  dependencies:
    "@types/eslint" "*"
    "@types/estree" "*"

"@types/eslint@*":
  version "9.6.1"
  dependencies:
    "@types/estree" "*"
    "@types/json-schema" "*"

"@types/estree@*", "@types/estree@^1.0.6":
  version "1.0.6"

"@types/express-serve-static-core@*", "@types/express-serve-static-core@^5.0.0":
  version "5.0.2"
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"
    "@types/send" "*"

"@types/express@*":
  version "5.0.0"
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "^5.0.0"
    "@types/qs" "*"
    "@types/serve-static" "*"

"@types/glob@^7.1.1":
  version "7.2.0"
  resolved "https://registry.npmmirror.com/@types/glob/download/@types/glob-7.2.0.tgz?cache=0&sync_timestamp=1637265569054&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fglob%2Fdownload%2F%40types%2Fglob-7.2.0.tgz"
  integrity sha1-vBtb86qS8lvV3TnzXFc2G9zlsus=
  dependencies:
    "@types/minimatch" "*"
    "@types/node" "*"

"@types/http-errors@*":
  version "2.0.4"

"@types/http-proxy@^1.17.5":
  version "1.17.15"
  dependencies:
    "@types/node" "*"

"@types/json-schema@*", "@types/json-schema@^7.0.5", "@types/json-schema@^7.0.8", "@types/json-schema@^7.0.9":
  version "7.0.15"

"@types/mime@^1":
  version "1.3.5"

"@types/mini-css-extract-plugin@^0.9.1":
  version "0.9.1"
  resolved "https://registry.npmmirror.com/@types/mini-css-extract-plugin/download/@types/mini-css-extract-plugin-0.9.1.tgz"
  integrity sha1-1L3eUZcyb8oDnUGPS92gPcdNxFE=
  dependencies:
    "@types/webpack" "*"

"@types/minimatch@*":
  version "5.1.2"

"@types/minimist@^1.2.0":
  version "1.2.5"

"@types/node@*":
  version "22.10.2"
  dependencies:
    undici-types "~6.20.0"

"@types/normalize-package-data@^2.4.0":
  version "2.4.4"

"@types/q@^1.5.1":
  version "1.5.8"

"@types/qs@*":
  version "6.9.17"

"@types/range-parser@*":
  version "1.2.7"

"@types/send@*":
  version "0.17.4"
  dependencies:
    "@types/mime" "^1"
    "@types/node" "*"

"@types/serve-static@*":
  version "1.15.7"
  dependencies:
    "@types/http-errors" "*"
    "@types/node" "*"
    "@types/send" "*"

"@types/source-list-map@*":
  version "0.1.6"

"@types/tapable@^1":
  version "1.0.12"

"@types/uglify-js@*":
  version "3.17.5"
  dependencies:
    source-map "^0.6.1"

"@types/webpack-dev-server@^3.11.0":
  version "3.11.6"
  resolved "https://registry.npmmirror.com/@types/webpack-dev-server/download/@types/webpack-dev-server-3.11.6.tgz"
  integrity sha1-2IiM/S8GMCA+E9PteDOk0RuKNNw=
  dependencies:
    "@types/connect-history-api-fallback" "*"
    "@types/express" "*"
    "@types/serve-static" "*"
    "@types/webpack" "^4"
    http-proxy-middleware "^1.0.0"

"@types/webpack-sources@*":
  version "3.2.3"
  dependencies:
    "@types/node" "*"
    "@types/source-list-map" "*"
    source-map "^0.7.3"

"@types/webpack@*":
  version "5.28.5"
  dependencies:
    "@types/node" "*"
    tapable "^2.2.0"
    webpack "^5"

"@types/webpack@^4", "@types/webpack@^4.0.0":
  version "4.41.40"
  dependencies:
    "@types/node" "*"
    "@types/tapable" "^1"
    "@types/uglify-js" "*"
    "@types/webpack-sources" "*"
    anymatch "^3.0.0"
    source-map "^0.6.0"

"@vant/icons@^1.8.0":
  version "1.8.0"

"@vant/popperjs@^1.2.1":
  version "1.3.0"

"@vant/use@^1.4.2":
  version "1.6.0"

"@vue/babel-helper-vue-jsx-merge-props@^1.4.0":
  version "1.4.0"

"@vue/babel-helper-vue-transform-on@1.2.5":
  version "1.2.5"

"@vue/babel-plugin-jsx@^1.0.3":
  version "1.2.5"
  dependencies:
    "@babel/helper-module-imports" "^7.24.7"
    "@babel/helper-plugin-utils" "^7.24.8"
    "@babel/plugin-syntax-jsx" "^7.24.7"
    "@babel/template" "^7.25.0"
    "@babel/traverse" "^7.25.6"
    "@babel/types" "^7.25.6"
    "@vue/babel-helper-vue-transform-on" "1.2.5"
    "@vue/babel-plugin-resolve-type" "1.2.5"
    html-tags "^3.3.1"
    svg-tags "^1.0.0"

"@vue/babel-plugin-resolve-type@1.2.5":
  version "1.2.5"
  dependencies:
    "@babel/code-frame" "^7.24.7"
    "@babel/helper-module-imports" "^7.24.7"
    "@babel/helper-plugin-utils" "^7.24.8"
    "@babel/parser" "^7.25.6"
    "@vue/compiler-sfc" "^3.5.3"

"@vue/babel-plugin-transform-vue-jsx@^1.4.0":
  version "1.4.0"
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-helper-vue-jsx-merge-props" "^1.4.0"
    html-tags "^2.0.0"
    lodash.kebabcase "^4.1.1"
    svg-tags "^1.0.0"

"@vue/babel-preset-app@^4.5.19":
  version "4.5.19"
  dependencies:
    "@babel/core" "^7.11.0"
    "@babel/helper-compilation-targets" "^7.9.6"
    "@babel/helper-module-imports" "^7.8.3"
    "@babel/plugin-proposal-class-properties" "^7.8.3"
    "@babel/plugin-proposal-decorators" "^7.8.3"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"
    "@babel/plugin-syntax-jsx" "^7.8.3"
    "@babel/plugin-transform-runtime" "^7.11.0"
    "@babel/preset-env" "^7.11.0"
    "@babel/runtime" "^7.11.0"
    "@vue/babel-plugin-jsx" "^1.0.3"
    "@vue/babel-preset-jsx" "^1.2.4"
    babel-plugin-dynamic-import-node "^2.3.3"
    core-js "^3.6.5"
    core-js-compat "^3.6.5"
    semver "^6.1.0"

"@vue/babel-preset-jsx@^1.2.4":
  version "1.4.0"
  dependencies:
    "@vue/babel-helper-vue-jsx-merge-props" "^1.4.0"
    "@vue/babel-plugin-transform-vue-jsx" "^1.4.0"
    "@vue/babel-sugar-composition-api-inject-h" "^1.4.0"
    "@vue/babel-sugar-composition-api-render-instance" "^1.4.0"
    "@vue/babel-sugar-functional-vue" "^1.4.0"
    "@vue/babel-sugar-inject-h" "^1.4.0"
    "@vue/babel-sugar-v-model" "^1.4.0"
    "@vue/babel-sugar-v-on" "^1.4.0"

"@vue/babel-sugar-composition-api-inject-h@^1.4.0":
  version "1.4.0"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-composition-api-render-instance@^1.4.0":
  version "1.4.0"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-functional-vue@^1.4.0":
  version "1.4.0"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-inject-h@^1.4.0":
  version "1.4.0"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-v-model@^1.4.0":
  version "1.4.0"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-helper-vue-jsx-merge-props" "^1.4.0"
    "@vue/babel-plugin-transform-vue-jsx" "^1.4.0"
    camelcase "^5.0.0"
    html-tags "^2.0.0"
    svg-tags "^1.0.0"

"@vue/babel-sugar-v-on@^1.4.0":
  version "1.4.0"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-plugin-transform-vue-jsx" "^1.4.0"
    camelcase "^5.0.0"

"@vue/cli-overlay@^4.5.19":
  version "4.5.19"

"@vue/cli-plugin-babel@~4.5.0":
  version "4.5.19"
  dependencies:
    "@babel/core" "^7.11.0"
    "@vue/babel-preset-app" "^4.5.19"
    "@vue/cli-shared-utils" "^4.5.19"
    babel-loader "^8.1.0"
    cache-loader "^4.1.0"
    thread-loader "^2.1.3"
    webpack "^4.0.0"

"@vue/cli-plugin-eslint@~4.5.0":
  version "4.5.19"
  dependencies:
    "@vue/cli-shared-utils" "^4.5.19"
    eslint-loader "^2.2.1"
    globby "^9.2.0"
    inquirer "^7.1.0"
    webpack "^4.0.0"
    yorkie "^2.0.0"

"@vue/cli-plugin-pwa@~4.5.0":
  version "4.5.19"
  dependencies:
    "@vue/cli-shared-utils" "^4.5.19"
    webpack "^4.0.0"
    workbox-webpack-plugin "^4.3.1"

"@vue/cli-plugin-router@^4.5.19", "@vue/cli-plugin-router@~4.5.0":
  version "4.5.19"
  dependencies:
    "@vue/cli-shared-utils" "^4.5.19"

"@vue/cli-plugin-vuex@^4.5.19", "@vue/cli-plugin-vuex@~4.5.0":
  version "4.5.19"

"@vue/cli-service@^3.0.0 || ^4.0.0-0", "@vue/cli-service@~4.5.0":
  version "4.5.19"
  dependencies:
    "@intervolga/optimize-cssnano-plugin" "^1.0.5"
    "@soda/friendly-errors-webpack-plugin" "^1.7.1"
    "@soda/get-current-script" "^1.0.0"
    "@types/minimist" "^1.2.0"
    "@types/webpack" "^4.0.0"
    "@types/webpack-dev-server" "^3.11.0"
    "@vue/cli-overlay" "^4.5.19"
    "@vue/cli-plugin-router" "^4.5.19"
    "@vue/cli-plugin-vuex" "^4.5.19"
    "@vue/cli-shared-utils" "^4.5.19"
    "@vue/component-compiler-utils" "^3.1.2"
    "@vue/preload-webpack-plugin" "^1.1.0"
    "@vue/web-component-wrapper" "^1.2.0"
    acorn "^7.4.0"
    acorn-walk "^7.1.1"
    address "^1.1.2"
    autoprefixer "^9.8.6"
    browserslist "^4.12.0"
    cache-loader "^4.1.0"
    case-sensitive-paths-webpack-plugin "^2.3.0"
    cli-highlight "^2.1.4"
    clipboardy "^2.3.0"
    cliui "^6.0.0"
    copy-webpack-plugin "^5.1.1"
    css-loader "^3.5.3"
    cssnano "^4.1.10"
    debug "^4.1.1"
    default-gateway "^5.0.5"
    dotenv "^8.2.0"
    dotenv-expand "^5.1.0"
    file-loader "^4.2.0"
    fs-extra "^7.0.1"
    globby "^9.2.0"
    hash-sum "^2.0.0"
    html-webpack-plugin "^3.2.0"
    launch-editor-middleware "^2.2.1"
    lodash.defaultsdeep "^4.6.1"
    lodash.mapvalues "^4.6.0"
    lodash.transform "^4.6.0"
    mini-css-extract-plugin "^0.9.0"
    minimist "^1.2.5"
    pnp-webpack-plugin "^1.6.4"
    portfinder "^1.0.26"
    postcss-loader "^3.0.0"
    ssri "^8.0.1"
    terser-webpack-plugin "^1.4.4"
    thread-loader "^2.1.3"
    url-loader "^2.2.0"
    vue-loader "^15.9.2"
    vue-style-loader "^4.1.2"
    webpack "^4.0.0"
    webpack-bundle-analyzer "^3.8.0"
    webpack-chain "^6.4.0"
    webpack-dev-server "^3.11.0"
    webpack-merge "^4.2.2"
  optionalDependencies:
    vue-loader-v16 "npm:vue-loader@^16.1.0"

"@vue/cli-shared-utils@^4.5.19":
  version "4.5.19"
  dependencies:
    "@achrinza/node-ipc" "9.2.2"
    "@hapi/joi" "^15.0.1"
    chalk "^2.4.2"
    execa "^1.0.0"
    launch-editor "^2.2.1"
    lru-cache "^5.1.1"
    open "^6.3.0"
    ora "^3.4.0"
    read-pkg "^5.1.1"
    request "^2.88.2"
    semver "^6.1.0"
    strip-ansi "^6.0.0"

"@vue/compiler-core@3.5.13":
  version "3.5.13"
  dependencies:
    "@babel/parser" "^7.25.3"
    "@vue/shared" "3.5.13"
    entities "^4.5.0"
    estree-walker "^2.0.2"
    source-map-js "^1.2.0"

"@vue/compiler-dom@3.5.13":
  version "3.5.13"
  dependencies:
    "@vue/compiler-core" "3.5.13"
    "@vue/shared" "3.5.13"

"@vue/compiler-sfc@^3.0.0", "@vue/compiler-sfc@^3.0.0-beta.14", "@vue/compiler-sfc@^3.5.3", "@vue/compiler-sfc@3.5.13":
  version "3.5.13"
  dependencies:
    "@babel/parser" "^7.25.3"
    "@vue/compiler-core" "3.5.13"
    "@vue/compiler-dom" "3.5.13"
    "@vue/compiler-ssr" "3.5.13"
    "@vue/shared" "3.5.13"
    estree-walker "^2.0.2"
    magic-string "^0.30.11"
    postcss "^8.4.48"
    source-map-js "^1.2.0"

"@vue/compiler-ssr@3.5.13":
  version "3.5.13"
  dependencies:
    "@vue/compiler-dom" "3.5.13"
    "@vue/shared" "3.5.13"

"@vue/component-compiler-utils@^3.1.0", "@vue/component-compiler-utils@^3.1.2":
  version "3.3.0"
  resolved "https://registry.npmmirror.com/@vue/component-compiler-utils/download/@vue/component-compiler-utils-3.3.0.tgz?cache=0&sync_timestamp=1635248303132&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40vue%2Fcomponent-compiler-utils%2Fdownload%2F%40vue%2Fcomponent-compiler-utils-3.3.0.tgz"
  integrity sha1-+fX7U0ZLDDeyyNLz+/5E32D2Hck=
  dependencies:
    consolidate "^0.15.1"
    hash-sum "^1.0.2"
    lru-cache "^4.1.2"
    merge-source-map "^1.1.0"
    postcss "^7.0.36"
    postcss-selector-parser "^6.0.2"
    source-map "~0.6.1"
    vue-template-es2015-compiler "^1.9.0"
  optionalDependencies:
    prettier "^1.18.2 || ^2.0.0"

"@vue/devtools-api@^6.0.0-beta.11", "@vue/devtools-api@^6.6.4":
  version "6.6.4"

"@vue/eslint-config-prettier@^6.0.0":
  version "6.0.0"
  resolved "https://registry.nlark.com/@vue/eslint-config-prettier/download/@vue/eslint-config-prettier-6.0.0.tgz"
  integrity sha1-rVkSswj0rkaEWOAqKwXbC50kZwA=
  dependencies:
    eslint-config-prettier "^6.0.0"

"@vue/preload-webpack-plugin@^1.1.0":
  version "1.1.2"
  resolved "https://registry.nlark.com/@vue/preload-webpack-plugin/download/@vue/preload-webpack-plugin-1.1.2.tgz"
  integrity sha1-zrkktOyzucQ4ccekKaAvhCPmIas=

"@vue/reactivity@3.5.13":
  version "3.5.13"
  dependencies:
    "@vue/shared" "3.5.13"

"@vue/runtime-core@3.5.13":
  version "3.5.13"
  dependencies:
    "@vue/reactivity" "3.5.13"
    "@vue/shared" "3.5.13"

"@vue/runtime-dom@3.5.13":
  version "3.5.13"
  dependencies:
    "@vue/reactivity" "3.5.13"
    "@vue/runtime-core" "3.5.13"
    "@vue/shared" "3.5.13"
    csstype "^3.1.3"

"@vue/server-renderer@3.5.13":
  version "3.5.13"
  dependencies:
    "@vue/compiler-ssr" "3.5.13"
    "@vue/shared" "3.5.13"

"@vue/shared@3.5.13":
  version "3.5.13"

"@vue/web-component-wrapper@^1.2.0":
  version "1.3.0"
  resolved "https://registry.nlark.com/@vue/web-component-wrapper/download/@vue/web-component-wrapper-1.3.0.tgz"
  integrity sha1-trQKdiVCnSvXwigd26YB7QXcfxo=

"@webassemblyjs/ast@^1.14.1", "@webassemblyjs/ast@1.14.1":
  version "1.14.1"
  dependencies:
    "@webassemblyjs/helper-numbers" "1.13.2"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"

"@webassemblyjs/ast@1.9.0":
  version "1.9.0"
  resolved "https://registry.nlark.com/@webassemblyjs/ast/download/@webassemblyjs/ast-1.9.0.tgz?cache=0&sync_timestamp=1625473368618&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40webassemblyjs%2Fast%2Fdownload%2F%40webassemblyjs%2Fast-1.9.0.tgz"
  integrity sha1-vYUGBLQEJFmlpBzX0zjL7Wle2WQ=
  dependencies:
    "@webassemblyjs/helper-module-context" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/wast-parser" "1.9.0"

"@webassemblyjs/floating-point-hex-parser@1.13.2":
  version "1.13.2"

"@webassemblyjs/floating-point-hex-parser@1.9.0":
  version "1.9.0"
  resolved "https://registry.nlark.com/@webassemblyjs/floating-point-hex-parser/download/@webassemblyjs/floating-point-hex-parser-1.9.0.tgz"
  integrity sha1-PD07Jxvd/ITesA9xNEQ4MR1S/7Q=

"@webassemblyjs/helper-api-error@1.13.2":
  version "1.13.2"

"@webassemblyjs/helper-api-error@1.9.0":
  version "1.9.0"
  resolved "https://registry.nlark.com/@webassemblyjs/helper-api-error/download/@webassemblyjs/helper-api-error-1.9.0.tgz?cache=0&sync_timestamp=1625473460936&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40webassemblyjs%2Fhelper-api-error%2Fdownload%2F%40webassemblyjs%2Fhelper-api-error-1.9.0.tgz"
  integrity sha1-ID9nbjM7lsnaLuqzzO8zxFkotqI=

"@webassemblyjs/helper-buffer@1.14.1":
  version "1.14.1"

"@webassemblyjs/helper-buffer@1.9.0":
  version "1.9.0"
  resolved "https://registry.nlark.com/@webassemblyjs/helper-buffer/download/@webassemblyjs/helper-buffer-1.9.0.tgz?cache=0&sync_timestamp=1625473462686&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40webassemblyjs%2Fhelper-buffer%2Fdownload%2F%40webassemblyjs%2Fhelper-buffer-1.9.0.tgz"
  integrity sha1-oUQtJpxf6yP8vJ73WdrDVH8p3gA=

"@webassemblyjs/helper-code-frame@1.9.0":
  version "1.9.0"
  resolved "https://registry.nlark.com/@webassemblyjs/helper-code-frame/download/@webassemblyjs/helper-code-frame-1.9.0.tgz?cache=0&sync_timestamp=1625473420790&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40webassemblyjs%2Fhelper-code-frame%2Fdownload%2F%40webassemblyjs%2Fhelper-code-frame-1.9.0.tgz"
  integrity sha1-ZH+Iks0gQ6gqwMjF51w28dkVnyc=
  dependencies:
    "@webassemblyjs/wast-printer" "1.9.0"

"@webassemblyjs/helper-fsm@1.9.0":
  version "1.9.0"
  resolved "https://registry.nlark.com/@webassemblyjs/helper-fsm/download/@webassemblyjs/helper-fsm-1.9.0.tgz?cache=0&sync_timestamp=1625473415428&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40webassemblyjs%2Fhelper-fsm%2Fdownload%2F%40webassemblyjs%2Fhelper-fsm-1.9.0.tgz"
  integrity sha1-wFJWtxJEIUZx9LCOwQitY7cO3bg=

"@webassemblyjs/helper-module-context@1.9.0":
  version "1.9.0"
  resolved "https://registry.nlark.com/@webassemblyjs/helper-module-context/download/@webassemblyjs/helper-module-context-1.9.0.tgz"
  integrity sha1-JdiIS3aDmHGgimxvgGw5ee9xLwc=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"

"@webassemblyjs/helper-numbers@1.13.2":
  version "1.13.2"
  dependencies:
    "@webassemblyjs/floating-point-hex-parser" "1.13.2"
    "@webassemblyjs/helper-api-error" "1.13.2"
    "@xtuc/long" "4.2.2"

"@webassemblyjs/helper-wasm-bytecode@1.13.2":
  version "1.13.2"

"@webassemblyjs/helper-wasm-bytecode@1.9.0":
  version "1.9.0"
  resolved "https://registry.nlark.com/@webassemblyjs/helper-wasm-bytecode/download/@webassemblyjs/helper-wasm-bytecode-1.9.0.tgz"
  integrity sha1-T+2L6sm4wU+MWLcNEk1UndH+V5A=

"@webassemblyjs/helper-wasm-section@1.14.1":
  version "1.14.1"
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-buffer" "1.14.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"
    "@webassemblyjs/wasm-gen" "1.14.1"

"@webassemblyjs/helper-wasm-section@1.9.0":
  version "1.9.0"
  resolved "https://registry.nlark.com/@webassemblyjs/helper-wasm-section/download/@webassemblyjs/helper-wasm-section-1.9.0.tgz"
  integrity sha1-WkE41aYpK6GLBMWuSXF+QWeWU0Y=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-buffer" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/wasm-gen" "1.9.0"

"@webassemblyjs/ieee754@1.13.2":
  version "1.13.2"
  dependencies:
    "@xtuc/ieee754" "^1.2.0"

"@webassemblyjs/ieee754@1.9.0":
  version "1.9.0"
  resolved "https://registry.nlark.com/@webassemblyjs/ieee754/download/@webassemblyjs/ieee754-1.9.0.tgz?cache=0&sync_timestamp=1625473454591&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40webassemblyjs%2Fieee754%2Fdownload%2F%40webassemblyjs%2Fieee754-1.9.0.tgz"
  integrity sha1-Fceg+6roP7JhQ7us9tbfFwKtOeQ=
  dependencies:
    "@xtuc/ieee754" "^1.2.0"

"@webassemblyjs/leb128@1.13.2":
  version "1.13.2"
  dependencies:
    "@xtuc/long" "4.2.2"

"@webassemblyjs/leb128@1.9.0":
  version "1.9.0"
  resolved "https://registry.nlark.com/@webassemblyjs/leb128/download/@webassemblyjs/leb128-1.9.0.tgz?cache=0&sync_timestamp=1625473456730&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40webassemblyjs%2Fleb128%2Fdownload%2F%40webassemblyjs%2Fleb128-1.9.0.tgz"
  integrity sha1-8Zygt2ptxVYjoJz/p2noOPoeHJU=
  dependencies:
    "@xtuc/long" "4.2.2"

"@webassemblyjs/utf8@1.13.2":
  version "1.13.2"

"@webassemblyjs/utf8@1.9.0":
  version "1.9.0"
  resolved "https://registry.nlark.com/@webassemblyjs/utf8/download/@webassemblyjs/utf8-1.9.0.tgz?cache=0&sync_timestamp=1625474622442&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40webassemblyjs%2Futf8%2Fdownload%2F%40webassemblyjs%2Futf8-1.9.0.tgz"
  integrity sha1-BNM7Y2945qaBMifoJAL3Y3tiKas=

"@webassemblyjs/wasm-edit@^1.14.1":
  version "1.14.1"
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-buffer" "1.14.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"
    "@webassemblyjs/helper-wasm-section" "1.14.1"
    "@webassemblyjs/wasm-gen" "1.14.1"
    "@webassemblyjs/wasm-opt" "1.14.1"
    "@webassemblyjs/wasm-parser" "1.14.1"
    "@webassemblyjs/wast-printer" "1.14.1"

"@webassemblyjs/wasm-edit@1.9.0":
  version "1.9.0"
  resolved "https://registry.nlark.com/@webassemblyjs/wasm-edit/download/@webassemblyjs/wasm-edit-1.9.0.tgz"
  integrity sha1-P+bXnT8PkiGDqoYALELdJWz+6c8=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-buffer" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/helper-wasm-section" "1.9.0"
    "@webassemblyjs/wasm-gen" "1.9.0"
    "@webassemblyjs/wasm-opt" "1.9.0"
    "@webassemblyjs/wasm-parser" "1.9.0"
    "@webassemblyjs/wast-printer" "1.9.0"

"@webassemblyjs/wasm-gen@1.14.1":
  version "1.14.1"
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"
    "@webassemblyjs/ieee754" "1.13.2"
    "@webassemblyjs/leb128" "1.13.2"
    "@webassemblyjs/utf8" "1.13.2"

"@webassemblyjs/wasm-gen@1.9.0":
  version "1.9.0"
  resolved "https://registry.nlark.com/@webassemblyjs/wasm-gen/download/@webassemblyjs/wasm-gen-1.9.0.tgz"
  integrity sha1-ULxw7Gje2OJ2OwGhQYv0NJGnpJw=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/ieee754" "1.9.0"
    "@webassemblyjs/leb128" "1.9.0"
    "@webassemblyjs/utf8" "1.9.0"

"@webassemblyjs/wasm-opt@1.14.1":
  version "1.14.1"
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-buffer" "1.14.1"
    "@webassemblyjs/wasm-gen" "1.14.1"
    "@webassemblyjs/wasm-parser" "1.14.1"

"@webassemblyjs/wasm-opt@1.9.0":
  version "1.9.0"
  resolved "https://registry.nlark.com/@webassemblyjs/wasm-opt/download/@webassemblyjs/wasm-opt-1.9.0.tgz"
  integrity sha1-IhEYHlsxMmRDzIES658LkChyGmE=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-buffer" "1.9.0"
    "@webassemblyjs/wasm-gen" "1.9.0"
    "@webassemblyjs/wasm-parser" "1.9.0"

"@webassemblyjs/wasm-parser@^1.14.1", "@webassemblyjs/wasm-parser@1.14.1":
  version "1.14.1"
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-api-error" "1.13.2"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"
    "@webassemblyjs/ieee754" "1.13.2"
    "@webassemblyjs/leb128" "1.13.2"
    "@webassemblyjs/utf8" "1.13.2"

"@webassemblyjs/wasm-parser@1.9.0":
  version "1.9.0"
  resolved "https://registry.nlark.com/@webassemblyjs/wasm-parser/download/@webassemblyjs/wasm-parser-1.9.0.tgz?cache=0&sync_timestamp=1625473358573&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40webassemblyjs%2Fwasm-parser%2Fdownload%2F%40webassemblyjs%2Fwasm-parser-1.9.0.tgz"
  integrity sha1-nUjkSCbfSmWYKUqmyHRp1kL/9l4=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-api-error" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/ieee754" "1.9.0"
    "@webassemblyjs/leb128" "1.9.0"
    "@webassemblyjs/utf8" "1.9.0"

"@webassemblyjs/wast-parser@1.9.0":
  version "1.9.0"
  resolved "https://registry.nlark.com/@webassemblyjs/wast-parser/download/@webassemblyjs/wast-parser-1.9.0.tgz"
  integrity sha1-MDERXXmsW9JhVWzsw/qQo+9FGRQ=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/floating-point-hex-parser" "1.9.0"
    "@webassemblyjs/helper-api-error" "1.9.0"
    "@webassemblyjs/helper-code-frame" "1.9.0"
    "@webassemblyjs/helper-fsm" "1.9.0"
    "@xtuc/long" "4.2.2"

"@webassemblyjs/wast-printer@1.14.1":
  version "1.14.1"
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@xtuc/long" "4.2.2"

"@webassemblyjs/wast-printer@1.9.0":
  version "1.9.0"
  resolved "https://registry.nlark.com/@webassemblyjs/wast-printer/download/@webassemblyjs/wast-printer-1.9.0.tgz"
  integrity sha1-STXVTIX+9jewDOn1I3dFHQDUeJk=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/wast-parser" "1.9.0"
    "@xtuc/long" "4.2.2"

"@xtuc/ieee754@^1.2.0":
  version "1.2.0"
  resolved "https://registry.nlark.com/@xtuc/ieee754/download/@xtuc/ieee754-1.2.0.tgz"
  integrity sha1-7vAUoxRa5Hehy8AM0eVSM23Ot5A=

"@xtuc/long@4.2.2":
  version "4.2.2"
  resolved "https://registry.nlark.com/@xtuc/long/download/@xtuc/long-4.2.2.tgz"
  integrity sha1-0pHGpOl5ibXGHZrPOWrk/hM6cY0=

accepts@~1.3.4, accepts@~1.3.8:
  version "1.3.8"
  dependencies:
    mime-types "~2.1.34"
    negotiator "0.6.3"

acorn-jsx@^5.2.0:
  version "5.3.2"
  resolved "https://registry.nlark.com/acorn-jsx/download/acorn-jsx-5.3.2.tgz?cache=0&sync_timestamp=1625793240297&other_urls=https%3A%2F%2Fregistry.nlark.com%2Facorn-jsx%2Fdownload%2Facorn-jsx-5.3.2.tgz"
  integrity sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=

acorn-walk@^7.1.1:
  version "7.2.0"
  resolved "https://registry.nlark.com/acorn-walk/download/acorn-walk-7.2.0.tgz?cache=0&sync_timestamp=1630916608758&other_urls=https%3A%2F%2Fregistry.nlark.com%2Facorn-walk%2Fdownload%2Facorn-walk-7.2.0.tgz"
  integrity sha1-DeiJpgEgOQmw++B7iTjcIdLpZ7w=

"acorn@^6.0.0 || ^7.0.0 || ^8.0.0", acorn@^7.1.1, acorn@^7.4.0:
  version "7.4.1"
  resolved "https://registry.npmmirror.com/acorn/download/acorn-7.4.1.tgz?cache=0&sync_timestamp=1637225522161&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Facorn%2Fdownload%2Facorn-7.4.1.tgz"
  integrity sha1-/q7SVZc9LndVW4PbwIhRpsY1IPo=

acorn@^6.4.1:
  version "6.4.2"
  resolved "https://registry.npmmirror.com/acorn/download/acorn-6.4.2.tgz?cache=0&sync_timestamp=1637225522161&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Facorn%2Fdownload%2Facorn-6.4.2.tgz"
  integrity sha1-NYZv1xBSjpLeEM8GAWSY5H454eY=

acorn@^8.14.0, acorn@^8.8.2:
  version "8.14.0"

address@^1.1.2:
  version "1.2.2"

ajv-errors@^1.0.0:
  version "1.0.1"
  resolved "https://registry.nlark.com/ajv-errors/download/ajv-errors-1.0.1.tgz"
  integrity sha1-81mGrOuRr63sQQL72FAUlQzvpk0=

ajv-formats@^2.1.1:
  version "2.1.1"
  dependencies:
    ajv "^8.0.0"

ajv-keywords@^3.1.0, ajv-keywords@^3.4.1, ajv-keywords@^3.5.2:
  version "3.5.2"
  resolved "https://registry.npmmirror.com/ajv-keywords/download/ajv-keywords-3.5.2.tgz?cache=0&sync_timestamp=1637524478967&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fajv-keywords%2Fdownload%2Fajv-keywords-3.5.2.tgz"
  integrity sha1-MfKdpatuANHC0yms97WSlhTVAU0=

ajv-keywords@^5.1.0:
  version "5.1.0"
  dependencies:
    fast-deep-equal "^3.1.3"

ajv@^6.1.0, ajv@^6.10.0, ajv@^6.10.2, ajv@^6.12.3, ajv@^6.12.4, ajv@^6.12.5, ajv@^6.9.1, ajv@>=5.0.0:
  version "6.12.6"
  resolved "https://registry.npmmirror.com/ajv/download/ajv-6.12.6.tgz?cache=0&sync_timestamp=1637522259668&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fajv%2Fdownload%2Fajv-6.12.6.tgz"
  integrity sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ajv@^8.0.0, ajv@^8.8.2, ajv@^8.9.0:
  version "8.17.1"
  dependencies:
    fast-deep-equal "^3.1.3"
    fast-uri "^3.0.1"
    json-schema-traverse "^1.0.0"
    require-from-string "^2.0.2"

alphanum-sort@^1.0.0:
  version "1.0.2"
  resolved "https://registry.nlark.com/alphanum-sort/download/alphanum-sort-1.0.2.tgz"
  integrity sha1-l6ERlkmyEa0zaR2fn0hqjsn74KM=

ansi-colors@^3.0.0:
  version "3.2.4"
  resolved "https://registry.nlark.com/ansi-colors/download/ansi-colors-3.2.4.tgz"
  integrity sha1-46PaS/uubIapwoViXeEkojQCb78=

ansi-escapes@^4.2.1:
  version "4.3.2"
  resolved "https://registry.nlark.com/ansi-escapes/download/ansi-escapes-4.3.2.tgz"
  integrity sha1-ayKR0dt9mLZSHV8e+kLQ86n+tl4=
  dependencies:
    type-fest "^0.21.3"

ansi-html-community@0.0.8:
  version "0.0.8"
  resolved "https://registry.nlark.com/ansi-html-community/download/ansi-html-community-0.0.8.tgz"
  integrity sha1-afvE1sy+OD+XNpNK40w/gpDxv0E=

ansi-regex@^2.0.0:
  version "2.1.1"
  resolved "https://registry.nlark.com/ansi-regex/download/ansi-regex-2.1.1.tgz?cache=0&sync_timestamp=1631634988487&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fansi-regex%2Fdownload%2Fansi-regex-2.1.1.tgz"
  integrity sha1-w7M6te42DYbg5ijwRorn7yfWVN8=

ansi-regex@^4.1.0:
  version "4.1.1"

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "https://registry.nlark.com/ansi-regex/download/ansi-regex-5.0.1.tgz?cache=0&sync_timestamp=1631634988487&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fansi-regex%2Fdownload%2Fansi-regex-5.0.1.tgz"
  integrity sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=

ansi-styles@^2.2.1:
  version "2.2.1"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/ansi-styles/-/ansi-styles-2.2.1.tgz"
  integrity sha512-kmCevFghRiWM7HB5zTPULl4r9bVFSWjz62MhqizDGUrq2NWuNMQyuv4tHHoKJHs69M/MF64lEcHdYIocrdWQYA==

ansi-styles@^3.2.0:
  version "3.2.1"
  resolved "https://registry.nlark.com/ansi-styles/download/ansi-styles-3.2.1.tgz"
  integrity sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "https://registry.nlark.com/ansi-styles/download/ansi-styles-3.2.1.tgz"
  integrity sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "https://registry.nlark.com/ansi-styles/download/ansi-styles-4.3.0.tgz"
  integrity sha1-7dgDYornHATIWuegkG7a00tkiTc=
  dependencies:
    color-convert "^2.0.1"

any-promise@^1.0.0:
  version "1.3.0"
  resolved "https://registry.nlark.com/any-promise/download/any-promise-1.3.0.tgz"
  integrity sha1-q8av7tzqUugJzcA3au0845Y10X8=

anymatch@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/anymatch/download/anymatch-2.0.0.tgz"
  integrity sha1-vLJLTzeTTZqnrBe0ra+J58du8us=
  dependencies:
    micromatch "^3.1.4"
    normalize-path "^2.1.1"

anymatch@^3.0.0, anymatch@~3.1.2:
  version "3.1.3"
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

aproba@^1.1.1:
  version "1.2.0"
  resolved "https://registry.nlark.com/aproba/download/aproba-1.2.0.tgz"
  integrity sha1-aALmJk79GMeQobDVF/DyYnvyyUo=

arch@^2.1.1:
  version "2.2.0"
  resolved "https://registry.nlark.com/arch/download/arch-2.2.0.tgz"
  integrity sha1-G8R4GPMFdk8jqzMGsL/AhsWinRE=

argparse@^1.0.7:
  version "1.0.10"
  resolved "https://registry.nlark.com/argparse/download/argparse-1.0.10.tgz"
  integrity sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=
  dependencies:
    sprintf-js "~1.0.2"

arr-diff@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/arr-diff/download/arr-diff-4.0.0.tgz"
  integrity sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA=

arr-flatten@^1.1.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/arr-flatten/download/arr-flatten-1.1.0.tgz"
  integrity sha1-NgSLv/TntH4TZkQxbJlmnqWukfE=

arr-union@^3.1.0:
  version "3.1.0"
  resolved "https://registry.nlark.com/arr-union/download/arr-union-3.1.0.tgz"
  integrity sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ=

array-buffer-byte-length@^1.0.1, array-buffer-byte-length@^1.0.2:
  version "1.0.2"
  dependencies:
    call-bound "^1.0.3"
    is-array-buffer "^3.0.5"

array-flatten@^2.1.0:
  version "2.1.2"
  resolved "https://registry.nlark.com/array-flatten/download/array-flatten-2.1.2.tgz"
  integrity sha1-JO+AoowaiTYX4hSbDG0NeIKTsJk=

array-flatten@1.1.1:
  version "1.1.1"
  resolved "https://registry.nlark.com/array-flatten/download/array-flatten-1.1.1.tgz"
  integrity sha1-ml9pkFGx5wczKPKgCJaLZOopVdI=

array-union@^1.0.1, array-union@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/array-union/download/array-union-1.0.2.tgz"
  integrity sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk=
  dependencies:
    array-uniq "^1.0.1"

array-uniq@^1.0.1:
  version "1.0.3"
  resolved "https://registry.nlark.com/array-uniq/download/array-uniq-1.0.3.tgz"
  integrity sha1-r2rId6Jcx/dOBYiUdThY39sk/bY=

array-unique@^0.3.2:
  version "0.3.2"
  resolved "https://registry.nlark.com/array-unique/download/array-unique-0.3.2.tgz"
  integrity sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg=

array.prototype.reduce@^1.0.6:
  version "1.0.7"
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-array-method-boxes-properly "^1.0.0"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    is-string "^1.0.7"

arraybuffer.prototype.slice@^1.0.4:
  version "1.0.4"
  dependencies:
    array-buffer-byte-length "^1.0.1"
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"
    is-array-buffer "^3.0.4"

asn1.js@^4.10.1:
  version "4.10.1"
  dependencies:
    bn.js "^4.0.0"
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"

asn1@~0.2.3:
  version "0.2.6"
  resolved "https://registry.npmmirror.com/asn1/download/asn1-0.2.6.tgz?cache=0&sync_timestamp=1635986760581&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fasn1%2Fdownload%2Fasn1-0.2.6.tgz"
  integrity sha1-DTp7tuZOAqkMAwOzHykoaOoJoI0=
  dependencies:
    safer-buffer "~2.1.0"

assert-plus@^1.0.0, assert-plus@1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/assert-plus/download/assert-plus-1.0.0.tgz"
  integrity sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=

assert@^1.1.1:
  version "1.5.1"
  dependencies:
    object.assign "^4.1.4"
    util "^0.10.4"

assign-symbols@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/assign-symbols/download/assign-symbols-1.0.0.tgz"
  integrity sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c=

astral-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/astral-regex/download/astral-regex-1.0.0.tgz"
  integrity sha1-bIw/uCfdQ+45GPJ7gngqt2WKb9k=

async-each@^1.0.1:
  version "1.0.6"

async-limiter@~1.0.0:
  version "1.0.1"
  resolved "https://registry.nlark.com/async-limiter/download/async-limiter-1.0.1.tgz"
  integrity sha1-3TeelPDbgxCwgpH51kwyCXZmF/0=

async@^2.6.4:
  version "2.6.4"
  dependencies:
    lodash "^4.17.14"

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://registry.nlark.com/asynckit/download/asynckit-0.4.0.tgz"
  integrity sha1-x57Zf380y48robyXkLzDZkdLS3k=

atob@^2.1.2:
  version "2.1.2"
  resolved "https://registry.nlark.com/atob/download/atob-2.1.2.tgz"
  integrity sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k=

autoprefixer@^7.1.1:
  version "7.2.6"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/autoprefixer/-/autoprefixer-7.2.6.tgz"
  integrity sha512-Iq8TRIB+/9eQ8rbGhcP7ct5cYb/3qjNYAR2SnzLCEcwF6rvVOax8+9+fccgXk4bEhQGjOZd5TLhsksmAdsbGqQ==
  dependencies:
    browserslist "^2.11.3"
    caniuse-lite "^1.0.30000805"
    normalize-range "^0.1.2"
    num2fraction "^1.2.2"
    postcss "^6.0.17"
    postcss-value-parser "^3.2.3"

autoprefixer@^9.8.6:
  version "9.8.8"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/autoprefixer/-/autoprefixer-9.8.8.tgz"
  integrity sha512-eM9d/swFopRt5gdJ7jrpCwgvEMIayITpojhkkSMRsFHYuH5bkSQ4p/9qTEHtmNudUZh22Tehu7I6CxAW0IXTKA==
  dependencies:
    browserslist "^4.12.0"
    caniuse-lite "^1.0.30001109"
    normalize-range "^0.1.2"
    num2fraction "^1.2.2"
    picocolors "^0.2.1"
    postcss "^7.0.32"
    postcss-value-parser "^4.1.0"

autoprefixer@8.0.0:
  version "8.0.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/autoprefixer/-/autoprefixer-8.0.0.tgz"
  integrity sha512-XBEqAoESCyGu3daYmWcTC37Dwmjvs0y40UtUO3MMX+Pd/w7jwNFfUKNtxoMFu0u0wcotP+arDpU3JVH54UV79Q==
  dependencies:
    browserslist "^3.0.0"
    caniuse-lite "^1.0.30000808"
    normalize-range "^0.1.2"
    num2fraction "^1.2.2"
    postcss "^6.0.17"
    postcss-value-parser "^3.2.3"

available-typed-arrays@^1.0.7:
  version "1.0.7"
  dependencies:
    possible-typed-array-names "^1.0.0"

aws-sign2@~0.7.0:
  version "0.7.0"
  resolved "https://registry.nlark.com/aws-sign2/download/aws-sign2-0.7.0.tgz"
  integrity sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg=

aws4@^1.8.0:
  version "1.13.2"

axios-retry@^3.2.4:
  version "3.9.1"
  dependencies:
    "@babel/runtime" "^7.15.4"
    is-retry-allowed "^2.2.0"

axios@^0.24.0:
  version "0.24.0"
  resolved "https://registry.npmmirror.com/axios/download/axios-0.24.0.tgz"
  integrity sha1-gE5voeS5xSiFAd2d/1anoJQNINY=
  dependencies:
    follow-redirects "^1.14.4"

babel-eslint@^10.1.0:
  version "10.1.0"
  resolved "https://registry.npmmirror.com/babel-eslint/download/babel-eslint-10.1.0.tgz"
  integrity sha1-aWjlaKkQt4+zd5zdi2rC9HmUMjI=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@babel/parser" "^7.7.0"
    "@babel/traverse" "^7.7.0"
    "@babel/types" "^7.7.0"
    eslint-visitor-keys "^1.0.0"
    resolve "^1.12.0"

babel-extract-comments@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/babel-extract-comments/download/babel-extract-comments-1.0.0.tgz"
  integrity sha1-Cirt+BQX7TkbheGLRhTmk6A1GiE=
  dependencies:
    babylon "^6.18.0"

babel-loader@^8.1.0:
  version "8.4.1"
  dependencies:
    find-cache-dir "^3.3.1"
    loader-utils "^2.0.4"
    make-dir "^3.1.0"
    schema-utils "^2.6.5"

babel-plugin-dynamic-import-node@^2.3.3:
  version "2.3.3"
  resolved "https://registry.nlark.com/babel-plugin-dynamic-import-node/download/babel-plugin-dynamic-import-node-2.3.3.tgz"
  integrity sha1-hP2hnJduxcbe/vV/lCez3vZuF6M=
  dependencies:
    object.assign "^4.1.0"

babel-plugin-import@^1.13.3:
  version "1.13.8"
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"

babel-plugin-polyfill-corejs2@^0.4.10:
  version "0.4.12"
  dependencies:
    "@babel/compat-data" "^7.22.6"
    "@babel/helper-define-polyfill-provider" "^0.6.3"
    semver "^6.3.1"

babel-plugin-polyfill-corejs3@^0.10.6:
  version "0.10.6"
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.6.2"
    core-js-compat "^3.38.0"

babel-plugin-polyfill-regenerator@^0.6.1:
  version "0.6.3"
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.6.3"

babel-plugin-syntax-object-rest-spread@^6.8.0:
  version "6.13.0"
  resolved "https://registry.nlark.com/babel-plugin-syntax-object-rest-spread/download/babel-plugin-syntax-object-rest-spread-6.13.0.tgz"
  integrity sha1-/WU28rzhODb/o6VFjEkDpZe7O/U=

babel-plugin-transform-object-rest-spread@^6.26.0:
  version "6.26.0"
  resolved "https://registry.nlark.com/babel-plugin-transform-object-rest-spread/download/babel-plugin-transform-object-rest-spread-6.26.0.tgz"
  integrity sha1-DzZpLVD+9rfi1LOsFHgTepY7ewY=
  dependencies:
    babel-plugin-syntax-object-rest-spread "^6.8.0"
    babel-runtime "^6.26.0"

babel-plugin-transform-remove-console@^6.9.4:
  version "6.9.4"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/babel-plugin-transform-remove-console/-/babel-plugin-transform-remove-console-6.9.4.tgz"
  integrity sha512-88blrUrMX3SPiGkT1GnvVY8E/7A+k6oj3MNvUtTIxJflFzXTw1bHkuJ/y039ouhFMp2prRn5cQGzokViYi1dsg==

babel-runtime@^6.23.0, babel-runtime@^6.26.0:
  version "6.26.0"
  resolved "https://registry.nlark.com/babel-runtime/download/babel-runtime-6.26.0.tgz"
  integrity sha1-llxwWGaOgrVde/4E/yM3vItWR/4=
  dependencies:
    core-js "^2.4.0"
    regenerator-runtime "^0.11.0"

babylon@^6.18.0:
  version "6.18.0"
  resolved "https://registry.nlark.com/babylon/download/babylon-6.18.0.tgz"
  integrity sha1-ry87iPpvXB5MY00aD46sT1WzleM=

balanced-match@^0.4.2:
  version "0.4.2"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/balanced-match/-/balanced-match-0.4.2.tgz"
  integrity sha512-STw03mQKnGUYtoNjmowo4F2cRmIIxYEGiMsjjwla/u5P1lxadj/05WkNaFjNiKTgJkj8KiXbgAiRTmcQRwQNtg==

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "https://registry.nlark.com/balanced-match/download/balanced-match-1.0.2.tgz"
  integrity sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=

balanced-match@0.1.0:
  version "0.1.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/balanced-match/-/balanced-match-0.1.0.tgz"
  integrity sha512-4xb6XqAEo3Z+5pEDJz33R8BZXI8FRJU+cDNLdKgDpmnz+pKKRVYLpdv+VvUAC7yUhBMj4izmyt19eCGv1QGV7A==

base@^0.11.1:
  version "0.11.2"
  resolved "https://registry.nlark.com/base/download/base-0.11.2.tgz"
  integrity sha1-e95c7RRbbVUakNuH+DxVi060io8=
  dependencies:
    cache-base "^1.0.1"
    class-utils "^0.3.5"
    component-emitter "^1.2.1"
    define-property "^1.0.0"
    isobject "^3.0.1"
    mixin-deep "^1.2.0"
    pascalcase "^0.1.1"

base64-js@^1.0.2:
  version "1.5.1"
  resolved "https://registry.nlark.com/base64-js/download/base64-js-1.5.1.tgz"
  integrity sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=

batch@0.6.1:
  version "0.6.1"
  resolved "https://registry.nlark.com/batch/download/batch-0.6.1.tgz"
  integrity sha1-3DQxT05nkxgJP8dgJyUl+UvyXBY=

bcrypt-pbkdf@^1.0.0:
  version "1.0.2"
  resolved "https://registry.nlark.com/bcrypt-pbkdf/download/bcrypt-pbkdf-1.0.2.tgz"
  integrity sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=
  dependencies:
    tweetnacl "^0.14.3"

bfj@^6.1.1:
  version "6.1.2"
  resolved "https://registry.nlark.com/bfj/download/bfj-6.1.2.tgz"
  integrity sha1-MlyGGoIryzWKQceKM7jm4ght3n8=
  dependencies:
    bluebird "^3.5.5"
    check-types "^8.0.3"
    hoopy "^0.1.4"
    tryer "^1.0.1"

big.js@^3.1.3:
  version "3.2.0"
  resolved "https://registry.nlark.com/big.js/download/big.js-3.2.0.tgz"
  integrity sha1-pfwpi4G54Nyi5FiCR4S2XFK6WI4=

big.js@^5.2.2:
  version "5.2.2"
  resolved "https://registry.nlark.com/big.js/download/big.js-5.2.2.tgz"
  integrity sha1-ZfCvOC9Xi83HQr2cKB6cstd2gyg=

binary-extensions@^1.0.0:
  version "1.13.1"
  resolved "https://registry.nlark.com/binary-extensions/download/binary-extensions-1.13.1.tgz"
  integrity sha1-WYr+VHVbKGilMw0q/51Ou1Mgm2U=

binary-extensions@^2.0.0:
  version "2.3.0"

bindings@^1.5.0:
  version "1.5.0"
  resolved "https://registry.nlark.com/bindings/download/bindings-1.5.0.tgz"
  integrity sha1-EDU8npRTNLwFEabZCzj7x8nFBN8=
  dependencies:
    file-uri-to-path "1.0.0"

bluebird@^3.1.1, bluebird@^3.5.5:
  version "3.7.2"
  resolved "https://registry.nlark.com/bluebird/download/bluebird-3.7.2.tgz"
  integrity sha1-nyKcFb4nJFT/qXOs4NvueaGww28=

bn.js@^4.0.0, bn.js@^4.1.0, bn.js@^4.11.9:
  version "4.12.1"

bn.js@^5.2.1:
  version "5.2.1"

body-parser@1.20.3:
  version "1.20.3"
  dependencies:
    bytes "3.1.2"
    content-type "~1.0.5"
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    on-finished "2.4.1"
    qs "6.13.0"
    raw-body "2.5.2"
    type-is "~1.6.18"
    unpipe "1.0.0"

bonjour@^3.5.0:
  version "3.5.0"
  resolved "https://registry.nlark.com/bonjour/download/bonjour-3.5.0.tgz"
  integrity sha1-jokKGD2O6aI5OzhExpGkK897yfU=
  dependencies:
    array-flatten "^2.1.0"
    deep-equal "^1.0.1"
    dns-equal "^1.0.0"
    dns-txt "^2.0.2"
    multicast-dns "^6.0.1"
    multicast-dns-service-types "^1.1.0"

boolbase@^1.0.0, boolbase@~1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/boolbase/download/boolbase-1.0.0.tgz"
  integrity sha1-aN/1++YMUes3cl6p4+0xDcwed24=

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "https://registry.nlark.com/brace-expansion/download/brace-expansion-1.1.11.tgz"
  integrity sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

braces@^2.3.1, braces@^2.3.2:
  version "2.3.2"
  resolved "https://registry.nlark.com/braces/download/braces-2.3.2.tgz"
  integrity sha1-WXn9PxTNUxVl5fot8av/8d+u5yk=
  dependencies:
    arr-flatten "^1.1.0"
    array-unique "^0.3.2"
    extend-shallow "^2.0.1"
    fill-range "^4.0.0"
    isobject "^3.0.1"
    repeat-element "^1.1.2"
    snapdragon "^0.8.1"
    snapdragon-node "^2.0.1"
    split-string "^3.0.2"
    to-regex "^3.0.1"

braces@^3.0.3:
  version "3.0.3"
  dependencies:
    fill-range "^7.1.1"

braces@~3.0.2:
  version "3.0.3"
  dependencies:
    fill-range "^7.1.1"

brorand@^1.0.1, brorand@^1.1.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/brorand/download/brorand-1.1.0.tgz"
  integrity sha1-EsJe/kCkXjwyPrhnWgoM5XsiNx8=

browserify-aes@^1.0.4, browserify-aes@^1.2.0:
  version "1.2.0"
  resolved "https://registry.nlark.com/browserify-aes/download/browserify-aes-1.2.0.tgz"
  integrity sha1-Mmc0ZC9APavDADIJhTu3CtQo70g=
  dependencies:
    buffer-xor "^1.0.3"
    cipher-base "^1.0.0"
    create-hash "^1.1.0"
    evp_bytestokey "^1.0.3"
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

browserify-cipher@^1.0.1:
  version "1.0.1"
  dependencies:
    browserify-aes "^1.0.4"
    browserify-des "^1.0.0"
    evp_bytestokey "^1.0.0"

browserify-des@^1.0.0:
  version "1.0.2"
  resolved "https://registry.nlark.com/browserify-des/download/browserify-des-1.0.2.tgz"
  integrity sha1-OvTx9Zg5QDVy8cZiBDdfen9wPpw=
  dependencies:
    cipher-base "^1.0.1"
    des.js "^1.0.0"
    inherits "^2.0.1"
    safe-buffer "^5.1.2"

browserify-rsa@^4.0.0, browserify-rsa@^4.1.0:
  version "4.1.1"
  dependencies:
    bn.js "^5.2.1"
    randombytes "^2.1.0"
    safe-buffer "^5.2.1"

browserify-sign@^4.2.3:
  version "4.2.3"
  dependencies:
    bn.js "^5.2.1"
    browserify-rsa "^4.1.0"
    create-hash "^1.2.0"
    create-hmac "^1.1.7"
    elliptic "^6.5.5"
    hash-base "~3.0"
    inherits "^2.0.4"
    parse-asn1 "^5.1.7"
    readable-stream "^2.3.8"
    safe-buffer "^5.2.1"

browserify-zlib@^0.2.0:
  version "0.2.0"
  resolved "https://registry.nlark.com/browserify-zlib/download/browserify-zlib-0.2.0.tgz"
  integrity sha1-KGlFnZqjviRf6P4sofRuLn9U1z8=
  dependencies:
    pako "~1.0.5"

browserslist@^2.0.0, browserslist@^2.11.3:
  version "2.11.3"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/browserslist/-/browserslist-2.11.3.tgz"
  integrity sha512-yWu5cXT7Av6mVwzWc8lMsJMHWn4xyjSuGYi4IozbVTLUOEYPSagUB8kiMDUHA1fS3zjr8nkxkn9jdvug4BBRmA==
  dependencies:
    caniuse-lite "^1.0.30000792"
    electron-to-chromium "^1.3.30"

browserslist@^3.0.0:
  version "3.2.8"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/browserslist/-/browserslist-3.2.8.tgz"
  integrity sha512-WHVocJYavUwVgVViC0ORikPHQquXwVh939TaelZ4WDqpWgTX/FsGhl/+P4qBUAGcRvtOgDgC+xftNWWp2RUTAQ==
  dependencies:
    caniuse-lite "^1.0.30000844"
    electron-to-chromium "^1.3.47"

browserslist@^4.0.0, browserslist@^4.12.0, browserslist@^4.24.0, browserslist@^4.24.2, "browserslist@>= 4.21.0":
  version "4.24.3"
  dependencies:
    caniuse-lite "^1.0.30001688"
    electron-to-chromium "^1.5.73"
    node-releases "^2.0.19"
    update-browserslist-db "^1.1.1"

buffer-from@^1.0.0:
  version "1.1.2"
  resolved "https://registry.nlark.com/buffer-from/download/buffer-from-1.1.2.tgz"
  integrity sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=

buffer-indexof@^1.0.0:
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/buffer-indexof/download/buffer-indexof-1.1.1.tgz"
  integrity sha1-Uvq8xqYG0aADAoAmSO9o9jnaJow=

buffer-json@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/buffer-json/download/buffer-json-2.0.0.tgz"
  integrity sha1-9z4TseQvGW/i/WfQAcfXEH7dfCM=

buffer-xor@^1.0.3:
  version "1.0.3"
  resolved "https://registry.nlark.com/buffer-xor/download/buffer-xor-1.0.3.tgz"
  integrity sha1-JuYe0UIvtw3ULm42cp7VHYVf6Nk=

buffer@^4.3.0:
  version "4.9.2"
  resolved "https://registry.nlark.com/buffer/download/buffer-4.9.2.tgz"
  integrity sha1-Iw6tNEACmIZEhBqwJEr4xEu+Pvg=
  dependencies:
    base64-js "^1.0.2"
    ieee754 "^1.1.4"
    isarray "^1.0.0"

builtin-status-codes@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/builtin-status-codes/download/builtin-status-codes-3.0.0.tgz"
  integrity sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug=

bytes@3.1.2:
  version "3.1.2"

cacache@^12.0.2, cacache@^12.0.3:
  version "12.0.4"
  resolved "https://registry.nlark.com/cacache/download/cacache-12.0.4.tgz"
  integrity sha1-ZovL0QWutfHZL+JVcOyVJcj6pAw=
  dependencies:
    bluebird "^3.5.5"
    chownr "^1.1.1"
    figgy-pudding "^3.5.1"
    glob "^7.1.4"
    graceful-fs "^4.1.15"
    infer-owner "^1.0.3"
    lru-cache "^5.1.1"
    mississippi "^3.0.0"
    mkdirp "^0.5.1"
    move-concurrently "^1.0.1"
    promise-inflight "^1.0.1"
    rimraf "^2.6.3"
    ssri "^6.0.1"
    unique-filename "^1.1.1"
    y18n "^4.0.0"

cache-base@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/cache-base/download/cache-base-1.0.1.tgz?cache=0&sync_timestamp=1636237452423&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcache-base%2Fdownload%2Fcache-base-1.0.1.tgz"
  integrity sha1-Cn9GQWgxyLZi7jb+TnxZ129marI=
  dependencies:
    collection-visit "^1.0.0"
    component-emitter "^1.2.1"
    get-value "^2.0.6"
    has-value "^1.0.0"
    isobject "^3.0.1"
    set-value "^2.0.0"
    to-object-path "^0.3.0"
    union-value "^1.0.0"
    unset-value "^1.0.0"

cache-loader@^4.1.0:
  version "4.1.0"
  resolved "https://registry.nlark.com/cache-loader/download/cache-loader-4.1.0.tgz"
  integrity sha1-mUjK41OuwKH8ser9ojAIFuyFOH4=
  dependencies:
    buffer-json "^2.0.0"
    find-cache-dir "^3.0.0"
    loader-utils "^1.2.3"
    mkdirp "^0.5.1"
    neo-async "^2.6.1"
    schema-utils "^2.0.0"

call-bind-apply-helpers@^1.0.0, call-bind-apply-helpers@^1.0.1:
  version "1.0.1"
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"

call-bind@^1.0.7, call-bind@^1.0.8:
  version "1.0.8"
  dependencies:
    call-bind-apply-helpers "^1.0.0"
    es-define-property "^1.0.0"
    get-intrinsic "^1.2.4"
    set-function-length "^1.2.2"

call-bound@^1.0.2, call-bound@^1.0.3:
  version "1.0.3"
  dependencies:
    call-bind-apply-helpers "^1.0.1"
    get-intrinsic "^1.2.6"

call-me-maybe@^1.0.1:
  version "1.0.2"

caller-callsite@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/caller-callsite/download/caller-callsite-2.0.0.tgz?cache=0&sync_timestamp=1633617059132&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcaller-callsite%2Fdownload%2Fcaller-callsite-2.0.0.tgz"
  integrity sha1-hH4PzgoiN1CpoCfFSzNzGtMVQTQ=
  dependencies:
    callsites "^2.0.0"

caller-path@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/caller-path/download/caller-path-2.0.0.tgz"
  integrity sha1-Ro+DBE42mrIBD6xfBs7uFbsssfQ=
  dependencies:
    caller-callsite "^2.0.0"

callsites@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/callsites/download/callsites-2.0.0.tgz?cache=0&sync_timestamp=1628464722297&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcallsites%2Fdownload%2Fcallsites-2.0.0.tgz"
  integrity sha1-BuuE8A7qQT2oav/vrL/7Ngk7PFA=

callsites@^3.0.0:
  version "3.1.0"
  resolved "https://registry.nlark.com/callsites/download/callsites-3.1.0.tgz?cache=0&sync_timestamp=1628464722297&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcallsites%2Fdownload%2Fcallsites-3.1.0.tgz"
  integrity sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=

camel-case@3.0.x:
  version "3.0.0"
  resolved "https://registry.nlark.com/camel-case/download/camel-case-3.0.0.tgz"
  integrity sha1-yjw2iKTpzzpM2nd9xNy8cTJJz3M=
  dependencies:
    no-case "^2.2.0"
    upper-case "^1.1.1"

camelcase@^5.0.0, camelcase@^5.3.1:
  version "5.3.1"
  resolved "https://registry.npmmirror.com/camelcase/download/camelcase-5.3.1.tgz?cache=0&sync_timestamp=1636945205805&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcamelcase%2Fdownload%2Fcamelcase-5.3.1.tgz"
  integrity sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=

caniuse-api@^2.0.0:
  version "2.0.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/caniuse-api/-/caniuse-api-2.0.0.tgz"
  integrity sha512-425yJRcUDCCMKc0Zga2KSUe7Qp7nCtL8H0BJIsDxF9yMzG2eSYvOggi5U1wXzxgcSgDGnzVLvZ8dZGMBrA6Ltg==
  dependencies:
    browserslist "^2.0.0"
    caniuse-lite "^1.0.0"
    lodash.memoize "^4.1.2"
    lodash.uniq "^4.5.0"

caniuse-api@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/caniuse-api/download/caniuse-api-3.0.0.tgz"
  integrity sha1-Xk2Q4idJYdRikZl99Znj7QCO5MA=
  dependencies:
    browserslist "^4.0.0"
    caniuse-lite "^1.0.0"
    lodash.memoize "^4.1.2"
    lodash.uniq "^4.5.0"

caniuse-lite@^1.0.0, caniuse-lite@^1.0.30000697, caniuse-lite@^1.0.30000792, caniuse-lite@^1.0.30000805, caniuse-lite@^1.0.30000808, caniuse-lite@^1.0.30000844, caniuse-lite@^1.0.30001109, caniuse-lite@^1.0.30001688:
  version "1.0.30001690"

case-sensitive-paths-webpack-plugin@^2.3.0:
  version "2.4.0"
  resolved "https://registry.nlark.com/case-sensitive-paths-webpack-plugin/download/case-sensitive-paths-webpack-plugin-2.4.0.tgz"
  integrity sha1-22QGbGQi7tLgjMFLmGykN5bbxtQ=

caseless@~0.12.0:
  version "0.12.0"
  resolved "https://registry.nlark.com/caseless/download/caseless-0.12.0.tgz"
  integrity sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=

chalk@^1.1.3:
  version "1.1.3"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/chalk/-/chalk-1.1.3.tgz"
  integrity sha512-U3lRVLMSlsCfjqYPbLyVv11M9CPW4I728d6TCKMAOJueEeB9/8o+eSsMnxPJD+Q+K909sdESg7C+tIkoH6on1A==
  dependencies:
    ansi-styles "^2.2.1"
    escape-string-regexp "^1.0.2"
    has-ansi "^2.0.0"
    strip-ansi "^3.0.0"
    supports-color "^2.0.0"

chalk@^2.0.1, chalk@^2.1.0, chalk@^2.4.1, chalk@^2.4.2:
  version "2.4.2"
  resolved "https://registry.nlark.com/chalk/download/chalk-2.4.2.tgz?cache=0&sync_timestamp=1627646734234&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fchalk%2Fdownload%2Fchalk-2.4.2.tgz"
  integrity sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/chalk/download/chalk-3.0.0.tgz?cache=0&sync_timestamp=1627646734234&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fchalk%2Fdownload%2Fchalk-3.0.0.tgz"
  integrity sha1-P3PCv1JlkfV0zEksUeJFY0n4ROQ=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chalk@^4.0.0:
  version "4.1.2"
  resolved "https://registry.nlark.com/chalk/download/chalk-4.1.2.tgz?cache=0&sync_timestamp=1627646734234&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fchalk%2Fdownload%2Fchalk-4.1.2.tgz"
  integrity sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chalk@^4.1.0:
  version "4.1.2"
  resolved "https://registry.nlark.com/chalk/download/chalk-4.1.2.tgz?cache=0&sync_timestamp=1627646734234&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fchalk%2Fdownload%2Fchalk-4.1.2.tgz"
  integrity sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chardet@^0.7.0:
  version "0.7.0"
  resolved "https://registry.npmmirror.com/chardet/download/chardet-0.7.0.tgz?cache=0&sync_timestamp=1634639163489&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fchardet%2Fdownload%2Fchardet-0.7.0.tgz"
  integrity sha1-kAlISfCTfy7twkJdDSip5fDLrZ4=

check-types@^8.0.3:
  version "8.0.3"
  resolved "https://registry.nlark.com/check-types/download/check-types-8.0.3.tgz"
  integrity sha1-M1bMoZyIlUTy16le1JzlCKDs9VI=

chokidar@^2.1.8:
  version "2.1.8"
  resolved "https://registry.npmmirror.com/chokidar/download/chokidar-2.1.8.tgz"
  integrity sha1-gEs6e2qZNYw8XGHnHYco8EHP+Rc=
  dependencies:
    anymatch "^2.0.0"
    async-each "^1.0.1"
    braces "^2.3.2"
    glob-parent "^3.1.0"
    inherits "^2.0.3"
    is-binary-path "^1.0.0"
    is-glob "^4.0.0"
    normalize-path "^3.0.0"
    path-is-absolute "^1.0.0"
    readdirp "^2.2.1"
    upath "^1.1.1"
  optionalDependencies:
    fsevents "^1.2.7"

chokidar@^3.4.1:
  version "3.6.0"
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

chownr@^1.1.1:
  version "1.1.4"
  resolved "https://registry.nlark.com/chownr/download/chownr-1.1.4.tgz"
  integrity sha1-b8nXtC0ypYNZYzdmbn0ICE2izGs=

chrome-trace-event@^1.0.2:
  version "1.0.4"

ci-info@^1.5.0:
  version "1.6.0"
  resolved "https://registry.nlark.com/ci-info/download/ci-info-1.6.0.tgz"
  integrity sha1-LKINu5zrMtRSSmgzAzE/AwSx5Jc=

cipher-base@^1.0.0, cipher-base@^1.0.1, cipher-base@^1.0.3:
  version "1.0.6"
  dependencies:
    inherits "^2.0.4"
    safe-buffer "^5.2.1"

class-utils@^0.3.5:
  version "0.3.6"
  resolved "https://registry.nlark.com/class-utils/download/class-utils-0.3.6.tgz"
  integrity sha1-+TNprouafOAv1B+q0MqDAzGQxGM=
  dependencies:
    arr-union "^3.1.0"
    define-property "^0.2.5"
    isobject "^3.0.0"
    static-extend "^0.1.1"

clean-css@4.2.x:
  version "4.2.4"
  resolved "https://registry.npmmirror.com/clean-css/download/clean-css-4.2.4.tgz?cache=0&sync_timestamp=1634992314911&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fclean-css%2Fdownload%2Fclean-css-4.2.4.tgz"
  integrity sha1-czv0brpOYHxokepXwkqYk1aDEXg=
  dependencies:
    source-map "~0.6.0"

cli-cursor@^2.1.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/cli-cursor/download/cli-cursor-2.1.0.tgz?cache=0&sync_timestamp=1629747506749&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcli-cursor%2Fdownload%2Fcli-cursor-2.1.0.tgz"
  integrity sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU=
  dependencies:
    restore-cursor "^2.0.0"

cli-cursor@^3.1.0:
  version "3.1.0"
  resolved "https://registry.nlark.com/cli-cursor/download/cli-cursor-3.1.0.tgz?cache=0&sync_timestamp=1629747506749&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcli-cursor%2Fdownload%2Fcli-cursor-3.1.0.tgz"
  integrity sha1-JkMFp65JDR0Dvwybp8kl0XU68wc=
  dependencies:
    restore-cursor "^3.1.0"

cli-highlight@^2.1.4:
  version "2.1.11"
  resolved "https://registry.nlark.com/cli-highlight/download/cli-highlight-2.1.11.tgz"
  integrity sha1-SXNvpFLwqvT65YDjCssmgo0twb8=
  dependencies:
    chalk "^4.0.0"
    highlight.js "^10.7.1"
    mz "^2.4.0"
    parse5 "^5.1.1"
    parse5-htmlparser2-tree-adapter "^6.0.0"
    yargs "^16.0.0"

cli-spinners@^2.0.0:
  version "2.9.2"

cli-width@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/cli-width/download/cli-width-3.0.0.tgz"
  integrity sha1-ovSEN6LKqaIkNueUvwceyeYc7fY=

clipboardy@^2.3.0:
  version "2.3.0"
  resolved "https://registry.npmmirror.com/clipboardy/download/clipboardy-2.3.0.tgz"
  integrity sha1-PCkDZQxo5GqRs4iYW8J3QofbopA=
  dependencies:
    arch "^2.1.1"
    execa "^1.0.0"
    is-wsl "^2.1.1"

cliui@^5.0.0:
  version "5.0.0"
  resolved "https://registry.nlark.com/cliui/download/cliui-5.0.0.tgz"
  integrity sha1-3u/P2y6AB4SqNPRvoI4GhRx7u8U=
  dependencies:
    string-width "^3.1.0"
    strip-ansi "^5.2.0"
    wrap-ansi "^5.1.0"

cliui@^6.0.0:
  version "6.0.0"
  resolved "https://registry.nlark.com/cliui/download/cliui-6.0.0.tgz"
  integrity sha1-UR1wLAxOQcoVbX0OlgIfI+EyJbE=
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.0"
    wrap-ansi "^6.2.0"

cliui@^7.0.2:
  version "7.0.4"
  resolved "https://registry.nlark.com/cliui/download/cliui-7.0.4.tgz"
  integrity sha1-oCZe5lVHb8gHrqnfPfjfd4OAi08=
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.0"
    wrap-ansi "^7.0.0"

clone@^1.0.2:
  version "1.0.4"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/clone/-/clone-1.0.4.tgz"
  integrity sha512-JQHZ2QMW6l3aH/j6xCqQThY/9OH4D/9ls34cgkUBiEeocRTU04tHfKPBsUK1PqZCUQM7GiA0IIXJSuXHI64Kbg==

coa@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/coa/download/coa-2.0.2.tgz"
  integrity sha1-Q/bCEVG07yv1cYfbDXPeIp4+fsM=
  dependencies:
    "@types/q" "^1.5.1"
    chalk "^2.4.1"
    q "^1.1.2"

collection-visit@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/collection-visit/download/collection-visit-1.0.0.tgz"
  integrity sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=
  dependencies:
    map-visit "^1.0.0"
    object-visit "^1.0.0"

color-convert@^1.3.0, color-convert@^1.8.2, color-convert@^1.9.0, color-convert@^1.9.1, color-convert@^1.9.3:
  version "1.9.3"
  resolved "https://registry.nlark.com/color-convert/download/color-convert-1.9.3.tgz"
  integrity sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "https://registry.nlark.com/color-convert/download/color-convert-2.0.1.tgz"
  integrity sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=
  dependencies:
    color-name "~1.1.4"

color-name@^1.0.0, color-name@~1.1.4:
  version "1.1.4"
  resolved "https://registry.nlark.com/color-name/download/color-name-1.1.4.tgz"
  integrity sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=

color-name@1.1.3:
  version "1.1.3"
  resolved "https://registry.nlark.com/color-name/download/color-name-1.1.3.tgz"
  integrity sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=

color-string@^0.3.0:
  version "0.3.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/color-string/-/color-string-0.3.0.tgz"
  integrity sha512-sz29j1bmSDfoAxKIEU6zwoIZXN6BrFbAMIhfYCNyiZXBDuU/aiHlN84lp/xDzL2ubyFhLDobHIlU1X70XRrMDA==
  dependencies:
    color-name "^1.0.0"

color-string@^1.4.0, color-string@^1.5.2, color-string@^1.6.0:
  version "1.9.1"
  dependencies:
    color-name "^1.0.0"
    simple-swizzle "^0.2.2"

color@^0.11.0:
  version "0.11.4"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/color/-/color-0.11.4.tgz"
  integrity sha512-Ajpjd8asqZ6EdxQeqGzU5WBhhTfJ/0cA4Wlbre7e5vXfmDSmda7Ov6jeKoru+b0vHcb1CqvuroTHp5zIWzhVMA==
  dependencies:
    clone "^1.0.2"
    color-convert "^1.3.0"
    color-string "^0.3.0"

color@^1.0.3:
  version "1.0.3"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/color/-/color-1.0.3.tgz"
  integrity sha512-HWCZFEr1styyDW4BAYpeDtVrUfTzPK0XOxs8izgcGofpl/T9U2m5f7cYjcPKorrCLKDrFlmcHciMdktwxr1btw==
  dependencies:
    color-convert "^1.8.2"
    color-string "^1.4.0"

color@^2.0.1:
  version "2.0.1"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/color/-/color-2.0.1.tgz"
  integrity sha512-ubUCVVKfT7r2w2D3qtHakj8mbmKms+tThR8gI8zEYCbUBl8/voqFGt3kgBqGwXAopgXybnkuOq+qMYCRrp4cXw==
  dependencies:
    color-convert "^1.9.1"
    color-string "^1.5.2"

color@^3.0.0:
  version "3.2.1"
  resolved "https://registry.nlark.com/color/download/color-3.2.1.tgz?cache=0&sync_timestamp=1628104117021&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcolor%2Fdownload%2Fcolor-3.2.1.tgz"
  integrity sha1-NUTcGYyvRJDD7MmnkLVP6f9F4WQ=
  dependencies:
    color-convert "^1.9.3"
    color-string "^1.6.0"

combined-stream@^1.0.6, combined-stream@~1.0.6:
  version "1.0.8"
  resolved "https://registry.nlark.com/combined-stream/download/combined-stream-1.0.8.tgz"
  integrity sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=
  dependencies:
    delayed-stream "~1.0.0"

commander@^2.18.0, commander@^2.20.0:
  version "2.20.3"
  resolved "https://registry.npmmirror.com/commander/download/commander-2.20.3.tgz"
  integrity sha1-/UhehMA+tIgcIHIrpIA16FMa6zM=

commander@~2.19.0:
  version "2.19.0"
  resolved "https://registry.npmmirror.com/commander/download/commander-2.19.0.tgz"
  integrity sha1-9hmKqE5bg8RgVLlN3tv+1e6f8So=

commander@2.17.x:
  version "2.17.1"
  resolved "https://registry.npmmirror.com/commander/download/commander-2.17.1.tgz"
  integrity sha1-vXerfebelCBc6sxy8XFtKfIKd78=

common-tags@^1.8.0:
  version "1.8.2"
  resolved "https://registry.npmmirror.com/common-tags/download/common-tags-1.8.2.tgz?cache=0&sync_timestamp=1637093528356&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcommon-tags%2Fdownload%2Fcommon-tags-1.8.2.tgz"
  integrity sha512-gk/Z852D2Wtb//0I+kRFNKKE9dIIVirjoqPoA1wJU+XePVXZfGeBpk45+A1rKO4Q43prqWBNY/MiIeRLbPWUaA==

commondir@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/commondir/download/commondir-1.0.1.tgz"
  integrity sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=

component-emitter@^1.2.1:
  version "1.3.1"

compressible@~2.0.18:
  version "2.0.18"
  dependencies:
    mime-db ">= 1.43.0 < 2"

compression@^1.7.4:
  version "1.7.5"
  dependencies:
    bytes "3.1.2"
    compressible "~2.0.18"
    debug "2.6.9"
    negotiator "~0.6.4"
    on-headers "~1.0.2"
    safe-buffer "5.2.1"
    vary "~1.1.2"

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://registry.nlark.com/concat-map/download/concat-map-0.0.1.tgz"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

concat-stream@^1.5.0:
  version "1.6.2"
  resolved "https://registry.nlark.com/concat-stream/download/concat-stream-1.6.2.tgz"
  integrity sha1-kEvfGUzTEi/Gdcd/xKw9T/D9GjQ=
  dependencies:
    buffer-from "^1.0.0"
    inherits "^2.0.3"
    readable-stream "^2.2.2"
    typedarray "^0.0.6"

connect-history-api-fallback@^1.6.0:
  version "1.6.0"
  resolved "https://registry.nlark.com/connect-history-api-fallback/download/connect-history-api-fallback-1.6.0.tgz"
  integrity sha1-izIIk1kwjRERFdgcrT/Oq4iPl7w=

console-browserify@^1.1.0:
  version "1.2.0"
  resolved "https://registry.nlark.com/console-browserify/download/console-browserify-1.2.0.tgz"
  integrity sha1-ZwY871fOts9Jk6KrOlWECujEkzY=

consolidate@^0.15.1:
  version "0.15.1"
  resolved "https://registry.nlark.com/consolidate/download/consolidate-0.15.1.tgz"
  integrity sha1-IasEMjXHGgfUXZqtmFk7DbpWurc=
  dependencies:
    bluebird "^3.1.1"

constants-browserify@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/constants-browserify/download/constants-browserify-1.0.0.tgz"
  integrity sha1-wguW2MYXdIqvHBYCF2DNJ/y4y3U=

content-disposition@0.5.4:
  version "0.5.4"
  dependencies:
    safe-buffer "5.2.1"

content-type@~1.0.4, content-type@~1.0.5:
  version "1.0.5"

convert-source-map@^2.0.0:
  version "2.0.0"

cookie-signature@1.0.6:
  version "1.0.6"
  resolved "https://registry.nlark.com/cookie-signature/download/cookie-signature-1.0.6.tgz"
  integrity sha1-4wOogrNCzD7oylE6eZmXNNqzriw=

cookie@0.7.1:
  version "0.7.1"

copy-concurrently@^1.0.0:
  version "1.0.5"
  resolved "https://registry.nlark.com/copy-concurrently/download/copy-concurrently-1.0.5.tgz"
  integrity sha1-kilzmMrjSTf8r9bsgTnBgFHwteA=
  dependencies:
    aproba "^1.1.1"
    fs-write-stream-atomic "^1.0.8"
    iferr "^0.1.5"
    mkdirp "^0.5.1"
    rimraf "^2.5.4"
    run-queue "^1.0.0"

copy-descriptor@^0.1.0:
  version "0.1.1"
  resolved "https://registry.nlark.com/copy-descriptor/download/copy-descriptor-0.1.1.tgz"
  integrity sha1-Z29us8OZl8LuGsOpJP1hJHSPV40=

copy-webpack-plugin@^5.1.1:
  version "5.1.2"
  resolved "https://registry.npmmirror.com/copy-webpack-plugin/download/copy-webpack-plugin-5.1.2.tgz?cache=0&sync_timestamp=1637161330355&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcopy-webpack-plugin%2Fdownload%2Fcopy-webpack-plugin-5.1.2.tgz"
  integrity sha1-ioieHcr6bJHGzUvhrRWPHTgjuuI=
  dependencies:
    cacache "^12.0.3"
    find-cache-dir "^2.1.0"
    glob-parent "^3.1.0"
    globby "^7.1.1"
    is-glob "^4.0.1"
    loader-utils "^1.2.3"
    minimatch "^3.0.4"
    normalize-path "^3.0.0"
    p-limit "^2.2.1"
    schema-utils "^1.0.0"
    serialize-javascript "^4.0.0"
    webpack-log "^2.0.0"

core-js-compat@^3.38.0, core-js-compat@^3.38.1, core-js-compat@^3.6.5:
  version "3.39.0"
  dependencies:
    browserslist "^4.24.2"

core-js@^2.4.0:
  version "2.6.12"
  resolved "https://registry.npmmirror.com/core-js/download/core-js-2.6.12.tgz"
  integrity sha1-2TM9+nsGXjR8xWgiGdb2kIWcwuw=

core-js@^3.6.5:
  version "3.39.0"

core-util-is@~1.0.0:
  version "1.0.3"
  resolved "https://registry.nlark.com/core-util-is/download/core-util-is-1.0.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcore-util-is%2Fdownload%2Fcore-util-is-1.0.3.tgz"
  integrity sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U=

core-util-is@1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/core-util-is/download/core-util-is-1.0.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcore-util-is%2Fdownload%2Fcore-util-is-1.0.2.tgz"
  integrity sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=

cosmiconfig@^5.0.0:
  version "5.2.1"
  resolved "https://registry.nlark.com/cosmiconfig/download/cosmiconfig-5.2.1.tgz"
  integrity sha1-BA9yaAnFked6F8CjYmykW08Wixo=
  dependencies:
    import-fresh "^2.0.0"
    is-directory "^0.3.1"
    js-yaml "^3.13.1"
    parse-json "^4.0.0"

create-ecdh@^4.0.4:
  version "4.0.4"
  dependencies:
    bn.js "^4.1.0"
    elliptic "^6.5.3"

create-hash@^1.1.0, create-hash@^1.1.2, create-hash@^1.2.0:
  version "1.2.0"
  resolved "https://registry.nlark.com/create-hash/download/create-hash-1.2.0.tgz"
  integrity sha1-iJB4rxGmN1a8+1m9IhmWvjqe8ZY=
  dependencies:
    cipher-base "^1.0.1"
    inherits "^2.0.1"
    md5.js "^1.3.4"
    ripemd160 "^2.0.1"
    sha.js "^2.4.0"

create-hmac@^1.1.4, create-hmac@^1.1.7:
  version "1.1.7"
  resolved "https://registry.nlark.com/create-hmac/download/create-hmac-1.1.7.tgz"
  integrity sha1-aRcMeLOrlXFHsriwRXLkfq0iQ/8=
  dependencies:
    cipher-base "^1.0.3"
    create-hash "^1.1.0"
    inherits "^2.0.1"
    ripemd160 "^2.0.0"
    safe-buffer "^5.0.1"
    sha.js "^2.4.8"

cross-spawn@^5.0.1:
  version "5.1.0"
  resolved "https://registry.nlark.com/cross-spawn/download/cross-spawn-5.1.0.tgz"
  integrity sha1-6L0O/uWPz/b4+UUQoKVUu/ojVEk=
  dependencies:
    lru-cache "^4.0.1"
    shebang-command "^1.2.0"
    which "^1.2.9"

cross-spawn@^6.0.0, cross-spawn@^6.0.5:
  version "6.0.6"
  dependencies:
    nice-try "^1.0.4"
    path-key "^2.0.1"
    semver "^5.5.0"
    shebang-command "^1.2.0"
    which "^1.2.9"

cross-spawn@^7.0.0:
  version "7.0.6"
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

crypto-browserify@^3.11.0:
  version "3.12.1"
  dependencies:
    browserify-cipher "^1.0.1"
    browserify-sign "^4.2.3"
    create-ecdh "^4.0.4"
    create-hash "^1.2.0"
    create-hmac "^1.1.7"
    diffie-hellman "^5.0.3"
    hash-base "~3.0.4"
    inherits "^2.0.4"
    pbkdf2 "^3.1.2"
    public-encrypt "^4.0.3"
    randombytes "^2.1.0"
    randomfill "^1.0.4"

crypto-js@^4.1.1:
  version "4.2.0"

css-color-function@~1.3.3:
  version "1.3.3"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/css-color-function/-/css-color-function-1.3.3.tgz"
  integrity sha512-YD/WhiRZIYgadwFJ48X5QmlOQ/w8Me4yQI6/eSUoiE8spIFp+S/rGpsAH48iyq/0ZWkCDWqVQKUlQmUzn7BQ9w==
  dependencies:
    balanced-match "0.1.0"
    color "^0.11.0"
    debug "^3.1.0"
    rgb "~0.1.0"

css-color-names@^0.0.4, css-color-names@0.0.4:
  version "0.0.4"
  resolved "https://registry.nlark.com/css-color-names/download/css-color-names-0.0.4.tgz"
  integrity sha1-gIrcLnnPhHOAabZGyyDsJ762KeA=

css-declaration-sorter@^4.0.1:
  version "4.0.1"
  resolved "https://registry.nlark.com/css-declaration-sorter/download/css-declaration-sorter-4.0.1.tgz"
  integrity sha1-wZiUD2OnbX42wecQGLABchBUyyI=
  dependencies:
    postcss "^7.0.1"
    timsort "^0.3.0"

css-init@^1.1.9:
  version "1.2.14"

css-loader@*, css-loader@^3.5.3:
  version "3.6.0"
  resolved "https://registry.npmmirror.com/css-loader/download/css-loader-3.6.0.tgz?cache=0&sync_timestamp=1635967924209&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcss-loader%2Fdownload%2Fcss-loader-3.6.0.tgz"
  integrity sha1-Lkssfm4tJ/jI8o9hv/zS5ske9kU=
  dependencies:
    camelcase "^5.3.1"
    cssesc "^3.0.0"
    icss-utils "^4.1.1"
    loader-utils "^1.2.3"
    normalize-path "^3.0.0"
    postcss "^7.0.32"
    postcss-modules-extract-imports "^2.0.0"
    postcss-modules-local-by-default "^3.0.2"
    postcss-modules-scope "^2.2.0"
    postcss-modules-values "^3.0.0"
    postcss-value-parser "^4.1.0"
    schema-utils "^2.7.0"
    semver "^6.3.0"

css-parse@~2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/css-parse/download/css-parse-2.0.0.tgz"
  integrity sha1-pGjuZnwW2BzPBcWMONKpfHgNv9Q=
  dependencies:
    css "^2.0.0"

css-select-base-adapter@^0.1.1:
  version "0.1.1"
  resolved "https://registry.nlark.com/css-select-base-adapter/download/css-select-base-adapter-0.1.1.tgz"
  integrity sha1-Oy/0lyzDYquIVhUHqVQIoUMhNdc=

css-select@^2.0.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/css-select/download/css-select-2.1.0.tgz?cache=0&sync_timestamp=1622994276976&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcss-select%2Fdownload%2Fcss-select-2.1.0.tgz"
  integrity sha1-ajRlM1ZjWTSoG6ymjQJVQyEF2+8=
  dependencies:
    boolbase "^1.0.0"
    css-what "^3.2.1"
    domutils "^1.7.0"
    nth-check "^1.0.2"

css-select@^4.1.3:
  version "4.3.0"
  dependencies:
    boolbase "^1.0.0"
    css-what "^6.0.1"
    domhandler "^4.3.1"
    domutils "^2.8.0"
    nth-check "^2.0.1"

css-tree@^1.1.2:
  version "1.1.3"
  resolved "https://registry.npmmirror.com/css-tree/-/css-tree-1.1.3.tgz"
  integrity sha512-tRpdppF7TRazZrjJ6v3stzv93qxRcSsFmW6cX0Zm2NVKpxE1WV1HblnghVv9TreireHkqI/VDEsfolRF1p6y7Q==
  dependencies:
    mdn-data "2.0.14"
    source-map "^0.6.1"

css-tree@1.0.0-alpha.37:
  version "1.0.0-alpha.37"
  resolved "https://registry.nlark.com/css-tree/download/css-tree-1.0.0-alpha.37.tgz"
  integrity sha1-mL69YsTB2flg7DQM+fdSLjBwmiI=
  dependencies:
    mdn-data "2.0.4"
    source-map "^0.6.1"

css-unit-converter@^1.1.1:
  version "1.1.2"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/css-unit-converter/-/css-unit-converter-1.1.2.tgz"
  integrity sha512-IiJwMC8rdZE0+xiEZHeru6YoONC4rfPMqGm2W85jMIbkFvv5nFTwJVFHam2eFrN6txmoUYFAFXiv8ICVeTO0MA==

css-what@^3.2.1:
  version "3.4.2"
  resolved "https://registry.npmmirror.com/css-what/download/css-what-3.4.2.tgz"
  integrity sha1-6nAm/LAXd+295SEk4h8yfnrpUOQ=

css-what@^6.0.1:
  version "6.1.0"

css@^2.0.0:
  version "2.2.4"
  resolved "https://registry.nlark.com/css/download/css-2.2.4.tgz"
  integrity sha1-xkZ1XHOXHyu6amAeLPL9cbEpiSk=
  dependencies:
    inherits "^2.0.3"
    source-map "^0.6.1"
    source-map-resolve "^0.5.2"
    urix "^0.1.0"

cssesc@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/cssesc/download/cssesc-3.0.0.tgz"
  integrity sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4=

cssnano-preset-default@^4.0.0, cssnano-preset-default@^4.0.8:
  version "4.0.8"
  resolved "https://registry.npmmirror.com/cssnano-preset-default/download/cssnano-preset-default-4.0.8.tgz?cache=0&sync_timestamp=1637084982358&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcssnano-preset-default%2Fdownload%2Fcssnano-preset-default-4.0.8.tgz"
  integrity sha1-kgYisfwelaNOiDggPxOXpQTy0/8=
  dependencies:
    css-declaration-sorter "^4.0.1"
    cssnano-util-raw-cache "^4.0.1"
    postcss "^7.0.0"
    postcss-calc "^7.0.1"
    postcss-colormin "^4.0.3"
    postcss-convert-values "^4.0.1"
    postcss-discard-comments "^4.0.2"
    postcss-discard-duplicates "^4.0.2"
    postcss-discard-empty "^4.0.1"
    postcss-discard-overridden "^4.0.1"
    postcss-merge-longhand "^4.0.11"
    postcss-merge-rules "^4.0.3"
    postcss-minify-font-values "^4.0.2"
    postcss-minify-gradients "^4.0.2"
    postcss-minify-params "^4.0.2"
    postcss-minify-selectors "^4.0.2"
    postcss-normalize-charset "^4.0.1"
    postcss-normalize-display-values "^4.0.2"
    postcss-normalize-positions "^4.0.2"
    postcss-normalize-repeat-style "^4.0.2"
    postcss-normalize-string "^4.0.2"
    postcss-normalize-timing-functions "^4.0.2"
    postcss-normalize-unicode "^4.0.1"
    postcss-normalize-url "^4.0.1"
    postcss-normalize-whitespace "^4.0.2"
    postcss-ordered-values "^4.1.2"
    postcss-reduce-initial "^4.0.3"
    postcss-reduce-transforms "^4.0.2"
    postcss-svgo "^4.0.3"
    postcss-unique-selectors "^4.0.1"

cssnano-util-get-arguments@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/cssnano-util-get-arguments/download/cssnano-util-get-arguments-4.0.0.tgz"
  integrity sha1-7ToIKZ8h11dBsg87gfGU7UnMFQ8=

cssnano-util-get-match@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/cssnano-util-get-match/download/cssnano-util-get-match-4.0.0.tgz"
  integrity sha1-wOTKB/U4a7F+xeUiULT1lhNlFW0=

cssnano-util-raw-cache@^4.0.1:
  version "4.0.1"
  resolved "https://registry.nlark.com/cssnano-util-raw-cache/download/cssnano-util-raw-cache-4.0.1.tgz"
  integrity sha1-sm1f1fcqEd/np4RvtMZyYPlr8oI=
  dependencies:
    postcss "^7.0.0"

cssnano-util-same-parent@^4.0.0:
  version "4.0.1"
  resolved "https://registry.nlark.com/cssnano-util-same-parent/download/cssnano-util-same-parent-4.0.1.tgz"
  integrity sha1-V0CC+yhZ0ttDOFWDXZqEVuoYu/M=

cssnano@^4.0.0:
  version "4.1.11"
  resolved "https://registry.npmmirror.com/cssnano/download/cssnano-4.1.11.tgz?cache=0&sync_timestamp=1637085734194&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcssnano%2Fdownload%2Fcssnano-4.1.11.tgz"
  integrity sha1-x7X1uB2iacsf2YLLlgwSAJEMmpk=
  dependencies:
    cosmiconfig "^5.0.0"
    cssnano-preset-default "^4.0.8"
    is-resolvable "^1.0.0"
    postcss "^7.0.0"

cssnano@^4.1.10:
  version "4.1.11"
  resolved "https://registry.npmmirror.com/cssnano/download/cssnano-4.1.11.tgz?cache=0&sync_timestamp=1637085734194&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcssnano%2Fdownload%2Fcssnano-4.1.11.tgz"
  integrity sha1-x7X1uB2iacsf2YLLlgwSAJEMmpk=
  dependencies:
    cosmiconfig "^5.0.0"
    cssnano-preset-default "^4.0.8"
    is-resolvable "^1.0.0"
    postcss "^7.0.0"

csso@^4.0.2:
  version "4.2.0"
  resolved "https://registry.npmmirror.com/csso/-/csso-4.2.0.tgz"
  integrity sha512-wvlcdIbf6pwKEk7vHj8/Bkc0B4ylXZruLvOgs9doS5eOsOpuodOV2zJChSpkp+pRpYQLQMeF04nr3Z68Sta9jA==
  dependencies:
    css-tree "^1.1.2"

csstype@^3.1.3:
  version "3.1.3"

cuint@^0.2.2:
  version "0.2.2"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/cuint/-/cuint-0.2.2.tgz"
  integrity sha512-d4ZVpCW31eWwCMe1YT3ur7mUDnTXbgwyzaL320DrcRT45rfjYxkt5QWLrmOJ+/UEAI2+fQgKe/fCjR8l4TpRgw==

cyclist@^1.0.1:
  version "1.0.2"

dashdash@^1.12.0:
  version "1.14.1"
  resolved "https://registry.nlark.com/dashdash/download/dashdash-1.14.1.tgz"
  integrity sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=
  dependencies:
    assert-plus "^1.0.0"

data-view-buffer@^1.0.2:
  version "1.0.2"
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    is-data-view "^1.0.2"

data-view-byte-length@^1.0.2:
  version "1.0.2"
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    is-data-view "^1.0.2"

data-view-byte-offset@^1.0.1:
  version "1.0.1"
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    is-data-view "^1.0.1"

date-fns@^2.26.0:
  version "2.30.0"
  dependencies:
    "@babel/runtime" "^7.21.0"

debug@^2.2.0:
  version "2.6.9"
  resolved "https://registry.npmmirror.com/debug/download/debug-2.6.9.tgz"
  integrity sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=
  dependencies:
    ms "2.0.0"

debug@^2.3.3:
  version "2.6.9"
  resolved "https://registry.npmmirror.com/debug/download/debug-2.6.9.tgz"
  integrity sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=
  dependencies:
    ms "2.0.0"

debug@^3.1.0:
  version "3.2.7"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/debug/-/debug-3.2.7.tgz"
  integrity sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==
  dependencies:
    ms "^2.1.1"

debug@^3.2.7:
  version "3.2.7"
  dependencies:
    ms "^2.1.1"

debug@^4.0.1, debug@^4.1.0, debug@^4.1.1, debug@^4.3.1:
  version "4.4.0"
  dependencies:
    ms "^2.1.3"

debug@~3.1.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/debug/download/debug-3.1.0.tgz"
  integrity sha1-W7WgZyYotkFJVmuhaBnmFRjGcmE=
  dependencies:
    ms "2.0.0"

debug@2.6.9:
  version "2.6.9"
  resolved "https://registry.npmmirror.com/debug/download/debug-2.6.9.tgz"
  integrity sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=
  dependencies:
    ms "2.0.0"

decamelize@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/decamelize/download/decamelize-1.2.0.tgz"
  integrity sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=

decode-uri-component@^0.2.0, decode-uri-component@^0.2.2:
  version "0.2.2"

deep-equal@^1.0.1:
  version "1.1.2"
  dependencies:
    is-arguments "^1.1.1"
    is-date-object "^1.0.5"
    is-regex "^1.1.4"
    object-is "^1.1.5"
    object-keys "^1.1.1"
    regexp.prototype.flags "^1.5.1"

deep-is@~0.1.3:
  version "0.1.4"
  resolved "https://registry.nlark.com/deep-is/download/deep-is-0.1.4.tgz"
  integrity sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=

deepmerge@^1.5.2:
  version "1.5.2"
  resolved "https://registry.nlark.com/deepmerge/download/deepmerge-1.5.2.tgz"
  integrity sha1-EEmdhohEza1P7ghC34x/bwyVp1M=

default-gateway@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmmirror.com/default-gateway/download/default-gateway-4.2.0.tgz"
  integrity sha1-FnEEx1AMIRX23WmwpTa7jtcgVSs=
  dependencies:
    execa "^1.0.0"
    ip-regex "^2.1.0"

default-gateway@^5.0.5:
  version "5.0.5"
  resolved "https://registry.npmmirror.com/default-gateway/download/default-gateway-5.0.5.tgz"
  integrity sha1-T9a9XShV05s0zFpZUFSG6ar8mxA=
  dependencies:
    execa "^3.3.0"

defaults@^1.0.3:
  version "1.0.4"
  dependencies:
    clone "^1.0.2"

define-data-property@^1.0.1, define-data-property@^1.1.4:
  version "1.1.4"
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    gopd "^1.0.1"

define-properties@^1.1.2, define-properties@^1.1.3, define-properties@^1.2.1:
  version "1.2.1"
  dependencies:
    define-data-property "^1.0.1"
    has-property-descriptors "^1.0.0"
    object-keys "^1.1.1"

define-property@^0.2.5:
  version "0.2.5"
  resolved "https://registry.nlark.com/define-property/download/define-property-0.2.5.tgz"
  integrity sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=
  dependencies:
    is-descriptor "^0.1.0"

define-property@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/define-property/download/define-property-1.0.0.tgz"
  integrity sha1-dp66rz9KY6rTr56NMEybvnm/sOY=
  dependencies:
    is-descriptor "^1.0.0"

define-property@^2.0.2:
  version "2.0.2"
  resolved "https://registry.nlark.com/define-property/download/define-property-2.0.2.tgz"
  integrity sha1-1Flono1lS6d+AqgX+HENcCyxbp0=
  dependencies:
    is-descriptor "^1.0.2"
    isobject "^3.0.1"

del@^4.1.1:
  version "4.1.1"
  resolved "https://registry.nlark.com/del/download/del-4.1.1.tgz"
  integrity sha1-no8RciLqRKMf86FWwEm5kFKp8LQ=
  dependencies:
    "@types/glob" "^7.1.1"
    globby "^6.1.0"
    is-path-cwd "^2.0.0"
    is-path-in-cwd "^2.0.0"
    p-map "^2.0.0"
    pify "^4.0.1"
    rimraf "^2.6.3"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/delayed-stream/download/delayed-stream-1.0.0.tgz"
  integrity sha1-3zrhmayt+31ECqrgsp4icrJOxhk=

depd@~1.1.2:
  version "1.1.2"
  resolved "https://registry.nlark.com/depd/download/depd-1.1.2.tgz"
  integrity sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=

depd@2.0.0:
  version "2.0.0"

des.js@^1.0.0:
  version "1.1.0"
  dependencies:
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"

destroy@1.2.0:
  version "1.2.0"

detect-node@^2.0.4:
  version "2.1.0"
  resolved "https://registry.nlark.com/detect-node/download/detect-node-2.1.0.tgz"
  integrity sha1-yccHdaScPQO8LAbZpzvlUPl4+LE=

diffie-hellman@^5.0.3:
  version "5.0.3"
  dependencies:
    bn.js "^4.1.0"
    miller-rabin "^4.0.0"
    randombytes "^2.0.0"

dijkstrajs@^1.0.1:
  version "1.0.3"

dir-glob@^2.0.0, dir-glob@^2.2.2:
  version "2.2.2"
  resolved "https://registry.nlark.com/dir-glob/download/dir-glob-2.2.2.tgz"
  integrity sha1-+gnwaUFTyJGLGLoN6vrpR2n8UMQ=
  dependencies:
    path-type "^3.0.0"

dns-equal@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/dns-equal/download/dns-equal-1.0.0.tgz"
  integrity sha1-s55/HabrCnW6nBcySzR1PEfgZU0=

dns-packet@^1.3.1:
  version "1.3.4"
  resolved "https://registry.nlark.com/dns-packet/download/dns-packet-1.3.4.tgz"
  integrity sha1-40VQZYJKJQe6iGxVqJljuxB97G8=
  dependencies:
    ip "^1.1.0"
    safe-buffer "^5.0.1"

dns-txt@^2.0.2:
  version "2.0.2"
  resolved "https://registry.nlark.com/dns-txt/download/dns-txt-2.0.2.tgz"
  integrity sha1-uR2Ab10nGI5Ks+fRB9iBocxGQrY=
  dependencies:
    buffer-indexof "^1.0.0"

doctrine@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/doctrine/download/doctrine-3.0.0.tgz"
  integrity sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=
  dependencies:
    esutils "^2.0.2"

dom-converter@^0.2.0:
  version "0.2.0"
  resolved "https://registry.nlark.com/dom-converter/download/dom-converter-0.2.0.tgz"
  integrity sha1-ZyGp2u4uKTaClVtq/kFncWJ7t2g=
  dependencies:
    utila "~0.4"

dom-serializer@^1.0.1:
  version "1.4.1"
  dependencies:
    domelementtype "^2.0.1"
    domhandler "^4.2.0"
    entities "^2.0.0"

dom-serializer@0:
  version "0.2.2"
  resolved "https://registry.nlark.com/dom-serializer/download/dom-serializer-0.2.2.tgz"
  integrity sha1-GvuB9TNxcXXUeGVd68XjMtn5u1E=
  dependencies:
    domelementtype "^2.0.1"
    entities "^2.0.0"

domain-browser@^1.1.1:
  version "1.2.0"
  resolved "https://registry.nlark.com/domain-browser/download/domain-browser-1.2.0.tgz?cache=0&sync_timestamp=1627591557212&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdomain-browser%2Fdownload%2Fdomain-browser-1.2.0.tgz"
  integrity sha1-PTH1AZGmdJ3RN1p/Ui6CPULlTto=

domelementtype@^2.0.1, domelementtype@^2.2.0:
  version "2.3.0"

domelementtype@1:
  version "1.3.1"
  resolved "https://registry.nlark.com/domelementtype/download/domelementtype-1.3.1.tgz"
  integrity sha1-0EjESzew0Qp/Kj1f7j9DM9eQSB8=

domhandler@^4.0.0, domhandler@^4.2.0, domhandler@^4.3.1:
  version "4.3.1"
  dependencies:
    domelementtype "^2.2.0"

domutils@^1.7.0:
  version "1.7.0"
  resolved "https://registry.nlark.com/domutils/download/domutils-1.7.0.tgz"
  integrity sha1-Vuo0HoNOBuZ0ivehyyXaZ+qfjCo=
  dependencies:
    dom-serializer "0"
    domelementtype "1"

domutils@^2.5.2:
  version "2.8.0"
  resolved "https://registry.nlark.com/domutils/download/domutils-2.8.0.tgz"
  integrity sha1-RDfe9dtuLR9dbuhZvZXKfQIEgTU=
  dependencies:
    dom-serializer "^1.0.1"
    domelementtype "^2.2.0"
    domhandler "^4.2.0"

domutils@^2.8.0:
  version "2.8.0"
  dependencies:
    dom-serializer "^1.0.1"
    domelementtype "^2.2.0"
    domhandler "^4.2.0"

dot-prop@^5.2.0:
  version "5.3.0"
  resolved "https://registry.nlark.com/dot-prop/download/dot-prop-5.3.0.tgz"
  integrity sha1-kMzOcIzZzYLMTcjD3dmr3VWyDog=
  dependencies:
    is-obj "^2.0.0"

dotenv-expand@^5.1.0:
  version "5.1.0"
  resolved "https://registry.nlark.com/dotenv-expand/download/dotenv-expand-5.1.0.tgz"
  integrity sha1-P7rwIL/XlIhAcuomsel5HUWmKfA=

dotenv@^8.2.0:
  version "8.6.0"
  resolved "https://registry.nlark.com/dotenv/download/dotenv-8.6.0.tgz"
  integrity sha1-Bhr2ZNGff02PxuT/m1hM4jety4s=

dunder-proto@^1.0.0, dunder-proto@^1.0.1:
  version "1.0.1"
  dependencies:
    call-bind-apply-helpers "^1.0.1"
    es-errors "^1.3.0"
    gopd "^1.2.0"

duplexer@^0.1.1:
  version "0.1.2"
  resolved "https://registry.nlark.com/duplexer/download/duplexer-0.1.2.tgz"
  integrity sha1-Or5DrvODX4rgd9E23c4PJ2sEAOY=

duplexify@^3.4.2, duplexify@^3.6.0:
  version "3.7.1"
  resolved "https://registry.nlark.com/duplexify/download/duplexify-3.7.1.tgz?cache=0&sync_timestamp=1626860876789&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fduplexify%2Fdownload%2Fduplexify-3.7.1.tgz"
  integrity sha1-Kk31MX9sz9kfhtb9JdjYoQO4gwk=
  dependencies:
    end-of-stream "^1.0.0"
    inherits "^2.0.1"
    readable-stream "^2.0.0"
    stream-shift "^1.0.0"

easy-stack@1.0.1:
  version "1.0.1"

ecc-jsbn@~0.1.1:
  version "0.1.2"
  resolved "https://registry.nlark.com/ecc-jsbn/download/ecc-jsbn-0.1.2.tgz"
  integrity sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=
  dependencies:
    jsbn "~0.1.0"
    safer-buffer "^2.1.0"

ee-first@1.1.1:
  version "1.1.1"
  resolved "https://registry.nlark.com/ee-first/download/ee-first-1.1.1.tgz"
  integrity sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=

ejs@^2.6.1:
  version "2.7.4"
  resolved "https://registry.npmmirror.com/ejs/download/ejs-2.7.4.tgz"
  integrity sha1-SGYSh1c9zFPjZsehrlLDoSDuybo=

electron-to-chromium@^1.3.30, electron-to-chromium@^1.3.47, electron-to-chromium@^1.5.73:
  version "1.5.76"

elliptic@^6.5.3, elliptic@^6.5.5:
  version "6.6.1"
  dependencies:
    bn.js "^4.11.9"
    brorand "^1.1.0"
    hash.js "^1.0.0"
    hmac-drbg "^1.0.1"
    inherits "^2.0.4"
    minimalistic-assert "^1.0.1"
    minimalistic-crypto-utils "^1.0.1"

emoji-regex@^7.0.1:
  version "7.0.3"
  resolved "https://registry.npmmirror.com/emoji-regex/download/emoji-regex-7.0.3.tgz"
  integrity sha1-kzoEBShgyF6DwSJHnEdIqOTHIVY=

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "https://registry.npmmirror.com/emoji-regex/download/emoji-regex-8.0.0.tgz"
  integrity sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=

emojis-list@^2.0.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/emojis-list/download/emojis-list-2.1.0.tgz"
  integrity sha1-TapNnbAPmBmIDHn6RXrlsJof04k=

emojis-list@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/emojis-list/download/emojis-list-3.0.0.tgz"
  integrity sha1-VXBmIEatKeLpFucariYKvf9Pang=

encodeurl@~1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/encodeurl/download/encodeurl-1.0.2.tgz"
  integrity sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=

encodeurl@~2.0.0:
  version "2.0.0"

end-of-stream@^1.0.0, end-of-stream@^1.1.0:
  version "1.4.4"
  resolved "https://registry.nlark.com/end-of-stream/download/end-of-stream-1.4.4.tgz"
  integrity sha1-WuZKX0UFe682JuwU2gyl5LJDHrA=
  dependencies:
    once "^1.4.0"

enhanced-resolve@^4.5.0:
  version "4.5.0"
  resolved "https://registry.nlark.com/enhanced-resolve/download/enhanced-resolve-4.5.0.tgz"
  integrity sha1-Lzz9hNvjtIfxjy2y7x4GSlccpew=
  dependencies:
    graceful-fs "^4.1.2"
    memory-fs "^0.5.0"
    tapable "^1.0.0"

enhanced-resolve@^5.17.1:
  version "5.18.0"
  dependencies:
    graceful-fs "^4.2.4"
    tapable "^2.2.0"

entities@^2.0.0:
  version "2.2.0"
  resolved "https://registry.nlark.com/entities/download/entities-2.2.0.tgz"
  integrity sha1-CY3JDruD2N/6CJ1VJWs1HTTE2lU=

entities@^4.5.0:
  version "4.5.0"

errno@^0.1.3, errno@~0.1.7:
  version "0.1.8"
  resolved "https://registry.nlark.com/errno/download/errno-0.1.8.tgz"
  integrity sha1-i7Ppx9Rjvkl2/4iPdrSAnrwugR8=
  dependencies:
    prr "~1.0.1"

error-ex@^1.3.1:
  version "1.3.2"
  resolved "https://registry.nlark.com/error-ex/download/error-ex-1.3.2.tgz"
  integrity sha1-tKxAZIEH/c3PriQvQovqihTU8b8=
  dependencies:
    is-arrayish "^0.2.1"

error-stack-parser@^2.0.6:
  version "2.1.4"
  dependencies:
    stackframe "^1.3.4"

eruda@^2.4.1:
  version "2.11.3"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/eruda/-/eruda-2.11.3.tgz"
  integrity sha512-e1EqrGJvN5PSXoFCSAmRqqDfziEL5bNSdPcJbRg5QVOHtcFMcG4n/6uTOfs2FTjAhg4X4GbxhHB+l7ITu/1zmQ==

es-abstract@^1.17.2, es-abstract@^1.23.2, es-abstract@^1.23.5, es-abstract@^1.23.6:
  version "1.23.9"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/es-abstract/-/es-abstract-1.23.9.tgz"
  integrity sha512-py07lI0wjxAC/DcfK1S6G7iANonniZwTISvdPzk9hzeH0IZIshbuuFxLIU96OyF89Yb9hiqWn8M/bY83KY5vzA==
  dependencies:
    array-buffer-byte-length "^1.0.2"
    arraybuffer.prototype.slice "^1.0.4"
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    data-view-buffer "^1.0.2"
    data-view-byte-length "^1.0.2"
    data-view-byte-offset "^1.0.1"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    es-set-tostringtag "^2.1.0"
    es-to-primitive "^1.3.0"
    function.prototype.name "^1.1.8"
    get-intrinsic "^1.2.7"
    get-proto "^1.0.0"
    get-symbol-description "^1.1.0"
    globalthis "^1.0.4"
    gopd "^1.2.0"
    has-property-descriptors "^1.0.2"
    has-proto "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    internal-slot "^1.1.0"
    is-array-buffer "^3.0.5"
    is-callable "^1.2.7"
    is-data-view "^1.0.2"
    is-regex "^1.2.1"
    is-shared-array-buffer "^1.0.4"
    is-string "^1.1.1"
    is-typed-array "^1.1.15"
    is-weakref "^1.1.0"
    math-intrinsics "^1.1.0"
    object-inspect "^1.13.3"
    object-keys "^1.1.1"
    object.assign "^4.1.7"
    own-keys "^1.0.1"
    regexp.prototype.flags "^1.5.3"
    safe-array-concat "^1.1.3"
    safe-push-apply "^1.0.0"
    safe-regex-test "^1.1.0"
    set-proto "^1.0.0"
    string.prototype.trim "^1.2.10"
    string.prototype.trimend "^1.0.9"
    string.prototype.trimstart "^1.0.8"
    typed-array-buffer "^1.0.3"
    typed-array-byte-length "^1.0.3"
    typed-array-byte-offset "^1.0.4"
    typed-array-length "^1.0.7"
    unbox-primitive "^1.1.0"
    which-typed-array "^1.1.18"

es-array-method-boxes-properly@^1.0.0:
  version "1.0.0"

es-define-property@^1.0.0, es-define-property@^1.0.1:
  version "1.0.1"

es-errors@^1.3.0:
  version "1.3.0"

es-module-lexer@^1.2.1:
  version "1.6.0"

es-object-atoms@^1.0.0:
  version "1.0.0"
  dependencies:
    es-errors "^1.3.0"

es-set-tostringtag@^2.1.0:
  version "2.1.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz"
  integrity sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==
  dependencies:
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"
    has-tostringtag "^1.0.2"
    hasown "^2.0.2"

es-to-primitive@^1.3.0:
  version "1.3.0"
  dependencies:
    is-callable "^1.2.7"
    is-date-object "^1.0.5"
    is-symbol "^1.0.4"

escalade@^3.1.1, escalade@^3.2.0:
  version "3.2.0"

escape-html@~1.0.3:
  version "1.0.3"
  resolved "https://registry.nlark.com/escape-html/download/escape-html-1.0.3.tgz"
  integrity sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=

escape-string-regexp@^1.0.2, escape-string-regexp@^1.0.5, escape-string-regexp@1.0.5:
  version "1.0.5"
  resolved "https://registry.nlark.com/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz"
  integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=

eslint-config-prettier@^6.0.0:
  version "6.15.0"
  resolved "https://registry.nlark.com/eslint-config-prettier/download/eslint-config-prettier-6.15.0.tgz?cache=0&sync_timestamp=1619270691651&other_urls=https%3A%2F%2Fregistry.nlark.com%2Feslint-config-prettier%2Fdownload%2Feslint-config-prettier-6.15.0.tgz"
  integrity sha1-f5P2y31FqS8VN6cOzAY2bhrG/tk=
  dependencies:
    get-stdin "^6.0.0"

eslint-loader@^2.2.1:
  version "2.2.1"
  resolved "https://registry.npmmirror.com/eslint-loader/download/eslint-loader-2.2.1.tgz"
  integrity sha1-KLnBLaVAV68IReKmEScBova/gzc=
  dependencies:
    loader-fs-cache "^1.0.0"
    loader-utils "^1.0.2"
    object-assign "^4.0.1"
    object-hash "^1.1.4"
    rimraf "^2.6.1"

eslint-plugin-prettier@^3.1.0, eslint-plugin-prettier@^3.3.1:
  version "3.4.1"
  resolved "https://registry.npmmirror.com/eslint-plugin-prettier/download/eslint-plugin-prettier-3.4.1.tgz?cache=0&sync_timestamp=1636301497154&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Feslint-plugin-prettier%2Fdownload%2Feslint-plugin-prettier-3.4.1.tgz"
  integrity sha1-6d2yAO+289Bf/oOxZlpxavSjh+U=
  dependencies:
    prettier-linter-helpers "^1.0.0"

eslint-plugin-vue@^7.0.0:
  version "7.20.0"
  resolved "https://registry.npmmirror.com/eslint-plugin-vue/download/eslint-plugin-vue-7.20.0.tgz?cache=0&sync_timestamp=1637230866112&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Feslint-plugin-vue%2Fdownload%2Feslint-plugin-vue-7.20.0.tgz"
  integrity sha1-mMIYhaa/3wcTw6kpV6Wv6q7tklM=
  dependencies:
    eslint-utils "^2.1.0"
    natural-compare "^1.4.0"
    semver "^6.3.0"
    vue-eslint-parser "^7.10.0"

eslint-scope@^4.0.3:
  version "4.0.3"
  resolved "https://registry.npmmirror.com/eslint-scope/download/eslint-scope-4.0.3.tgz?cache=0&sync_timestamp=1637466929956&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Feslint-scope%2Fdownload%2Feslint-scope-4.0.3.tgz"
  integrity sha1-ygODMxD2iJoyZHgaqC5j65z+eEg=
  dependencies:
    esrecurse "^4.1.0"
    estraverse "^4.1.1"

eslint-scope@^5.0.0, eslint-scope@^5.1.1, eslint-scope@5.1.1:
  version "5.1.1"
  resolved "https://registry.npmmirror.com/eslint-scope/download/eslint-scope-5.1.1.tgz?cache=0&sync_timestamp=1637466929956&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Feslint-scope%2Fdownload%2Feslint-scope-5.1.1.tgz"
  integrity sha1-54blmmbLkrP2wfsNUIqrF0hI9Iw=
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^4.1.1"

eslint-utils@^1.4.3:
  version "1.4.3"
  resolved "https://registry.nlark.com/eslint-utils/download/eslint-utils-1.4.3.tgz"
  integrity sha1-dP7HxU0Hdrb2fgJRBAtYBlZOmB8=
  dependencies:
    eslint-visitor-keys "^1.1.0"

eslint-utils@^2.1.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/eslint-utils/download/eslint-utils-2.1.0.tgz"
  integrity sha1-0t5eA0JOcH3BDHQGjd7a5wh0Gyc=
  dependencies:
    eslint-visitor-keys "^1.1.0"

eslint-visitor-keys@^1.0.0, eslint-visitor-keys@^1.1.0:
  version "1.3.0"
  resolved "https://registry.npmmirror.com/eslint-visitor-keys/download/eslint-visitor-keys-1.3.0.tgz?cache=0&sync_timestamp=1636378433512&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Feslint-visitor-keys%2Fdownload%2Feslint-visitor-keys-1.3.0.tgz"
  integrity sha1-MOvR73wv3/AcOk8VEESvJfqwUj4=

"eslint@^6.2.0 || ^7.0.0 || ^8.0.0", eslint@^6.7.2, "eslint@>= 1.6.0 < 7.0.0", "eslint@>= 4.12.1", "eslint@>= 5.0.0", "eslint@>=1.6.0 <7.0.0", eslint@>=3.14.1, eslint@>=5.0.0:
  version "6.8.0"
  resolved "https://registry.npmmirror.com/eslint/download/eslint-6.8.0.tgz?cache=0&sync_timestamp=1637475932309&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Feslint%2Fdownload%2Feslint-6.8.0.tgz"
  integrity sha1-YiYtZylzn5J1cjgkMC+yJ8jJP/s=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    ajv "^6.10.0"
    chalk "^2.1.0"
    cross-spawn "^6.0.5"
    debug "^4.0.1"
    doctrine "^3.0.0"
    eslint-scope "^5.0.0"
    eslint-utils "^1.4.3"
    eslint-visitor-keys "^1.1.0"
    espree "^6.1.2"
    esquery "^1.0.1"
    esutils "^2.0.2"
    file-entry-cache "^5.0.1"
    functional-red-black-tree "^1.0.1"
    glob-parent "^5.0.0"
    globals "^12.1.0"
    ignore "^4.0.6"
    import-fresh "^3.0.0"
    imurmurhash "^0.1.4"
    inquirer "^7.0.0"
    is-glob "^4.0.0"
    js-yaml "^3.13.1"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.3.0"
    lodash "^4.17.14"
    minimatch "^3.0.4"
    mkdirp "^0.5.1"
    natural-compare "^1.4.0"
    optionator "^0.8.3"
    progress "^2.0.0"
    regexpp "^2.0.1"
    semver "^6.1.2"
    strip-ansi "^5.2.0"
    strip-json-comments "^3.0.1"
    table "^5.2.3"
    text-table "^0.2.0"
    v8-compile-cache "^2.0.3"

espree@^6.1.2, espree@^6.2.1:
  version "6.2.1"
  resolved "https://registry.npmmirror.com/espree/download/espree-6.2.1.tgz?cache=0&sync_timestamp=1637466456948&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fespree%2Fdownload%2Fespree-6.2.1.tgz"
  integrity sha1-d/xy4f10SiBSwg84pbV1gy6Cc0o=
  dependencies:
    acorn "^7.1.1"
    acorn-jsx "^5.2.0"
    eslint-visitor-keys "^1.1.0"

esprima@^4.0.0:
  version "4.0.1"
  resolved "https://registry.nlark.com/esprima/download/esprima-4.0.1.tgz"
  integrity sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=

esquery@^1.0.1, esquery@^1.4.0:
  version "1.6.0"
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.1.0, esrecurse@^4.3.0:
  version "4.3.0"
  resolved "https://registry.nlark.com/esrecurse/download/esrecurse-4.3.0.tgz"
  integrity sha1-eteWTWeauyi+5yzsY3WLHF0smSE=
  dependencies:
    estraverse "^5.2.0"

estraverse@^4.1.1:
  version "4.3.0"
  resolved "https://registry.npmmirror.com/estraverse/download/estraverse-4.3.0.tgz?cache=0&sync_timestamp=1635237716974&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Festraverse%2Fdownload%2Festraverse-4.3.0.tgz"
  integrity sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=

estraverse@^5.1.0:
  version "5.3.0"
  resolved "https://registry.npmmirror.com/estraverse/download/estraverse-5.3.0.tgz?cache=0&sync_timestamp=1635237716974&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Festraverse%2Fdownload%2Festraverse-5.3.0.tgz"
  integrity sha1-LupSkHAvJquP5TcDcP+GyWXSESM=

estraverse@^5.2.0:
  version "5.3.0"
  resolved "https://registry.npmmirror.com/estraverse/download/estraverse-5.3.0.tgz?cache=0&sync_timestamp=1635237716974&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Festraverse%2Fdownload%2Festraverse-5.3.0.tgz"
  integrity sha1-LupSkHAvJquP5TcDcP+GyWXSESM=

estree-walker@^2.0.2:
  version "2.0.2"
  resolved "https://registry.nlark.com/estree-walker/download/estree-walker-2.0.2.tgz"
  integrity sha1-UvAQF4wqTBF6d1fP6UKtt9LaTKw=

esutils@^2.0.2:
  version "2.0.3"
  resolved "https://registry.nlark.com/esutils/download/esutils-2.0.3.tgz"
  integrity sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=

etag@~1.8.1:
  version "1.8.1"
  resolved "https://registry.nlark.com/etag/download/etag-1.8.1.tgz"
  integrity sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=

event-pubsub@4.3.0:
  version "4.3.0"
  resolved "https://registry.nlark.com/event-pubsub/download/event-pubsub-4.3.0.tgz"
  integrity sha1-9o2Ba8KfHsAsU53FjI3UDOcss24=

eventemitter3@^4.0.0:
  version "4.0.7"
  resolved "https://registry.nlark.com/eventemitter3/download/eventemitter3-4.0.7.tgz"
  integrity sha1-Lem2j2Uo1WRO9cWVJqG0oHMGFp8=

events@^3.0.0, events@^3.2.0, events@^3.3.0:
  version "3.3.0"
  resolved "https://registry.npmmirror.com/events/download/events-3.3.0.tgz"
  integrity sha1-Mala0Kkk4tLEGagTrrLE6HjqdAA=

eventsource@^2.0.2:
  version "2.0.2"

evp_bytestokey@^1.0.0, evp_bytestokey@^1.0.3:
  version "1.0.3"
  resolved "https://registry.nlark.com/evp_bytestokey/download/evp_bytestokey-1.0.3.tgz"
  integrity sha1-f8vbGY3HGVlDLv4ThCaE4FJaywI=
  dependencies:
    md5.js "^1.3.4"
    safe-buffer "^5.1.1"

execa@^0.8.0:
  version "0.8.0"
  resolved "https://registry.npmmirror.com/execa/download/execa-0.8.0.tgz?cache=0&sync_timestamp=1637147245057&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fexeca%2Fdownload%2Fexeca-0.8.0.tgz"
  integrity sha1-2NdrvBtVIX7RkP1t1J08d07PyNo=
  dependencies:
    cross-spawn "^5.0.1"
    get-stream "^3.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

execa@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/execa/download/execa-1.0.0.tgz?cache=0&sync_timestamp=1637147245057&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fexeca%2Fdownload%2Fexeca-1.0.0.tgz"
  integrity sha1-xiNqW7TfbW8V6I5/AXeYIWdJ3dg=
  dependencies:
    cross-spawn "^6.0.0"
    get-stream "^4.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

execa@^3.3.0:
  version "3.4.0"
  resolved "https://registry.npmmirror.com/execa/download/execa-3.4.0.tgz?cache=0&sync_timestamp=1637147245057&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fexeca%2Fdownload%2Fexeca-3.4.0.tgz"
  integrity sha1-wI7UVQ72XYWPrCaf/IVyRG8364k=
  dependencies:
    cross-spawn "^7.0.0"
    get-stream "^5.0.0"
    human-signals "^1.1.1"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.0"
    onetime "^5.1.0"
    p-finally "^2.0.0"
    signal-exit "^3.0.2"
    strip-final-newline "^2.0.0"

expand-brackets@^2.1.4:
  version "2.1.4"
  resolved "https://registry.nlark.com/expand-brackets/download/expand-brackets-2.1.4.tgz"
  integrity sha1-t3c14xXOMPa27/D4OwQVGiJEliI=
  dependencies:
    debug "^2.3.3"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    posix-character-classes "^0.1.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

express@^4.16.3, express@^4.17.1:
  version "4.21.2"
  dependencies:
    accepts "~1.3.8"
    array-flatten "1.1.1"
    body-parser "1.20.3"
    content-disposition "0.5.4"
    content-type "~1.0.4"
    cookie "0.7.1"
    cookie-signature "1.0.6"
    debug "2.6.9"
    depd "2.0.0"
    encodeurl "~2.0.0"
    escape-html "~1.0.3"
    etag "~1.8.1"
    finalhandler "1.3.1"
    fresh "0.5.2"
    http-errors "2.0.0"
    merge-descriptors "1.0.3"
    methods "~1.1.2"
    on-finished "2.4.1"
    parseurl "~1.3.3"
    path-to-regexp "0.1.12"
    proxy-addr "~2.0.7"
    qs "6.13.0"
    range-parser "~1.2.1"
    safe-buffer "5.2.1"
    send "0.19.0"
    serve-static "1.16.2"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    type-is "~1.6.18"
    utils-merge "1.0.1"
    vary "~1.1.2"

extend-shallow@^2.0.1:
  version "2.0.1"
  resolved "https://registry.nlark.com/extend-shallow/download/extend-shallow-2.0.1.tgz"
  integrity sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=
  dependencies:
    is-extendable "^0.1.0"

extend-shallow@^3.0.0:
  version "3.0.2"
  resolved "https://registry.nlark.com/extend-shallow/download/extend-shallow-3.0.2.tgz"
  integrity sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=
  dependencies:
    assign-symbols "^1.0.0"
    is-extendable "^1.0.1"

extend-shallow@^3.0.2:
  version "3.0.2"
  resolved "https://registry.nlark.com/extend-shallow/download/extend-shallow-3.0.2.tgz"
  integrity sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=
  dependencies:
    assign-symbols "^1.0.0"
    is-extendable "^1.0.1"

extend@~3.0.2:
  version "3.0.2"
  resolved "https://registry.nlark.com/extend/download/extend-3.0.2.tgz"
  integrity sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo=

external-editor@^3.0.3:
  version "3.1.0"
  resolved "https://registry.nlark.com/external-editor/download/external-editor-3.1.0.tgz"
  integrity sha1-ywP3QL764D6k0oPK7SdBqD8zVJU=
  dependencies:
    chardet "^0.7.0"
    iconv-lite "^0.4.24"
    tmp "^0.0.33"

extglob@^2.0.4:
  version "2.0.4"
  resolved "https://registry.nlark.com/extglob/download/extglob-2.0.4.tgz"
  integrity sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM=
  dependencies:
    array-unique "^0.3.2"
    define-property "^1.0.0"
    expand-brackets "^2.1.4"
    extend-shallow "^2.0.1"
    fragment-cache "^0.2.1"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

extsprintf@^1.2.0:
  version "1.4.1"
  resolved "https://registry.npmmirror.com/extsprintf/download/extsprintf-1.4.1.tgz"
  integrity sha1-jRcsBkhn8jXAyEpZaAbSeb9LzAc=

extsprintf@1.3.0:
  version "1.3.0"
  resolved "https://registry.npmmirror.com/extsprintf/download/extsprintf-1.3.0.tgz"
  integrity sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "https://registry.nlark.com/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz"
  integrity sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=

fast-diff@^1.1.2:
  version "1.3.0"

fast-glob@^2.2.6:
  version "2.2.7"
  resolved "https://registry.nlark.com/fast-glob/download/fast-glob-2.2.7.tgz?cache=0&sync_timestamp=1625773305786&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ffast-glob%2Fdownload%2Ffast-glob-2.2.7.tgz"
  integrity sha1-aVOFfDr6R1//ku5gFdUtpwpM050=
  dependencies:
    "@mrmlnc/readdir-enhanced" "^2.2.1"
    "@nodelib/fs.stat" "^1.1.2"
    glob-parent "^3.1.0"
    is-glob "^4.0.0"
    merge2 "^1.2.3"
    micromatch "^3.1.10"

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz"
  integrity sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=

fast-levenshtein@~2.0.6:
  version "2.0.6"
  resolved "https://registry.nlark.com/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz"
  integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=

fast-uri@^3.0.1:
  version "3.0.3"

faye-websocket@^0.11.3, faye-websocket@^0.11.4:
  version "0.11.4"
  resolved "https://registry.nlark.com/faye-websocket/download/faye-websocket-0.11.4.tgz"
  integrity sha1-fw2Sdc/dhqHJY9yLZfzEUe3Lsdo=
  dependencies:
    websocket-driver ">=0.5.1"

figgy-pudding@^3.5.1:
  version "3.5.2"
  resolved "https://registry.nlark.com/figgy-pudding/download/figgy-pudding-3.5.2.tgz"
  integrity sha1-tO7oFIq7Adzx0aw0Nn1Z4S+mHW4=

figures@^3.0.0:
  version "3.2.0"
  resolved "https://registry.nlark.com/figures/download/figures-3.2.0.tgz?cache=0&sync_timestamp=1625254307578&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ffigures%2Fdownload%2Ffigures-3.2.0.tgz"
  integrity sha1-YlwYvSk8YE3EqN2y/r8MiDQXRq8=
  dependencies:
    escape-string-regexp "^1.0.5"

file-entry-cache@^5.0.1:
  version "5.0.1"
  resolved "https://registry.nlark.com/file-entry-cache/download/file-entry-cache-5.0.1.tgz"
  integrity sha1-yg9u+m3T1WEzP7FFFQZcL6/fQ5w=
  dependencies:
    flat-cache "^2.0.1"

file-loader@*, file-loader@^4.2.0:
  version "4.3.0"
  resolved "https://registry.nlark.com/file-loader/download/file-loader-4.3.0.tgz"
  integrity sha1-eA8ED3KbPRgBnyBgX3I+hEuKWK8=
  dependencies:
    loader-utils "^1.2.3"
    schema-utils "^2.5.0"

file-uri-to-path@1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/file-uri-to-path/download/file-uri-to-path-1.0.0.tgz"
  integrity sha1-VTp7hEb/b2hDWcRF8eN6BdrMM90=

filesize@^3.6.1:
  version "3.6.1"
  resolved "https://registry.npmmirror.com/filesize/download/filesize-3.6.1.tgz?cache=0&sync_timestamp=1635763993879&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ffilesize%2Fdownload%2Ffilesize-3.6.1.tgz"
  integrity sha1-CQuz7gG2+AGoqL6Z0xcQs0Irsxc=

fill-range@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/fill-range/download/fill-range-4.0.0.tgz"
  integrity sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=
  dependencies:
    extend-shallow "^2.0.1"
    is-number "^3.0.0"
    repeat-string "^1.6.1"
    to-regex-range "^2.1.0"

fill-range@^7.1.1:
  version "7.1.1"
  dependencies:
    to-regex-range "^5.0.1"

filter-obj@^1.1.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/filter-obj/download/filter-obj-1.1.0.tgz?cache=0&sync_timestamp=1630004447934&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ffilter-obj%2Fdownload%2Ffilter-obj-1.1.0.tgz"
  integrity sha1-mzERErxsYSehbgFsbF1/GeCAXFs=

finalhandler@1.3.1:
  version "1.3.1"
  dependencies:
    debug "2.6.9"
    encodeurl "~2.0.0"
    escape-html "~1.0.3"
    on-finished "2.4.1"
    parseurl "~1.3.3"
    statuses "2.0.1"
    unpipe "~1.0.0"

find-cache-dir@^0.1.1:
  version "0.1.1"
  resolved "https://registry.nlark.com/find-cache-dir/download/find-cache-dir-0.1.1.tgz"
  integrity sha1-yN765XyKUqinhPnjHFfHQumToLk=
  dependencies:
    commondir "^1.0.1"
    mkdirp "^0.5.1"
    pkg-dir "^1.0.0"

find-cache-dir@^2.1.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/find-cache-dir/download/find-cache-dir-2.1.0.tgz"
  integrity sha1-jQ+UzRP+Q8bHwmGg2GEVypGMBfc=
  dependencies:
    commondir "^1.0.1"
    make-dir "^2.0.0"
    pkg-dir "^3.0.0"

find-cache-dir@^3.0.0:
  version "3.3.2"
  resolved "https://registry.nlark.com/find-cache-dir/download/find-cache-dir-3.3.2.tgz"
  integrity sha1-swxbbv8HMHMa6pu9nb7L2AJW1ks=
  dependencies:
    commondir "^1.0.1"
    make-dir "^3.0.2"
    pkg-dir "^4.1.0"

find-cache-dir@^3.3.1:
  version "3.3.2"
  resolved "https://registry.nlark.com/find-cache-dir/download/find-cache-dir-3.3.2.tgz"
  integrity sha1-swxbbv8HMHMa6pu9nb7L2AJW1ks=
  dependencies:
    commondir "^1.0.1"
    make-dir "^3.0.2"
    pkg-dir "^4.1.0"

find-up@^1.0.0:
  version "1.1.2"
  resolved "https://registry.npmmirror.com/find-up/download/find-up-1.1.2.tgz"
  integrity sha1-ay6YIrGizgpgq2TWEOzK1TyyTQ8=
  dependencies:
    path-exists "^2.0.0"
    pinkie-promise "^2.0.0"

find-up@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/find-up/download/find-up-3.0.0.tgz"
  integrity sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=
  dependencies:
    locate-path "^3.0.0"

find-up@^4.0.0:
  version "4.1.0"
  resolved "https://registry.npmmirror.com/find-up/download/find-up-4.1.0.tgz"
  integrity sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

find-up@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmmirror.com/find-up/download/find-up-4.1.0.tgz"
  integrity sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

flat-cache@^2.0.1:
  version "2.0.1"
  resolved "https://registry.nlark.com/flat-cache/download/flat-cache-2.0.1.tgz"
  integrity sha1-XSltbwS9pEpGMKMBQTvbwuwIXsA=
  dependencies:
    flatted "^2.0.0"
    rimraf "2.6.3"
    write "1.0.3"

flatted@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/flatted/download/flatted-2.0.2.tgz?cache=0&sync_timestamp=1636473868538&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fflatted%2Fdownload%2Fflatted-2.0.2.tgz"
  integrity sha1-RXWyHivO50NKqb5mL0t7X5wrUTg=

flatten@^1.0.2:
  version "1.0.3"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/flatten/-/flatten-1.0.3.tgz"
  integrity sha512-dVsPA/UwQ8+2uoFe5GHtiBMu48dWLTdsuEd7CKGlZlD78r1TTWBvDuFaFGKCo/ZfEr95Uk56vZoX86OsHkUeIg==

flush-write-stream@^1.0.0:
  version "1.1.1"
  resolved "https://registry.nlark.com/flush-write-stream/download/flush-write-stream-1.1.1.tgz"
  integrity sha1-jdfYc6G6vCB9lOrQwuDkQnbr8ug=
  dependencies:
    inherits "^2.0.3"
    readable-stream "^2.3.6"

follow-redirects@^1.0.0, follow-redirects@^1.14.4:
  version "1.15.9"

for-each@^0.3.3:
  version "0.3.3"
  dependencies:
    is-callable "^1.1.3"

for-in@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/for-in/download/for-in-1.0.2.tgz"
  integrity sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=

forever-agent@~0.6.1:
  version "0.6.1"
  resolved "https://registry.nlark.com/forever-agent/download/forever-agent-0.6.1.tgz"
  integrity sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=

form-data@~2.3.2:
  version "2.3.3"
  resolved "https://registry.nlark.com/form-data/download/form-data-2.3.3.tgz"
  integrity sha1-3M5SwF9kTymManq5Nr1yTO/786Y=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.6"
    mime-types "^2.1.12"

forwarded@0.2.0:
  version "0.2.0"
  resolved "https://registry.nlark.com/forwarded/download/forwarded-0.2.0.tgz"
  integrity sha1-ImmTZCiq1MFcfr6XeahL8LKoGBE=

fragment-cache@^0.2.1:
  version "0.2.1"
  resolved "https://registry.nlark.com/fragment-cache/download/fragment-cache-0.2.1.tgz"
  integrity sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=
  dependencies:
    map-cache "^0.2.2"

fresh@0.5.2:
  version "0.5.2"
  resolved "https://registry.nlark.com/fresh/download/fresh-0.5.2.tgz"
  integrity sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=

from2@^2.1.0:
  version "2.3.0"
  resolved "https://registry.nlark.com/from2/download/from2-2.3.0.tgz"
  integrity sha1-i/tVAr3kpNNs/e6gB/zKIdfjgq8=
  dependencies:
    inherits "^2.0.1"
    readable-stream "^2.0.0"

fs-extra@^4.0.2:
  version "4.0.3"
  resolved "https://registry.nlark.com/fs-extra/download/fs-extra-4.0.3.tgz"
  integrity sha1-DYUhIuW8W+tFP7Ao6cDJvzY0DJQ=
  dependencies:
    graceful-fs "^4.1.2"
    jsonfile "^4.0.0"
    universalify "^0.1.0"

fs-extra@^7.0.1:
  version "7.0.1"
  resolved "https://registry.nlark.com/fs-extra/download/fs-extra-7.0.1.tgz"
  integrity sha1-TxicRKoSO4lfcigE9V6iPq3DSOk=
  dependencies:
    graceful-fs "^4.1.2"
    jsonfile "^4.0.0"
    universalify "^0.1.0"

fs-write-stream-atomic@^1.0.8:
  version "1.0.10"
  resolved "https://registry.npmmirror.com/fs-write-stream-atomic/download/fs-write-stream-atomic-1.0.10.tgz"
  integrity sha1-tH31NJPvkR33VzHnCp3tAYnbQMk=
  dependencies:
    graceful-fs "^4.1.2"
    iferr "^0.1.5"
    imurmurhash "^0.1.4"
    readable-stream "1 || 2"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/fs.realpath/download/fs.realpath-1.0.0.tgz"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

fsevents@^1.2.7:
  version "1.2.13"
  resolved "https://registry.npmmirror.com/fsevents/download/fsevents-1.2.13.tgz"
  integrity sha1-8yXLBFVZJCi88Rs4M3DvcOO/zDg=
  dependencies:
    bindings "^1.5.0"
    nan "^2.12.1"

fsevents@~2.3.2:
  version "2.3.3"

function-bind@^1.1.2:
  version "1.1.2"

function.prototype.name@^1.1.6, function.prototype.name@^1.1.8:
  version "1.1.8"
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    functions-have-names "^1.2.3"
    hasown "^2.0.2"
    is-callable "^1.2.7"

functional-red-black-tree@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/functional-red-black-tree/download/functional-red-black-tree-1.0.1.tgz"
  integrity sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=

functions-have-names@^1.2.3:
  version "1.2.3"

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "https://registry.nlark.com/gensync/download/gensync-1.0.0-beta.2.tgz"
  integrity sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=

get-caller-file@^2.0.1, get-caller-file@^2.0.5:
  version "2.0.5"
  resolved "https://registry.nlark.com/get-caller-file/download/get-caller-file-2.0.5.tgz"
  integrity sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=

get-intrinsic@^1.2.4, get-intrinsic@^1.2.5, get-intrinsic@^1.2.6, get-intrinsic@^1.2.7:
  version "1.2.7"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/get-intrinsic/-/get-intrinsic-1.2.7.tgz"
  integrity sha512-VW6Pxhsrk0KAOqs3WEd0klDiF/+V7gQOpAvY1jVU/LHmaD/kQO4523aiJuikX/QAKYiW6x8Jh+RJej1almdtCA==
  dependencies:
    call-bind-apply-helpers "^1.0.1"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    function-bind "^1.1.2"
    get-proto "^1.0.0"
    gopd "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    math-intrinsics "^1.1.0"

get-own-enumerable-property-symbols@^3.0.0:
  version "3.0.2"
  resolved "https://registry.nlark.com/get-own-enumerable-property-symbols/download/get-own-enumerable-property-symbols-3.0.2.tgz"
  integrity sha1-tf3nfyLL4185C04ImSLFC85u9mQ=

get-proto@^1.0.0:
  version "1.0.1"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/get-proto/-/get-proto-1.0.1.tgz"
  integrity sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==
  dependencies:
    dunder-proto "^1.0.1"
    es-object-atoms "^1.0.0"

get-stdin@^6.0.0:
  version "6.0.0"
  resolved "https://registry.nlark.com/get-stdin/download/get-stdin-6.0.0.tgz"
  integrity sha1-ngm/cSs2CrkiXoEgSPcf3pyJZXs=

get-stream@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/get-stream/download/get-stream-3.0.0.tgz"
  integrity sha1-jpQ9E1jcN1VQVOy+LtsFqhdO3hQ=

get-stream@^4.0.0:
  version "4.1.0"
  resolved "https://registry.nlark.com/get-stream/download/get-stream-4.1.0.tgz"
  integrity sha1-wbJVV189wh1Zv8ec09K0axw6VLU=
  dependencies:
    pump "^3.0.0"

get-stream@^5.0.0:
  version "5.2.0"
  resolved "https://registry.nlark.com/get-stream/download/get-stream-5.2.0.tgz"
  integrity sha1-SWaheV7lrOZecGxLe+txJX1uItM=
  dependencies:
    pump "^3.0.0"

get-symbol-description@^1.1.0:
  version "1.1.0"
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"

get-value@^2.0.3, get-value@^2.0.6:
  version "2.0.6"
  resolved "https://registry.nlark.com/get-value/download/get-value-2.0.6.tgz"
  integrity sha1-3BXKHGcjh8p2vTesCjlbogQqLCg=

getpass@^0.1.1:
  version "0.1.7"
  resolved "https://registry.nlark.com/getpass/download/getpass-0.1.7.tgz"
  integrity sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=
  dependencies:
    assert-plus "^1.0.0"

git-rev-sync@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmmirror.com/git-rev-sync/-/git-rev-sync-3.0.2.tgz"
  integrity sha512-Nd5RiYpyncjLv0j6IONy0lGzAqdRXUaBctuGBbrEA2m6Bn4iDrN/9MeQTXuiquw8AEKL9D2BW0nw5m/lQvxqnQ==
  dependencies:
    escape-string-regexp "1.0.5"
    graceful-fs "4.1.15"
    shelljs "0.8.5"

glob-parent@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/glob-parent/download/glob-parent-3.1.0.tgz?cache=0&sync_timestamp=1632953810778&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglob-parent%2Fdownload%2Fglob-parent-3.1.0.tgz"
  integrity sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4=
  dependencies:
    is-glob "^3.1.0"
    path-dirname "^1.0.0"

glob-parent@^5.0.0:
  version "5.1.2"
  resolved "https://registry.npmmirror.com/glob-parent/download/glob-parent-5.1.2.tgz?cache=0&sync_timestamp=1632953810778&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglob-parent%2Fdownload%2Fglob-parent-5.1.2.tgz"
  integrity sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=
  dependencies:
    is-glob "^4.0.1"

glob-parent@~5.1.2:
  version "5.1.2"
  resolved "https://registry.npmmirror.com/glob-parent/download/glob-parent-5.1.2.tgz?cache=0&sync_timestamp=1632953810778&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglob-parent%2Fdownload%2Fglob-parent-5.1.2.tgz"
  integrity sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=
  dependencies:
    is-glob "^4.0.1"

glob-to-regexp@^0.3.0:
  version "0.3.0"
  resolved "https://registry.nlark.com/glob-to-regexp/download/glob-to-regexp-0.3.0.tgz"
  integrity sha1-jFoUlNIGbFcMw7/kSWF1rMTVAqs=

glob-to-regexp@^0.4.1:
  version "0.4.1"
  resolved "https://registry.nlark.com/glob-to-regexp/download/glob-to-regexp-0.4.1.tgz"
  integrity sha1-x1KXCHyFG5pXi9IX3VmpL1n+VG4=

glob@^7.0.0, glob@^7.0.3, glob@^7.1.2, glob@^7.1.3, glob@^7.1.4, glob@^7.1.6, glob@^7.2.0:
  version "7.2.3"
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

globals@^11.1.0:
  version "11.12.0"
  resolved "https://registry.npmmirror.com/globals/download/globals-11.12.0.tgz?cache=0&sync_timestamp=1635390798667&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglobals%2Fdownload%2Fglobals-11.12.0.tgz"
  integrity sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=

globals@^12.1.0:
  version "12.4.0"
  resolved "https://registry.npmmirror.com/globals/download/globals-12.4.0.tgz?cache=0&sync_timestamp=1635390798667&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglobals%2Fdownload%2Fglobals-12.4.0.tgz"
  integrity sha1-oYgTV2pBsAokqX5/gVkYwuGZJfg=
  dependencies:
    type-fest "^0.8.1"

globalthis@^1.0.4:
  version "1.0.4"
  dependencies:
    define-properties "^1.2.1"
    gopd "^1.0.1"

globby@^6.1.0:
  version "6.1.0"
  resolved "https://registry.nlark.com/globby/download/globby-6.1.0.tgz?cache=0&sync_timestamp=1629801109090&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fglobby%2Fdownload%2Fglobby-6.1.0.tgz"
  integrity sha1-9abXDoOV4hyFj7BInWTfAkJNUGw=
  dependencies:
    array-union "^1.0.1"
    glob "^7.0.3"
    object-assign "^4.0.1"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"

globby@^7.1.1:
  version "7.1.1"
  resolved "https://registry.nlark.com/globby/download/globby-7.1.1.tgz?cache=0&sync_timestamp=1629801109090&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fglobby%2Fdownload%2Fglobby-7.1.1.tgz"
  integrity sha1-+yzP+UAfhgCUXfral0QMypcrhoA=
  dependencies:
    array-union "^1.0.1"
    dir-glob "^2.0.0"
    glob "^7.1.2"
    ignore "^3.3.5"
    pify "^3.0.0"
    slash "^1.0.0"

globby@^9.2.0:
  version "9.2.0"
  resolved "https://registry.nlark.com/globby/download/globby-9.2.0.tgz?cache=0&sync_timestamp=1629801109090&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fglobby%2Fdownload%2Fglobby-9.2.0.tgz"
  integrity sha1-/QKacGxwPSm90XD0tts6P3p8tj0=
  dependencies:
    "@types/glob" "^7.1.1"
    array-union "^1.0.2"
    dir-glob "^2.2.2"
    fast-glob "^2.2.6"
    glob "^7.1.3"
    ignore "^4.0.3"
    pify "^4.0.1"
    slash "^2.0.0"

gopd@^1.0.1, gopd@^1.2.0:
  version "1.2.0"

graceful-fs@^4.1.11, graceful-fs@^4.1.15, graceful-fs@^4.1.2, graceful-fs@^4.1.6, graceful-fs@^4.2.11, graceful-fs@^4.2.4:
  version "4.2.11"

graceful-fs@4.1.15:
  version "4.1.15"
  resolved "https://registry.npmmirror.com/graceful-fs/-/graceful-fs-4.1.15.tgz"
  integrity sha512-6uHUhOPEBgQ24HM+r6b/QwWfZq+yiFcipKFrOFiBEnWdy5sdzYoi+pJeQaPI5qOLRFqWmAXUPQNsielzdLoecA==

gzip-size@^5.0.0:
  version "5.1.1"
  resolved "https://registry.nlark.com/gzip-size/download/gzip-size-5.1.1.tgz"
  integrity sha1-y5vuaS+HwGErIyhAqHOQTkwTUnQ=
  dependencies:
    duplexer "^0.1.1"
    pify "^4.0.1"

handle-thing@^2.0.0:
  version "2.0.1"
  resolved "https://registry.nlark.com/handle-thing/download/handle-thing-2.0.1.tgz"
  integrity sha1-hX95zjWVgMNA1DCBzGSJcNC7I04=

har-schema@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/har-schema/download/har-schema-2.0.0.tgz"
  integrity sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI=

har-validator@~5.1.3:
  version "5.1.5"
  resolved "https://registry.npmmirror.com/har-validator/download/har-validator-5.1.5.tgz"
  integrity sha1-HwgDufjLIMD6E4It8ezds2veHv0=
  dependencies:
    ajv "^6.12.3"
    har-schema "^2.0.0"

has-ansi@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/has-ansi/-/has-ansi-2.0.0.tgz"
  integrity sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE=
  dependencies:
    ansi-regex "^2.0.0"

has-bigints@^1.0.2:
  version "1.1.0"

has-flag@^1.0.0:
  version "1.0.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/has-flag/-/has-flag-1.0.0.tgz"
  integrity sha512-DyYHfIYwAJmjAjSSPKANxI8bFY9YtFrgkAfinBojQ8YJTOuOuav64tMUJv584SES4xl74PmuaevIyaLESHdTAA==

has-flag@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/has-flag/download/has-flag-3.0.0.tgz?cache=0&sync_timestamp=1626715907927&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fhas-flag%2Fdownload%2Fhas-flag-3.0.0.tgz"
  integrity sha1-tdRU3CGZriJWmfNGfloH87lVuv0=

has-flag@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/has-flag/download/has-flag-4.0.0.tgz?cache=0&sync_timestamp=1626715907927&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fhas-flag%2Fdownload%2Fhas-flag-4.0.0.tgz"
  integrity sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=

has-property-descriptors@^1.0.0, has-property-descriptors@^1.0.2:
  version "1.0.2"
  dependencies:
    es-define-property "^1.0.0"

has-proto@^1.2.0:
  version "1.2.0"
  dependencies:
    dunder-proto "^1.0.0"

has-symbols@^1.0.1, has-symbols@^1.0.3, has-symbols@^1.1.0:
  version "1.1.0"

has-tostringtag@^1.0.0, has-tostringtag@^1.0.2:
  version "1.0.2"
  dependencies:
    has-symbols "^1.0.3"

has-value@^0.3.1:
  version "0.3.1"
  resolved "https://registry.nlark.com/has-value/download/has-value-0.3.1.tgz"
  integrity sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=
  dependencies:
    get-value "^2.0.3"
    has-values "^0.1.4"
    isobject "^2.0.0"

has-value@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/has-value/download/has-value-1.0.0.tgz"
  integrity sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=
  dependencies:
    get-value "^2.0.6"
    has-values "^1.0.0"
    isobject "^3.0.0"

has-values@^0.1.4:
  version "0.1.4"
  resolved "https://registry.nlark.com/has-values/download/has-values-0.1.4.tgz"
  integrity sha1-bWHeldkd/Km5oCCJrThL/49it3E=

has-values@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/has-values/download/has-values-1.0.0.tgz"
  integrity sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=
  dependencies:
    is-number "^3.0.0"
    kind-of "^4.0.0"

has@^1.0.0:
  version "1.0.4"

hash-base@^3.0.0:
  version "3.1.0"
  resolved "https://registry.nlark.com/hash-base/download/hash-base-3.1.0.tgz"
  integrity sha1-VcOB2eBuHSmXqIO0o/3f5/DTrzM=
  dependencies:
    inherits "^2.0.4"
    readable-stream "^3.6.0"
    safe-buffer "^5.2.0"

hash-base@~3.0, hash-base@~3.0.4:
  version "3.0.5"
  dependencies:
    inherits "^2.0.4"
    safe-buffer "^5.2.1"

hash-sum@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/hash-sum/download/hash-sum-1.0.2.tgz"
  integrity sha1-M7QHd3VMZDJXPBIMw4CLvRDUfwQ=

hash-sum@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/hash-sum/download/hash-sum-2.0.0.tgz"
  integrity sha1-gdAbtd6OpKIUrV1urRtSNGCwtFo=

hash.js@^1.0.0, hash.js@^1.0.3:
  version "1.1.7"
  resolved "https://registry.nlark.com/hash.js/download/hash.js-1.1.7.tgz?cache=0&sync_timestamp=1622702479319&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fhash.js%2Fdownload%2Fhash.js-1.1.7.tgz"
  integrity sha1-C6vKU46NTuSg+JiNaIZlN6ADz0I=
  dependencies:
    inherits "^2.0.3"
    minimalistic-assert "^1.0.1"

hasown@^2.0.0, hasown@^2.0.2:
  version "2.0.2"
  dependencies:
    function-bind "^1.1.2"

he@1.2.x:
  version "1.2.0"
  resolved "https://registry.nlark.com/he/download/he-1.2.0.tgz"
  integrity sha1-hK5l+n6vsWX922FWauFLrwVmTw8=

hex-color-regex@^1.1.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/hex-color-regex/download/hex-color-regex-1.1.0.tgz"
  integrity sha1-TAb8y0YC/iYCs8k9+C1+fb8aio4=

highlight.js@^10.7.1:
  version "10.7.3"
  resolved "https://registry.npmmirror.com/highlight.js/download/highlight.js-10.7.3.tgz"
  integrity sha1-aXJy45kTVuQMPKxWanTu9oF1ZTE=

hls.js@^1.1.1:
  version "1.5.18"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/hls.js/-/hls.js-1.5.18.tgz"
  integrity sha512-znxR+2jecWluu/0KOBqUcvVyAB5tLff10vjMGrpAlz1eFY+ZhF1bY3r82V+Bk7WJdk03iTjtja9KFFz5BrqjSA==

hmac-drbg@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/hmac-drbg/download/hmac-drbg-1.0.1.tgz"
  integrity sha1-0nRXAQJabHdabFRXk+1QL8DGSaE=
  dependencies:
    hash.js "^1.0.3"
    minimalistic-assert "^1.0.0"
    minimalistic-crypto-utils "^1.0.1"

hoopy@^0.1.4:
  version "0.1.4"
  resolved "https://registry.nlark.com/hoopy/download/hoopy-0.1.4.tgz"
  integrity sha1-YJIH1mEQADOpqUAq096mdzgcGx0=

hosted-git-info@^2.1.4:
  version "2.8.9"
  resolved "https://registry.nlark.com/hosted-git-info/download/hosted-git-info-2.8.9.tgz"
  integrity sha1-3/wL+aIcAiCQkPKqaUKeFBTa8/k=

hpack.js@^2.1.6:
  version "2.1.6"
  resolved "https://registry.nlark.com/hpack.js/download/hpack.js-2.1.6.tgz"
  integrity sha1-h3dMCUnlE/QuhFdbPEVoH63ioLI=
  dependencies:
    inherits "^2.0.1"
    obuf "^1.0.0"
    readable-stream "^2.0.1"
    wbuf "^1.1.0"

hsl-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/hsl-regex/download/hsl-regex-1.0.0.tgz"
  integrity sha1-1JMwx4ntgZ4nakwNJy3/owsY/m4=

hsla-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/hsla-regex/download/hsla-regex-1.0.0.tgz"
  integrity sha1-wc56MWjIxmFAM6S194d/OyJfnDg=

html-entities@^1.3.1:
  version "1.4.0"
  resolved "https://registry.nlark.com/html-entities/download/html-entities-1.4.0.tgz"
  integrity sha1-z70bAdKvr5rcobEK59/6uYxx0tw=

html-minifier@^3.2.3:
  version "3.5.21"
  resolved "https://registry.nlark.com/html-minifier/download/html-minifier-3.5.21.tgz"
  integrity sha1-0AQOBUcw41TbAIRjWTGUAVIS0gw=
  dependencies:
    camel-case "3.0.x"
    clean-css "4.2.x"
    commander "2.17.x"
    he "1.2.x"
    param-case "2.1.x"
    relateurl "0.2.x"
    uglify-js "3.4.x"

html-tags@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/html-tags/download/html-tags-2.0.0.tgz"
  integrity sha1-ELMKOGCF9Dzt41PMj6fLDe7qZos=

html-tags@^3.3.1:
  version "3.3.1"

html-webpack-plugin@^3.2.0, html-webpack-plugin@>=2.26.0:
  version "3.2.0"
  resolved "https://registry.npmmirror.com/html-webpack-plugin/download/html-webpack-plugin-3.2.0.tgz"
  integrity sha1-sBq71yOsqqeze2r0SS69oD2d03s=
  dependencies:
    html-minifier "^3.2.3"
    loader-utils "^0.2.16"
    lodash "^4.17.3"
    pretty-error "^2.0.2"
    tapable "^1.0.0"
    toposort "^1.0.0"
    util.promisify "1.0.0"

htmlparser2@^6.1.0:
  version "6.1.0"
  resolved "https://registry.npmmirror.com/htmlparser2/download/htmlparser2-6.1.0.tgz?cache=0&sync_timestamp=1636640853072&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fhtmlparser2%2Fdownload%2Fhtmlparser2-6.1.0.tgz"
  integrity sha1-xNditsM3GgXb5l6UrkOp+EX7j7c=
  dependencies:
    domelementtype "^2.0.1"
    domhandler "^4.0.0"
    domutils "^2.5.2"
    entities "^2.0.0"

http-deceiver@^1.2.7:
  version "1.2.7"
  resolved "https://registry.nlark.com/http-deceiver/download/http-deceiver-1.2.7.tgz"
  integrity sha1-+nFolEq5pRnTN8sL7HKE3D5yPYc=

http-errors@~1.6.2:
  version "1.6.3"
  resolved "https://registry.npmmirror.com/http-errors/download/http-errors-1.6.3.tgz?cache=0&sync_timestamp=1636932182141&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fhttp-errors%2Fdownload%2Fhttp-errors-1.6.3.tgz"
  integrity sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.3"
    setprototypeof "1.1.0"
    statuses ">= 1.4.0 < 2"

http-errors@2.0.0:
  version "2.0.0"
  dependencies:
    depd "2.0.0"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    toidentifier "1.0.1"

http-parser-js@>=0.5.1:
  version "0.5.8"

http-proxy-middleware@^1.0.0:
  version "1.3.1"
  resolved "https://registry.nlark.com/http-proxy-middleware/download/http-proxy-middleware-1.3.1.tgz"
  integrity sha1-Q3ANbZ7st0Gb8IahKND3IF2etmU=
  dependencies:
    "@types/http-proxy" "^1.17.5"
    http-proxy "^1.18.1"
    is-glob "^4.0.1"
    is-plain-obj "^3.0.0"
    micromatch "^4.0.2"

http-proxy-middleware@0.19.1:
  version "0.19.1"
  resolved "https://registry.nlark.com/http-proxy-middleware/download/http-proxy-middleware-0.19.1.tgz"
  integrity sha1-GDx9xKoUeRUDBkmMIQza+WCApDo=
  dependencies:
    http-proxy "^1.17.0"
    is-glob "^4.0.0"
    lodash "^4.17.11"
    micromatch "^3.1.10"

http-proxy@^1.17.0, http-proxy@^1.18.1:
  version "1.18.1"
  resolved "https://registry.nlark.com/http-proxy/download/http-proxy-1.18.1.tgz"
  integrity sha1-QBVB8FNIhLv5UmAzTnL4juOXZUk=
  dependencies:
    eventemitter3 "^4.0.0"
    follow-redirects "^1.0.0"
    requires-port "^1.0.0"

http-signature@~1.2.0:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/http-signature/download/http-signature-1.2.0.tgz?cache=0&sync_timestamp=1637178646601&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fhttp-signature%2Fdownload%2Fhttp-signature-1.2.0.tgz"
  integrity sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=
  dependencies:
    assert-plus "^1.0.0"
    jsprim "^1.2.2"
    sshpk "^1.7.0"

https-browserify@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/https-browserify/download/https-browserify-1.0.0.tgz"
  integrity sha1-7AbBDgo0wPL68Zn3/X/Hj//QPHM=

human-signals@^1.1.1:
  version "1.1.1"
  resolved "https://registry.nlark.com/human-signals/download/human-signals-1.1.1.tgz"
  integrity sha1-xbHNFPUK6uCatsWf5jujOV/k36M=

iconv-lite@^0.4.24, iconv-lite@0.4.24:
  version "0.4.24"
  resolved "https://registry.nlark.com/iconv-lite/download/iconv-lite-0.4.24.tgz?cache=0&sync_timestamp=1622686400731&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ficonv-lite%2Fdownload%2Ficonv-lite-0.4.24.tgz"
  integrity sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

icss-utils@^4.0.0, icss-utils@^4.1.1:
  version "4.1.1"
  resolved "https://registry.nlark.com/icss-utils/download/icss-utils-4.1.1.tgz"
  integrity sha1-IRcLU3ie4nRHwvR91oMIFAP5pGc=
  dependencies:
    postcss "^7.0.14"

ieee754@^1.1.4:
  version "1.2.1"
  resolved "https://registry.nlark.com/ieee754/download/ieee754-1.2.1.tgz"
  integrity sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=

iferr@^0.1.5:
  version "0.1.5"
  resolved "https://registry.nlark.com/iferr/download/iferr-0.1.5.tgz"
  integrity sha1-xg7taebY/bazEEofy8ocGS3FtQE=

ignore@^3.3.5:
  version "3.3.10"
  resolved "https://registry.npmmirror.com/ignore/download/ignore-3.3.10.tgz?cache=0&sync_timestamp=1635926632542&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fignore%2Fdownload%2Fignore-3.3.10.tgz"
  integrity sha1-Cpf7h2mG6AgcYxFg+PnziRV/AEM=

ignore@^4.0.3, ignore@^4.0.6:
  version "4.0.6"
  resolved "https://registry.npmmirror.com/ignore/download/ignore-4.0.6.tgz?cache=0&sync_timestamp=1635926632542&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fignore%2Fdownload%2Fignore-4.0.6.tgz"
  integrity sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw=

import-cwd@^2.0.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/import-cwd/download/import-cwd-2.1.0.tgz"
  integrity sha1-qmzzbnInYShcs3HsZRn1PiQ1sKk=
  dependencies:
    import-from "^2.1.0"

import-fresh@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/import-fresh/download/import-fresh-2.0.0.tgz"
  integrity sha1-2BNVwVYS04bGH53dOSLUMEgipUY=
  dependencies:
    caller-path "^2.0.0"
    resolve-from "^3.0.0"

import-fresh@^3.0.0:
  version "3.3.0"
  resolved "https://registry.nlark.com/import-fresh/download/import-fresh-3.3.0.tgz"
  integrity sha1-NxYsJfy566oublPVtNiM4X2eDCs=
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

import-from@^2.1.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/import-from/download/import-from-2.1.0.tgz"
  integrity sha1-M1238qev/VOqpHHUuAId7ja387E=
  dependencies:
    resolve-from "^3.0.0"

import-local@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/import-local/download/import-local-2.0.0.tgz?cache=0&sync_timestamp=1633327317807&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fimport-local%2Fdownload%2Fimport-local-2.0.0.tgz"
  integrity sha1-VQcL44pZk88Y72236WH1vuXFoJ0=
  dependencies:
    pkg-dir "^3.0.0"
    resolve-cwd "^2.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "https://registry.nlark.com/imurmurhash/download/imurmurhash-0.1.4.tgz"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

indexes-of@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/indexes-of/download/indexes-of-1.0.1.tgz"
  integrity sha1-8w9xbI4r00bHtn0985FVZqfAVgc=

infer-owner@^1.0.3:
  version "1.0.4"
  resolved "https://registry.nlark.com/infer-owner/download/infer-owner-1.0.4.tgz"
  integrity sha1-xM78qo5RBRwqQLos6KPScpWvlGc=

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://registry.nlark.com/inflight/download/inflight-1.0.6.tgz"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@^2.0.1, inherits@^2.0.3, inherits@^2.0.4, inherits@~2.0.1, inherits@~2.0.3, inherits@2, inherits@2.0.4:
  version "2.0.4"
  resolved "https://registry.nlark.com/inherits/download/inherits-2.0.4.tgz"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

inherits@2.0.3:
  version "2.0.3"
  resolved "https://registry.nlark.com/inherits/download/inherits-2.0.3.tgz"
  integrity sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=

inquirer@^7.0.0, inquirer@^7.1.0:
  version "7.3.3"
  resolved "https://registry.npmmirror.com/inquirer/download/inquirer-7.3.3.tgz?cache=0&sync_timestamp=1633472938029&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Finquirer%2Fdownload%2Finquirer-7.3.3.tgz"
  integrity sha1-BNF2sq8Er8FXqD/XwQDpjuCq0AM=
  dependencies:
    ansi-escapes "^4.2.1"
    chalk "^4.1.0"
    cli-cursor "^3.1.0"
    cli-width "^3.0.0"
    external-editor "^3.0.3"
    figures "^3.0.0"
    lodash "^4.17.19"
    mute-stream "0.0.8"
    run-async "^2.4.0"
    rxjs "^6.6.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"
    through "^2.3.6"

internal-ip@^4.3.0:
  version "4.3.0"
  resolved "https://registry.npmmirror.com/internal-ip/download/internal-ip-4.3.0.tgz"
  integrity sha1-hFRSuq2dLKO2nGNaE3rLmg2tCQc=
  dependencies:
    default-gateway "^4.2.0"
    ipaddr.js "^1.9.0"

internal-slot@^1.1.0:
  version "1.1.0"
  dependencies:
    es-errors "^1.3.0"
    hasown "^2.0.2"
    side-channel "^1.1.0"

interpret@^1.0.0:
  version "1.4.0"
  resolved "https://registry.npmmirror.com/interpret/-/interpret-1.4.0.tgz"
  integrity sha512-agE4QfB2Lkp9uICn7BAqoscw4SZP9kTE2hxiFI3jBPmXJfdqiahTbUuKGsMoN2GtqL9AxhYioAcVvgsb1HvRbA==

ip-regex@^2.1.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/ip-regex/download/ip-regex-2.1.0.tgz"
  integrity sha1-+ni/XS5pE8kRzp+BnuUUa7bYROk=

ip@^1.1.0, ip@^1.1.5:
  version "1.1.9"

ipaddr.js@^1.9.0, ipaddr.js@1.9.1:
  version "1.9.1"
  resolved "https://registry.nlark.com/ipaddr.js/download/ipaddr.js-1.9.1.tgz"
  integrity sha1-v/OFQ+64mEglB5/zoqjmy9RngbM=

is-absolute-url@^2.0.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/is-absolute-url/download/is-absolute-url-2.1.0.tgz"
  integrity sha1-UFMN+4T8yap9vnhS6Do3uTufKqY=

is-absolute-url@^3.0.3:
  version "3.0.3"
  resolved "https://registry.nlark.com/is-absolute-url/download/is-absolute-url-3.0.3.tgz"
  integrity sha1-lsaiK2ojkpsR6gr7GDbDatSl1pg=

is-accessor-descriptor@^1.0.1:
  version "1.0.1"
  dependencies:
    hasown "^2.0.0"

is-arguments@^1.1.1:
  version "1.2.0"
  dependencies:
    call-bound "^1.0.2"
    has-tostringtag "^1.0.2"

is-array-buffer@^3.0.4, is-array-buffer@^3.0.5:
  version "3.0.5"
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    get-intrinsic "^1.2.6"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "https://registry.nlark.com/is-arrayish/download/is-arrayish-0.2.1.tgz"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-arrayish@^0.3.1:
  version "0.3.2"
  resolved "https://registry.nlark.com/is-arrayish/download/is-arrayish-0.3.2.tgz"
  integrity sha1-RXSirlb3qyBolvtDHq7tBm/fjwM=

is-async-function@^2.0.0:
  version "2.0.0"
  dependencies:
    has-tostringtag "^1.0.0"

is-bigint@^1.1.0:
  version "1.1.0"
  dependencies:
    has-bigints "^1.0.2"

is-binary-path@^1.0.0:
  version "1.0.1"
  resolved "https://registry.nlark.com/is-binary-path/download/is-binary-path-1.0.1.tgz"
  integrity sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg=
  dependencies:
    binary-extensions "^1.0.0"

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/is-binary-path/download/is-binary-path-2.1.0.tgz"
  integrity sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=
  dependencies:
    binary-extensions "^2.0.0"

is-boolean-object@^1.2.1:
  version "1.2.1"
  dependencies:
    call-bound "^1.0.2"
    has-tostringtag "^1.0.2"

is-buffer@^1.1.5:
  version "1.1.6"
  resolved "https://registry.nlark.com/is-buffer/download/is-buffer-1.1.6.tgz"
  integrity sha1-76ouqdqg16suoTqXsritUf776L4=

is-callable@^1.1.3, is-callable@^1.2.7:
  version "1.2.7"

is-ci@^1.0.10:
  version "1.2.1"
  resolved "https://registry.npmmirror.com/is-ci/download/is-ci-1.2.1.tgz"
  integrity sha1-43ecjuF/zPQoSI9uKBGH8uYyhBw=
  dependencies:
    ci-info "^1.5.0"

is-color-stop@^1.0.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/is-color-stop/download/is-color-stop-1.1.0.tgz"
  integrity sha1-z/9HGu5N1cnhWFmPvhKWe1za00U=
  dependencies:
    css-color-names "^0.0.4"
    hex-color-regex "^1.1.0"
    hsl-regex "^1.0.0"
    hsla-regex "^1.0.0"
    rgb-regex "^1.0.1"
    rgba-regex "^1.0.0"

is-core-module@^2.16.0:
  version "2.16.1"
  dependencies:
    hasown "^2.0.2"

is-data-descriptor@^1.0.1:
  version "1.0.1"
  dependencies:
    hasown "^2.0.0"

is-data-view@^1.0.1, is-data-view@^1.0.2:
  version "1.0.2"
  dependencies:
    call-bound "^1.0.2"
    get-intrinsic "^1.2.6"
    is-typed-array "^1.1.13"

is-date-object@^1.0.5, is-date-object@^1.1.0:
  version "1.1.0"
  dependencies:
    call-bound "^1.0.2"
    has-tostringtag "^1.0.2"

is-descriptor@^0.1.0:
  version "0.1.7"
  dependencies:
    is-accessor-descriptor "^1.0.1"
    is-data-descriptor "^1.0.1"

is-descriptor@^1.0.0, is-descriptor@^1.0.2:
  version "1.0.3"
  dependencies:
    is-accessor-descriptor "^1.0.1"
    is-data-descriptor "^1.0.1"

is-directory@^0.3.1:
  version "0.3.1"
  resolved "https://registry.nlark.com/is-directory/download/is-directory-0.3.1.tgz"
  integrity sha1-YTObbyR1/Hcv2cnYP1yFddwVSuE=

is-docker@^2.0.0:
  version "2.2.1"
  resolved "https://registry.nlark.com/is-docker/download/is-docker-2.2.1.tgz?cache=0&sync_timestamp=1630451108035&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-docker%2Fdownload%2Fis-docker-2.2.1.tgz"
  integrity sha1-M+6r4jz+hvFL3kQIoCwM+4U6zao=

is-extendable@^0.1.0, is-extendable@^0.1.1:
  version "0.1.1"
  resolved "https://registry.nlark.com/is-extendable/download/is-extendable-0.1.1.tgz"
  integrity sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=

is-extendable@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/is-extendable/download/is-extendable-1.0.1.tgz"
  integrity sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=
  dependencies:
    is-plain-object "^2.0.4"

is-extglob@^2.1.0, is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://registry.nlark.com/is-extglob/download/is-extglob-2.1.1.tgz"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-finalizationregistry@^1.1.0:
  version "1.1.1"
  dependencies:
    call-bound "^1.0.3"

is-fullwidth-code-point@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz"
  integrity sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz"
  integrity sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=

is-generator-function@^1.0.10:
  version "1.0.10"
  dependencies:
    has-tostringtag "^1.0.0"

is-glob@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/is-glob/download/is-glob-3.1.0.tgz"
  integrity sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=
  dependencies:
    is-extglob "^2.1.0"

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@~4.0.1:
  version "4.0.3"
  resolved "https://registry.npmmirror.com/is-glob/download/is-glob-4.0.3.tgz"
  integrity sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=
  dependencies:
    is-extglob "^2.1.1"

is-map@^2.0.3:
  version "2.0.3"

is-number-object@^1.1.1:
  version "1.1.1"
  dependencies:
    call-bound "^1.0.3"
    has-tostringtag "^1.0.2"

is-number@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/is-number/download/is-number-3.0.0.tgz"
  integrity sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=
  dependencies:
    kind-of "^3.0.2"

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://registry.nlark.com/is-number/download/is-number-7.0.0.tgz"
  integrity sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=

is-obj@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/is-obj/download/is-obj-1.0.1.tgz"
  integrity sha1-PkcprB9f3gJc19g6iW2rn09n2w8=

is-obj@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/is-obj/download/is-obj-2.0.0.tgz"
  integrity sha1-Rz+wXZc3BeP9liBUUBjKjiLvSYI=

is-path-cwd@^2.0.0:
  version "2.2.0"
  resolved "https://registry.nlark.com/is-path-cwd/download/is-path-cwd-2.2.0.tgz"
  integrity sha1-Z9Q7gmZKe1GR/ZEZEn6zAASKn9s=

is-path-in-cwd@^2.0.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/is-path-in-cwd/download/is-path-in-cwd-2.1.0.tgz"
  integrity sha1-v+Lcomxp85cmWkAJljYCk1oFOss=
  dependencies:
    is-path-inside "^2.1.0"

is-path-inside@^2.1.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/is-path-inside/download/is-path-inside-2.1.0.tgz"
  integrity sha1-fJgQWH1lmkDSe8201WFuqwWUlLI=
  dependencies:
    path-is-inside "^1.0.2"

is-plain-obj@^1.0.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/is-plain-obj/download/is-plain-obj-1.1.0.tgz"
  integrity sha1-caUMhCnfync8kqOQpKA7OfzVHT4=

is-plain-obj@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/is-plain-obj/download/is-plain-obj-3.0.0.tgz"
  integrity sha1-r28uoUrFpkYYOlu9tbqrvBVq2dc=

is-plain-object@^2.0.3, is-plain-object@^2.0.4:
  version "2.0.4"
  resolved "https://registry.nlark.com/is-plain-object/download/is-plain-object-2.0.4.tgz"
  integrity sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=
  dependencies:
    isobject "^3.0.1"

is-regex@^1.1.4, is-regex@^1.2.1:
  version "1.2.1"
  dependencies:
    call-bound "^1.0.2"
    gopd "^1.2.0"
    has-tostringtag "^1.0.2"
    hasown "^2.0.2"

is-regexp@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/is-regexp/download/is-regexp-1.0.0.tgz"
  integrity sha1-/S2INUXEa6xaYz57mgnof6LLUGk=

is-resolvable@^1.0.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/is-resolvable/download/is-resolvable-1.1.0.tgz"
  integrity sha1-+xj4fOH+uSUWnJpAfBkxijIG7Yg=

is-retry-allowed@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npm.taobao.org/is-retry-allowed/download/is-retry-allowed-2.2.0.tgz?cache=0&sync_timestamp=1618646227600&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-retry-allowed%2Fdownload%2Fis-retry-allowed-2.2.0.tgz"
  integrity sha1-iPNMvSNuBD5xtpMtCbDGX7e01x0=

is-set@^2.0.3:
  version "2.0.3"

is-shared-array-buffer@^1.0.4:
  version "1.0.4"
  dependencies:
    call-bound "^1.0.3"

is-stream@^1.1.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/is-stream/download/is-stream-1.1.0.tgz"
  integrity sha1-EtSj3U5o4Lec6428hBc66A2RykQ=

is-stream@^2.0.0:
  version "2.0.1"
  resolved "https://registry.nlark.com/is-stream/download/is-stream-2.0.1.tgz"
  integrity sha1-+sHj1TuXrVqdCunO8jifWBClwHc=

is-string@^1.0.7, is-string@^1.1.1:
  version "1.1.1"
  dependencies:
    call-bound "^1.0.3"
    has-tostringtag "^1.0.2"

is-symbol@^1.0.4, is-symbol@^1.1.1:
  version "1.1.1"
  dependencies:
    call-bound "^1.0.2"
    has-symbols "^1.1.0"
    safe-regex-test "^1.1.0"

is-typed-array@^1.1.13, is-typed-array@^1.1.14, is-typed-array@^1.1.15:
  version "1.1.15"
  dependencies:
    which-typed-array "^1.1.16"

is-typedarray@~1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/is-typedarray/download/is-typedarray-1.0.0.tgz"
  integrity sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=

is-weakmap@^2.0.2:
  version "2.0.2"

is-weakref@^1.0.2, is-weakref@^1.1.0:
  version "1.1.0"
  dependencies:
    call-bound "^1.0.2"

is-weakset@^2.0.3:
  version "2.0.4"
  dependencies:
    call-bound "^1.0.3"
    get-intrinsic "^1.2.6"

is-windows@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/is-windows/download/is-windows-1.0.2.tgz"
  integrity sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0=

is-wsl@^1.1.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/is-wsl/download/is-wsl-1.1.0.tgz"
  integrity sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0=

is-wsl@^2.1.1:
  version "2.2.0"
  resolved "https://registry.nlark.com/is-wsl/download/is-wsl-2.2.0.tgz"
  integrity sha1-dKTHbnfKn9P5MvKQwX6jJs0VcnE=
  dependencies:
    is-docker "^2.0.0"

isarray@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/isarray/download/isarray-1.0.0.tgz"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isarray@^2.0.5:
  version "2.0.5"

isarray@~1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/isarray/download/isarray-1.0.0.tgz"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isarray@1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/isarray/download/isarray-1.0.0.tgz"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/isexe/download/isexe-2.0.0.tgz"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

isnumeric@^0.2.0:
  version "0.2.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/isnumeric/-/isnumeric-0.2.0.tgz"
  integrity sha512-uSJoAwnN1eCKDFKi8hL3UCYJSkQv+NwhKzhevUPIn/QZ8ILO21f+wQnlZHU0eh1rsLO1gI4w/HQdeOSTKwlqMg==

isobject@^2.0.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/isobject/download/isobject-2.1.0.tgz"
  integrity sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=
  dependencies:
    isarray "1.0.0"

isobject@^3.0.0, isobject@^3.0.1:
  version "3.0.1"
  resolved "https://registry.nlark.com/isobject/download/isobject-3.0.1.tgz"
  integrity sha1-TkMekrEalzFjaqH5yNHMvP2reN8=

isstream@~0.1.2:
  version "0.1.2"
  resolved "https://registry.nlark.com/isstream/download/isstream-0.1.2.tgz"
  integrity sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=

javascript-stringify@^2.0.1:
  version "2.1.0"
  resolved "https://registry.nlark.com/javascript-stringify/download/javascript-stringify-2.1.0.tgz"
  integrity sha1-J8dlOb4U2L0Sghmi1zGwkzeQTnk=

jest-worker@^27.4.5:
  version "27.5.1"
  dependencies:
    "@types/node" "*"
    merge-stream "^2.0.0"
    supports-color "^8.0.0"

js-base64@^2.1.9:
  version "2.6.4"
  resolved "https://registry.npmjs.org/js-base64/-/js-base64-2.6.4.tgz"
  integrity sha512-pZe//GGmwJndub7ZghVHz7vjb2LgC1m8B07Au3eYqeqv9emhESByMXxaEgkUkEqJe87oBbSniGYoQNIBklc7IQ==

js-message@1.0.7:
  version "1.0.7"
  resolved "https://registry.nlark.com/js-message/download/js-message-1.0.7.tgz"
  integrity sha1-+93QU8ekcCGHG7iyyVOXzBfCDkc=

js-tokens@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/js-tokens/download/js-tokens-4.0.0.tgz"
  integrity sha1-GSA/tZmR35jjoocFDUZHzerzJJk=

js-yaml@^3.13.1:
  version "3.14.1"
  resolved "https://registry.nlark.com/js-yaml/download/js-yaml-3.14.1.tgz"
  integrity sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

jsbn@~0.1.0:
  version "0.1.1"
  resolved "https://registry.nlark.com/jsbn/download/jsbn-0.1.1.tgz"
  integrity sha1-peZUwuWi3rXyAdls77yoDA7y9RM=

jsesc@^3.0.2:
  version "3.1.0"

jsesc@~3.0.2:
  version "3.0.2"

json-parse-better-errors@^1.0.1, json-parse-better-errors@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/json-parse-better-errors/download/json-parse-better-errors-1.0.2.tgz"
  integrity sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk=

json-parse-even-better-errors@^2.3.0, json-parse-even-better-errors@^2.3.1:
  version "2.3.1"
  resolved "https://registry.nlark.com/json-parse-even-better-errors/download/json-parse-even-better-errors-2.3.1.tgz"
  integrity sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "https://registry.nlark.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz"
  integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=

json-schema-traverse@^1.0.0:
  version "1.0.0"

json-schema@0.4.0:
  version "0.4.0"

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz"
  integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=

json-stable-stringify@^1.0.1:
  version "1.2.1"
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    isarray "^2.0.5"
    jsonify "^0.0.1"
    object-keys "^1.1.1"

json-stringify-safe@~5.0.1:
  version "5.0.1"
  resolved "https://registry.nlark.com/json-stringify-safe/download/json-stringify-safe-5.0.1.tgz"
  integrity sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=

json5@^0.5.0:
  version "0.5.1"
  resolved "https://registry.nlark.com/json5/download/json5-0.5.1.tgz"
  integrity sha1-Hq3nrMASA0rYTiOWdn6tn6VJWCE=

json5@^1.0.1:
  version "1.0.2"
  dependencies:
    minimist "^1.2.0"

json5@^2.1.2, json5@^2.2.3:
  version "2.2.3"

jsonfile@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/jsonfile/download/jsonfile-4.0.0.tgz"
  integrity sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss=
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonify@^0.0.1:
  version "0.0.1"

jsprim@^1.2.2:
  version "1.4.2"
  dependencies:
    assert-plus "1.0.0"
    extsprintf "1.3.0"
    json-schema "0.4.0"
    verror "1.10.0"

killable@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/killable/download/killable-1.0.1.tgz"
  integrity sha1-TIzkQRh6Bhx0dPuHygjipjgZSJI=

kind-of@^3.0.2, kind-of@^3.0.3, kind-of@^3.2.0:
  version "3.2.2"
  resolved "https://registry.nlark.com/kind-of/download/kind-of-3.2.2.tgz"
  integrity sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/kind-of/download/kind-of-4.0.0.tgz"
  integrity sha1-IIE989cSkosgc3hpGkUGb65y3Vc=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^6.0.2:
  version "6.0.3"
  resolved "https://registry.nlark.com/kind-of/download/kind-of-6.0.3.tgz"
  integrity sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=

launch-editor-middleware@^2.2.1:
  version "2.9.1"
  dependencies:
    launch-editor "^2.9.1"

launch-editor@^2.2.1, launch-editor@^2.9.1:
  version "2.9.1"
  dependencies:
    picocolors "^1.0.0"
    shell-quote "^1.8.1"

levn@^0.3.0, levn@~0.3.0:
  version "0.3.0"
  resolved "https://registry.nlark.com/levn/download/levn-0.3.0.tgz"
  integrity sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4=
  dependencies:
    prelude-ls "~1.1.2"
    type-check "~0.3.2"

lines-and-columns@^1.1.6:
  version "1.2.4"
  resolved "https://registry.npmmirror.com/lines-and-columns/download/lines-and-columns-1.2.4.tgz"
  integrity sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==

loader-fs-cache@^1.0.0:
  version "1.0.3"
  resolved "https://registry.nlark.com/loader-fs-cache/download/loader-fs-cache-1.0.3.tgz"
  integrity sha1-8IZXZG1gcHi+LwoDL4vWndbyd9k=
  dependencies:
    find-cache-dir "^0.1.1"
    mkdirp "^0.5.1"

loader-runner@^2.3.1, loader-runner@^2.4.0:
  version "2.4.0"
  resolved "https://registry.nlark.com/loader-runner/download/loader-runner-2.4.0.tgz"
  integrity sha1-7UcGa/5TTX6ExMe5mYwqdWB9k1c=

loader-runner@^4.2.0:
  version "4.3.0"

loader-utils@^0.2.16:
  version "0.2.17"
  resolved "https://registry.npmmirror.com/loader-utils/download/loader-utils-0.2.17.tgz?cache=0&sync_timestamp=1636687952028&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Floader-utils%2Fdownload%2Floader-utils-0.2.17.tgz"
  integrity sha1-+G5jdNQyBabmxg6RlvF8Apm/s0g=
  dependencies:
    big.js "^3.1.3"
    emojis-list "^2.0.0"
    json5 "^0.5.0"
    object-assign "^4.0.1"

loader-utils@^1.0.2, loader-utils@^1.1.0, loader-utils@^1.2.3:
  version "1.4.2"
  dependencies:
    big.js "^5.2.2"
    emojis-list "^3.0.0"
    json5 "^1.0.1"

loader-utils@^2.0.0:
  version "2.0.4"
  dependencies:
    big.js "^5.2.2"
    emojis-list "^3.0.0"
    json5 "^2.1.2"

loader-utils@^2.0.4:
  version "2.0.4"
  dependencies:
    big.js "^5.2.2"
    emojis-list "^3.0.0"
    json5 "^2.1.2"

locate-path@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/locate-path/download/locate-path-3.0.0.tgz?cache=0&sync_timestamp=1629895618224&other_urls=https%3A%2F%2Fregistry.nlark.com%2Flocate-path%2Fdownload%2Flocate-path-3.0.0.tgz"
  integrity sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=
  dependencies:
    p-locate "^3.0.0"
    path-exists "^3.0.0"

locate-path@^5.0.0:
  version "5.0.0"
  resolved "https://registry.nlark.com/locate-path/download/locate-path-5.0.0.tgz?cache=0&sync_timestamp=1629895618224&other_urls=https%3A%2F%2Fregistry.nlark.com%2Flocate-path%2Fdownload%2Flocate-path-5.0.0.tgz"
  integrity sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=
  dependencies:
    p-locate "^4.1.0"

lodash._reinterpolate@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/lodash._reinterpolate/download/lodash._reinterpolate-3.0.0.tgz"
  integrity sha1-DM8tiRZq8Ds2Y8eWU4t1rG4RTZ0=

lodash.clonedeep@^4.5.0:
  version "4.5.0"
  resolved "https://registry.nlark.com/lodash.clonedeep/download/lodash.clonedeep-4.5.0.tgz"
  integrity sha1-4j8/nE+Pvd6HJSnBBxhXoIblzO8=

lodash.debounce@^4.0.8:
  version "4.0.8"
  resolved "https://registry.nlark.com/lodash.debounce/download/lodash.debounce-4.0.8.tgz"
  integrity sha1-gteb/zCmfEAF/9XiUVMArZyk168=

lodash.defaultsdeep@^4.6.1:
  version "4.6.1"
  resolved "https://registry.nlark.com/lodash.defaultsdeep/download/lodash.defaultsdeep-4.6.1.tgz"
  integrity sha1-US6b1yHSctlOPTpjZT+hdRZ0HKY=

lodash.kebabcase@^4.1.1:
  version "4.1.1"
  resolved "https://registry.nlark.com/lodash.kebabcase/download/lodash.kebabcase-4.1.1.tgz"
  integrity sha1-hImxyw0p/4gZXM7KRI/21swpXDY=

lodash.mapvalues@^4.6.0:
  version "4.6.0"
  resolved "https://registry.nlark.com/lodash.mapvalues/download/lodash.mapvalues-4.6.0.tgz"
  integrity sha1-G6+lAF3p3W9PJmaMMMo3IwzJaJw=

lodash.memoize@^4.1.2:
  version "4.1.2"
  resolved "https://registry.nlark.com/lodash.memoize/download/lodash.memoize-4.1.2.tgz"
  integrity sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4=

lodash.template@^4.2.4, lodash.template@^4.4.0:
  version "4.5.0"
  resolved "https://registry.nlark.com/lodash.template/download/lodash.template-4.5.0.tgz"
  integrity sha1-+XYZXPPzR9DV9SSDVp/oAxzM6Ks=
  dependencies:
    lodash._reinterpolate "^3.0.0"
    lodash.templatesettings "^4.0.0"

lodash.templatesettings@^4.0.0:
  version "4.2.0"
  resolved "https://registry.nlark.com/lodash.templatesettings/download/lodash.templatesettings-4.2.0.tgz"
  integrity sha1-5IExDwSdPPbUfpEq0JMTsVTw+zM=
  dependencies:
    lodash._reinterpolate "^3.0.0"

lodash.transform@^4.6.0:
  version "4.6.0"
  resolved "https://registry.nlark.com/lodash.transform/download/lodash.transform-4.6.0.tgz"
  integrity sha1-EjBkIvYzJK7YSD0/ODMrX2cFR6A=

lodash.uniq@^4.5.0:
  version "4.5.0"
  resolved "https://registry.nlark.com/lodash.uniq/download/lodash.uniq-4.5.0.tgz"
  integrity sha1-0CJTc662Uq3BvILklFM5qEJ1R3M=

lodash@^4.17.11, lodash@^4.17.14, lodash@^4.17.15, lodash@^4.17.19, lodash@^4.17.20, lodash@^4.17.21, lodash@^4.17.3:
  version "4.17.21"
  resolved "https://registry.nlark.com/lodash/download/lodash-4.17.21.tgz?cache=0&sync_timestamp=1622685830565&other_urls=https%3A%2F%2Fregistry.nlark.com%2Flodash%2Fdownload%2Flodash-4.17.21.tgz"
  integrity sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=

log-symbols@^2.2.0:
  version "2.2.0"
  resolved "https://registry.nlark.com/log-symbols/download/log-symbols-2.2.0.tgz"
  integrity sha1-V0Dhxdbw39pK2TI7UzIQfva0xAo=
  dependencies:
    chalk "^2.0.1"

loglevel@^1.6.8:
  version "1.9.2"

lower-case@^1.1.1:
  version "1.1.4"
  resolved "https://registry.nlark.com/lower-case/download/lower-case-1.1.4.tgz"
  integrity sha1-miyr0bno4K6ZOkv31YdcOcQujqw=

lru-cache@^4.0.1:
  version "4.1.5"
  resolved "https://registry.nlark.com/lru-cache/download/lru-cache-4.1.5.tgz"
  integrity sha1-i75Q6oW+1ZvJ4z3KuCNe6bz0Q80=
  dependencies:
    pseudomap "^1.0.2"
    yallist "^2.1.2"

lru-cache@^4.1.2:
  version "4.1.5"
  resolved "https://registry.nlark.com/lru-cache/download/lru-cache-4.1.5.tgz"
  integrity sha1-i75Q6oW+1ZvJ4z3KuCNe6bz0Q80=
  dependencies:
    pseudomap "^1.0.2"
    yallist "^2.1.2"

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "https://registry.nlark.com/lru-cache/download/lru-cache-5.1.1.tgz"
  integrity sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=
  dependencies:
    yallist "^3.0.2"

magic-string@^0.30.11:
  version "0.30.17"
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.5.0"

make-dir@^2.0.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/make-dir/download/make-dir-2.1.0.tgz"
  integrity sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU=
  dependencies:
    pify "^4.0.1"
    semver "^5.6.0"

make-dir@^3.0.2, make-dir@^3.1.0:
  version "3.1.0"
  resolved "https://registry.nlark.com/make-dir/download/make-dir-3.1.0.tgz"
  integrity sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8=
  dependencies:
    semver "^6.0.0"

map-cache@^0.2.2:
  version "0.2.2"
  resolved "https://registry.nlark.com/map-cache/download/map-cache-0.2.2.tgz"
  integrity sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=

map-visit@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/map-visit/download/map-visit-1.0.0.tgz"
  integrity sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=
  dependencies:
    object-visit "^1.0.0"

math-expression-evaluator@^1.2.14:
  version "1.4.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/math-expression-evaluator/-/math-expression-evaluator-1.4.0.tgz"
  integrity sha512-4vRUvPyxdO8cWULGTh9dZWL2tZK6LDBvj+OGHBER7poH9Qdt7kXEoj20wiz4lQUbUXQZFjPbe5mVDo9nutizCw==

math-intrinsics@^1.1.0:
  version "1.1.0"

md5.js@^1.3.4:
  version "1.3.5"
  resolved "https://registry.nlark.com/md5.js/download/md5.js-1.3.5.tgz"
  integrity sha1-tdB7jjIW4+J81yjXL3DR5qNCAF8=
  dependencies:
    hash-base "^3.0.0"
    inherits "^2.0.1"
    safe-buffer "^5.1.2"

mdn-data@2.0.14:
  version "2.0.14"
  resolved "https://registry.nlark.com/mdn-data/download/mdn-data-2.0.14.tgz?cache=0&sync_timestamp=1631835963997&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fmdn-data%2Fdownload%2Fmdn-data-2.0.14.tgz"
  integrity sha1-cRP8QoGRfWPOKbQ0RvcB5owlulA=

mdn-data@2.0.4:
  version "2.0.4"
  resolved "https://registry.nlark.com/mdn-data/download/mdn-data-2.0.4.tgz?cache=0&sync_timestamp=1631835963997&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fmdn-data%2Fdownload%2Fmdn-data-2.0.4.tgz"
  integrity sha1-aZs8OKxvHXKAkaZGULZdOIUC/Vs=

media-typer@0.3.0:
  version "0.3.0"
  resolved "https://registry.nlark.com/media-typer/download/media-typer-0.3.0.tgz"
  integrity sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=

memory-fs@^0.4.1:
  version "0.4.1"
  resolved "https://registry.nlark.com/memory-fs/download/memory-fs-0.4.1.tgz"
  integrity sha1-OpoguEYlI+RHz7x+i7gO1me/xVI=
  dependencies:
    errno "^0.1.3"
    readable-stream "^2.0.1"

memory-fs@^0.5.0:
  version "0.5.0"
  resolved "https://registry.nlark.com/memory-fs/download/memory-fs-0.5.0.tgz"
  integrity sha1-MkwBKIuIZSlm0WHbd4OHIIRajjw=
  dependencies:
    errno "^0.1.3"
    readable-stream "^2.0.1"

merge-descriptors@1.0.3:
  version "1.0.3"

merge-source-map@^1.1.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/merge-source-map/download/merge-source-map-1.1.0.tgz"
  integrity sha1-L93n5gIJOfcJBqaPLXrmheTIxkY=
  dependencies:
    source-map "^0.6.1"

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/merge-stream/download/merge-stream-2.0.0.tgz"
  integrity sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=

merge2@^1.2.3:
  version "1.4.1"
  resolved "https://registry.nlark.com/merge2/download/merge2-1.4.1.tgz"
  integrity sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=

methods@~1.1.2:
  version "1.1.2"
  resolved "https://registry.nlark.com/methods/download/methods-1.1.2.tgz"
  integrity sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=

micromatch@^3.1.10, micromatch@^3.1.4:
  version "3.1.10"
  resolved "https://registry.nlark.com/micromatch/download/micromatch-3.1.10.tgz"
  integrity sha1-cIWbyVyYQJUvNZoGij/En57PrCM=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    braces "^2.3.1"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    extglob "^2.0.4"
    fragment-cache "^0.2.1"
    kind-of "^6.0.2"
    nanomatch "^1.2.9"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.2"

micromatch@^4.0.2:
  version "4.0.8"
  dependencies:
    braces "^3.0.3"
    picomatch "^2.3.1"

miller-rabin@^4.0.0:
  version "4.0.1"
  resolved "https://registry.nlark.com/miller-rabin/download/miller-rabin-4.0.1.tgz"
  integrity sha1-8IA1HIZbDcViqEYpZtqlNUPHik0=
  dependencies:
    bn.js "^4.0.0"
    brorand "^1.0.1"

"mime-db@>= 1.43.0 < 2":
  version "1.53.0"

mime-db@1.52.0:
  version "1.52.0"

mime-types@^2.1.12, mime-types@^2.1.27, mime-types@~2.1.17, mime-types@~2.1.19, mime-types@~2.1.24, mime-types@~2.1.34:
  version "2.1.35"
  dependencies:
    mime-db "1.52.0"

mime@^2.3.1, mime@^2.4.4:
  version "2.6.0"
  resolved "https://registry.npmmirror.com/mime/download/mime-2.6.0.tgz"
  integrity sha1-oqaCqVzU0MsdYlfij4PafjWAA2c=

mime@1.6.0:
  version "1.6.0"
  resolved "https://registry.npmmirror.com/mime/download/mime-1.6.0.tgz"
  integrity sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=

mimic-fn@^1.0.0:
  version "1.2.0"
  resolved "https://registry.nlark.com/mimic-fn/download/mimic-fn-1.2.0.tgz"
  integrity sha1-ggyGo5M0ZA6ZUWkovQP8qIBX0CI=

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/mimic-fn/download/mimic-fn-2.1.0.tgz"
  integrity sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=

mini-css-extract-plugin@^0.9.0:
  version "0.9.0"
  resolved "https://registry.npmmirror.com/mini-css-extract-plugin/download/mini-css-extract-plugin-0.9.0.tgz"
  integrity sha1-R/LPB6oWWrNXM7H8l9TEbAVkM54=
  dependencies:
    loader-utils "^1.1.0"
    normalize-url "1.9.1"
    schema-utils "^1.0.0"
    webpack-sources "^1.1.0"

minimalistic-assert@^1.0.0, minimalistic-assert@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/minimalistic-assert/download/minimalistic-assert-1.0.1.tgz"
  integrity sha1-LhlN4ERibUoQ5/f7wAznPoPk1cc=

minimalistic-crypto-utils@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/minimalistic-crypto-utils/download/minimalistic-crypto-utils-1.0.1.tgz"
  integrity sha1-9sAMHAsIIkblxNmd+4x8CDsrWCo=

minimatch@^3.0.4, minimatch@^3.1.1:
  version "3.1.2"
  dependencies:
    brace-expansion "^1.1.7"

minimist@^1.2.0, minimist@^1.2.5, minimist@^1.2.6:
  version "1.2.8"

minipass@^3.1.1:
  version "3.3.6"
  dependencies:
    yallist "^4.0.0"

mississippi@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/mississippi/download/mississippi-3.0.0.tgz"
  integrity sha1-6goykfl+C16HdrNj1fChLZTGcCI=
  dependencies:
    concat-stream "^1.5.0"
    duplexify "^3.4.2"
    end-of-stream "^1.1.0"
    flush-write-stream "^1.0.0"
    from2 "^2.1.0"
    parallel-transform "^1.1.0"
    pump "^3.0.0"
    pumpify "^1.3.3"
    stream-each "^1.1.0"
    through2 "^2.0.0"

mitt@^2.1.0:
  version "2.1.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/mitt/-/mitt-2.1.0.tgz"
  integrity sha512-ILj2TpLiysu2wkBbWjAmww7TkZb65aiQO+DkVdUTBpBXq+MHYiETENkKFMtsJZX1Lf4pe4QOrTSjIfUwN5lRdg==

mixin-deep@^1.2.0:
  version "1.3.2"
  resolved "https://registry.nlark.com/mixin-deep/download/mixin-deep-1.3.2.tgz"
  integrity sha1-ESC0PcNZp4Xc5ltVuC4lfM9HlWY=
  dependencies:
    for-in "^1.0.2"
    is-extendable "^1.0.1"

mkdirp@^0.5.0, mkdirp@^0.5.1, mkdirp@^0.5.3, mkdirp@^0.5.6, mkdirp@~0.5.1:
  version "0.5.6"
  dependencies:
    minimist "^1.2.6"

mkdirp@~1.0.4:
  version "1.0.4"
  resolved "https://registry.npmmirror.com/mkdirp/download/mkdirp-1.0.4.tgz"
  integrity sha1-PrXtYmInVteaXw4qIh3+utdcL34=

move-concurrently@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/move-concurrently/download/move-concurrently-1.0.1.tgz"
  integrity sha1-viwAX9oy4LKa8fBdfEszIUxwH5I=
  dependencies:
    aproba "^1.1.1"
    copy-concurrently "^1.0.0"
    fs-write-stream-atomic "^1.0.8"
    mkdirp "^0.5.1"
    rimraf "^2.5.4"
    run-queue "^1.0.3"

ms@^2.1.1, ms@^2.1.3, ms@2.1.3:
  version "2.1.3"
  resolved "https://registry.npmmirror.com/ms/download/ms-2.1.3.tgz"
  integrity sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=

ms@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/ms/download/ms-2.0.0.tgz"
  integrity sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=

multicast-dns-service-types@^1.1.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/multicast-dns-service-types/download/multicast-dns-service-types-1.1.0.tgz"
  integrity sha1-iZ8R2WhuXgXLkbNdXw5jt3PPyQE=

multicast-dns@^6.0.1:
  version "6.2.3"
  resolved "https://registry.npmmirror.com/multicast-dns/download/multicast-dns-6.2.3.tgz?cache=0&sync_timestamp=1633354996608&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fmulticast-dns%2Fdownload%2Fmulticast-dns-6.2.3.tgz"
  integrity sha1-oOx72QVcQoL3kMPIL04o2zsxsik=
  dependencies:
    dns-packet "^1.3.1"
    thunky "^1.0.2"

mute-stream@0.0.8:
  version "0.0.8"
  resolved "https://registry.nlark.com/mute-stream/download/mute-stream-0.0.8.tgz"
  integrity sha1-FjDEKyJR/4HiooPelqVJfqkuXg0=

mz@^2.4.0:
  version "2.7.0"
  resolved "https://registry.nlark.com/mz/download/mz-2.7.0.tgz"
  integrity sha1-lQCAV6Vsr63CvGPd5/n/aVWUjjI=
  dependencies:
    any-promise "^1.0.0"
    object-assign "^4.0.1"
    thenify-all "^1.0.0"

nan@^2.12.1:
  version "2.22.0"

nanoid@^3.1.30, nanoid@^3.3.7:
  version "3.3.8"

nanomatch@^1.2.9:
  version "1.2.13"
  resolved "https://registry.nlark.com/nanomatch/download/nanomatch-1.2.13.tgz"
  integrity sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    fragment-cache "^0.2.1"
    is-windows "^1.0.2"
    kind-of "^6.0.2"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

nats.ws@^1.29.2:
  version "1.29.2"
  optionalDependencies:
    nkeys.js "1.1.0"

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "https://registry.nlark.com/natural-compare/download/natural-compare-1.4.0.tgz"
  integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=

negotiator@~0.6.4:
  version "0.6.4"

negotiator@0.6.3:
  version "0.6.3"

neo-async@^2.5.0, neo-async@^2.6.0, neo-async@^2.6.1, neo-async@^2.6.2:
  version "2.6.2"
  resolved "https://registry.nlark.com/neo-async/download/neo-async-2.6.2.tgz"
  integrity sha1-tKr7k+OustgXTKU88WOrfXMIMF8=

nice-try@^1.0.4:
  version "1.0.5"
  resolved "https://registry.nlark.com/nice-try/download/nice-try-1.0.5.tgz"
  integrity sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y=

nkeys.js@1.1.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/nkeys.js/-/nkeys.js-1.1.0.tgz"
  integrity sha512-tB/a0shZL5UZWSwsoeyqfTszONTt4k2YS0tuQioMOD180+MbombYVgzDUYHlx+gejYK6rgf08n/2Df99WY0Sxg==
  dependencies:
    tweetnacl "1.0.3"

no-case@^2.2.0:
  version "2.3.2"
  resolved "https://registry.nlark.com/no-case/download/no-case-2.3.2.tgz"
  integrity sha1-YLgTOWvjmz8SiKTB7V0efSi0ZKw=
  dependencies:
    lower-case "^1.1.1"

node-forge@^0.10.0:
  version "0.10.0"
  resolved "https://registry.nlark.com/node-forge/download/node-forge-0.10.0.tgz"
  integrity sha1-Mt6ir7Ppkm8C7lzoeUkCaRpna/M=

node-libs-browser@^2.2.1:
  version "2.2.1"
  resolved "https://registry.nlark.com/node-libs-browser/download/node-libs-browser-2.2.1.tgz"
  integrity sha1-tk9RPRgzhiX5A0bSew0jXmMfZCU=
  dependencies:
    assert "^1.1.1"
    browserify-zlib "^0.2.0"
    buffer "^4.3.0"
    console-browserify "^1.1.0"
    constants-browserify "^1.0.0"
    crypto-browserify "^3.11.0"
    domain-browser "^1.1.1"
    events "^3.0.0"
    https-browserify "^1.0.0"
    os-browserify "^0.3.0"
    path-browserify "0.0.1"
    process "^0.11.10"
    punycode "^1.2.4"
    querystring-es3 "^0.2.0"
    readable-stream "^2.3.3"
    stream-browserify "^2.0.1"
    stream-http "^2.7.2"
    string_decoder "^1.0.0"
    timers-browserify "^2.0.4"
    tty-browserify "0.0.0"
    url "^0.11.0"
    util "^0.11.0"
    vm-browserify "^1.0.1"

node-releases@^2.0.19:
  version "2.0.19"

normalize-package-data@^2.5.0:
  version "2.5.0"
  resolved "https://registry.nlark.com/normalize-package-data/download/normalize-package-data-2.5.0.tgz"
  integrity sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=
  dependencies:
    hosted-git-info "^2.1.4"
    resolve "^1.10.0"
    semver "2 || 3 || 4 || 5"
    validate-npm-package-license "^3.0.1"

normalize-path@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/normalize-path/download/normalize-path-1.0.0.tgz"
  integrity sha1-MtDkcvkf80VwHBWoMRAY07CpA3k=

normalize-path@^2.1.1:
  version "2.1.1"
  resolved "https://registry.nlark.com/normalize-path/download/normalize-path-2.1.1.tgz"
  integrity sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=
  dependencies:
    remove-trailing-separator "^1.0.1"

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/normalize-path/download/normalize-path-3.0.0.tgz"
  integrity sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=

normalize-range@^0.1.2:
  version "0.1.2"
  resolved "https://registry.nlark.com/normalize-range/download/normalize-range-0.1.2.tgz"
  integrity sha1-LRDAa9/TEuqXd2laTShDlFa3WUI=

normalize-url@^3.0.0:
  version "3.3.0"
  resolved "https://registry.nlark.com/normalize-url/download/normalize-url-3.3.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fnormalize-url%2Fdownload%2Fnormalize-url-3.3.0.tgz"
  integrity sha1-suHE3E98bVd0PfczpPWXjRhlBVk=

normalize-url@1.9.1:
  version "1.9.1"
  resolved "https://registry.nlark.com/normalize-url/download/normalize-url-1.9.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fnormalize-url%2Fdownload%2Fnormalize-url-1.9.1.tgz"
  integrity sha1-LMDWazHqIwNkWENuNiDYWVTGbDw=
  dependencies:
    object-assign "^4.0.1"
    prepend-http "^1.0.0"
    query-string "^4.1.0"
    sort-keys "^1.0.0"

npm-run-path@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/npm-run-path/download/npm-run-path-2.0.2.tgz"
  integrity sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8=
  dependencies:
    path-key "^2.0.0"

npm-run-path@^4.0.0:
  version "4.0.1"
  resolved "https://registry.npmmirror.com/npm-run-path/download/npm-run-path-4.0.1.tgz"
  integrity sha1-t+zR5e1T2o43pV4cImnguX7XSOo=
  dependencies:
    path-key "^3.0.0"

nth-check@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/nth-check/download/nth-check-1.0.2.tgz"
  integrity sha1-sr0pXDfj3VijvwcAN2Zjuk2c8Fw=
  dependencies:
    boolbase "~1.0.0"

nth-check@^2.0.1:
  version "2.1.1"
  dependencies:
    boolbase "^1.0.0"

num2fraction@^1.2.2:
  version "1.2.2"
  resolved "https://registry.nlark.com/num2fraction/download/num2fraction-1.2.2.tgz"
  integrity sha1-b2gragJ6Tp3fpFZM0lidHU5mnt4=

number-precision@^1.3.2:
  version "1.6.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/number-precision/-/number-precision-1.6.0.tgz"
  integrity sha512-05OLPgbgmnixJw+VvEh18yNPUo3iyp4BEWJcrLu4X9W05KmMifN7Mu5exYvQXqxxeNWhvIF+j3Rij+HmddM/hQ==

oauth-sign@~0.9.0:
  version "0.9.0"
  resolved "https://registry.nlark.com/oauth-sign/download/oauth-sign-0.9.0.tgz"
  integrity sha1-R6ewFrqmi1+g7PPe4IqFxnmsZFU=

object-assign@^4.0.1, object-assign@^4.1.0, object-assign@>=4.0.1:
  version "4.1.1"
  resolved "https://registry.nlark.com/object-assign/download/object-assign-4.1.1.tgz"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

object-copy@^0.1.0:
  version "0.1.0"
  resolved "https://registry.nlark.com/object-copy/download/object-copy-0.1.0.tgz"
  integrity sha1-fn2Fi3gb18mRpBupde04EnVOmYw=
  dependencies:
    copy-descriptor "^0.1.0"
    define-property "^0.2.5"
    kind-of "^3.0.3"

object-hash@^1.1.4:
  version "1.3.1"
  resolved "https://registry.nlark.com/object-hash/download/object-hash-1.3.1.tgz"
  integrity sha1-/eRSCYqVHLFF8Dm7fUVUSd3BJt8=

object-inspect@^1.13.3:
  version "1.13.3"

object-is@^1.1.5:
  version "1.1.6"
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"

object-keys@^1.1.1:
  version "1.1.1"
  resolved "https://registry.nlark.com/object-keys/download/object-keys-1.1.1.tgz"
  integrity sha1-HEfyct8nfzsdrwYWd9nILiMixg4=

object-visit@^1.0.0:
  version "1.0.1"
  resolved "https://registry.nlark.com/object-visit/download/object-visit-1.0.1.tgz"
  integrity sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=
  dependencies:
    isobject "^3.0.0"

object.assign@^4.1.0, object.assign@^4.1.4, object.assign@^4.1.7:
  version "4.1.7"
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"
    has-symbols "^1.1.0"
    object-keys "^1.1.1"

object.getownpropertydescriptors@^2.0.3, object.getownpropertydescriptors@^2.1.0:
  version "2.1.8"
  dependencies:
    array.prototype.reduce "^1.0.6"
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-object-atoms "^1.0.0"
    gopd "^1.0.1"
    safe-array-concat "^1.1.2"

object.pick@^1.3.0:
  version "1.3.0"
  resolved "https://registry.nlark.com/object.pick/download/object.pick-1.3.0.tgz"
  integrity sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=
  dependencies:
    isobject "^3.0.1"

object.values@^1.1.0:
  version "1.2.1"
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

obuf@^1.0.0, obuf@^1.1.2:
  version "1.1.2"
  resolved "https://registry.nlark.com/obuf/download/obuf-1.1.2.tgz"
  integrity sha1-Cb6jND1BhZ69RGKS0RydTbYZCE4=

on-finished@2.4.1:
  version "2.4.1"
  dependencies:
    ee-first "1.1.1"

on-headers@~1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/on-headers/download/on-headers-1.0.2.tgz"
  integrity sha1-dysK5qqlJcOZ5Imt+tkMQD6zwo8=

once@^1.3.0, once@^1.3.1, once@^1.4.0:
  version "1.4.0"
  resolved "https://registry.nlark.com/once/download/once-1.4.0.tgz"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

onecolor@^3.0.4:
  version "3.1.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/onecolor/-/onecolor-3.1.0.tgz"
  integrity sha512-YZSypViXzu3ul5LMu/m6XjJ9ol8qAy9S2VjHl5E6UlhUH1KGKWabyEJifn0Jjpw23bYDzC2ucKMPGiH5kfwSGQ==

onetime@^2.0.0:
  version "2.0.1"
  resolved "https://registry.nlark.com/onetime/download/onetime-2.0.1.tgz"
  integrity sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ=
  dependencies:
    mimic-fn "^1.0.0"

onetime@^5.1.0:
  version "5.1.2"
  resolved "https://registry.nlark.com/onetime/download/onetime-5.1.2.tgz"
  integrity sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=
  dependencies:
    mimic-fn "^2.1.0"

open@^6.3.0:
  version "6.4.0"
  resolved "https://registry.npmmirror.com/open/download/open-6.4.0.tgz?cache=0&sync_timestamp=1635048715897&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fopen%2Fdownload%2Fopen-6.4.0.tgz"
  integrity sha1-XBPpbQ3IlGhhZPGJZez+iJ7PyKk=
  dependencies:
    is-wsl "^1.1.0"

opener@^1.5.1:
  version "1.5.2"
  resolved "https://registry.nlark.com/opener/download/opener-1.5.2.tgz"
  integrity sha1-XTfh81B3udysQwE3InGv3rKhNZg=

opn@^5.5.0:
  version "5.5.0"
  resolved "https://registry.npmmirror.com/opn/download/opn-5.5.0.tgz"
  integrity sha1-/HFk+rVtI1kExRw7J9pnWMo7m/w=
  dependencies:
    is-wsl "^1.1.0"

optionator@^0.8.3:
  version "0.8.3"
  resolved "https://registry.nlark.com/optionator/download/optionator-0.8.3.tgz"
  integrity sha1-hPodA2/p08fiHZmIS2ARZ+yPtJU=
  dependencies:
    deep-is "~0.1.3"
    fast-levenshtein "~2.0.6"
    levn "~0.3.0"
    prelude-ls "~1.1.2"
    type-check "~0.3.2"
    word-wrap "~1.2.3"

ora@^3.4.0:
  version "3.4.0"
  resolved "https://registry.nlark.com/ora/download/ora-3.4.0.tgz?cache=0&sync_timestamp=1631556658795&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fora%2Fdownload%2Fora-3.4.0.tgz"
  integrity sha1-vwdSSRBZo+8+1MhQl1Md6f280xg=
  dependencies:
    chalk "^2.4.2"
    cli-cursor "^2.1.0"
    cli-spinners "^2.0.0"
    log-symbols "^2.2.0"
    strip-ansi "^5.2.0"
    wcwidth "^1.0.1"

os-browserify@^0.3.0:
  version "0.3.0"
  resolved "https://registry.nlark.com/os-browserify/download/os-browserify-0.3.0.tgz"
  integrity sha1-hUNzx/XCMVkU/Jv8a9gjj92h7Cc=

os-tmpdir@~1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/os-tmpdir/download/os-tmpdir-1.0.2.tgz"
  integrity sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=

own-keys@^1.0.1:
  version "1.0.1"
  dependencies:
    get-intrinsic "^1.2.6"
    object-keys "^1.1.1"
    safe-push-apply "^1.0.0"

p-finally@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/p-finally/download/p-finally-1.0.0.tgz"
  integrity sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4=

p-finally@^2.0.0:
  version "2.0.1"
  resolved "https://registry.nlark.com/p-finally/download/p-finally-2.0.1.tgz"
  integrity sha1-vW/KqcVZoJa2gIBvTWV7Pw8kBWE=

p-limit@^2.0.0, p-limit@^2.2.0, p-limit@^2.2.1:
  version "2.3.0"
  resolved "https://registry.nlark.com/p-limit/download/p-limit-2.3.0.tgz?cache=0&sync_timestamp=1628812766275&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fp-limit%2Fdownload%2Fp-limit-2.3.0.tgz"
  integrity sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=
  dependencies:
    p-try "^2.0.0"

p-locate@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/p-locate/download/p-locate-3.0.0.tgz"
  integrity sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=
  dependencies:
    p-limit "^2.0.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "https://registry.nlark.com/p-locate/download/p-locate-4.1.0.tgz"
  integrity sha1-o0KLtwiLOmApL2aRkni3wpetTwc=
  dependencies:
    p-limit "^2.2.0"

p-map@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/p-map/download/p-map-2.1.0.tgz?cache=0&sync_timestamp=1635931861684&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fp-map%2Fdownload%2Fp-map-2.1.0.tgz"
  integrity sha1-MQko/u+cnsxltosXaTAYpmXOoXU=

p-retry@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmmirror.com/p-retry/download/p-retry-3.0.1.tgz?cache=0&sync_timestamp=1635966813736&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fp-retry%2Fdownload%2Fp-retry-3.0.1.tgz"
  integrity sha1-MWtMiJPiyNwc+okfQGxLQivr8yg=
  dependencies:
    retry "^0.12.0"

p-try@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npmmirror.com/p-try/download/p-try-2.2.0.tgz?cache=0&sync_timestamp=1633364600466&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fp-try%2Fdownload%2Fp-try-2.2.0.tgz"
  integrity sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=

pako@~1.0.5:
  version "1.0.11"
  resolved "https://registry.nlark.com/pako/download/pako-1.0.11.tgz"
  integrity sha1-bJWZ00DVTf05RjgCUqNXBaa5kr8=

parallel-transform@^1.1.0:
  version "1.2.0"
  resolved "https://registry.nlark.com/parallel-transform/download/parallel-transform-1.2.0.tgz"
  integrity sha1-kEnKN9bLIYLDsdLHIL6U0UpYFPw=
  dependencies:
    cyclist "^1.0.1"
    inherits "^2.0.3"
    readable-stream "^2.1.5"

param-case@2.1.x:
  version "2.1.1"
  resolved "https://registry.nlark.com/param-case/download/param-case-2.1.1.tgz"
  integrity sha1-35T9jPZTHs915r75oIWPvHK+Ikc=
  dependencies:
    no-case "^2.2.0"

parent-module@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/parent-module/download/parent-module-1.0.1.tgz?cache=0&sync_timestamp=1633337488003&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fparent-module%2Fdownload%2Fparent-module-1.0.1.tgz"
  integrity sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=
  dependencies:
    callsites "^3.0.0"

parse-asn1@^5.0.0, parse-asn1@^5.1.7:
  version "5.1.7"
  dependencies:
    asn1.js "^4.10.1"
    browserify-aes "^1.2.0"
    evp_bytestokey "^1.0.3"
    hash-base "~3.0"
    pbkdf2 "^3.1.2"
    safe-buffer "^5.2.1"

parse-json@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/parse-json/download/parse-json-4.0.0.tgz?cache=0&sync_timestamp=1637475636838&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fparse-json%2Fdownload%2Fparse-json-4.0.0.tgz"
  integrity sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=
  dependencies:
    error-ex "^1.3.1"
    json-parse-better-errors "^1.0.1"

parse-json@^5.0.0:
  version "5.2.0"
  resolved "https://registry.npmmirror.com/parse-json/download/parse-json-5.2.0.tgz?cache=0&sync_timestamp=1637475636838&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fparse-json%2Fdownload%2Fparse-json-5.2.0.tgz"
  integrity sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

parse5-htmlparser2-tree-adapter@^6.0.0:
  version "6.0.1"
  resolved "https://registry.nlark.com/parse5-htmlparser2-tree-adapter/download/parse5-htmlparser2-tree-adapter-6.0.1.tgz"
  integrity sha1-LN+a2CMyEUA3DU2/XT6Sx8jdxuY=
  dependencies:
    parse5 "^6.0.1"

parse5@^5.1.1:
  version "5.1.1"
  resolved "https://registry.nlark.com/parse5/download/parse5-5.1.1.tgz"
  integrity sha1-9o5OW6GFKsLK3AD0VV//bCq7YXg=

parse5@^6.0.1:
  version "6.0.1"
  resolved "https://registry.nlark.com/parse5/download/parse5-6.0.1.tgz"
  integrity sha1-4aHAhcVps9wIMhGE8Zo5zCf3wws=

parseurl@~1.3.2, parseurl@~1.3.3:
  version "1.3.3"
  resolved "https://registry.nlark.com/parseurl/download/parseurl-1.3.3.tgz"
  integrity sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=

pascalcase@^0.1.1:
  version "0.1.1"
  resolved "https://registry.nlark.com/pascalcase/download/pascalcase-0.1.1.tgz"
  integrity sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ=

path-browserify@0.0.1:
  version "0.0.1"
  resolved "https://registry.nlark.com/path-browserify/download/path-browserify-0.0.1.tgz"
  integrity sha1-5sTd1+06onxoogzE5Q4aTug7vEo=

path-dirname@^1.0.0:
  version "1.0.2"
  resolved "https://registry.nlark.com/path-dirname/download/path-dirname-1.0.2.tgz"
  integrity sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA=

path-exists@^2.0.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/path-exists/download/path-exists-2.1.0.tgz?cache=0&sync_timestamp=1628765027018&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpath-exists%2Fdownload%2Fpath-exists-2.1.0.tgz"
  integrity sha1-D+tsZPD8UY2adU3V77YscCJ2H0s=
  dependencies:
    pinkie-promise "^2.0.0"

path-exists@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/path-exists/download/path-exists-3.0.0.tgz?cache=0&sync_timestamp=1628765027018&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpath-exists%2Fdownload%2Fpath-exists-3.0.0.tgz"
  integrity sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=

path-exists@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/path-exists/download/path-exists-4.0.0.tgz?cache=0&sync_timestamp=1628765027018&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpath-exists%2Fdownload%2Fpath-exists-4.0.0.tgz"
  integrity sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "https://registry.nlark.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-is-inside@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/path-is-inside/download/path-is-inside-1.0.2.tgz"
  integrity sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM=

path-key@^2.0.0, path-key@^2.0.1:
  version "2.0.1"
  resolved "https://registry.nlark.com/path-key/download/path-key-2.0.1.tgz"
  integrity sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"
  resolved "https://registry.nlark.com/path-key/download/path-key-3.1.1.tgz"
  integrity sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=

path-parse@^1.0.7:
  version "1.0.7"
  resolved "https://registry.nlark.com/path-parse/download/path-parse-1.0.7.tgz"
  integrity sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=

path-to-regexp@0.1.12:
  version "0.1.12"

path-type@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/path-type/download/path-type-3.0.0.tgz"
  integrity sha1-zvMdyOCho7sNEFwM2Xzzv0f0428=
  dependencies:
    pify "^3.0.0"

pbkdf2@^3.1.2:
  version "3.1.2"
  dependencies:
    create-hash "^1.1.2"
    create-hmac "^1.1.4"
    ripemd160 "^2.0.1"
    safe-buffer "^5.0.1"
    sha.js "^2.4.8"

performance-now@^2.1.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/performance-now/download/performance-now-2.1.0.tgz"
  integrity sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=

picocolors@^0.2.1:
  version "0.2.1"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/picocolors/-/picocolors-0.2.1.tgz"
  integrity sha512-cMlDqaLEqfSaW8Z7N5Jw+lyIW869EzT73/F5lhtY9cLGoVxSXznfgfXMO0Z5K0o0Q2TkTXq+0KFsdnSe3jDViA==

picocolors@^1.0.0, picocolors@^1.1.0, picocolors@^1.1.1:
  version "1.1.1"

picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.3.1:
  version "2.3.1"

pify@^2.0.0:
  version "2.3.0"
  resolved "https://registry.nlark.com/pify/download/pify-2.3.0.tgz"
  integrity sha1-7RQaasBDqEnqWISY59yosVMw6Qw=

pify@^2.3.0:
  version "2.3.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/pify/-/pify-2.3.0.tgz"
  integrity sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog==

pify@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/pify/download/pify-3.0.0.tgz"
  integrity sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=

pify@^4.0.1:
  version "4.0.1"
  resolved "https://registry.nlark.com/pify/download/pify-4.0.1.tgz"
  integrity sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE=

pinkie-promise@^2.0.0:
  version "2.0.1"
  resolved "https://registry.nlark.com/pinkie-promise/download/pinkie-promise-2.0.1.tgz"
  integrity sha1-ITXW36ejWMBprJsXh3YogihFD/o=
  dependencies:
    pinkie "^2.0.0"

pinkie@^2.0.0:
  version "2.0.4"
  resolved "https://registry.nlark.com/pinkie/download/pinkie-2.0.4.tgz"
  integrity sha1-clVrgM+g1IqXToDnckjoDtT3+HA=

pixrem@^4.0.0:
  version "4.0.1"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/pixrem/-/pixrem-4.0.1.tgz"
  integrity sha512-sFMGjkE42jNwAMBvBdWSx9f6MGZfufkfChzLxow7JmKY6XHWpNqLIwTQABcJOek0LCdtNf22cdL5fYzKLELmCA==
  dependencies:
    browserslist "^2.0.0"
    postcss "^6.0.0"
    reduce-css-calc "^1.2.7"

pkg-dir@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/pkg-dir/download/pkg-dir-1.0.0.tgz?cache=0&sync_timestamp=1633498116014&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpkg-dir%2Fdownload%2Fpkg-dir-1.0.0.tgz"
  integrity sha1-ektQio1bstYp1EcFb/TpyTFM89Q=
  dependencies:
    find-up "^1.0.0"

pkg-dir@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/pkg-dir/download/pkg-dir-3.0.0.tgz?cache=0&sync_timestamp=1633498116014&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpkg-dir%2Fdownload%2Fpkg-dir-3.0.0.tgz"
  integrity sha1-J0kCDyOe2ZCIGx9xIQ1R62UjvqM=
  dependencies:
    find-up "^3.0.0"

pkg-dir@^4.1.0:
  version "4.2.0"
  resolved "https://registry.npmmirror.com/pkg-dir/download/pkg-dir-4.2.0.tgz?cache=0&sync_timestamp=1633498116014&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpkg-dir%2Fdownload%2Fpkg-dir-4.2.0.tgz"
  integrity sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=
  dependencies:
    find-up "^4.0.0"

pleeease-filters@^4.0.0:
  version "4.0.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/pleeease-filters/-/pleeease-filters-4.0.0.tgz"
  integrity sha512-EtyjMK41s2+q/eM6wED/9U5bWzowfP/ImQb9AxKzt/+Xlbb5CxQ7dAToEoSQuxyidmkZzHAl483m2sBm2XvfFw==
  dependencies:
    onecolor "^3.0.4"
    postcss "^6.0.1"

pngjs@^5.0.0:
  version "5.0.0"
  resolved "https://registry.nlark.com/pngjs/download/pngjs-5.0.0.tgz"
  integrity sha1-553SshV2f9nARWHAEjbflgvOf7s=

pnp-webpack-plugin@^1.6.4:
  version "1.7.0"
  resolved "https://registry.nlark.com/pnp-webpack-plugin/download/pnp-webpack-plugin-1.7.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpnp-webpack-plugin%2Fdownload%2Fpnp-webpack-plugin-1.7.0.tgz"
  integrity sha1-ZXQThPbYBW824iVajWf/wghm9ck=
  dependencies:
    ts-pnp "^1.1.6"

portfinder@^1.0.26:
  version "1.0.32"
  dependencies:
    async "^2.6.4"
    debug "^3.2.7"
    mkdirp "^0.5.6"

posix-character-classes@^0.1.0:
  version "0.1.1"
  resolved "https://registry.nlark.com/posix-character-classes/download/posix-character-classes-0.1.1.tgz"
  integrity sha1-AerA/jta9xoqbAL+q7jB/vfgDqs=

possible-typed-array-names@^1.0.0:
  version "1.0.0"

postcss-apply@^0.8.0:
  version "0.8.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss-apply/-/postcss-apply-0.8.0.tgz"
  integrity sha512-J8HDo5oAW4HSZ8rsSQrfUC4kRurHZFzNC0gGIrJEuRu0imobXMtPhAXQN7d44eDXKWshABjtFxowbQi8OIznwg==
  dependencies:
    babel-runtime "^6.23.0"
    balanced-match "^0.4.2"
    postcss "^6.0.0"

postcss-aspect-ratio-mini@^1.0.1:
  version "1.1.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss-aspect-ratio-mini/-/postcss-aspect-ratio-mini-1.1.0.tgz"
  integrity sha512-ddD7k55Rez1La9tmyzZOlS6bAXF9EWpyXQz3eXScJfvcVmPsx33rOwVpN2d5ZRVOjAmkWgyOMV8sZ46rnIyiDg==
  dependencies:
    number-precision "^1.3.2"
    postcss "^7.0.6"

postcss-attribute-case-insensitive@^2.0.0:
  version "2.0.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss-attribute-case-insensitive/-/postcss-attribute-case-insensitive-2.0.0.tgz"
  integrity sha512-J8sdzNF8glKA0ZpNLOftTaNu12a48wJ97sVDTYGRR6gX8cSoRE4tXoVgYRemFz3TvSurbmVuMQUmqUclA0hL/Q==
  dependencies:
    postcss "^6.0.0"
    postcss-selector-parser "^2.2.3"

postcss-calc@^6.0.0:
  version "6.0.2"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss-calc/-/postcss-calc-6.0.2.tgz"
  integrity sha512-fiznXjEN5T42Qm7qqMCVJXS3roaj9r4xsSi+meaBVe7CJBl8t/QLOXu02Z2E6oWAMWIvCuF6JrvzFekmVEbOKA==
  dependencies:
    css-unit-converter "^1.1.1"
    postcss "^7.0.2"
    postcss-selector-parser "^2.2.2"
    reduce-css-calc "^2.0.0"

postcss-calc@^7.0.1:
  version "7.0.5"
  resolved "https://registry.nlark.com/postcss-calc/download/postcss-calc-7.0.5.tgz"
  integrity sha1-+KbpnxLmGcLrwjz2xIb9wVhgkz4=
  dependencies:
    postcss "^7.0.27"
    postcss-selector-parser "^6.0.2"
    postcss-value-parser "^4.0.2"

postcss-color-function@^4.0.0:
  version "4.1.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss-color-function/-/postcss-color-function-4.1.0.tgz"
  integrity sha512-2/fuv6mP5Lt03XbRpVfMdGC8lRP1sykme+H1bR4ARyOmSMB8LPSjcL6EAI1iX6dqUF+jNEvKIVVXhan1w/oFDQ==
  dependencies:
    css-color-function "~1.3.3"
    postcss "^6.0.23"
    postcss-message-helpers "^2.0.0"
    postcss-value-parser "^3.3.1"

postcss-color-gray@^4.0.0:
  version "4.1.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss-color-gray/-/postcss-color-gray-4.1.0.tgz"
  integrity sha512-L4iLKQLdqChz6ZOgGb6dRxkBNw78JFYcJmBz1orHpZoeLtuhDDGegRtX9gSyfoCIM7rWZ3VNOyiqqvk83BEN+w==
  dependencies:
    color "^2.0.1"
    postcss "^6.0.14"
    postcss-message-helpers "^2.0.0"
    reduce-function-call "^1.0.2"

postcss-color-hex-alpha@^3.0.0:
  version "3.0.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss-color-hex-alpha/-/postcss-color-hex-alpha-3.0.0.tgz"
  integrity sha512-Tpg2yEBcdE0e7jJSyRaLinX8xqf4geXIBClhbSA3hhnpXELJcqfvoRD8V9RHV6oYyoR/vyS3dCg1EuCEw6CvhQ==
  dependencies:
    color "^1.0.3"
    postcss "^6.0.1"
    postcss-message-helpers "^2.0.0"

postcss-color-hsl@^2.0.0:
  version "2.0.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss-color-hsl/-/postcss-color-hsl-2.0.0.tgz"
  integrity sha512-4DNpOj3NWejHtjV4mLxf+rmE1KA+IKDJH8QSThgJOrjGFuiqOPxkFSZX1RQJ+XQISZD3MW/JDaZoNnmxS9pSBQ==
  dependencies:
    postcss "^6.0.1"
    postcss-value-parser "^3.3.0"
    units-css "^0.4.0"

postcss-color-hwb@^3.0.0:
  version "3.0.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss-color-hwb/-/postcss-color-hwb-3.0.0.tgz"
  integrity sha512-53CnpaKZKKiuQ2KvVStY4uVYnp2UCSUCDGYnW2sbe4oJBq3/H4eCcO4My2cMv3l8czsh3yBRLNA9Ls8IwqARhA==
  dependencies:
    color "^1.0.3"
    postcss "^6.0.1"
    postcss-message-helpers "^2.0.0"
    reduce-function-call "^1.0.2"

postcss-color-rebeccapurple@^3.0.0:
  version "3.1.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss-color-rebeccapurple/-/postcss-color-rebeccapurple-3.1.0.tgz"
  integrity sha512-212hJUk9uSsbwO5ECqVjmh/iLsmiVL1xy9ce9TVf+X3cK/ZlUIlaMdoxje/YpsL9cmUH3I7io+/G2LyWx5rg1g==
  dependencies:
    postcss "^6.0.22"
    postcss-values-parser "^1.5.0"

postcss-color-rgb@^2.0.0:
  version "2.0.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss-color-rgb/-/postcss-color-rgb-2.0.0.tgz"
  integrity sha512-oGgwvqUHEz4CYmbwUwQ4LwIr4Wqw9p1r+6fJFQqdZPp+ulXFsmJ1cAqJ+V7x5VoNMOuAi1e3UPyJ01JpUtX+Vw==
  dependencies:
    postcss "^6.0.1"
    postcss-value-parser "^3.3.0"

postcss-color-rgba-fallback@^3.0.0:
  version "3.0.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss-color-rgba-fallback/-/postcss-color-rgba-fallback-3.0.0.tgz"
  integrity sha512-RBCHHCQ0sVJH+y31jiVSLBM/b3vvfiU8g9tyAglpF7NOuYzcw41Tu+iGEm8B2zVEAYYgEhWCASNY/j4Wh3vi4w==
  dependencies:
    postcss "^6.0.6"
    postcss-value-parser "^3.3.0"
    rgb-hex "^2.1.0"

postcss-colormin@^4.0.3:
  version "4.0.3"
  resolved "https://registry.npmmirror.com/postcss-colormin/download/postcss-colormin-4.0.3.tgz"
  integrity sha1-rgYLzpPteUrHEmTwgTLVUJVr04E=
  dependencies:
    browserslist "^4.0.0"
    color "^3.0.0"
    has "^1.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-convert-values@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmmirror.com/postcss-convert-values/download/postcss-convert-values-4.0.1.tgz?cache=0&sync_timestamp=1635857664165&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpostcss-convert-values%2Fdownload%2Fpostcss-convert-values-4.0.1.tgz"
  integrity sha1-yjgT7U2g+BL51DcDWE5Enr4Ymn8=
  dependencies:
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-cssnext@^3.1.0:
  version "3.1.1"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss-cssnext/-/postcss-cssnext-3.1.1.tgz"
  integrity sha512-u9e0CYE7zudnr/LK+FL02cwli8cQEwwmzzN3JVeOEZiDs0q8IDfGdI6C9FCkHtIKEEjY3AATW3dqCCvqDK+Sfg==
  dependencies:
    autoprefixer "^7.1.1"
    caniuse-api "^2.0.0"
    chalk "^2.0.1"
    pixrem "^4.0.0"
    pleeease-filters "^4.0.0"
    postcss "^6.0.5"
    postcss-apply "^0.8.0"
    postcss-attribute-case-insensitive "^2.0.0"
    postcss-calc "^6.0.0"
    postcss-color-function "^4.0.0"
    postcss-color-gray "^4.0.0"
    postcss-color-hex-alpha "^3.0.0"
    postcss-color-hsl "^2.0.0"
    postcss-color-hwb "^3.0.0"
    postcss-color-rebeccapurple "^3.0.0"
    postcss-color-rgb "^2.0.0"
    postcss-color-rgba-fallback "^3.0.0"
    postcss-custom-media "^6.0.0"
    postcss-custom-properties "^6.1.0"
    postcss-custom-selectors "^4.0.1"
    postcss-font-family-system-ui "^3.0.0"
    postcss-font-variant "^3.0.0"
    postcss-image-set-polyfill "^0.3.5"
    postcss-initial "^2.0.0"
    postcss-media-minmax "^3.0.0"
    postcss-nesting "^4.0.1"
    postcss-pseudo-class-any-link "^4.0.0"
    postcss-pseudoelements "^5.0.0"
    postcss-replace-overflow-wrap "^2.0.0"
    postcss-selector-matches "^3.0.1"
    postcss-selector-not "^3.0.1"

postcss-custom-media@^6.0.0:
  version "6.0.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss-custom-media/-/postcss-custom-media-6.0.0.tgz"
  integrity sha512-MLjf2Yghub+USZpLWCB11hLrEEaCWM4lYf4UR9ui3iPCQFdywvEaY5yt4PnOClGrACGaHTNKPF9koiZLdJOmYw==
  dependencies:
    postcss "^6.0.1"

postcss-custom-properties@^6.1.0:
  version "6.3.1"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss-custom-properties/-/postcss-custom-properties-6.3.1.tgz"
  integrity sha512-zoiwn4sCiUFbr4KcgcNZLFkR6gVQom647L+z1p/KBVHZ1OYwT87apnS42atJtx6XlX2yI7N5fjXbFixShQO2QQ==
  dependencies:
    balanced-match "^1.0.0"
    postcss "^6.0.18"

postcss-custom-selectors@^4.0.1:
  version "4.0.1"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss-custom-selectors/-/postcss-custom-selectors-4.0.1.tgz"
  integrity sha512-mXhdsA16oq2xbXhIJs6LscTq8peUrvAD4w6I8+BhfIK5ZQH2sVjSM1xYJdadFBfR8MISg4466I+V860Hi68PfQ==
  dependencies:
    postcss "^6.0.1"
    postcss-selector-matches "^3.0.0"

postcss-discard-comments@^4.0.2:
  version "4.0.2"
  resolved "https://registry.nlark.com/postcss-discard-comments/download/postcss-discard-comments-4.0.2.tgz"
  integrity sha1-H7q9LCRr/2qq15l7KwkY9NevQDM=
  dependencies:
    postcss "^7.0.0"

postcss-discard-duplicates@^4.0.2:
  version "4.0.2"
  resolved "https://registry.nlark.com/postcss-discard-duplicates/download/postcss-discard-duplicates-4.0.2.tgz"
  integrity sha1-P+EzzTyCKC5VD8myORdqkge3hOs=
  dependencies:
    postcss "^7.0.0"

postcss-discard-empty@^4.0.1:
  version "4.0.1"
  resolved "https://registry.nlark.com/postcss-discard-empty/download/postcss-discard-empty-4.0.1.tgz"
  integrity sha1-yMlR6fc+2UKAGUWERKAq2Qu592U=
  dependencies:
    postcss "^7.0.0"

postcss-discard-overridden@^4.0.1:
  version "4.0.1"
  resolved "https://registry.nlark.com/postcss-discard-overridden/download/postcss-discard-overridden-4.0.1.tgz"
  integrity sha1-ZSrvipZybwKfXj4AFG7npOdV/1c=
  dependencies:
    postcss "^7.0.0"

postcss-font-family-system-ui@^3.0.0:
  version "3.0.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss-font-family-system-ui/-/postcss-font-family-system-ui-3.0.0.tgz"
  integrity sha512-58G/hTxMSSKlIRpcPUjlyo6hV2MEzvcVO2m4L/T7Bb2fJTG4DYYfQjQeRvuimKQh1V1sOzCIz99g+H2aFNtlQw==
  dependencies:
    postcss "^6.0"

postcss-font-variant@^3.0.0:
  version "3.0.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss-font-variant/-/postcss-font-variant-3.0.0.tgz"
  integrity sha512-zZ89AX55J3Bfn6xQBOwNV75I+4Mqj7fhILlwu2KT+pDC3Xsu5vI0YQil3Q1imJQGhN5XnN/DMFQp+UvV91IXig==
  dependencies:
    postcss "^6.0.1"

postcss-image-set-polyfill@^0.3.5:
  version "0.3.5"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss-image-set-polyfill/-/postcss-image-set-polyfill-0.3.5.tgz"
  integrity sha512-4Df/0UNixbXqkw8k+j4CWbicfeZe8/pzkePgBRwWk+iA8wpz1r9ac0YZoXAFAtGISGrw+2d+Kzm/RItZIlu43Q==
  dependencies:
    postcss "^6.0.1"
    postcss-media-query-parser "^0.2.3"

postcss-import@^12.0.1:
  version "12.0.1"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss-import/-/postcss-import-12.0.1.tgz"
  integrity sha512-3Gti33dmCjyKBgimqGxL3vcV8w9+bsHwO5UrBawp796+jdardbcFl4RP5w/76BwNL7aGzpKstIfF9I+kdE8pTw==
  dependencies:
    postcss "^7.0.1"
    postcss-value-parser "^3.2.3"
    read-cache "^1.0.0"
    resolve "^1.1.7"

postcss-initial@^2.0.0:
  version "2.0.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss-initial/-/postcss-initial-2.0.0.tgz"
  integrity sha512-XiU1taLGTuHjr/9QMrGMA3Y6zIIxlUudHL7XwLDepPPNSp8pJTYkv5VCQC8IynXGsXa3FKzNJ37eZdfsI71NjA==
  dependencies:
    lodash.template "^4.2.4"
    postcss "^6.0.1"

postcss-load-config@^2.0.0:
  version "2.1.2"
  resolved "https://registry.npmmirror.com/postcss-load-config/download/postcss-load-config-2.1.2.tgz"
  integrity sha1-xepQTyxK7zPHNZo03jVzdyrXUCo=
  dependencies:
    cosmiconfig "^5.0.0"
    import-cwd "^2.0.0"

postcss-loader@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/postcss-loader/download/postcss-loader-3.0.0.tgz?cache=0&sync_timestamp=1634140473743&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpostcss-loader%2Fdownload%2Fpostcss-loader-3.0.0.tgz"
  integrity sha1-a5eUPkfHLYRfqeA/Jzdz1OjdbC0=
  dependencies:
    loader-utils "^1.1.0"
    postcss "^7.0.0"
    postcss-load-config "^2.0.0"
    schema-utils "^1.0.0"

postcss-media-minmax@^3.0.0:
  version "3.0.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss-media-minmax/-/postcss-media-minmax-3.0.0.tgz"
  integrity sha512-lYwGbUhk6+8NSMJ4P2T4+Zi0tbHUDFgdHXC4zTe/P7zkIk+lRuaEpZcZFzlL3dxWarnwc6ImpDV4MGBV4uDDXg==
  dependencies:
    postcss "^6.0.1"

postcss-media-query-parser@^0.2.3:
  version "0.2.3"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss-media-query-parser/-/postcss-media-query-parser-0.2.3.tgz"
  integrity sha512-3sOlxmbKcSHMjlUXQZKQ06jOswE7oVkXPxmZdoB1r5l0q6gTFTQSHxNxOrCccElbW7dxNytifNEo8qidX2Vsig==

postcss-merge-longhand@^4.0.11:
  version "4.0.11"
  resolved "https://registry.npmmirror.com/postcss-merge-longhand/download/postcss-merge-longhand-4.0.11.tgz?cache=0&sync_timestamp=1637084982494&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpostcss-merge-longhand%2Fdownload%2Fpostcss-merge-longhand-4.0.11.tgz"
  integrity sha1-YvSaE+Sg7gTnuY9CuxYGLKJUniQ=
  dependencies:
    css-color-names "0.0.4"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"
    stylehacks "^4.0.0"

postcss-merge-rules@^4.0.3:
  version "4.0.3"
  resolved "https://registry.npmmirror.com/postcss-merge-rules/download/postcss-merge-rules-4.0.3.tgz?cache=0&sync_timestamp=1637085799347&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpostcss-merge-rules%2Fdownload%2Fpostcss-merge-rules-4.0.3.tgz"
  integrity sha1-NivqT/Wh+Y5AdacTxsslrv75plA=
  dependencies:
    browserslist "^4.0.0"
    caniuse-api "^3.0.0"
    cssnano-util-same-parent "^4.0.0"
    postcss "^7.0.0"
    postcss-selector-parser "^3.0.0"
    vendors "^1.0.0"

postcss-message-helpers@^2.0.0:
  version "2.0.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss-message-helpers/-/postcss-message-helpers-2.0.0.tgz"
  integrity sha512-tPLZzVAiIJp46TBbpXtrUAKqedXSyW5xDEo1sikrfEfnTs+49SBZR/xDdqCiJvSSbtr615xDsaMF3RrxS2jZlA==

postcss-minify-font-values@^4.0.2:
  version "4.0.2"
  resolved "https://registry.nlark.com/postcss-minify-font-values/download/postcss-minify-font-values-4.0.2.tgz"
  integrity sha1-zUw0TM5HQ0P6xdgiBqssvLiv1aY=
  dependencies:
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-minify-gradients@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmmirror.com/postcss-minify-gradients/download/postcss-minify-gradients-4.0.2.tgz?cache=0&sync_timestamp=1635856887200&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpostcss-minify-gradients%2Fdownload%2Fpostcss-minify-gradients-4.0.2.tgz"
  integrity sha1-k7KcL/UJnFNe7NpWxKpuZlpmNHE=
  dependencies:
    cssnano-util-get-arguments "^4.0.0"
    is-color-stop "^1.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-minify-params@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmmirror.com/postcss-minify-params/download/postcss-minify-params-4.0.2.tgz?cache=0&sync_timestamp=1637084983019&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpostcss-minify-params%2Fdownload%2Fpostcss-minify-params-4.0.2.tgz"
  integrity sha1-a5zvAwwR41Jh+V9hjJADbWgNuHQ=
  dependencies:
    alphanum-sort "^1.0.0"
    browserslist "^4.0.0"
    cssnano-util-get-arguments "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"
    uniqs "^2.0.0"

postcss-minify-selectors@^4.0.2:
  version "4.0.2"
  resolved "https://registry.nlark.com/postcss-minify-selectors/download/postcss-minify-selectors-4.0.2.tgz"
  integrity sha1-4uXrQL/uUA0M2SQ1APX46kJi+9g=
  dependencies:
    alphanum-sort "^1.0.0"
    has "^1.0.0"
    postcss "^7.0.0"
    postcss-selector-parser "^3.0.0"

postcss-modules-extract-imports@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/postcss-modules-extract-imports/download/postcss-modules-extract-imports-2.0.0.tgz"
  integrity sha1-gYcZoa4doyX5gyRGsBE27rSTzX4=
  dependencies:
    postcss "^7.0.5"

postcss-modules-local-by-default@^3.0.2:
  version "3.0.3"
  resolved "https://registry.nlark.com/postcss-modules-local-by-default/download/postcss-modules-local-by-default-3.0.3.tgz"
  integrity sha1-uxTgzHgnnVBNvcv9fgyiiZP/u7A=
  dependencies:
    icss-utils "^4.1.1"
    postcss "^7.0.32"
    postcss-selector-parser "^6.0.2"
    postcss-value-parser "^4.1.0"

postcss-modules-scope@^2.2.0:
  version "2.2.0"
  resolved "https://registry.nlark.com/postcss-modules-scope/download/postcss-modules-scope-2.2.0.tgz"
  integrity sha1-OFyuATzHdD9afXYC0Qc6iequYu4=
  dependencies:
    postcss "^7.0.6"
    postcss-selector-parser "^6.0.0"

postcss-modules-values@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/postcss-modules-values/download/postcss-modules-values-3.0.0.tgz"
  integrity sha1-W1AA1uuuKbQlUwG0o6VFdEI+fxA=
  dependencies:
    icss-utils "^4.0.0"
    postcss "^7.0.6"

postcss-nesting@^4.0.1:
  version "4.2.1"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss-nesting/-/postcss-nesting-4.2.1.tgz"
  integrity sha512-IkyWXICwagCnlaviRexi7qOdwPw3+xVVjgFfGsxmztvRVaNxAlrypOIKqDE5mxY+BVxnId1rnUKBRQoNE2VDaA==
  dependencies:
    postcss "^6.0.11"

postcss-normalize-charset@^4.0.1:
  version "4.0.1"
  resolved "https://registry.nlark.com/postcss-normalize-charset/download/postcss-normalize-charset-4.0.1.tgz"
  integrity sha1-izWt067oOhNrBHHg1ZvlilAoXdQ=
  dependencies:
    postcss "^7.0.0"

postcss-normalize-display-values@^4.0.2:
  version "4.0.2"
  resolved "https://registry.nlark.com/postcss-normalize-display-values/download/postcss-normalize-display-values-4.0.2.tgz"
  integrity sha1-Db4EpM6QY9RmftK+R2u4MMglk1o=
  dependencies:
    cssnano-util-get-match "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-positions@^4.0.2:
  version "4.0.2"
  resolved "https://registry.nlark.com/postcss-normalize-positions/download/postcss-normalize-positions-4.0.2.tgz"
  integrity sha1-BfdX+E8mBDc3g2ipH4ky1LECkX8=
  dependencies:
    cssnano-util-get-arguments "^4.0.0"
    has "^1.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-repeat-style@^4.0.2:
  version "4.0.2"
  resolved "https://registry.nlark.com/postcss-normalize-repeat-style/download/postcss-normalize-repeat-style-4.0.2.tgz"
  integrity sha1-xOu8KJ85kaAo1EdRy90RkYsXkQw=
  dependencies:
    cssnano-util-get-arguments "^4.0.0"
    cssnano-util-get-match "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-string@^4.0.2:
  version "4.0.2"
  resolved "https://registry.nlark.com/postcss-normalize-string/download/postcss-normalize-string-4.0.2.tgz"
  integrity sha1-zUTECrB6DHo23F6Zqs4eyk7CaQw=
  dependencies:
    has "^1.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-timing-functions@^4.0.2:
  version "4.0.2"
  resolved "https://registry.nlark.com/postcss-normalize-timing-functions/download/postcss-normalize-timing-functions-4.0.2.tgz"
  integrity sha1-jgCcoqOUnNr4rSPmtquZy159KNk=
  dependencies:
    cssnano-util-get-match "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-unicode@^4.0.1:
  version "4.0.1"
  resolved "https://registry.nlark.com/postcss-normalize-unicode/download/postcss-normalize-unicode-4.0.1.tgz"
  integrity sha1-hBvUj9zzAZrUuqdJOj02O1KuHPs=
  dependencies:
    browserslist "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-url@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmmirror.com/postcss-normalize-url/download/postcss-normalize-url-4.0.1.tgz?cache=0&sync_timestamp=1637084983535&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpostcss-normalize-url%2Fdownload%2Fpostcss-normalize-url-4.0.1.tgz"
  integrity sha1-EOQ3+GvHx+WPe5ZS7YeNqqlfquE=
  dependencies:
    is-absolute-url "^2.0.0"
    normalize-url "^3.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-whitespace@^4.0.2:
  version "4.0.2"
  resolved "https://registry.nlark.com/postcss-normalize-whitespace/download/postcss-normalize-whitespace-4.0.2.tgz"
  integrity sha1-vx1AcP5Pzqh9E0joJdjMDF+qfYI=
  dependencies:
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-ordered-values@^4.1.2:
  version "4.1.2"
  resolved "https://registry.nlark.com/postcss-ordered-values/download/postcss-ordered-values-4.1.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-ordered-values%2Fdownload%2Fpostcss-ordered-values-4.1.2.tgz"
  integrity sha1-DPdcgg7H1cTSgBiVWeC1ceusDu4=
  dependencies:
    cssnano-util-get-arguments "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-pseudo-class-any-link@^4.0.0:
  version "4.0.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss-pseudo-class-any-link/-/postcss-pseudo-class-any-link-4.0.0.tgz"
  integrity sha512-xindgr9qqu6HDnHKk7lJSIII6nvNmGKxIb8BOUUfteLctichwaNEGtwWvq66Sc/EuTu/bK/+fAyLRwWkPx/nFA==
  dependencies:
    postcss "^6.0.1"
    postcss-selector-parser "^2.2.3"

postcss-pseudoelements@^5.0.0:
  version "5.0.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss-pseudoelements/-/postcss-pseudoelements-5.0.0.tgz"
  integrity sha512-XHKyn37k6nm8R9C1g+PXisjp5Y8ISOpeuQe9IVCAe5I2+t2WVBtF3YhS1TsRz3asMkW1zg+tIe6g7eX/sT/QOg==
  dependencies:
    postcss "^6.0.0"

postcss-px-to-viewport@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/postcss-px-to-viewport/-/postcss-px-to-viewport-1.1.1.tgz"
  integrity sha512-2x9oGnBms+e0cYtBJOZdlwrFg/mLR4P1g2IFu7jYKvnqnH/HLhoKyareW2Q/x4sg0BgklHlP1qeWo2oCyPm8FQ==
  dependencies:
    object-assign ">=4.0.1"
    postcss ">=5.0.2"

postcss-reduce-initial@^4.0.3:
  version "4.0.3"
  resolved "https://registry.nlark.com/postcss-reduce-initial/download/postcss-reduce-initial-4.0.3.tgz"
  integrity sha1-f9QuvqXpyBRgljniwuhK4nC6SN8=
  dependencies:
    browserslist "^4.0.0"
    caniuse-api "^3.0.0"
    has "^1.0.0"
    postcss "^7.0.0"

postcss-reduce-transforms@^4.0.2:
  version "4.0.2"
  resolved "https://registry.nlark.com/postcss-reduce-transforms/download/postcss-reduce-transforms-4.0.2.tgz"
  integrity sha1-F++kBerMbge+NBSlyi0QdGgdTik=
  dependencies:
    cssnano-util-get-match "^4.0.0"
    has "^1.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-replace-overflow-wrap@^2.0.0:
  version "2.0.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss-replace-overflow-wrap/-/postcss-replace-overflow-wrap-2.0.0.tgz"
  integrity sha512-JNYt+DiUQiCtsy17cqcHQlCJ5huBPDq8F49hlnTd747GCMvaUEq4ouI0QR0E38zdKs1ptnzhoGYwm6mvpbeD5w==
  dependencies:
    postcss "^6.0.1"

postcss-selector-matches@^3.0.0, postcss-selector-matches@^3.0.1:
  version "3.0.1"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss-selector-matches/-/postcss-selector-matches-3.0.1.tgz"
  integrity sha512-R8p740Ufx5ho+agdlZ389OOOrylLJGMWwnpbm9LBoV/L4aL12MRR+zuGWJo03XYUAeXtAggSEnn6wngoSOqweQ==
  dependencies:
    balanced-match "^0.4.2"
    postcss "^6.0.1"

postcss-selector-not@^3.0.1:
  version "3.0.1"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss-selector-not/-/postcss-selector-not-3.0.1.tgz"
  integrity sha512-Tt/45Rvaj3I/nCHHMG3L9ioiJjI2YEk8lsvQbko+N6wrOBfLOx0yk51DMT3Ynhd7GzQwXinzeO3gYnwtUe83pA==
  dependencies:
    balanced-match "^0.4.2"
    postcss "^6.0.1"

postcss-selector-parser@^2.2.2:
  version "2.2.3"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss-selector-parser/-/postcss-selector-parser-2.2.3.tgz"
  integrity sha512-3pqyakeGhrO0BQ5+/tGTfvi5IAUAhHRayGK8WFSu06aEv2BmHoXw/Mhb+w7VY5HERIuC+QoUI7wgrCcq2hqCVA==
  dependencies:
    flatten "^1.0.2"
    indexes-of "^1.0.1"
    uniq "^1.0.1"

postcss-selector-parser@^2.2.3:
  version "2.2.3"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss-selector-parser/-/postcss-selector-parser-2.2.3.tgz"
  integrity sha512-3pqyakeGhrO0BQ5+/tGTfvi5IAUAhHRayGK8WFSu06aEv2BmHoXw/Mhb+w7VY5HERIuC+QoUI7wgrCcq2hqCVA==
  dependencies:
    flatten "^1.0.2"
    indexes-of "^1.0.1"
    uniq "^1.0.1"

postcss-selector-parser@^3.0.0:
  version "3.1.2"
  resolved "https://registry.nlark.com/postcss-selector-parser/download/postcss-selector-parser-3.1.2.tgz"
  integrity sha1-sxD1xMD9r3b5SQK7qjDbaqhPUnA=
  dependencies:
    dot-prop "^5.2.0"
    indexes-of "^1.0.1"
    uniq "^1.0.1"

postcss-selector-parser@^6.0.0, postcss-selector-parser@^6.0.2:
  version "6.1.2"
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss-svgo@^4.0.3:
  version "4.0.3"
  resolved "https://registry.npmmirror.com/postcss-svgo/download/postcss-svgo-4.0.3.tgz"
  integrity sha1-NDos26yVBdQWJD1Jb3JPOIlMlB4=
  dependencies:
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"
    svgo "^1.0.0"

postcss-unique-selectors@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmmirror.com/postcss-unique-selectors/download/postcss-unique-selectors-4.0.1.tgz?cache=0&sync_timestamp=1637084982907&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpostcss-unique-selectors%2Fdownload%2Fpostcss-unique-selectors-4.0.1.tgz"
  integrity sha1-lEaRHzKJv9ZMbWgPBzwDsfnuS6w=
  dependencies:
    alphanum-sort "^1.0.0"
    postcss "^7.0.0"
    uniqs "^2.0.0"

postcss-url@^8.0.0:
  version "8.0.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss-url/-/postcss-url-8.0.0.tgz"
  integrity sha512-E2cbOQ5aii2zNHh8F6fk1cxls7QVFZjLPSrqvmiza8OuXLzIpErij8BDS5Y3STPfJgpIMNCPEr8JlKQWEoozUw==
  dependencies:
    mime "^2.3.1"
    minimatch "^3.0.4"
    mkdirp "^0.5.0"
    postcss "^7.0.2"
    xxhashjs "^0.2.1"

postcss-value-parser@^3.0.0:
  version "3.3.1"
  resolved "https://registry.nlark.com/postcss-value-parser/download/postcss-value-parser-3.3.1.tgz"
  integrity sha1-n/giVH4okyE88cMO+lGsX9G6goE=

postcss-value-parser@^3.2.3, postcss-value-parser@^3.3.0:
  version "3.3.1"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss-value-parser/-/postcss-value-parser-3.3.1.tgz"
  integrity sha512-pISE66AbVkp4fDQ7VHBwRNXzAAKJjw4Vw7nWI/+Q3vuly7SNfgYXvm6i5IgFylHGK5sP/xHAbB7N49OS4gWNyQ==

postcss-value-parser@^3.3.1:
  version "3.3.1"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss-value-parser/-/postcss-value-parser-3.3.1.tgz"
  integrity sha512-pISE66AbVkp4fDQ7VHBwRNXzAAKJjw4Vw7nWI/+Q3vuly7SNfgYXvm6i5IgFylHGK5sP/xHAbB7N49OS4gWNyQ==

postcss-value-parser@^4.0.2, postcss-value-parser@^4.1.0:
  version "4.2.0"
  resolved "https://registry.npmmirror.com/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz"
  integrity sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==

postcss-values-parser@^1.5.0:
  version "1.5.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss-values-parser/-/postcss-values-parser-1.5.0.tgz"
  integrity sha512-3M3p+2gMp0AH3da530TlX8kiO1nxdTnc3C6vr8dMxRLIlh8UYkz0/wcwptSXjhtx2Fr0TySI7a+BHDQ8NL7LaQ==
  dependencies:
    flatten "^1.0.2"
    indexes-of "^1.0.1"
    uniq "^1.0.1"

postcss-viewport-units@^0.1.6:
  version "0.1.6"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss-viewport-units/-/postcss-viewport-units-0.1.6.tgz"
  integrity sha512-uddXE6gtmuPwMICbZGSA4HijOGrUkUNacvCktXPB9lsM/bSq8aBkWUxfuPGDFLhdJYZTtCANzeQ/65fAfq+6Tw==
  dependencies:
    postcss "^5.2.8"

postcss@^5.2.8:
  version "5.2.18"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss/-/postcss-5.2.18.tgz"
  integrity sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg==
  dependencies:
    chalk "^1.1.3"
    js-base64 "^2.1.9"
    source-map "^0.5.6"
    supports-color "^3.2.3"

postcss@^6.0:
  version "6.0.23"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss/-/postcss-6.0.23.tgz"
  integrity sha512-soOk1h6J3VMTZtVeVpv15/Hpdl2cBLX3CAw4TAbkpTJiNPk9YP/zWcD1ND+xEtvyuuvKzbxliTOIyvkSeSJ6ag==
  dependencies:
    chalk "^2.4.1"
    source-map "^0.6.1"
    supports-color "^5.4.0"

postcss@^6.0.0:
  version "6.0.23"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss/-/postcss-6.0.23.tgz"
  integrity sha512-soOk1h6J3VMTZtVeVpv15/Hpdl2cBLX3CAw4TAbkpTJiNPk9YP/zWcD1ND+xEtvyuuvKzbxliTOIyvkSeSJ6ag==
  dependencies:
    chalk "^2.4.1"
    source-map "^0.6.1"
    supports-color "^5.4.0"

postcss@^6.0.1:
  version "6.0.23"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss/-/postcss-6.0.23.tgz"
  integrity sha512-soOk1h6J3VMTZtVeVpv15/Hpdl2cBLX3CAw4TAbkpTJiNPk9YP/zWcD1ND+xEtvyuuvKzbxliTOIyvkSeSJ6ag==
  dependencies:
    chalk "^2.4.1"
    source-map "^0.6.1"
    supports-color "^5.4.0"

postcss@^6.0.11:
  version "6.0.23"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss/-/postcss-6.0.23.tgz"
  integrity sha512-soOk1h6J3VMTZtVeVpv15/Hpdl2cBLX3CAw4TAbkpTJiNPk9YP/zWcD1ND+xEtvyuuvKzbxliTOIyvkSeSJ6ag==
  dependencies:
    chalk "^2.4.1"
    source-map "^0.6.1"
    supports-color "^5.4.0"

postcss@^6.0.14:
  version "6.0.23"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss/-/postcss-6.0.23.tgz"
  integrity sha512-soOk1h6J3VMTZtVeVpv15/Hpdl2cBLX3CAw4TAbkpTJiNPk9YP/zWcD1ND+xEtvyuuvKzbxliTOIyvkSeSJ6ag==
  dependencies:
    chalk "^2.4.1"
    source-map "^0.6.1"
    supports-color "^5.4.0"

postcss@^6.0.17, postcss@^6.0.5:
  version "6.0.23"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss/-/postcss-6.0.23.tgz"
  integrity sha512-soOk1h6J3VMTZtVeVpv15/Hpdl2cBLX3CAw4TAbkpTJiNPk9YP/zWcD1ND+xEtvyuuvKzbxliTOIyvkSeSJ6ag==
  dependencies:
    chalk "^2.4.1"
    source-map "^0.6.1"
    supports-color "^5.4.0"

postcss@^6.0.18:
  version "6.0.23"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss/-/postcss-6.0.23.tgz"
  integrity sha512-soOk1h6J3VMTZtVeVpv15/Hpdl2cBLX3CAw4TAbkpTJiNPk9YP/zWcD1ND+xEtvyuuvKzbxliTOIyvkSeSJ6ag==
  dependencies:
    chalk "^2.4.1"
    source-map "^0.6.1"
    supports-color "^5.4.0"

postcss@^6.0.22:
  version "6.0.23"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss/-/postcss-6.0.23.tgz"
  integrity sha512-soOk1h6J3VMTZtVeVpv15/Hpdl2cBLX3CAw4TAbkpTJiNPk9YP/zWcD1ND+xEtvyuuvKzbxliTOIyvkSeSJ6ag==
  dependencies:
    chalk "^2.4.1"
    source-map "^0.6.1"
    supports-color "^5.4.0"

postcss@^6.0.23:
  version "6.0.23"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss/-/postcss-6.0.23.tgz"
  integrity sha512-soOk1h6J3VMTZtVeVpv15/Hpdl2cBLX3CAw4TAbkpTJiNPk9YP/zWcD1ND+xEtvyuuvKzbxliTOIyvkSeSJ6ag==
  dependencies:
    chalk "^2.4.1"
    source-map "^0.6.1"
    supports-color "^5.4.0"

postcss@^6.0.6:
  version "6.0.23"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss/-/postcss-6.0.23.tgz"
  integrity sha512-soOk1h6J3VMTZtVeVpv15/Hpdl2cBLX3CAw4TAbkpTJiNPk9YP/zWcD1ND+xEtvyuuvKzbxliTOIyvkSeSJ6ag==
  dependencies:
    chalk "^2.4.1"
    source-map "^0.6.1"
    supports-color "^5.4.0"

postcss@^7.0.0, postcss@^7.0.32:
  version "7.0.39"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss/-/postcss-7.0.39.tgz"
  integrity sha512-yioayjNbHn6z1/Bywyb2Y4s3yvDAeXGOyxqD+LnVOinq6Mdmd++SW2wUNVzavyyHxd6+DxzWGIuosg6P1Rj8uA==
  dependencies:
    picocolors "^0.2.1"
    source-map "^0.6.1"

postcss@^7.0.1:
  version "7.0.39"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss/-/postcss-7.0.39.tgz"
  integrity sha512-yioayjNbHn6z1/Bywyb2Y4s3yvDAeXGOyxqD+LnVOinq6Mdmd++SW2wUNVzavyyHxd6+DxzWGIuosg6P1Rj8uA==
  dependencies:
    picocolors "^0.2.1"
    source-map "^0.6.1"

postcss@^7.0.14:
  version "7.0.39"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss/-/postcss-7.0.39.tgz"
  integrity sha512-yioayjNbHn6z1/Bywyb2Y4s3yvDAeXGOyxqD+LnVOinq6Mdmd++SW2wUNVzavyyHxd6+DxzWGIuosg6P1Rj8uA==
  dependencies:
    picocolors "^0.2.1"
    source-map "^0.6.1"

postcss@^7.0.2:
  version "7.0.39"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss/-/postcss-7.0.39.tgz"
  integrity sha512-yioayjNbHn6z1/Bywyb2Y4s3yvDAeXGOyxqD+LnVOinq6Mdmd++SW2wUNVzavyyHxd6+DxzWGIuosg6P1Rj8uA==
  dependencies:
    picocolors "^0.2.1"
    source-map "^0.6.1"

postcss@^7.0.27:
  version "7.0.39"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss/-/postcss-7.0.39.tgz"
  integrity sha512-yioayjNbHn6z1/Bywyb2Y4s3yvDAeXGOyxqD+LnVOinq6Mdmd++SW2wUNVzavyyHxd6+DxzWGIuosg6P1Rj8uA==
  dependencies:
    picocolors "^0.2.1"
    source-map "^0.6.1"

postcss@^7.0.32:
  version "7.0.39"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss/-/postcss-7.0.39.tgz"
  integrity sha512-yioayjNbHn6z1/Bywyb2Y4s3yvDAeXGOyxqD+LnVOinq6Mdmd++SW2wUNVzavyyHxd6+DxzWGIuosg6P1Rj8uA==
  dependencies:
    picocolors "^0.2.1"
    source-map "^0.6.1"

postcss@^7.0.36:
  version "7.0.39"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss/-/postcss-7.0.39.tgz"
  integrity sha512-yioayjNbHn6z1/Bywyb2Y4s3yvDAeXGOyxqD+LnVOinq6Mdmd++SW2wUNVzavyyHxd6+DxzWGIuosg6P1Rj8uA==
  dependencies:
    picocolors "^0.2.1"
    source-map "^0.6.1"

postcss@^7.0.5:
  version "7.0.39"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss/-/postcss-7.0.39.tgz"
  integrity sha512-yioayjNbHn6z1/Bywyb2Y4s3yvDAeXGOyxqD+LnVOinq6Mdmd++SW2wUNVzavyyHxd6+DxzWGIuosg6P1Rj8uA==
  dependencies:
    picocolors "^0.2.1"
    source-map "^0.6.1"

postcss@^7.0.6:
  version "7.0.39"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss/-/postcss-7.0.39.tgz"
  integrity sha512-yioayjNbHn6z1/Bywyb2Y4s3yvDAeXGOyxqD+LnVOinq6Mdmd++SW2wUNVzavyyHxd6+DxzWGIuosg6P1Rj8uA==
  dependencies:
    picocolors "^0.2.1"
    source-map "^0.6.1"

postcss@^8.4.48, postcss@^8.4.5, postcss@>=5.0.2:
  version "8.4.49"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/postcss/-/postcss-8.4.49.tgz"
  integrity sha512-OCVPnIObs4N29kxTjzLfUryOkvZEq+pf8jTF0lg8E7uETuWHA+v7j3c/xJmiqpX450191LlmZfUKkXxkTry7nA==
  dependencies:
    nanoid "^3.3.7"
    picocolors "^1.1.1"
    source-map-js "^1.2.1"

prelude-ls@~1.1.2:
  version "1.1.2"
  resolved "https://registry.nlark.com/prelude-ls/download/prelude-ls-1.1.2.tgz"
  integrity sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ=

prepend-http@^1.0.0:
  version "1.0.4"
  resolved "https://registry.nlark.com/prepend-http/download/prepend-http-1.0.4.tgz?cache=0&sync_timestamp=1628547381568&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fprepend-http%2Fdownload%2Fprepend-http-1.0.4.tgz"
  integrity sha1-1PRWKwzjaW5BrFLQ4ALlemNdxtw=

prettier-linter-helpers@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/prettier-linter-helpers/download/prettier-linter-helpers-1.0.0.tgz"
  integrity sha1-0j1B/hN1ZG3i0BBNNFSjAIgCz3s=
  dependencies:
    fast-diff "^1.1.2"

"prettier@^1.18.2 || ^2.0.0", prettier@^2.2.1, "prettier@>= 1.13.0", prettier@>=1.13.0:
  version "2.8.8"

pretty-bytes@^5.1.0:
  version "5.6.0"
  resolved "https://registry.nlark.com/pretty-bytes/download/pretty-bytes-5.6.0.tgz?cache=0&sync_timestamp=1622687247373&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpretty-bytes%2Fdownload%2Fpretty-bytes-5.6.0.tgz"
  integrity sha1-NWJW9kOAR3PIL2RyP+eMksYr6us=

pretty-error@^2.0.2:
  version "2.1.2"
  resolved "https://registry.npmmirror.com/pretty-error/download/pretty-error-2.1.2.tgz?cache=0&sync_timestamp=1635212589197&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpretty-error%2Fdownload%2Fpretty-error-2.1.2.tgz"
  integrity sha1-von4LYGxyG7I/fvDhQRYgnJ/k7Y=
  dependencies:
    lodash "^4.17.20"
    renderkid "^2.0.4"

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "https://registry.nlark.com/process-nextick-args/download/process-nextick-args-2.0.1.tgz"
  integrity sha1-eCDZsWEgzFXKmud5JoCufbptf+I=

process@^0.11.10:
  version "0.11.10"
  resolved "https://registry.nlark.com/process/download/process-0.11.10.tgz"
  integrity sha1-czIwDoQBYb2j5podHZGn1LwW8YI=

progress@^2.0.0:
  version "2.0.3"
  resolved "https://registry.nlark.com/progress/download/progress-2.0.3.tgz"
  integrity sha1-foz42PW48jnBvGi+tOt4Vn1XLvg=

promise-inflight@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/promise-inflight/download/promise-inflight-1.0.1.tgz"
  integrity sha1-mEcocL8igTL8vdhoEputEsPAKeM=

proxy-addr@~2.0.7:
  version "2.0.7"
  dependencies:
    forwarded "0.2.0"
    ipaddr.js "1.9.1"

prr@~1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/prr/download/prr-1.0.1.tgz"
  integrity sha1-0/wRS6BplaRexok/SEzrHXj19HY=

pseudomap@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/pseudomap/download/pseudomap-1.0.2.tgz"
  integrity sha1-8FKijacOYYkX7wqKw0wa5aaChrM=

psl@^1.1.28:
  version "1.15.0"
  dependencies:
    punycode "^2.3.1"

public-encrypt@^4.0.3:
  version "4.0.3"
  dependencies:
    bn.js "^4.1.0"
    browserify-rsa "^4.0.0"
    create-hash "^1.1.0"
    parse-asn1 "^5.0.0"
    randombytes "^2.0.1"
    safe-buffer "^5.1.2"

pump@^2.0.0:
  version "2.0.1"
  resolved "https://registry.nlark.com/pump/download/pump-2.0.1.tgz"
  integrity sha1-Ejma3W5M91Jtlzy8i1zi4pCLOQk=
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

pump@^3.0.0:
  version "3.0.2"
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

pumpify@^1.3.3:
  version "1.5.1"
  resolved "https://registry.nlark.com/pumpify/download/pumpify-1.5.1.tgz"
  integrity sha1-NlE74karJ1cLGjdKXOJ4v9dDcM4=
  dependencies:
    duplexify "^3.6.0"
    inherits "^2.0.3"
    pump "^2.0.0"

punycode@^1.2.4:
  version "1.4.1"
  resolved "https://registry.nlark.com/punycode/download/punycode-1.4.1.tgz"
  integrity sha1-wNWmOycYgArY4esPpSachN1BhF4=

punycode@^1.4.1:
  version "1.4.1"

punycode@^2.1.0, punycode@^2.1.1, punycode@^2.3.1:
  version "2.3.1"

q@^1.1.2:
  version "1.5.1"
  resolved "https://registry.nlark.com/q/download/q-1.5.1.tgz"
  integrity sha1-fjL3W0E4EpHQRhHxvxQQmsAGUdc=

qrcode@^1.5.0:
  version "1.5.4"
  dependencies:
    dijkstrajs "^1.0.1"
    pngjs "^5.0.0"
    yargs "^15.3.1"

qs@^6.12.3:
  version "6.13.1"
  dependencies:
    side-channel "^1.0.6"

qs@~6.5.2:
  version "6.5.3"

qs@6.13.0:
  version "6.13.0"
  dependencies:
    side-channel "^1.0.6"

query-string@^4.1.0:
  version "4.3.4"
  resolved "https://registry.nlark.com/query-string/download/query-string-4.3.4.tgz?cache=0&sync_timestamp=1624297110017&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fquery-string%2Fdownload%2Fquery-string-4.3.4.tgz"
  integrity sha1-u7aTucqRXCMlFbIosaArYJBD2+s=
  dependencies:
    object-assign "^4.1.0"
    strict-uri-encode "^1.0.0"

query-string@^7.0.1:
  version "7.1.3"
  dependencies:
    decode-uri-component "^0.2.2"
    filter-obj "^1.1.0"
    split-on-first "^1.0.0"
    strict-uri-encode "^2.0.0"

querystring-es3@^0.2.0:
  version "0.2.1"
  resolved "https://registry.nlark.com/querystring-es3/download/querystring-es3-0.2.1.tgz"
  integrity sha1-nsYfeQSYdXB9aUFFlv2Qek1xHnM=

querystringify@^2.1.1:
  version "2.2.0"
  resolved "https://registry.nlark.com/querystringify/download/querystringify-2.2.0.tgz"
  integrity sha1-M0WUG0FTy50ILY7uTNogFqmu9/Y=

randombytes@^2.0.0, randombytes@^2.0.1, randombytes@^2.0.5, randombytes@^2.1.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/randombytes/download/randombytes-2.1.0.tgz"
  integrity sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo=
  dependencies:
    safe-buffer "^5.1.0"

randomfill@^1.0.4:
  version "1.0.4"
  dependencies:
    randombytes "^2.0.5"
    safe-buffer "^5.1.0"

range-parser@^1.2.1, range-parser@~1.2.1:
  version "1.2.1"
  resolved "https://registry.nlark.com/range-parser/download/range-parser-1.2.1.tgz"
  integrity sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE=

raw-body@2.5.2:
  version "2.5.2"
  dependencies:
    bytes "3.1.2"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

read-cache@^1.0.0:
  version "1.0.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/read-cache/-/read-cache-1.0.0.tgz"
  integrity sha512-Owdv/Ft7IjOgm/i0xvNDZ1LrRANRfew4b2prF3OWMQLxLfu3bS8FVhCsrSCMK4lR56Y9ya+AThoTpDCTxCmpRA==
  dependencies:
    pify "^2.3.0"

read-pkg@^5.1.1:
  version "5.2.0"
  resolved "https://registry.nlark.com/read-pkg/download/read-pkg-5.2.0.tgz?cache=0&sync_timestamp=1628984780649&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fread-pkg%2Fdownload%2Fread-pkg-5.2.0.tgz"
  integrity sha1-e/KVQ4yloz5WzTDgU7NO5yUMk8w=
  dependencies:
    "@types/normalize-package-data" "^2.4.0"
    normalize-package-data "^2.5.0"
    parse-json "^5.0.0"
    type-fest "^0.6.0"

readable-stream@^2.0.0, readable-stream@^2.0.1, readable-stream@^2.0.2, readable-stream@^2.1.5, readable-stream@^2.2.2, readable-stream@^2.3.3, readable-stream@^2.3.6, readable-stream@^2.3.8, readable-stream@~2.3.6, "readable-stream@1 || 2":
  version "2.3.8"
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@^3.0.6:
  version "3.6.2"
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readable-stream@^3.6.0:
  version "3.6.2"
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readdirp@^2.2.1:
  version "2.2.1"
  resolved "https://registry.nlark.com/readdirp/download/readdirp-2.2.1.tgz"
  integrity sha1-DodiKjMlqjPokihcr4tOhGUppSU=
  dependencies:
    graceful-fs "^4.1.11"
    micromatch "^3.1.10"
    readable-stream "^2.0.2"

readdirp@~3.6.0:
  version "3.6.0"
  resolved "https://registry.nlark.com/readdirp/download/readdirp-3.6.0.tgz"
  integrity sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=
  dependencies:
    picomatch "^2.2.1"

rechoir@^0.6.2:
  version "0.6.2"
  resolved "https://registry.npmmirror.com/rechoir/-/rechoir-0.6.2.tgz"
  integrity sha512-HFM8rkZ+i3zrV+4LQjwQ0W+ez98pApMGM3HUrN04j3CqzPOzl9nmP15Y8YXNm8QHGv/eacOVEjqhmWpkRV0NAw==
  dependencies:
    resolve "^1.1.6"

reduce-css-calc@^1.2.7:
  version "1.3.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/reduce-css-calc/-/reduce-css-calc-1.3.0.tgz"
  integrity sha512-0dVfwYVOlf/LBA2ec4OwQ6p3X9mYxn/wOl2xTcLwjnPYrkgEfPx3VI4eGCH3rQLlPISG5v9I9bkZosKsNRTRKA==
  dependencies:
    balanced-match "^0.4.2"
    math-expression-evaluator "^1.2.14"
    reduce-function-call "^1.0.1"

reduce-css-calc@^2.0.0:
  version "2.1.8"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/reduce-css-calc/-/reduce-css-calc-2.1.8.tgz"
  integrity sha512-8liAVezDmUcH+tdzoEGrhfbGcP7nOV4NkGE3a74+qqvE7nt9i4sKLGBuZNOnpI4WiGksiNPklZxva80061QiPg==
  dependencies:
    css-unit-converter "^1.1.1"
    postcss-value-parser "^3.3.0"

reduce-function-call@^1.0.1, reduce-function-call@^1.0.2:
  version "1.0.3"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/reduce-function-call/-/reduce-function-call-1.0.3.tgz"
  integrity sha512-Hl/tuV2VDgWgCSEeWMLwxLZqX7OK59eU1guxXsRKTAyeYimivsKdtcV4fu3r710tpG5GmDKDhQ0HSZLExnNmyQ==
  dependencies:
    balanced-match "^1.0.0"

reflect.getprototypeof@^1.0.6, reflect.getprototypeof@^1.0.9:
  version "1.0.9"
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    dunder-proto "^1.0.1"
    es-abstract "^1.23.6"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"
    gopd "^1.2.0"
    which-builtin-type "^1.2.1"

regenerate-unicode-properties@^10.2.0:
  version "10.2.0"
  dependencies:
    regenerate "^1.4.2"

regenerate@^1.4.2:
  version "1.4.2"
  resolved "https://registry.nlark.com/regenerate/download/regenerate-1.4.2.tgz"
  integrity sha1-uTRtiCfo9aMve6KWN9OYtpAUhIo=

regenerator-runtime@^0.11.0:
  version "0.11.1"
  resolved "https://registry.nlark.com/regenerator-runtime/download/regenerator-runtime-0.11.1.tgz"
  integrity sha1-vgWtf5v30i4Fb5cmzuUBf78Z4uk=

regenerator-runtime@^0.14.0:
  version "0.14.1"

regenerator-transform@^0.15.2:
  version "0.15.2"
  dependencies:
    "@babel/runtime" "^7.8.4"

regex-not@^1.0.0, regex-not@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/regex-not/download/regex-not-1.0.2.tgz"
  integrity sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw=
  dependencies:
    extend-shallow "^3.0.2"
    safe-regex "^1.1.0"

regexp.prototype.flags@^1.5.1, regexp.prototype.flags@^1.5.3:
  version "1.5.3"
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-errors "^1.3.0"
    set-function-name "^2.0.2"

regexpp@^2.0.1:
  version "2.0.1"
  resolved "https://registry.nlark.com/regexpp/download/regexpp-2.0.1.tgz"
  integrity sha1-jRnTHPYySCtYkEn4KB+T28uk0H8=

regexpu-core@^6.2.0:
  version "6.2.0"
  dependencies:
    regenerate "^1.4.2"
    regenerate-unicode-properties "^10.2.0"
    regjsgen "^0.8.0"
    regjsparser "^0.12.0"
    unicode-match-property-ecmascript "^2.0.0"
    unicode-match-property-value-ecmascript "^2.1.0"

register-service-worker@^1.7.1:
  version "1.7.2"
  resolved "https://registry.nlark.com/register-service-worker/download/register-service-worker-1.7.2.tgz"
  integrity sha1-ZRaYPh73kKmMQiWvEha8gJQaS9I=

regjsgen@^0.8.0:
  version "0.8.0"

regjsparser@^0.12.0:
  version "0.12.0"
  dependencies:
    jsesc "~3.0.2"

relateurl@0.2.x:
  version "0.2.7"
  resolved "https://registry.nlark.com/relateurl/download/relateurl-0.2.7.tgz"
  integrity sha1-VNvzd+UUQKypCkzSdGANP/LYiKk=

remove-trailing-separator@^1.0.1:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/remove-trailing-separator/download/remove-trailing-separator-1.1.0.tgz"
  integrity sha1-wkvOKig62tW8P1jg1IJJuSN52O8=

renderkid@^2.0.4:
  version "2.0.7"
  resolved "https://registry.npmmirror.com/renderkid/download/renderkid-2.0.7.tgz?cache=0&sync_timestamp=1635212582997&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Frenderkid%2Fdownload%2Frenderkid-2.0.7.tgz"
  integrity sha1-Rk8namvc7mBvShWZP5sp/HTKhgk=
  dependencies:
    css-select "^4.1.3"
    dom-converter "^0.2.0"
    htmlparser2 "^6.1.0"
    lodash "^4.17.21"
    strip-ansi "^3.0.1"

repeat-element@^1.1.2:
  version "1.1.4"
  resolved "https://registry.nlark.com/repeat-element/download/repeat-element-1.1.4.tgz"
  integrity sha1-vmgVIIR6tYx1aKx1+/rSjtQtOek=

repeat-string@^1.6.1:
  version "1.6.1"
  resolved "https://registry.nlark.com/repeat-string/download/repeat-string-1.6.1.tgz"
  integrity sha1-jcrkcOHIirwtYA//Sndihtp15jc=

request@^2.88.2:
  version "2.88.2"
  resolved "https://registry.npmmirror.com/request/download/request-2.88.2.tgz"
  integrity sha1-1zyRhzHLWofaBH4gcjQUb2ZNErM=
  dependencies:
    aws-sign2 "~0.7.0"
    aws4 "^1.8.0"
    caseless "~0.12.0"
    combined-stream "~1.0.6"
    extend "~3.0.2"
    forever-agent "~0.6.1"
    form-data "~2.3.2"
    har-validator "~5.1.3"
    http-signature "~1.2.0"
    is-typedarray "~1.0.0"
    isstream "~0.1.2"
    json-stringify-safe "~5.0.1"
    mime-types "~2.1.19"
    oauth-sign "~0.9.0"
    performance-now "^2.1.0"
    qs "~6.5.2"
    safe-buffer "^5.1.2"
    tough-cookie "~2.5.0"
    tunnel-agent "^0.6.0"
    uuid "^3.3.2"

require-directory@^2.1.1:
  version "2.1.1"
  resolved "https://registry.nlark.com/require-directory/download/require-directory-2.1.1.tgz"
  integrity sha1-jGStX9MNqxyXbiNE/+f3kqam30I=

require-from-string@^2.0.2:
  version "2.0.2"

require-main-filename@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/require-main-filename/download/require-main-filename-2.0.0.tgz"
  integrity sha1-0LMp7MfMD2Fkn2IhW+aa9UqomJs=

requires-port@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/requires-port/download/requires-port-1.0.0.tgz"
  integrity sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=

resolve-cwd@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/resolve-cwd/download/resolve-cwd-2.0.0.tgz"
  integrity sha1-AKn3OHVW4nA46uIyyqNypqWbZlo=
  dependencies:
    resolve-from "^3.0.0"

resolve-from@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/resolve-from/download/resolve-from-3.0.0.tgz"
  integrity sha1-six699nWiBvItuZTM17rywoYh0g=

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/resolve-from/download/resolve-from-4.0.0.tgz"
  integrity sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=

resolve-url@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npmmirror.com/resolve-url/download/resolve-url-0.2.1.tgz"
  integrity sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=

resolve@^1.1.6, resolve@^1.1.7, resolve@^1.10.0, resolve@^1.12.0, resolve@^1.14.2:
  version "1.22.10"
  dependencies:
    is-core-module "^2.16.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

restore-cursor@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/restore-cursor/download/restore-cursor-2.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Frestore-cursor%2Fdownload%2Frestore-cursor-2.0.0.tgz"
  integrity sha1-n37ih/gv0ybU/RYpI9YhKe7g368=
  dependencies:
    onetime "^2.0.0"
    signal-exit "^3.0.2"

restore-cursor@^3.1.0:
  version "3.1.0"
  resolved "https://registry.nlark.com/restore-cursor/download/restore-cursor-3.1.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Frestore-cursor%2Fdownload%2Frestore-cursor-3.1.0.tgz"
  integrity sha1-OfZ8VLOnpYzqUjbZXPADQjljH34=
  dependencies:
    onetime "^5.1.0"
    signal-exit "^3.0.2"

ret@~0.1.10:
  version "0.1.15"
  resolved "https://registry.nlark.com/ret/download/ret-0.1.15.tgz"
  integrity sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w=

retry@^0.12.0:
  version "0.12.0"
  resolved "https://registry.nlark.com/retry/download/retry-0.12.0.tgz"
  integrity sha1-G0KmJmoh8HQh0bC1S33BZ7AcATs=

rgb-hex@^2.1.0:
  version "2.1.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/rgb-hex/-/rgb-hex-2.1.0.tgz"
  integrity sha512-1hDa60MqECZiEGsa9TtcOE9VbV6fhZSMQARy7U+a2HkhIJyoEBhcG4v/qYAJYzV3Bbj+j52sBoAIKKF5EPuQZw==

rgb-regex@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/rgb-regex/download/rgb-regex-1.0.1.tgz"
  integrity sha1-wODWiC3w4jviVKR16O3UGRX+rrE=

rgb@~0.1.0:
  version "0.1.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/rgb/-/rgb-0.1.0.tgz"
  integrity sha512-F49dXX73a92N09uQkfCp2QjwXpmJcn9/i9PvjmwsSIXUGqRLCf/yx5Q9gRxuLQTq248kakqQuc8GX/U/CxSqlA==

rgba-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/rgba-regex/download/rgba-regex-1.0.0.tgz"
  integrity sha1-QzdOLiyglosO8VI0YLfXMP8i7rM=

rimraf@^2.5.4, rimraf@^2.6.1, rimraf@^2.6.3:
  version "2.7.1"
  resolved "https://registry.nlark.com/rimraf/download/rimraf-2.7.1.tgz"
  integrity sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w=
  dependencies:
    glob "^7.1.3"

rimraf@2.6.3:
  version "2.6.3"
  resolved "https://registry.nlark.com/rimraf/download/rimraf-2.6.3.tgz"
  integrity sha1-stEE/g2Psnz54KHNqCYt04M8bKs=
  dependencies:
    glob "^7.1.3"

ripemd160@^2.0.0, ripemd160@^2.0.1:
  version "2.0.2"
  resolved "https://registry.nlark.com/ripemd160/download/ripemd160-2.0.2.tgz"
  integrity sha1-ocGm9iR1FXe6XQeRTLyShQWFiQw=
  dependencies:
    hash-base "^3.0.0"
    inherits "^2.0.1"

run-async@^2.4.0:
  version "2.4.1"
  resolved "https://registry.nlark.com/run-async/download/run-async-2.4.1.tgz"
  integrity sha1-hEDsz5nqPnC9QJ1JqriOEMGJpFU=

run-queue@^1.0.0, run-queue@^1.0.3:
  version "1.0.3"
  resolved "https://registry.nlark.com/run-queue/download/run-queue-1.0.3.tgz"
  integrity sha1-6Eg5bwV9Ij8kOGkkYY4laUFh7Ec=
  dependencies:
    aproba "^1.1.1"

rxjs@^6.6.0:
  version "6.6.7"
  resolved "https://registry.npmmirror.com/rxjs/download/rxjs-6.6.7.tgz"
  integrity sha1-kKwBisq/SRv2UEQjXVhjxNq4BMk=
  dependencies:
    tslib "^1.9.0"

safe-array-concat@^1.1.2, safe-array-concat@^1.1.3:
  version "1.1.3"
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.2"
    get-intrinsic "^1.2.6"
    has-symbols "^1.1.0"
    isarray "^2.0.5"

safe-buffer@^5.0.1, safe-buffer@^5.1.0, safe-buffer@^5.1.1, safe-buffer@^5.1.2, safe-buffer@^5.2.0, safe-buffer@^5.2.1, safe-buffer@>=5.1.0, safe-buffer@~5.2.0, safe-buffer@5.2.1:
  version "5.2.1"
  resolved "https://registry.nlark.com/safe-buffer/download/safe-buffer-5.2.1.tgz"
  integrity sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=

safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "https://registry.nlark.com/safe-buffer/download/safe-buffer-5.1.2.tgz"
  integrity sha1-mR7GnSluAxN0fVm9/St0XDX4go0=

safe-push-apply@^1.0.0:
  version "1.0.0"
  dependencies:
    es-errors "^1.3.0"
    isarray "^2.0.5"

safe-regex-test@^1.1.0:
  version "1.1.0"
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    is-regex "^1.2.1"

safe-regex@^1.1.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/safe-regex/download/safe-regex-1.1.0.tgz"
  integrity sha1-QKNmnzsHfR6UPURinhV91IAjvy4=
  dependencies:
    ret "~0.1.10"

safer-buffer@^2.0.2, safer-buffer@^2.1.0, safer-buffer@^2.1.2, "safer-buffer@>= 2.1.2 < 3", safer-buffer@~2.1.0:
  version "2.1.2"
  resolved "https://registry.nlark.com/safer-buffer/download/safer-buffer-2.1.2.tgz"
  integrity sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=

sax@~1.2.4:
  version "1.2.4"
  resolved "https://registry.nlark.com/sax/download/sax-1.2.4.tgz"
  integrity sha1-KBYjTiN4vdxOU1T6tcqold9xANk=

schema-utils@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/schema-utils/download/schema-utils-1.0.0.tgz?cache=0&sync_timestamp=1637075888461&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fschema-utils%2Fdownload%2Fschema-utils-1.0.0.tgz"
  integrity sha1-C3mpMgTXtgDUsoUNH2bCo0lRx3A=
  dependencies:
    ajv "^6.1.0"
    ajv-errors "^1.0.0"
    ajv-keywords "^3.1.0"

schema-utils@^2.0.0:
  version "2.7.1"
  resolved "https://registry.npmmirror.com/schema-utils/download/schema-utils-2.7.1.tgz?cache=0&sync_timestamp=1637075888461&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fschema-utils%2Fdownload%2Fschema-utils-2.7.1.tgz"
  integrity sha1-HKTzLRskxZDCA7jnpQvw6kzTlNc=
  dependencies:
    "@types/json-schema" "^7.0.5"
    ajv "^6.12.4"
    ajv-keywords "^3.5.2"

schema-utils@^2.5.0:
  version "2.7.1"
  resolved "https://registry.npmmirror.com/schema-utils/download/schema-utils-2.7.1.tgz?cache=0&sync_timestamp=1637075888461&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fschema-utils%2Fdownload%2Fschema-utils-2.7.1.tgz"
  integrity sha1-HKTzLRskxZDCA7jnpQvw6kzTlNc=
  dependencies:
    "@types/json-schema" "^7.0.5"
    ajv "^6.12.4"
    ajv-keywords "^3.5.2"

schema-utils@^2.6.5:
  version "2.7.1"
  resolved "https://registry.npmmirror.com/schema-utils/download/schema-utils-2.7.1.tgz?cache=0&sync_timestamp=1637075888461&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fschema-utils%2Fdownload%2Fschema-utils-2.7.1.tgz"
  integrity sha1-HKTzLRskxZDCA7jnpQvw6kzTlNc=
  dependencies:
    "@types/json-schema" "^7.0.5"
    ajv "^6.12.4"
    ajv-keywords "^3.5.2"

schema-utils@^2.7.0:
  version "2.7.1"
  resolved "https://registry.npmmirror.com/schema-utils/download/schema-utils-2.7.1.tgz?cache=0&sync_timestamp=1637075888461&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fschema-utils%2Fdownload%2Fschema-utils-2.7.1.tgz"
  integrity sha1-HKTzLRskxZDCA7jnpQvw6kzTlNc=
  dependencies:
    "@types/json-schema" "^7.0.5"
    ajv "^6.12.4"
    ajv-keywords "^3.5.2"

schema-utils@^3.2.0:
  version "3.3.0"
  dependencies:
    "@types/json-schema" "^7.0.8"
    ajv "^6.12.5"
    ajv-keywords "^3.5.2"

schema-utils@^4.3.0:
  version "4.3.0"
  dependencies:
    "@types/json-schema" "^7.0.9"
    ajv "^8.9.0"
    ajv-formats "^2.1.1"
    ajv-keywords "^5.1.0"

select-hose@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/select-hose/download/select-hose-2.0.0.tgz"
  integrity sha1-Yl2GWPhlr0Psliv8N2o3NZpJlMo=

selfsigned@^1.10.8:
  version "1.10.14"
  dependencies:
    node-forge "^0.10.0"

semver@^5.5.0:
  version "5.7.2"

semver@^5.6.0:
  version "5.7.2"

semver@^6.0.0, semver@^6.1.0, semver@^6.1.2, semver@^6.3.0, semver@^6.3.1:
  version "6.3.1"

"semver@2 || 3 || 4 || 5":
  version "5.7.2"

send@0.19.0:
  version "0.19.0"
  dependencies:
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    fresh "0.5.2"
    http-errors "2.0.0"
    mime "1.6.0"
    ms "2.1.3"
    on-finished "2.4.1"
    range-parser "~1.2.1"
    statuses "2.0.1"

serialize-javascript@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/serialize-javascript/download/serialize-javascript-4.0.0.tgz"
  integrity sha1-tSXhI4SJpez8Qq+sw/6Z5mb0sao=
  dependencies:
    randombytes "^2.1.0"

serialize-javascript@^6.0.2:
  version "6.0.2"
  dependencies:
    randombytes "^2.1.0"

serve-index@^1.9.1:
  version "1.9.1"
  resolved "https://registry.nlark.com/serve-index/download/serve-index-1.9.1.tgz"
  integrity sha1-03aNabHn2C5c4FD/9bRTvqEqkjk=
  dependencies:
    accepts "~1.3.4"
    batch "0.6.1"
    debug "2.6.9"
    escape-html "~1.0.3"
    http-errors "~1.6.2"
    mime-types "~2.1.17"
    parseurl "~1.3.2"

serve-static@1.16.2:
  version "1.16.2"
  dependencies:
    encodeurl "~2.0.0"
    escape-html "~1.0.3"
    parseurl "~1.3.3"
    send "0.19.0"

set-blocking@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/set-blocking/download/set-blocking-2.0.0.tgz"
  integrity sha1-BF+XgtARrppoA93TgrJDkrPYkPc=

set-function-length@^1.2.2:
  version "1.2.2"
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.2"

set-function-name@^2.0.2:
  version "2.0.2"
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    functions-have-names "^1.2.3"
    has-property-descriptors "^1.0.2"

set-proto@^1.0.0:
  version "1.0.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/set-proto/-/set-proto-1.0.0.tgz"
  integrity sha512-RJRdvCo6IAnPdsvP/7m6bsQqNnn1FCBX5ZNtFL98MmFF/4xAIJTIg1YbHW5DC2W5SKZanrC6i4HsJqlajw/dZw==
  dependencies:
    dunder-proto "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"

set-value@^2.0.0, set-value@^2.0.1:
  version "2.0.1"
  resolved "https://registry.nlark.com/set-value/download/set-value-2.0.1.tgz?cache=0&sync_timestamp=1631437777668&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fset-value%2Fdownload%2Fset-value-2.0.1.tgz"
  integrity sha1-oY1AUw5vB95CKMfe/kInr4ytAFs=
  dependencies:
    extend-shallow "^2.0.1"
    is-extendable "^0.1.1"
    is-plain-object "^2.0.3"
    split-string "^3.0.1"

setimmediate@^1.0.4:
  version "1.0.5"
  resolved "https://registry.nlark.com/setimmediate/download/setimmediate-1.0.5.tgz"
  integrity sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU=

setprototypeof@1.1.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/setprototypeof/download/setprototypeof-1.1.0.tgz"
  integrity sha1-0L2FU2iHtv58DYGMuWLZ2RxU5lY=

setprototypeof@1.2.0:
  version "1.2.0"

sha.js@^2.4.0, sha.js@^2.4.8:
  version "2.4.11"
  resolved "https://registry.nlark.com/sha.js/download/sha.js-2.4.11.tgz"
  integrity sha1-N6XPC4HsvGlD3hCbopYNGyZYSuc=
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

shebang-command@^1.2.0:
  version "1.2.0"
  resolved "https://registry.nlark.com/shebang-command/download/shebang-command-1.2.0.tgz"
  integrity sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=
  dependencies:
    shebang-regex "^1.0.0"

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/shebang-command/download/shebang-command-2.0.0.tgz"
  integrity sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/shebang-regex/download/shebang-regex-1.0.0.tgz?cache=0&sync_timestamp=1628896304371&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fshebang-regex%2Fdownload%2Fshebang-regex-1.0.0.tgz"
  integrity sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/shebang-regex/download/shebang-regex-3.0.0.tgz?cache=0&sync_timestamp=1628896304371&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fshebang-regex%2Fdownload%2Fshebang-regex-3.0.0.tgz"
  integrity sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=

shell-quote@^1.8.1:
  version "1.8.2"

shelljs@0.8.5:
  version "0.8.5"
  resolved "https://registry.npmmirror.com/shelljs/-/shelljs-0.8.5.tgz"
  integrity sha512-TiwcRcrkhHvbrZbnRcFYMLl30Dfov3HKqzp5tO5b4pt6G/SezKcYhmDg15zXVBswHmctSAQKznqNW2LO5tTDow==
  dependencies:
    glob "^7.0.0"
    interpret "^1.0.0"
    rechoir "^0.6.2"

side-channel-list@^1.0.0:
  version "1.0.0"
  dependencies:
    es-errors "^1.3.0"
    object-inspect "^1.13.3"

side-channel-map@^1.0.1:
  version "1.0.1"
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.5"
    object-inspect "^1.13.3"

side-channel-weakmap@^1.0.2:
  version "1.0.2"
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.5"
    object-inspect "^1.13.3"
    side-channel-map "^1.0.1"

side-channel@^1.0.6, side-channel@^1.1.0:
  version "1.1.0"
  dependencies:
    es-errors "^1.3.0"
    object-inspect "^1.13.3"
    side-channel-list "^1.0.0"
    side-channel-map "^1.0.1"
    side-channel-weakmap "^1.0.2"

signal-exit@^3.0.0, signal-exit@^3.0.2:
  version "3.0.7"

simple-swizzle@^0.2.2:
  version "0.2.2"
  resolved "https://registry.npmmirror.com/simple-swizzle/download/simple-swizzle-0.2.2.tgz"
  integrity sha1-pNprY1/8zMoz9w0Xy5JZLeleVXo=
  dependencies:
    is-arrayish "^0.3.1"

slash@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/slash/download/slash-1.0.0.tgz"
  integrity sha1-xB8vbDn8FtHNF61LXYlhFK5HDVU=

slash@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/slash/download/slash-2.0.0.tgz"
  integrity sha1-3lUoUaF1nfOo8gZTVEL17E3eq0Q=

slice-ansi@^2.1.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/slice-ansi/download/slice-ansi-2.1.0.tgz"
  integrity sha1-ys12k0YaY3pXiNkqfdT7oGjoFjY=
  dependencies:
    ansi-styles "^3.2.0"
    astral-regex "^1.0.0"
    is-fullwidth-code-point "^2.0.0"

snapdragon-node@^2.0.1:
  version "2.1.1"
  resolved "https://registry.nlark.com/snapdragon-node/download/snapdragon-node-2.1.1.tgz"
  integrity sha1-bBdfhv8UvbByRWPo88GwIaKGhTs=
  dependencies:
    define-property "^1.0.0"
    isobject "^3.0.0"
    snapdragon-util "^3.0.1"

snapdragon-util@^3.0.1:
  version "3.0.1"
  resolved "https://registry.nlark.com/snapdragon-util/download/snapdragon-util-3.0.1.tgz"
  integrity sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI=
  dependencies:
    kind-of "^3.2.0"

snapdragon@^0.8.1:
  version "0.8.2"
  resolved "https://registry.nlark.com/snapdragon/download/snapdragon-0.8.2.tgz"
  integrity sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0=
  dependencies:
    base "^0.11.1"
    debug "^2.2.0"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    map-cache "^0.2.2"
    source-map "^0.5.6"
    source-map-resolve "^0.5.0"
    use "^3.1.0"

sockjs-client@^1.5.0:
  version "1.6.1"
  dependencies:
    debug "^3.2.7"
    eventsource "^2.0.2"
    faye-websocket "^0.11.4"
    inherits "^2.0.4"
    url-parse "^1.5.10"

sockjs@^0.3.21:
  version "0.3.24"
  dependencies:
    faye-websocket "^0.11.3"
    uuid "^8.3.2"
    websocket-driver "^0.7.4"

sort-keys@^1.0.0:
  version "1.1.2"
  resolved "https://registry.nlark.com/sort-keys/download/sort-keys-1.1.2.tgz"
  integrity sha1-RBttTTRnmPG05J6JIK37oOVD+a0=
  dependencies:
    is-plain-obj "^1.0.0"

source-list-map@^2.0.0:
  version "2.0.1"
  resolved "https://registry.nlark.com/source-list-map/download/source-list-map-2.0.1.tgz"
  integrity sha1-OZO9hzv8SEecyp6jpUeDXHwVSzQ=

source-map-js@^1.2.0, source-map-js@^1.2.1:
  version "1.2.1"

source-map-resolve@^0.5.0, source-map-resolve@^0.5.2:
  version "0.5.3"
  resolved "https://registry.nlark.com/source-map-resolve/download/source-map-resolve-0.5.3.tgz"
  integrity sha1-GQhmvs51U+H48mei7oLGBrVQmho=
  dependencies:
    atob "^2.1.2"
    decode-uri-component "^0.2.0"
    resolve-url "^0.2.1"
    source-map-url "^0.4.0"
    urix "^0.1.0"

source-map-support@~0.5.12, source-map-support@~0.5.20:
  version "0.5.21"
  resolved "https://registry.npmmirror.com/source-map-support/download/source-map-support-0.5.21.tgz?cache=0&sync_timestamp=1637320290160&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsource-map-support%2Fdownload%2Fsource-map-support-0.5.21.tgz"
  integrity sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map-url@^0.4.0:
  version "0.4.1"
  resolved "https://registry.nlark.com/source-map-url/download/source-map-url-0.4.1.tgz"
  integrity sha1-CvZmBadFpaL5HPG7+KevvCg97FY=

source-map@^0.5.6:
  version "0.5.7"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/source-map/-/source-map-0.5.7.tgz"
  integrity sha512-LbrmJOMUSdEVxIKvdcJzQC+nQhe8FUZQTXQy6+I75skNgn3OoQ0DZA8YnFa7gp8tqtL3KPf1kmo0R5DoApeSGQ==

source-map@^0.6.0, source-map@^0.6.1, source-map@~0.6.0, source-map@~0.6.1:
  version "0.6.1"
  resolved "https://registry.nlark.com/source-map/download/source-map-0.6.1.tgz"
  integrity sha1-dHIq8y6WFOnCh6jQu95IteLxomM=

source-map@^0.7.3:
  version "0.7.4"

spdx-correct@^3.0.0:
  version "3.2.0"
  dependencies:
    spdx-expression-parse "^3.0.0"
    spdx-license-ids "^3.0.0"

spdx-exceptions@^2.1.0:
  version "2.5.0"

spdx-expression-parse@^3.0.0:
  version "3.0.1"
  resolved "https://registry.nlark.com/spdx-expression-parse/download/spdx-expression-parse-3.0.1.tgz"
  integrity sha1-z3D1BILu/cmOPOCmgz5KU87rpnk=
  dependencies:
    spdx-exceptions "^2.1.0"
    spdx-license-ids "^3.0.0"

spdx-license-ids@^3.0.0:
  version "3.0.20"

spdy-transport@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/spdy-transport/download/spdy-transport-3.0.0.tgz"
  integrity sha1-ANSGOmQArXXfkzYaFghgXl3NzzE=
  dependencies:
    debug "^4.1.0"
    detect-node "^2.0.4"
    hpack.js "^2.1.6"
    obuf "^1.1.2"
    readable-stream "^3.0.6"
    wbuf "^1.7.3"

spdy@^4.0.2:
  version "4.0.2"
  resolved "https://registry.nlark.com/spdy/download/spdy-4.0.2.tgz"
  integrity sha1-t09GYgOj7aRSwCSSuR+56EonZ3s=
  dependencies:
    debug "^4.1.0"
    handle-thing "^2.0.0"
    http-deceiver "^1.2.7"
    select-hose "^2.0.0"
    spdy-transport "^3.0.0"

split-on-first@^1.0.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/split-on-first/download/split-on-first-1.1.0.tgz"
  integrity sha1-9hCv7uOxK84dDDBCXnY5i3gkml8=

split-string@^3.0.1, split-string@^3.0.2:
  version "3.1.0"
  resolved "https://registry.nlark.com/split-string/download/split-string-3.1.0.tgz"
  integrity sha1-fLCd2jqGWFcFxks5pkZgOGguj+I=
  dependencies:
    extend-shallow "^3.0.0"

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "https://registry.nlark.com/sprintf-js/download/sprintf-js-1.0.3.tgz"
  integrity sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=

sshpk@^1.7.0:
  version "1.18.0"
  dependencies:
    asn1 "~0.2.3"
    assert-plus "^1.0.0"
    bcrypt-pbkdf "^1.0.0"
    dashdash "^1.12.0"
    ecc-jsbn "~0.1.1"
    getpass "^0.1.1"
    jsbn "~0.1.0"
    safer-buffer "^2.0.2"
    tweetnacl "~0.14.0"

ssri@^6.0.1:
  version "6.0.2"
  resolved "https://registry.nlark.com/ssri/download/ssri-6.0.2.tgz"
  integrity sha1-FXk5E08gRk5zAd26PpD/qPdyisU=
  dependencies:
    figgy-pudding "^3.5.1"

ssri@^8.0.1:
  version "8.0.1"
  resolved "https://registry.nlark.com/ssri/download/ssri-8.0.1.tgz"
  integrity sha1-Y45OQ54v+9LNKJd21cpFfE9Roq8=
  dependencies:
    minipass "^3.1.1"

stable@^0.1.8:
  version "0.1.8"
  resolved "https://registry.npmmirror.com/stable/download/stable-0.1.8.tgz"
  integrity sha1-g26zyDgv4pNv6vVEYxAXzn1Ho88=

stackframe@^1.3.4:
  version "1.3.4"

static-extend@^0.1.1:
  version "0.1.2"
  resolved "https://registry.nlark.com/static-extend/download/static-extend-0.1.2.tgz"
  integrity sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=
  dependencies:
    define-property "^0.2.5"
    object-copy "^0.1.0"

"statuses@>= 1.4.0 < 2":
  version "1.5.0"
  resolved "https://registry.nlark.com/statuses/download/statuses-1.5.0.tgz"
  integrity sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=

statuses@2.0.1:
  version "2.0.1"

store2@^2.12.0:
  version "2.14.4"

stream-browserify@^2.0.1:
  version "2.0.2"
  resolved "https://registry.nlark.com/stream-browserify/download/stream-browserify-2.0.2.tgz"
  integrity sha1-h1IdOKRKp+6RzhzSpH3wy0ndZgs=
  dependencies:
    inherits "~2.0.1"
    readable-stream "^2.0.2"

stream-each@^1.1.0:
  version "1.2.3"
  resolved "https://registry.nlark.com/stream-each/download/stream-each-1.2.3.tgz"
  integrity sha1-6+J6DDibBPvMIzZClS4Qcxr6m64=
  dependencies:
    end-of-stream "^1.1.0"
    stream-shift "^1.0.0"

stream-http@^2.7.2:
  version "2.8.3"
  resolved "https://registry.nlark.com/stream-http/download/stream-http-2.8.3.tgz"
  integrity sha1-stJCRpKIpaJ+xP6JM6z2I95lFPw=
  dependencies:
    builtin-status-codes "^3.0.0"
    inherits "^2.0.1"
    readable-stream "^2.3.6"
    to-arraybuffer "^1.0.0"
    xtend "^4.0.0"

stream-shift@^1.0.0:
  version "1.0.3"

strict-uri-encode@^1.0.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/strict-uri-encode/download/strict-uri-encode-1.1.0.tgz"
  integrity sha1-J5siXfHVgrH1TmWt3UNS4Y+qBxM=

strict-uri-encode@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/strict-uri-encode/download/strict-uri-encode-2.0.0.tgz"
  integrity sha1-ucczDHBChi9rFC3CdLvMWGbONUY=

string_decoder@^1.0.0, string_decoder@^1.1.1:
  version "1.3.0"
  resolved "https://registry.nlark.com/string_decoder/download/string_decoder-1.3.0.tgz"
  integrity sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=
  dependencies:
    safe-buffer "~5.2.0"

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "https://registry.nlark.com/string_decoder/download/string_decoder-1.1.1.tgz"
  integrity sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=
  dependencies:
    safe-buffer "~5.1.0"

string-width@^3.0.0, string-width@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/string-width/download/string-width-3.1.0.tgz?cache=0&sync_timestamp=1632421013520&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fstring-width%2Fdownload%2Fstring-width-3.1.0.tgz"
  integrity sha1-InZ74htirxCBV0MG9prFG2IgOWE=
  dependencies:
    emoji-regex "^7.0.1"
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^5.1.0"

string-width@^4.1.0, string-width@^4.2.0, string-width@^4.2.3:
  version "4.2.3"
  resolved "https://registry.npmmirror.com/string-width/download/string-width-4.2.3.tgz?cache=0&sync_timestamp=1632421013520&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fstring-width%2Fdownload%2Fstring-width-4.2.3.tgz"
  integrity sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string.prototype.trim@^1.2.10:
  version "1.2.10"
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.2"
    define-data-property "^1.1.4"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-object-atoms "^1.0.0"
    has-property-descriptors "^1.0.2"

string.prototype.trimend@^1.0.9:
  version "1.0.9"
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.2"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

string.prototype.trimstart@^1.0.8:
  version "1.0.8"
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

stringify-object@^3.3.0:
  version "3.3.0"
  resolved "https://registry.nlark.com/stringify-object/download/stringify-object-3.3.0.tgz?cache=0&sync_timestamp=1629674903267&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fstringify-object%2Fdownload%2Fstringify-object-3.3.0.tgz"
  integrity sha1-cDBlrvyhkwDTzoivT1s5VtdVZik=
  dependencies:
    get-own-enumerable-property-symbols "^3.0.0"
    is-obj "^1.0.1"
    is-regexp "^1.0.0"

strip-ansi@^3.0.0:
  version "3.0.1"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/strip-ansi/-/strip-ansi-3.0.1.tgz"
  integrity sha512-VhumSSbBqDTP8p2ZLKj40UjBCV4+v8bUSEpUb4KjRgWk9pbqGF4REFj6KEagidb2f/M6AzC0EmFyDNGaw9OCzg==
  dependencies:
    ansi-regex "^2.0.0"

strip-ansi@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-3.0.1.tgz?cache=0&sync_timestamp=1632420562057&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fstrip-ansi%2Fdownload%2Fstrip-ansi-3.0.1.tgz"
  integrity sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=
  dependencies:
    ansi-regex "^2.0.0"

strip-ansi@^5.0.0:
  version "5.2.0"
  resolved "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-5.2.0.tgz?cache=0&sync_timestamp=1632420562057&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fstrip-ansi%2Fdownload%2Fstrip-ansi-5.2.0.tgz"
  integrity sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=
  dependencies:
    ansi-regex "^4.1.0"

strip-ansi@^5.1.0:
  version "5.2.0"
  resolved "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-5.2.0.tgz?cache=0&sync_timestamp=1632420562057&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fstrip-ansi%2Fdownload%2Fstrip-ansi-5.2.0.tgz"
  integrity sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=
  dependencies:
    ansi-regex "^4.1.0"

strip-ansi@^5.2.0:
  version "5.2.0"
  resolved "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-5.2.0.tgz?cache=0&sync_timestamp=1632420562057&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fstrip-ansi%2Fdownload%2Fstrip-ansi-5.2.0.tgz"
  integrity sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=
  dependencies:
    ansi-regex "^4.1.0"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-6.0.1.tgz?cache=0&sync_timestamp=1632420562057&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fstrip-ansi%2Fdownload%2Fstrip-ansi-6.0.1.tgz"
  integrity sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=
  dependencies:
    ansi-regex "^5.0.1"

strip-comments@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/strip-comments/download/strip-comments-1.0.2.tgz"
  integrity sha1-grnEXn8FhzvuU/NxaK+TCqNoZ50=
  dependencies:
    babel-extract-comments "^1.0.0"
    babel-plugin-transform-object-rest-spread "^6.26.0"

strip-eof@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/strip-eof/download/strip-eof-1.0.0.tgz"
  integrity sha1-u0P/VZim6wXYm1n80SnJgzE2Br8=

strip-final-newline@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/strip-final-newline/download/strip-final-newline-2.0.0.tgz"
  integrity sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=

strip-indent@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/strip-indent/download/strip-indent-2.0.0.tgz"
  integrity sha1-XvjbKV0B5u1sv3qrlpmNeCJSe2g=

strip-json-comments@^3.0.1:
  version "3.1.1"
  resolved "https://registry.nlark.com/strip-json-comments/download/strip-json-comments-3.1.1.tgz"
  integrity sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=

style-resources-loader@^1.4.1:
  version "1.5.0"
  dependencies:
    glob "^7.2.0"
    loader-utils "^2.0.0"
    schema-utils "^2.7.0"
    tslib "^2.3.1"

stylehacks@^4.0.0:
  version "4.0.3"
  resolved "https://registry.nlark.com/stylehacks/download/stylehacks-4.0.3.tgz"
  integrity sha1-Zxj8r00eB9ihMYaQiB6NlnJqcdU=
  dependencies:
    browserslist "^4.0.0"
    postcss "^7.0.0"
    postcss-selector-parser "^3.0.0"

stylus-loader@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmmirror.com/stylus-loader/download/stylus-loader-3.0.2.tgz"
  integrity sha1-J6cGQgsFo44DjnyssVNXjUUFE8Y=
  dependencies:
    loader-utils "^1.0.2"
    lodash.clonedeep "^4.5.0"
    when "~3.6.x"

stylus@^0.54.7, stylus@>=0.52.4:
  version "0.54.8"
  resolved "https://registry.nlark.com/stylus/download/stylus-0.54.8.tgz"
  integrity sha1-PaPmWWa8Vnp7BEv+DuzmU+CZ0Uc=
  dependencies:
    css-parse "~2.0.0"
    debug "~3.1.0"
    glob "^7.1.6"
    mkdirp "~1.0.4"
    safer-buffer "^2.1.2"
    sax "~1.2.4"
    semver "^6.3.0"
    source-map "^0.7.3"

supports-color@^2.0.0:
  version "2.0.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/supports-color/-/supports-color-2.0.0.tgz"
  integrity sha512-KKNVtd6pCYgPIKU4cp2733HWYCpplQhddZLBUryaAHou723x+FRzQ5Df824Fj+IyyuiQTRoub4SnIFfIcrp70g==

supports-color@^3.2.3:
  version "3.2.3"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/supports-color/-/supports-color-3.2.3.tgz"
  integrity sha512-Jds2VIYDrlp5ui7t8abHN2bjAu4LV/q4N2KivFPpGH0lrka0BMq/33AmECUXlKPcHigkNaqfXRENFju+rlcy+A==
  dependencies:
    has-flag "^1.0.0"

supports-color@^5.3.0:
  version "5.5.0"
  resolved "https://registry.npmmirror.com/supports-color/download/supports-color-5.5.0.tgz"
  integrity sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=
  dependencies:
    has-flag "^3.0.0"

supports-color@^5.4.0:
  version "5.5.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/supports-color/-/supports-color-5.5.0.tgz"
  integrity sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==
  dependencies:
    has-flag "^3.0.0"

supports-color@^6.1.0:
  version "6.1.0"
  resolved "https://registry.npmmirror.com/supports-color/download/supports-color-6.1.0.tgz"
  integrity sha1-B2Srxpxj1ayELdSGfo0CXogN+PM=
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.1.0:
  version "7.2.0"
  resolved "https://registry.npmmirror.com/supports-color/download/supports-color-7.2.0.tgz"
  integrity sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=
  dependencies:
    has-flag "^4.0.0"

supports-color@^8.0.0:
  version "8.1.1"
  resolved "https://registry.npmmirror.com/supports-color/download/supports-color-8.1.1.tgz"
  integrity sha1-zW/BfihQDP9WwbhsCn/UpUpzAFw=
  dependencies:
    has-flag "^4.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"
  integrity sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==

svg-tags@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/svg-tags/download/svg-tags-1.0.0.tgz"
  integrity sha1-WPcc7jvVGbWdSyqEO2x95krAR2Q=

svgo@^1.0.0:
  version "1.3.2"
  resolved "https://registry.npmmirror.com/svgo/download/svgo-1.3.2.tgz"
  integrity sha1-ttxRHAYzRsnkFbgeQ0ARRbltQWc=
  dependencies:
    chalk "^2.4.1"
    coa "^2.0.2"
    css-select "^2.0.0"
    css-select-base-adapter "^0.1.1"
    css-tree "1.0.0-alpha.37"
    csso "^4.0.2"
    js-yaml "^3.13.1"
    mkdirp "~0.5.1"
    object.values "^1.1.0"
    sax "~1.2.4"
    stable "^0.1.8"
    unquote "~1.1.1"
    util.promisify "~1.0.0"

table@^5.2.3:
  version "5.4.6"
  resolved "https://registry.npmmirror.com/table/download/table-5.4.6.tgz?cache=0&sync_timestamp=1636037112686&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ftable%2Fdownload%2Ftable-5.4.6.tgz"
  integrity sha1-EpLRlQDOP4YFOwXw6Ofko7shB54=
  dependencies:
    ajv "^6.10.2"
    lodash "^4.17.14"
    slice-ansi "^2.1.0"
    string-width "^3.0.0"

tapable@^1.0.0, tapable@^1.1.3:
  version "1.1.3"
  resolved "https://registry.nlark.com/tapable/download/tapable-1.1.3.tgz"
  integrity sha1-ofzMBrWNth/XpF2i2kT186Pme6I=

tapable@^2.1.1, tapable@^2.2.0:
  version "2.2.1"
  resolved "https://registry.nlark.com/tapable/download/tapable-2.2.1.tgz"
  integrity sha1-GWenPvQGCoLxKrlq+G1S/bdu7KA=

terser-webpack-plugin@^1.4.3, terser-webpack-plugin@^1.4.4:
  version "1.4.6"
  dependencies:
    cacache "^12.0.2"
    find-cache-dir "^2.1.0"
    is-wsl "^1.1.0"
    schema-utils "^1.0.0"
    serialize-javascript "^4.0.0"
    source-map "^0.6.1"
    terser "^4.1.2"
    webpack-sources "^1.4.0"
    worker-farm "^1.7.0"

terser-webpack-plugin@^5.3.10:
  version "5.3.11"
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.25"
    jest-worker "^27.4.5"
    schema-utils "^4.3.0"
    serialize-javascript "^6.0.2"
    terser "^5.31.1"

terser@^4.1.2:
  version "4.8.1"
  dependencies:
    commander "^2.20.0"
    source-map "~0.6.1"
    source-map-support "~0.5.12"

terser@^5.31.1:
  version "5.37.0"
  dependencies:
    "@jridgewell/source-map" "^0.3.3"
    acorn "^8.8.2"
    commander "^2.20.0"
    source-map-support "~0.5.20"

text-table@^0.2.0:
  version "0.2.0"
  resolved "https://registry.nlark.com/text-table/download/text-table-0.2.0.tgz"
  integrity sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=

thenify-all@^1.0.0:
  version "1.6.0"
  resolved "https://registry.nlark.com/thenify-all/download/thenify-all-1.6.0.tgz"
  integrity sha1-GhkY1ALY/D+Y+/I02wvMjMEOlyY=
  dependencies:
    thenify ">= 3.1.0 < 4"

"thenify@>= 3.1.0 < 4":
  version "3.3.1"
  resolved "https://registry.nlark.com/thenify/download/thenify-3.3.1.tgz"
  integrity sha1-iTLmhqQGYDigFt2eLKRq3Zg4qV8=
  dependencies:
    any-promise "^1.0.0"

thread-loader@^2.1.3:
  version "2.1.3"
  resolved "https://registry.nlark.com/thread-loader/download/thread-loader-2.1.3.tgz"
  integrity sha1-y9LBOfwrLebp0o9iKGq3cMGsvdo=
  dependencies:
    loader-runner "^2.3.1"
    loader-utils "^1.1.0"
    neo-async "^2.6.0"

through@^2.3.6:
  version "2.3.8"
  resolved "https://registry.nlark.com/through/download/through-2.3.8.tgz"
  integrity sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=

through2@^2.0.0:
  version "2.0.5"
  resolved "https://registry.nlark.com/through2/download/through2-2.0.5.tgz"
  integrity sha1-AcHjnrMdB8t9A6lqcIIyYLIxMs0=
  dependencies:
    readable-stream "~2.3.6"
    xtend "~4.0.1"

thunky@^1.0.2:
  version "1.1.0"
  resolved "https://registry.nlark.com/thunky/download/thunky-1.1.0.tgz"
  integrity sha1-Wrr3FKlAXbBQRzK7zNLO3Z75U30=

timers-browserify@^2.0.4:
  version "2.0.12"
  resolved "https://registry.nlark.com/timers-browserify/download/timers-browserify-2.0.12.tgz"
  integrity sha1-RKRcEfv0B/NPl7zNFXfGUjYbAO4=
  dependencies:
    setimmediate "^1.0.4"

timsort@^0.3.0:
  version "0.3.0"
  resolved "https://registry.nlark.com/timsort/download/timsort-0.3.0.tgz"
  integrity sha1-QFQRqOfmM5/mTbmiNN4R3DHgK9Q=

tmp@^0.0.33:
  version "0.0.33"
  resolved "https://registry.nlark.com/tmp/download/tmp-0.0.33.tgz"
  integrity sha1-bTQzWIl2jSGyvNoKonfO07G/rfk=
  dependencies:
    os-tmpdir "~1.0.2"

to-arraybuffer@^1.0.0:
  version "1.0.1"
  resolved "https://registry.nlark.com/to-arraybuffer/download/to-arraybuffer-1.0.1.tgz"
  integrity sha1-fSKbH8xjfkZsoIEYCDanqr/4P0M=

to-object-path@^0.3.0:
  version "0.3.0"
  resolved "https://registry.nlark.com/to-object-path/download/to-object-path-0.3.0.tgz"
  integrity sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=
  dependencies:
    kind-of "^3.0.2"

to-regex-range@^2.1.0:
  version "2.1.1"
  resolved "https://registry.nlark.com/to-regex-range/download/to-regex-range-2.1.1.tgz"
  integrity sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=
  dependencies:
    is-number "^3.0.0"
    repeat-string "^1.6.1"

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://registry.nlark.com/to-regex-range/download/to-regex-range-5.0.1.tgz"
  integrity sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=
  dependencies:
    is-number "^7.0.0"

to-regex@^3.0.1, to-regex@^3.0.2:
  version "3.0.2"
  resolved "https://registry.nlark.com/to-regex/download/to-regex-3.0.2.tgz"
  integrity sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4=
  dependencies:
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    regex-not "^1.0.2"
    safe-regex "^1.1.0"

toidentifier@1.0.1:
  version "1.0.1"

toposort@^1.0.0:
  version "1.0.7"
  resolved "https://registry.nlark.com/toposort/download/toposort-1.0.7.tgz"
  integrity sha1-LmhELZ9k7HILjMieZEOsbKqVACk=

tough-cookie@~2.5.0:
  version "2.5.0"
  resolved "https://registry.nlark.com/tough-cookie/download/tough-cookie-2.5.0.tgz"
  integrity sha1-zZ+yoKodWhK0c72fuW+j3P9lreI=
  dependencies:
    psl "^1.1.28"
    punycode "^2.1.1"

tryer@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/tryer/download/tryer-1.0.1.tgz"
  integrity sha1-8shUBoALmw90yfdGW4HqrSQSUvg=

ts-pnp@^1.1.6:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/ts-pnp/download/ts-pnp-1.2.0.tgz"
  integrity sha1-pQCtCEsHmPHDBxrzkeZZEshrypI=

tslib@^1.9.0:
  version "1.14.1"
  resolved "https://registry.nlark.com/tslib/download/tslib-1.14.1.tgz"
  integrity sha1-zy04vcNKE0vK8QkcQfZhni9nLQA=

tslib@^2.3.1:
  version "2.8.1"

tty-browserify@0.0.0:
  version "0.0.0"
  resolved "https://registry.nlark.com/tty-browserify/download/tty-browserify-0.0.0.tgz"
  integrity sha1-oVe6QC2iTpv5V/mqadUk7tQpAaY=

tunnel-agent@^0.6.0:
  version "0.6.0"
  resolved "https://registry.nlark.com/tunnel-agent/download/tunnel-agent-0.6.0.tgz"
  integrity sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=
  dependencies:
    safe-buffer "^5.0.1"

tweetnacl@^0.14.3, tweetnacl@~0.14.0:
  version "0.14.5"
  resolved "https://registry.nlark.com/tweetnacl/download/tweetnacl-0.14.5.tgz"
  integrity sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=

tweetnacl@1.0.3:
  version "1.0.3"
  resolved "https://registry.npmmirror.com/tweetnacl/-/tweetnacl-1.0.3.tgz"
  integrity sha512-6rt+RN7aOi1nGMyC4Xa5DdYiukl2UWCbcJft7YhxReBGQD7OAM8Pbxw6YMo4r2diNEA8FEmu32YOn9rhaiE5yw==

type-check@~0.3.2:
  version "0.3.2"
  resolved "https://registry.nlark.com/type-check/download/type-check-0.3.2.tgz"
  integrity sha1-WITKtRLPHTVeP7eE8wgEsrUg23I=
  dependencies:
    prelude-ls "~1.1.2"

type-fest@^0.21.3:
  version "0.21.3"
  resolved "https://registry.npmmirror.com/type-fest/download/type-fest-0.21.3.tgz?cache=0&sync_timestamp=1637149872065&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ftype-fest%2Fdownload%2Ftype-fest-0.21.3.tgz"
  integrity sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc=

type-fest@^0.6.0:
  version "0.6.0"
  resolved "https://registry.npmmirror.com/type-fest/download/type-fest-0.6.0.tgz?cache=0&sync_timestamp=1637149872065&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ftype-fest%2Fdownload%2Ftype-fest-0.6.0.tgz"
  integrity sha1-jSojcNPfiG61yQraHFv2GIrPg4s=

type-fest@^0.8.1:
  version "0.8.1"
  resolved "https://registry.npmmirror.com/type-fest/download/type-fest-0.8.1.tgz?cache=0&sync_timestamp=1637149872065&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ftype-fest%2Fdownload%2Ftype-fest-0.8.1.tgz"
  integrity sha1-CeJJ696FHTseSNJ8EFREZn8XuD0=

type-is@~1.6.18:
  version "1.6.18"
  resolved "https://registry.nlark.com/type-is/download/type-is-1.6.18.tgz"
  integrity sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.24"

typed-array-buffer@^1.0.3:
  version "1.0.3"
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    is-typed-array "^1.1.14"

typed-array-byte-length@^1.0.3:
  version "1.0.3"
  dependencies:
    call-bind "^1.0.8"
    for-each "^0.3.3"
    gopd "^1.2.0"
    has-proto "^1.2.0"
    is-typed-array "^1.1.14"

typed-array-byte-offset@^1.0.4:
  version "1.0.4"
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.8"
    for-each "^0.3.3"
    gopd "^1.2.0"
    has-proto "^1.2.0"
    is-typed-array "^1.1.15"
    reflect.getprototypeof "^1.0.9"

typed-array-length@^1.0.7:
  version "1.0.7"
  dependencies:
    call-bind "^1.0.7"
    for-each "^0.3.3"
    gopd "^1.0.1"
    is-typed-array "^1.1.13"
    possible-typed-array-names "^1.0.0"
    reflect.getprototypeof "^1.0.6"

typedarray@^0.0.6:
  version "0.0.6"
  resolved "https://registry.nlark.com/typedarray/download/typedarray-0.0.6.tgz"
  integrity sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=

ua-parser-js@^1.0.2:
  version "1.0.40"

uglify-js@3.4.x:
  version "3.4.10"
  resolved "https://registry.npmmirror.com/uglify-js/download/uglify-js-3.4.10.tgz?cache=0&sync_timestamp=1635828986075&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fuglify-js%2Fdownload%2Fuglify-js-3.4.10.tgz"
  integrity sha1-mtlWPY6zrN+404WX0q8dgV9qdV8=
  dependencies:
    commander "~2.19.0"
    source-map "~0.6.1"

unbox-primitive@^1.1.0:
  version "1.1.0"
  dependencies:
    call-bound "^1.0.3"
    has-bigints "^1.0.2"
    has-symbols "^1.1.0"
    which-boxed-primitive "^1.1.1"

undici-types@~6.20.0:
  version "6.20.0"

unicode-canonical-property-names-ecmascript@^2.0.0:
  version "2.0.1"

unicode-match-property-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/unicode-match-property-ecmascript/download/unicode-match-property-ecmascript-2.0.0.tgz?cache=0&sync_timestamp=1631618696521&other_urls=https%3A%2F%2Fregistry.nlark.com%2Funicode-match-property-ecmascript%2Fdownload%2Funicode-match-property-ecmascript-2.0.0.tgz"
  integrity sha1-VP0W4OyxZ88Ezx91a9zJLrp5dsM=
  dependencies:
    unicode-canonical-property-names-ecmascript "^2.0.0"
    unicode-property-aliases-ecmascript "^2.0.0"

unicode-match-property-value-ecmascript@^2.1.0:
  version "2.2.0"

unicode-property-aliases-ecmascript@^2.0.0:
  version "2.1.0"

union-value@^1.0.0:
  version "1.0.1"
  resolved "https://registry.nlark.com/union-value/download/union-value-1.0.1.tgz"
  integrity sha1-C2/nuDWuzaYcbqTU8CwUIh4QmEc=
  dependencies:
    arr-union "^3.1.0"
    get-value "^2.0.6"
    is-extendable "^0.1.1"
    set-value "^2.0.1"

uniq@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/uniq/download/uniq-1.0.1.tgz"
  integrity sha1-sxxa6CVIRKOoKBVBzisEuGWnNP8=

uniqs@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/uniqs/download/uniqs-2.0.0.tgz"
  integrity sha1-/+3ks2slKQaW5uFl1KWe25mOawI=

unique-filename@^1.1.1:
  version "1.1.1"
  resolved "https://registry.nlark.com/unique-filename/download/unique-filename-1.1.1.tgz"
  integrity sha1-HWl2k2mtoFgxA6HmrodoG1ZXMjA=
  dependencies:
    unique-slug "^2.0.0"

unique-slug@^2.0.0:
  version "2.0.2"
  resolved "https://registry.nlark.com/unique-slug/download/unique-slug-2.0.2.tgz"
  integrity sha1-uqvOkQg/xk6UWw861hPiZPfNTmw=
  dependencies:
    imurmurhash "^0.1.4"

units-css@^0.4.0:
  version "0.4.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/units-css/-/units-css-0.4.0.tgz"
  integrity sha512-WijzYC+chwzg2D6HmNGUSzPAgFRJfuxVyG9oiY28Ei5E+g6fHoPkhXUr5GV+5hE/RTHZNd9SuX2KLioYHdttoA==
  dependencies:
    isnumeric "^0.2.0"
    viewport-dimensions "^0.2.0"

universalify@^0.1.0:
  version "0.1.2"
  resolved "https://registry.nlark.com/universalify/download/universalify-0.1.2.tgz"
  integrity sha1-tkb2m+OULavOzJ1mOcgNwQXvqmY=

unpipe@~1.0.0, unpipe@1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/unpipe/download/unpipe-1.0.0.tgz"
  integrity sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=

unquote@~1.1.1:
  version "1.1.1"
  resolved "https://registry.nlark.com/unquote/download/unquote-1.1.1.tgz"
  integrity sha1-j97XMk7G6IoP+LkF58CYzcCG1UQ=

unset-value@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/unset-value/download/unset-value-1.0.0.tgz"
  integrity sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=
  dependencies:
    has-value "^0.3.1"
    isobject "^3.0.0"

upath@^1.1.1:
  version "1.2.0"
  resolved "https://registry.nlark.com/upath/download/upath-1.2.0.tgz"
  integrity sha1-j2bbzVWog6za5ECK+LA1pQRMGJQ=

update-browserslist-db@^1.1.1:
  version "1.1.1"
  dependencies:
    escalade "^3.2.0"
    picocolors "^1.1.0"

upper-case@^1.1.1:
  version "1.1.3"
  resolved "https://registry.nlark.com/upper-case/download/upper-case-1.1.3.tgz"
  integrity sha1-9rRQHC7EzdJrp4vnIilh3ndiFZg=

uri-js@^4.2.2:
  version "4.4.1"
  resolved "https://registry.nlark.com/uri-js/download/uri-js-4.4.1.tgz"
  integrity sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=
  dependencies:
    punycode "^2.1.0"

urix@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmmirror.com/urix/download/urix-0.1.0.tgz"
  integrity sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=

url-loader@^2.2.0:
  version "2.3.0"
  resolved "https://registry.npmmirror.com/url-loader/download/url-loader-2.3.0.tgz"
  integrity sha1-4OLvZY8APvuMpBsPP/v3a6uIZYs=
  dependencies:
    loader-utils "^1.2.3"
    mime "^2.4.4"
    schema-utils "^2.5.0"

url-parse@^1.5.10:
  version "1.5.10"
  dependencies:
    querystringify "^2.1.1"
    requires-port "^1.0.0"

url@^0.11.0:
  version "0.11.4"
  dependencies:
    punycode "^1.4.1"
    qs "^6.12.3"

use@^3.1.0:
  version "3.1.1"
  resolved "https://registry.nlark.com/use/download/use-3.1.1.tgz"
  integrity sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8=

util-deprecate@^1.0.1, util-deprecate@^1.0.2, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "https://registry.nlark.com/util-deprecate/download/util-deprecate-1.0.2.tgz"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

util.promisify@~1.0.0:
  version "1.0.1"
  resolved "https://registry.nlark.com/util.promisify/download/util.promisify-1.0.1.tgz"
  integrity sha1-a693dLgO6w91INi4HQeYKlmruu4=
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.2"
    has-symbols "^1.0.1"
    object.getownpropertydescriptors "^2.1.0"

util.promisify@1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/util.promisify/download/util.promisify-1.0.0.tgz"
  integrity sha1-RA9xZaRZyaFtwUXrjnLzVocJcDA=
  dependencies:
    define-properties "^1.1.2"
    object.getownpropertydescriptors "^2.0.3"

util@^0.10.4:
  version "0.10.4"
  dependencies:
    inherits "2.0.3"

util@^0.11.0:
  version "0.11.1"
  resolved "https://registry.nlark.com/util/download/util-0.11.1.tgz"
  integrity sha1-MjZzNyDsZLsn9uJvQhqqLhtYjWE=
  dependencies:
    inherits "2.0.3"

utila@~0.4:
  version "0.4.0"
  resolved "https://registry.nlark.com/utila/download/utila-0.4.0.tgz"
  integrity sha1-ihagXURWV6Oupe7MWxKk+lN5dyw=

utils-merge@1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/utils-merge/download/utils-merge-1.0.1.tgz"
  integrity sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=

uuid@^3.3.2:
  version "3.4.0"
  resolved "https://registry.npmmirror.com/uuid/download/uuid-3.4.0.tgz"
  integrity sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4=

uuid@^8.3.2:
  version "8.3.2"

v3-infinite-loading@^1.0.1:
  version "1.3.2"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/v3-infinite-loading/-/v3-infinite-loading-1.3.2.tgz"
  integrity sha512-2Bg8X8nqd9tKEOJdITABtuX2BMSYo/t+eZOAD+9/xz0fGyGDclglmWjo9Zs4dr/T63liO7EniBAunBesM2QhBA==

v8-compile-cache@^2.0.3:
  version "2.4.0"

validate-npm-package-license@^3.0.1:
  version "3.0.4"
  resolved "https://registry.nlark.com/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz"
  integrity sha1-/JH2uce6FchX9MssXe/uw51PQQo=
  dependencies:
    spdx-correct "^3.0.0"
    spdx-expression-parse "^3.0.0"

vant@3:
  version "3.6.16"
  dependencies:
    "@vant/icons" "^1.8.0"
    "@vant/popperjs" "^1.2.1"
    "@vant/use" "^1.4.2"

vary@~1.1.2:
  version "1.1.2"
  resolved "https://registry.nlark.com/vary/download/vary-1.1.2.tgz"
  integrity sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=

vendors@^1.0.0:
  version "1.0.4"
  resolved "https://registry.nlark.com/vendors/download/vendors-1.0.4.tgz"
  integrity sha1-4rgApT56Kbk1BsPPQRANFsTErY4=

verror@1.10.0:
  version "1.10.0"
  resolved "https://registry.npmmirror.com/verror/download/verror-1.10.0.tgz?cache=0&sync_timestamp=1635885061482&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fverror%2Fdownload%2Fverror-1.10.0.tgz"
  integrity sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=
  dependencies:
    assert-plus "^1.0.0"
    core-util-is "1.0.2"
    extsprintf "^1.2.0"

viewport-dimensions@^0.2.0:
  version "0.2.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/viewport-dimensions/-/viewport-dimensions-0.2.0.tgz"
  integrity sha512-94JqlKxEP4m7WO+N3rm4tFRGXZmXXwSPQCoV+EPxDnn8YAGiLU3T+Ha1imLreAjXsHl0K+ELnIqv64i1XZHLFQ==

vm-browserify@^1.0.1:
  version "1.1.2"
  resolved "https://registry.nlark.com/vm-browserify/download/vm-browserify-1.1.2.tgz"
  integrity sha1-eGQcSIuObKkadfUR56OzKobl3aA=

vue-eslint-parser@^7.10.0:
  version "7.11.0"
  resolved "https://registry.npmmirror.com/vue-eslint-parser/download/vue-eslint-parser-7.11.0.tgz"
  integrity sha1-IUtd6pYQB/z/su5luJEjB2KNDa8=
  dependencies:
    debug "^4.1.1"
    eslint-scope "^5.1.1"
    eslint-visitor-keys "^1.1.0"
    espree "^6.2.1"
    esquery "^1.4.0"
    lodash "^4.17.21"
    semver "^6.3.0"

vue-hot-reload-api@^2.3.0:
  version "2.3.4"
  resolved "https://registry.nlark.com/vue-hot-reload-api/download/vue-hot-reload-api-2.3.4.tgz"
  integrity sha1-UylVzB6yCKPZkLOp+acFdGV+CPI=

vue-loader-v16@^16.0.0-beta.5.4:
  version "16.0.0-beta.5.4"
  resolved "https://registry.nlark.com/vue-loader-v16/download/vue-loader-v16-16.0.0-beta.5.4.tgz"
  integrity sha1-j7z4fcacmXns13etuk6KDn/ZWn4=
  dependencies:
    "@types/mini-css-extract-plugin" "^0.9.1"
    chalk "^3.0.0"
    hash-sum "^2.0.0"
    loader-utils "^1.2.3"
    merge-source-map "^1.1.0"
    source-map "^0.6.1"

"vue-loader-v16@npm:vue-loader@^16.1.0":
  version "16.8.3"
  resolved "https://registry.npmmirror.com/vue-loader/download/vue-loader-16.8.3.tgz"
  integrity sha1-1D5nXe9bqTRdbH8FkUwT2GGZcIc=
  dependencies:
    chalk "^4.1.0"
    hash-sum "^2.0.0"
    loader-utils "^2.0.0"

vue-loader@^15.9.2:
  version "15.11.1"
  dependencies:
    "@vue/component-compiler-utils" "^3.1.0"
    hash-sum "^1.0.2"
    loader-utils "^1.1.0"
    vue-hot-reload-api "^2.3.0"
    vue-style-loader "^4.1.0"

vue-loading-overlay@^4.0:
  version "4.0.4"
  resolved "https://registry.npmjs.org/vue-loading-overlay/-/vue-loading-overlay-4.0.4.tgz"
  integrity sha512-ADIEUXLo+IEpK8hd2+iqhZtEV3m0WNh5FlGvAc6FWG0KJAEHGTGUFdugQgAtM6Y3iFZc/ovzMd7A9dN+7QC7hw==

vue-observe-visibility@^2.0.0-alpha.1:
  version "2.0.0-alpha.1"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/vue-observe-visibility/-/vue-observe-visibility-2.0.0-alpha.1.tgz"
  integrity sha512-flFbp/gs9pZniXR6fans8smv1kDScJ8RS7rEpMjhVabiKeq7Qz3D9+eGsypncjfIyyU84saU88XZ0zjbD6Gq/g==

vue-progressive-image@^4.0.0:
  version "4.0.0"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/vue-progressive-image/-/vue-progressive-image-4.0.0.tgz"
  integrity sha512-ieLNX0q28Y8Oo4vsyi7vAtH9PzzpThiqk/IqO+jRbFE4xdM9ZYirhmRFfNnaxGSAhYWnQzY7z/BRTqupmpyKRQ==

vue-resize@^2.0.0-alpha.1:
  version "2.0.0-alpha.1"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/vue-resize/-/vue-resize-2.0.0-alpha.1.tgz"
  integrity sha512-7+iqOueLU7uc9NrMfrzbG8hwMqchfVfSzpVlCMeJQe4pyibqyoifDNbKTZvwxZKDvGkB+PdFeKvnGZMoEb8esg==

vue-router@^4.0.0-0:
  version "4.5.0"
  dependencies:
    "@vue/devtools-api" "^6.6.4"

vue-style-loader@^4.1.0, vue-style-loader@^4.1.2:
  version "4.1.3"
  resolved "https://registry.nlark.com/vue-style-loader/download/vue-style-loader-4.1.3.tgz"
  integrity sha1-bVWGOlH6dXqyTonZNxRlByqnvDU=
  dependencies:
    hash-sum "^1.0.2"
    loader-utils "^1.0.2"

vue-template-es2015-compiler@^1.9.0:
  version "1.9.1"
  resolved "https://registry.nlark.com/vue-template-es2015-compiler/download/vue-template-es2015-compiler-1.9.1.tgz"
  integrity sha1-HuO8mhbsv1EYvjNLsV+cRvgvWCU=

vue-virtual-scroller@^2.0.0-beta.8:
  version "2.0.0-beta.8"
  resolved "https://registry.npmjs.org/vue-virtual-scroller/-/vue-virtual-scroller-2.0.0-beta.8.tgz"
  integrity sha512-b8/f5NQ5nIEBRTNi6GcPItE4s7kxNHw2AIHLtDp+2QvqdTjVN0FgONwX9cr53jWRgnu+HRLPaWDOR2JPI5MTfQ==
  dependencies:
    mitt "^2.1.0"
    vue-observe-visibility "^2.0.0-alpha.1"
    vue-resize "^2.0.0-alpha.1"

vue@*, "vue@^2 || ^3.0.0-0", vue@^3.0.0, vue@^3.2, vue@^3.2.0, vue@3.5.13:
  version "3.5.13"
  dependencies:
    "@vue/compiler-dom" "3.5.13"
    "@vue/compiler-sfc" "3.5.13"
    "@vue/runtime-dom" "3.5.13"
    "@vue/server-renderer" "3.5.13"
    "@vue/shared" "3.5.13"

vue3-marquee@^4.1.0:
  version "4.2.2"

vuex@^4.0.0-0:
  version "4.1.0"
  dependencies:
    "@vue/devtools-api" "^6.0.0-beta.11"

watchpack-chokidar2@^2.0.1:
  version "2.0.1"
  resolved "https://registry.nlark.com/watchpack-chokidar2/download/watchpack-chokidar2-2.0.1.tgz"
  integrity sha1-OFAAcu5uzmbzdpk2lQ6hdxvhyVc=
  dependencies:
    chokidar "^2.1.8"

watchpack@^1.7.4:
  version "1.7.5"
  resolved "https://registry.nlark.com/watchpack/download/watchpack-1.7.5.tgz"
  integrity sha1-EmfmxV4Lm1vkTCAjrtVDeiwmxFM=
  dependencies:
    graceful-fs "^4.1.2"
    neo-async "^2.5.0"
  optionalDependencies:
    chokidar "^3.4.1"
    watchpack-chokidar2 "^2.0.1"

watchpack@^2.4.1:
  version "2.4.2"
  dependencies:
    glob-to-regexp "^0.4.1"
    graceful-fs "^4.1.2"

watermark-package@^2.1.2:
  version "2.1.2"
  resolved "https://registry.npmmirror.com/watermark-package/-/watermark-package-2.1.2.tgz"
  integrity sha512-6QIV2Q6q6ZudTFO5ZHkE26an3fT/7yPbQzBqsvuxkKTowdcCviXzXnt23X127quHaivQXQkAMQt2B4s/FcS/Fg==

wbuf@^1.1.0, wbuf@^1.7.3:
  version "1.7.3"
  resolved "https://registry.nlark.com/wbuf/download/wbuf-1.7.3.tgz"
  integrity sha1-wdjRSTFtPqhShIiVy2oL/oh7h98=
  dependencies:
    minimalistic-assert "^1.0.0"

wcwidth@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/wcwidth/download/wcwidth-1.0.1.tgz"
  integrity sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g=
  dependencies:
    defaults "^1.0.3"

webpack-bundle-analyzer@^3.8.0:
  version "3.9.0"
  resolved "https://registry.npmmirror.com/webpack-bundle-analyzer/download/webpack-bundle-analyzer-3.9.0.tgz?cache=0&sync_timestamp=1634019921368&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fwebpack-bundle-analyzer%2Fdownload%2Fwebpack-bundle-analyzer-3.9.0.tgz"
  integrity sha1-9vlNsQj7V05BWtMT3kGicH0z7zw=
  dependencies:
    acorn "^7.1.1"
    acorn-walk "^7.1.1"
    bfj "^6.1.1"
    chalk "^2.4.1"
    commander "^2.18.0"
    ejs "^2.6.1"
    express "^4.16.3"
    filesize "^3.6.1"
    gzip-size "^5.0.0"
    lodash "^4.17.19"
    mkdirp "^0.5.1"
    opener "^1.5.1"
    ws "^6.0.0"

webpack-chain@^6.4.0:
  version "6.5.1"
  resolved "https://registry.nlark.com/webpack-chain/download/webpack-chain-6.5.1.tgz"
  integrity sha1-TycoTLu2N+PI+970Pu9YjU2GEgY=
  dependencies:
    deepmerge "^1.5.2"
    javascript-stringify "^2.0.1"

webpack-dev-middleware@^3.7.2:
  version "3.7.3"
  resolved "https://registry.npmmirror.com/webpack-dev-middleware/download/webpack-dev-middleware-3.7.3.tgz?cache=0&sync_timestamp=1637165508298&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fwebpack-dev-middleware%2Fdownload%2Fwebpack-dev-middleware-3.7.3.tgz"
  integrity sha1-Bjk3KxQyYuK4SrldO5GnWXBhwsU=
  dependencies:
    memory-fs "^0.4.1"
    mime "^2.4.4"
    mkdirp "^0.5.1"
    range-parser "^1.2.1"
    webpack-log "^2.0.0"

webpack-dev-server@^3.11.0:
  version "3.11.3"
  resolved "https://registry.npmmirror.com/webpack-dev-server/download/webpack-dev-server-3.11.3.tgz?cache=0&sync_timestamp=1636807928809&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fwebpack-dev-server%2Fdownload%2Fwebpack-dev-server-3.11.3.tgz"
  integrity sha512-3x31rjbEQWKMNzacUZRE6wXvUFuGpH7vr0lIEbYpMAG9BOxi0928QU1BBswOAP3kg3H1O4hiS+sq4YyAn6ANnA==
  dependencies:
    ansi-html-community "0.0.8"
    bonjour "^3.5.0"
    chokidar "^2.1.8"
    compression "^1.7.4"
    connect-history-api-fallback "^1.6.0"
    debug "^4.1.1"
    del "^4.1.1"
    express "^4.17.1"
    html-entities "^1.3.1"
    http-proxy-middleware "0.19.1"
    import-local "^2.0.0"
    internal-ip "^4.3.0"
    ip "^1.1.5"
    is-absolute-url "^3.0.3"
    killable "^1.0.1"
    loglevel "^1.6.8"
    opn "^5.5.0"
    p-retry "^3.0.1"
    portfinder "^1.0.26"
    schema-utils "^1.0.0"
    selfsigned "^1.10.8"
    semver "^6.3.0"
    serve-index "^1.9.1"
    sockjs "^0.3.21"
    sockjs-client "^1.5.0"
    spdy "^4.0.2"
    strip-ansi "^3.0.1"
    supports-color "^6.1.0"
    url "^0.11.0"
    webpack-dev-middleware "^3.7.2"
    webpack-log "^2.0.0"
    ws "^6.2.1"
    yargs "^13.3.2"

webpack-log@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/webpack-log/download/webpack-log-2.0.0.tgz"
  integrity sha1-W3ko4GN1k/EZ0y9iJ8HgrDHhtH8=
  dependencies:
    ansi-colors "^3.0.0"
    uuid "^3.3.2"

webpack-merge@^4.2.2:
  version "4.2.2"
  resolved "https://registry.nlark.com/webpack-merge/download/webpack-merge-4.2.2.tgz"
  integrity sha1-onxS6ng9E5iv0gh/VH17nS9DY00=
  dependencies:
    lodash "^4.17.15"

webpack-sources@^1.1.0, webpack-sources@^1.4.0, webpack-sources@^1.4.1:
  version "1.4.3"
  resolved "https://registry.npmmirror.com/webpack-sources/download/webpack-sources-1.4.3.tgz?cache=0&sync_timestamp=1636982731420&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fwebpack-sources%2Fdownload%2Fwebpack-sources-1.4.3.tgz"
  integrity sha1-7t2OwLko+/HL/plOItLYkPMwqTM=
  dependencies:
    source-list-map "^2.0.0"
    source-map "~0.6.1"

webpack-sources@^3.2.3:
  version "3.2.3"

"webpack@^1.0.0 || ^2.0.0 || ^3.0.0 || ^4.0.0", "webpack@^2.0.0 || ^3.0.0 || ^4.0.0", "webpack@^3.0.0 || ^4.0.0 || ^5.0.0", "webpack@^3.0.0 || ^4.1.0 || ^5.0.0-0", webpack@^4.0.0, "webpack@^4.0.0 || ^5.0.0", "webpack@^4.1.0 || ^5.0.0-0", webpack@^4.4.0, "webpack@>= 4", webpack@>=2, "webpack@>=2.0.0 <5.0.0", webpack@>=4.0.0:
  version "4.47.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-module-context" "1.9.0"
    "@webassemblyjs/wasm-edit" "1.9.0"
    "@webassemblyjs/wasm-parser" "1.9.0"
    acorn "^6.4.1"
    ajv "^6.10.2"
    ajv-keywords "^3.4.1"
    chrome-trace-event "^1.0.2"
    enhanced-resolve "^4.5.0"
    eslint-scope "^4.0.3"
    json-parse-better-errors "^1.0.2"
    loader-runner "^2.4.0"
    loader-utils "^1.2.3"
    memory-fs "^0.4.1"
    micromatch "^3.1.10"
    mkdirp "^0.5.3"
    neo-async "^2.6.1"
    node-libs-browser "^2.2.1"
    schema-utils "^1.0.0"
    tapable "^1.1.3"
    terser-webpack-plugin "^1.4.3"
    watchpack "^1.7.4"
    webpack-sources "^1.4.1"

webpack@^5, webpack@^5.1.0:
  version "5.97.1"
  dependencies:
    "@types/eslint-scope" "^3.7.7"
    "@types/estree" "^1.0.6"
    "@webassemblyjs/ast" "^1.14.1"
    "@webassemblyjs/wasm-edit" "^1.14.1"
    "@webassemblyjs/wasm-parser" "^1.14.1"
    acorn "^8.14.0"
    browserslist "^4.24.0"
    chrome-trace-event "^1.0.2"
    enhanced-resolve "^5.17.1"
    es-module-lexer "^1.2.1"
    eslint-scope "5.1.1"
    events "^3.2.0"
    glob-to-regexp "^0.4.1"
    graceful-fs "^4.2.11"
    json-parse-even-better-errors "^2.3.1"
    loader-runner "^4.2.0"
    mime-types "^2.1.27"
    neo-async "^2.6.2"
    schema-utils "^3.2.0"
    tapable "^2.1.1"
    terser-webpack-plugin "^5.3.10"
    watchpack "^2.4.1"
    webpack-sources "^3.2.3"

websocket-driver@^0.7.4, websocket-driver@>=0.5.1:
  version "0.7.4"
  resolved "https://registry.nlark.com/websocket-driver/download/websocket-driver-0.7.4.tgz"
  integrity sha1-ia1Slbv2S0gKvLox5JU6ynBvV2A=
  dependencies:
    http-parser-js ">=0.5.1"
    safe-buffer ">=5.1.0"
    websocket-extensions ">=0.1.1"

websocket-extensions@>=0.1.1:
  version "0.1.4"
  resolved "https://registry.nlark.com/websocket-extensions/download/websocket-extensions-0.1.4.tgz"
  integrity sha1-f4RzvIOd/YdgituV1+sHUhFXikI=

when@~3.6.x:
  version "3.6.4"
  resolved "https://registry.nlark.com/when/download/when-3.6.4.tgz"
  integrity sha1-RztRfsFZ4rhQBUl6E5g/CVQS404=

which-boxed-primitive@^1.1.0, which-boxed-primitive@^1.1.1:
  version "1.1.1"
  dependencies:
    is-bigint "^1.1.0"
    is-boolean-object "^1.2.1"
    is-number-object "^1.1.1"
    is-string "^1.1.1"
    is-symbol "^1.1.1"

which-builtin-type@^1.2.1:
  version "1.2.1"
  dependencies:
    call-bound "^1.0.2"
    function.prototype.name "^1.1.6"
    has-tostringtag "^1.0.2"
    is-async-function "^2.0.0"
    is-date-object "^1.1.0"
    is-finalizationregistry "^1.1.0"
    is-generator-function "^1.0.10"
    is-regex "^1.2.1"
    is-weakref "^1.0.2"
    isarray "^2.0.5"
    which-boxed-primitive "^1.1.0"
    which-collection "^1.0.2"
    which-typed-array "^1.1.16"

which-collection@^1.0.2:
  version "1.0.2"
  dependencies:
    is-map "^2.0.3"
    is-set "^2.0.3"
    is-weakmap "^2.0.2"
    is-weakset "^2.0.3"

which-module@^2.0.0:
  version "2.0.1"

which-typed-array@^1.1.16, which-typed-array@^1.1.18:
  version "1.1.18"
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    for-each "^0.3.3"
    gopd "^1.2.0"
    has-tostringtag "^1.0.2"

which@^1.2.9:
  version "1.3.1"
  resolved "https://registry.nlark.com/which/download/which-1.3.1.tgz"
  integrity sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=
  dependencies:
    isexe "^2.0.0"

which@^2.0.1:
  version "2.0.2"
  resolved "https://registry.nlark.com/which/download/which-2.0.2.tgz"
  integrity sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=
  dependencies:
    isexe "^2.0.0"

word-wrap@~1.2.3:
  version "1.2.5"

workbox-background-sync@^4.3.1:
  version "4.3.1"
  resolved "https://registry.npmmirror.com/workbox-background-sync/download/workbox-background-sync-4.3.1.tgz?cache=0&sync_timestamp=1637020700475&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fworkbox-background-sync%2Fdownload%2Fworkbox-background-sync-4.3.1.tgz"
  integrity sha1-JoIbm/Funjf9HWQCie3dwIr9GVA=
  dependencies:
    workbox-core "^4.3.1"

workbox-broadcast-update@^4.3.1:
  version "4.3.1"
  resolved "https://registry.npmmirror.com/workbox-broadcast-update/download/workbox-broadcast-update-4.3.1.tgz?cache=0&sync_timestamp=1637021348613&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fworkbox-broadcast-update%2Fdownload%2Fworkbox-broadcast-update-4.3.1.tgz"
  integrity sha1-4sAoCxSeOlBJg7dXYGrQQfMyw1s=
  dependencies:
    workbox-core "^4.3.1"

workbox-build@^4.3.1:
  version "4.3.1"
  resolved "https://registry.npmmirror.com/workbox-build/download/workbox-build-4.3.1.tgz?cache=0&sync_timestamp=1637021337356&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fworkbox-build%2Fdownload%2Fworkbox-build-4.3.1.tgz"
  integrity sha1-QU9w+01t5H9lOGCLgOxSQS0jPmQ=
  dependencies:
    "@babel/runtime" "^7.3.4"
    "@hapi/joi" "^15.0.0"
    common-tags "^1.8.0"
    fs-extra "^4.0.2"
    glob "^7.1.3"
    lodash.template "^4.4.0"
    pretty-bytes "^5.1.0"
    stringify-object "^3.3.0"
    strip-comments "^1.0.2"
    workbox-background-sync "^4.3.1"
    workbox-broadcast-update "^4.3.1"
    workbox-cacheable-response "^4.3.1"
    workbox-core "^4.3.1"
    workbox-expiration "^4.3.1"
    workbox-google-analytics "^4.3.1"
    workbox-navigation-preload "^4.3.1"
    workbox-precaching "^4.3.1"
    workbox-range-requests "^4.3.1"
    workbox-routing "^4.3.1"
    workbox-strategies "^4.3.1"
    workbox-streams "^4.3.1"
    workbox-sw "^4.3.1"
    workbox-window "^4.3.1"

workbox-cacheable-response@^4.3.1:
  version "4.3.1"
  resolved "https://registry.npmmirror.com/workbox-cacheable-response/download/workbox-cacheable-response-4.3.1.tgz?cache=0&sync_timestamp=*************&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fworkbox-cacheable-response%2Fdownload%2Fworkbox-cacheable-response-4.3.1.tgz"
  integrity sha1-9T4HkXnAlaPxnlMTsoSXXJFCjJE=
  dependencies:
    workbox-core "^4.3.1"

workbox-core@^4.3.1:
  version "4.3.1"
  resolved "https://registry.npmmirror.com/workbox-core/download/workbox-core-4.3.1.tgz?cache=0&sync_timestamp=*************&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fworkbox-core%2Fdownload%2Fworkbox-core-4.3.1.tgz"
  integrity sha1-AF0sagahcUN6/WyikEpXJ+zXO+Y=

workbox-expiration@^4.3.1:
  version "4.3.1"
  resolved "https://registry.npmmirror.com/workbox-expiration/download/workbox-expiration-4.3.1.tgz?cache=0&sync_timestamp=1637021348724&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fworkbox-expiration%2Fdownload%2Fworkbox-expiration-4.3.1.tgz"
  integrity sha1-15BDNWICnlaDfzQdf1U8Snjr6SE=
  dependencies:
    workbox-core "^4.3.1"

workbox-google-analytics@^4.3.1:
  version "4.3.1"
  resolved "https://registry.npmmirror.com/workbox-google-analytics/download/workbox-google-analytics-4.3.1.tgz?cache=0&sync_timestamp=*************&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fworkbox-google-analytics%2Fdownload%2Fworkbox-google-analytics-4.3.1.tgz"
  integrity sha1-ntoBg7EDiQtcJW5vTqFaHxVIUZo=
  dependencies:
    workbox-background-sync "^4.3.1"
    workbox-core "^4.3.1"
    workbox-routing "^4.3.1"
    workbox-strategies "^4.3.1"

workbox-navigation-preload@^4.3.1:
  version "4.3.1"
  resolved "https://registry.npmmirror.com/workbox-navigation-preload/download/workbox-navigation-preload-4.3.1.tgz?cache=0&sync_timestamp=*************&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fworkbox-navigation-preload%2Fdownload%2Fworkbox-navigation-preload-4.3.1.tgz"
  integrity sha1-Kcjk21hDgDs0zZbcFV+evZr6RT0=
  dependencies:
    workbox-core "^4.3.1"

workbox-precaching@^4.3.1:
  version "4.3.1"
  resolved "https://registry.npmmirror.com/workbox-precaching/download/workbox-precaching-4.3.1.tgz?cache=0&sync_timestamp=*************&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fworkbox-precaching%2Fdownload%2Fworkbox-precaching-4.3.1.tgz"
  integrity sha1-n8Re0SLZS74fDqlYT/WUCWB3HLo=
  dependencies:
    workbox-core "^4.3.1"

workbox-range-requests@^4.3.1:
  version "4.3.1"
  resolved "https://registry.npmmirror.com/workbox-range-requests/download/workbox-range-requests-4.3.1.tgz?cache=0&sync_timestamp=*************&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fworkbox-range-requests%2Fdownload%2Fworkbox-range-requests-4.3.1.tgz"
  integrity sha1-+KRwGIkiFFy/DAmpotXjVkUkTnQ=
  dependencies:
    workbox-core "^4.3.1"

workbox-routing@^4.3.1:
  version "4.3.1"
  resolved "https://registry.npmmirror.com/workbox-routing/download/workbox-routing-4.3.1.tgz?cache=0&sync_timestamp=*************&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fworkbox-routing%2Fdownload%2Fworkbox-routing-4.3.1.tgz"
  integrity sha1-pnWEGvYj4LsMZ85O2OckrAvtDNo=
  dependencies:
    workbox-core "^4.3.1"

workbox-strategies@^4.3.1:
  version "4.3.1"
  resolved "https://registry.npmmirror.com/workbox-strategies/download/workbox-strategies-4.3.1.tgz?cache=0&sync_timestamp=*************&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fworkbox-strategies%2Fdownload%2Fworkbox-strategies-4.3.1.tgz"
  integrity sha1-0r4DxO8hTBFeGrKcnHWcn+Pp5kY=
  dependencies:
    workbox-core "^4.3.1"

workbox-streams@^4.3.1:
  version "4.3.1"
  resolved "https://registry.npmmirror.com/workbox-streams/download/workbox-streams-4.3.1.tgz?cache=0&sync_timestamp=*************&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fworkbox-streams%2Fdownload%2Fworkbox-streams-4.3.1.tgz"
  integrity sha1-C1facOmCVy3gnIdC3Qy0Cmt8LMM=
  dependencies:
    workbox-core "^4.3.1"

workbox-sw@^4.3.1:
  version "4.3.1"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/workbox-sw/-/workbox-sw-4.3.1.tgz"
  integrity sha512-0jXdusCL2uC5gM3yYFT6QMBzKfBr2XTk0g5TPAV4y8IZDyVNDyj1a8uSXy3/XrvkVTmQvLN4O5k3JawGReXr9w==

workbox-webpack-plugin@^4.3.1:
  version "4.3.1"
  resolved "https://registry.npmmirror.com/workbox-webpack-plugin/download/workbox-webpack-plugin-4.3.1.tgz"
  integrity sha1-R/9eocwHS2xA+1qGEIhjokEg1L0=
  dependencies:
    "@babel/runtime" "^7.0.0"
    json-stable-stringify "^1.0.1"
    workbox-build "^4.3.1"

workbox-window@^4.3.1:
  version "4.3.1"
  resolved "https://registry.npmmirror.com/workbox-window/download/workbox-window-4.3.1.tgz?cache=0&sync_timestamp=1637021349217&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fworkbox-window%2Fdownload%2Fworkbox-window-4.3.1.tgz"
  integrity sha1-7mBRvxDwavpUg8m436BTGZTt4PM=
  dependencies:
    workbox-core "^4.3.1"

worker-farm@^1.7.0:
  version "1.7.0"
  resolved "https://registry.nlark.com/worker-farm/download/worker-farm-1.7.0.tgz"
  integrity sha1-JqlMU5G7ypJhUgAvabhKS/dy5ag=
  dependencies:
    errno "~0.1.7"

worker-plugin@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmmirror.com/worker-plugin/-/worker-plugin-5.0.1.tgz"
  integrity sha512-Pn7+19jIiANcGuTSGdy+vrzyF+SGH03A5wV8iu4jRTMAOfAC9bNeiHo4+l5tPS7F0uvICMBv+h8UCvL7lunxcA==
  dependencies:
    loader-utils "^1.1.0"

wrap-ansi@^5.1.0:
  version "5.1.0"
  resolved "https://registry.nlark.com/wrap-ansi/download/wrap-ansi-5.1.0.tgz?cache=0&sync_timestamp=1631557327268&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fwrap-ansi%2Fdownload%2Fwrap-ansi-5.1.0.tgz"
  integrity sha1-H9H2cjXVttD+54EFYAG/tpTAOwk=
  dependencies:
    ansi-styles "^3.2.0"
    string-width "^3.0.0"
    strip-ansi "^5.0.0"

wrap-ansi@^6.2.0:
  version "6.2.0"
  resolved "https://registry.nlark.com/wrap-ansi/download/wrap-ansi-6.2.0.tgz?cache=0&sync_timestamp=1631557327268&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fwrap-ansi%2Fdownload%2Fwrap-ansi-6.2.0.tgz"
  integrity sha1-6Tk7oHEC5skaOyIUePAlfNKFblM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^7.0.0:
  version "7.0.0"
  resolved "https://registry.nlark.com/wrap-ansi/download/wrap-ansi-7.0.0.tgz?cache=0&sync_timestamp=1631557327268&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fwrap-ansi%2Fdownload%2Fwrap-ansi-7.0.0.tgz"
  integrity sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrappy@1:
  version "1.0.2"
  resolved "https://registry.nlark.com/wrappy/download/wrappy-1.0.2.tgz"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

write@1.0.3:
  version "1.0.3"
  resolved "https://registry.nlark.com/write/download/write-1.0.3.tgz"
  integrity sha1-CADhRSO5I6OH5BUSPIZWFqrg9cM=
  dependencies:
    mkdirp "^0.5.1"

ws@^6.0.0, ws@^6.2.1:
  version "6.2.3"
  dependencies:
    async-limiter "~1.0.0"

xtend@^4.0.0, xtend@~4.0.1:
  version "4.0.2"
  resolved "https://registry.nlark.com/xtend/download/xtend-4.0.2.tgz"
  integrity sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q=

xxhashjs@^0.2.1:
  version "0.2.2"
  resolved "https://packages.aliyun.com/6343ccdcd4fad3d161e96750/npm/npm-registry/xxhashjs/-/xxhashjs-0.2.2.tgz"
  integrity sha512-AkTuIuVTET12tpsVIQo+ZU6f/qDmKuRUcjaqR+OIvm+aCBsZ95i7UVY5WJ9TMsSaZ0DA2WxoZ4acu0sPH+OKAw==
  dependencies:
    cuint "^0.2.2"

y18n@^4.0.0:
  version "4.0.3"
  resolved "https://registry.nlark.com/y18n/download/y18n-4.0.3.tgz"
  integrity sha1-tfJZyCzW4zaSHv17/Yv1YN6e7t8=

y18n@^5.0.5:
  version "5.0.8"
  resolved "https://registry.nlark.com/y18n/download/y18n-5.0.8.tgz"
  integrity sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU=

yallist@^2.1.2:
  version "2.1.2"
  resolved "https://registry.nlark.com/yallist/download/yallist-2.1.2.tgz"
  integrity sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI=

yallist@^3.0.2:
  version "3.1.1"
  resolved "https://registry.nlark.com/yallist/download/yallist-3.1.1.tgz"
  integrity sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=

yallist@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/yallist/download/yallist-4.0.0.tgz"
  integrity sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI=

yargs-parser@^13.1.2:
  version "13.1.2"
  resolved "https://registry.npmmirror.com/yargs-parser/download/yargs-parser-13.1.2.tgz?cache=0&sync_timestamp=1637031053426&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fyargs-parser%2Fdownload%2Fyargs-parser-13.1.2.tgz"
  integrity sha1-Ew8JcC667vJlDVTObj5XBvek+zg=
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs-parser@^18.1.2:
  version "18.1.3"
  resolved "https://registry.npmmirror.com/yargs-parser/download/yargs-parser-18.1.3.tgz?cache=0&sync_timestamp=1637031053426&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fyargs-parser%2Fdownload%2Fyargs-parser-18.1.3.tgz"
  integrity sha1-vmjEl1xrKr9GkjawyHA2L6sJp7A=
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs-parser@^20.2.2:
  version "20.2.9"
  resolved "https://registry.npmmirror.com/yargs-parser/download/yargs-parser-20.2.9.tgz?cache=0&sync_timestamp=1637031053426&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fyargs-parser%2Fdownload%2Fyargs-parser-20.2.9.tgz"
  integrity sha1-LrfcOwKJcY/ClfNidThFxBoMlO4=

yargs@^13.3.2:
  version "13.3.2"
  resolved "https://registry.npmmirror.com/yargs/download/yargs-13.3.2.tgz"
  integrity sha1-rX/+/sGqWVZayRX4Lcyzipwxot0=
  dependencies:
    cliui "^5.0.0"
    find-up "^3.0.0"
    get-caller-file "^2.0.1"
    require-directory "^2.1.1"
    require-main-filename "^2.0.0"
    set-blocking "^2.0.0"
    string-width "^3.0.0"
    which-module "^2.0.0"
    y18n "^4.0.0"
    yargs-parser "^13.1.2"

yargs@^15.3.1:
  version "15.4.1"
  resolved "https://registry.npmmirror.com/yargs/download/yargs-15.4.1.tgz"
  integrity sha1-DYehbeAa7p2L7Cv7909nhRcw9Pg=
  dependencies:
    cliui "^6.0.0"
    decamelize "^1.2.0"
    find-up "^4.1.0"
    get-caller-file "^2.0.1"
    require-directory "^2.1.1"
    require-main-filename "^2.0.0"
    set-blocking "^2.0.0"
    string-width "^4.2.0"
    which-module "^2.0.0"
    y18n "^4.0.0"
    yargs-parser "^18.1.2"

yargs@^16.0.0:
  version "16.2.0"
  resolved "https://registry.npmmirror.com/yargs/download/yargs-16.2.0.tgz"
  integrity sha1-HIK/D2tqZur85+8w43b0mhJHf2Y=
  dependencies:
    cliui "^7.0.2"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.0"
    y18n "^5.0.5"
    yargs-parser "^20.2.2"

yorkie@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/yorkie/download/yorkie-2.0.0.tgz"
  integrity sha1-kkEZEtQ1IU4SxRwq4Qk+VLa7g9k=
  dependencies:
    execa "^0.8.0"
    is-ci "^1.0.10"
    normalize-path "^1.0.0"
    strip-indent "^2.0.0"
