const WorkerPlugin = require('worker-plugin');
const pxtoviewport = require("postcss-px-to-viewport");
const git = require('git-rev-sync');

const ReportEnvironmentMap = {
  production: 'prod',
  stage: 'daily',
  development: 'local'
}
function getReportEnvironment() {
  return ReportEnvironmentMap[process.env.NODE_ENV] || 'local'
}

module.exports = {
  css: {
    loaderOptions: {
      //配置路vw vm适配
      postcss: {
        plugins: [
          pxtoviewport({
            viewportWidth: 1200,
            mediaQuery: true,
            landscape: true,
            landscapeWidth: 1920
          }),
        ],
      },
    },
  },
  pwa: {
    workboxOptions: {
      importScripts: ['https://g.alicdn.com/kg/workbox/4.3.1/workbox-sw.js']
    }
  },
  configureWebpack: {
    plugins: [
      new WorkerPlugin()
    ]
  },
  chainWebpack: config => {
    config.plugin('define')
      .tap(options => {
        options[0].VUE_APP_BL_RELEASE = JSON.stringify(git.short())
        options[0].VUE_APP_ENVIRONMENT = JSON.stringify(getReportEnvironment())
        return options
      })
  }
};
