# GitLab Commit 获取与代码迁移工具

## 功能介绍

这个工具用于获取指定 GitLab commit 的详细信息和变更内容，帮助您分析需要迁移的代码。工具会生成以下内容：

1. commit 详情信息
2. 变更文件列表及差异内容
3. 简要分析报告

## 使用方法

### 前提条件

- 已安装 Node.js 环境
- 已获取 GitLab 个人访问令牌(Personal Access Token)

### 配置步骤

1. 打开`get-gitlab-commit.js`文件
2. 修改配置部分：
   ```js
   const config = {
     // GitLab API基础URL
     baseUrl: "https://g.ktvsky.com/api/v4",
     // 项目ID或路径（URL编码）
     projectId: "web/tesla_ktv",
     // Commit SHA
     commitSha: "be0d919a409d71dc899b7a963fa3c5456ea51cce",
     // 个人访问令牌（需要用户填写）
     privateToken: "请在此处填入您的个人访问令牌",
     // 输出目录
     outputDir: path.join(__dirname, "../commit-analysis"),
   };
   ```
3. 将您的个人访问令牌填入`privateToken`字段
4. 如需获取其他 commit，请修改`commitSha`字段

### 运行工具

```bash
cd node/gitlab-scripts
node get-gitlab-commit.js
```

### 查看结果

执行成功后，结果将保存在`commit-analysis`目录中：

- `commit-details.json`: commit 的详细信息
- `commit-diff.json`: 所有变更文件的差异信息
- `analysis-report.json`: 简要分析报告
- `diff_[文件路径].txt`: 每个变更文件的具体差异内容

## 代码迁移最佳实践

### 分析步骤

1. **了解 commit 目的**：查看 commit message 和详情，理解这次变更的目的和背景
2. **识别核心变更**：确定哪些文件包含核心功能变更，哪些是附带变更
3. **分析依赖关系**：确定变更代码是否依赖其他模块或组件
4. **检查当前项目**：对比当前项目的代码结构和设计模式

### 迁移策略

1. **保持一致性**：尽量保持与当前项目的代码风格和架构一致
2. **最小化变更**：只迁移必要的代码，避免引入不必要的变更
3. **适应性调整**：根据当前项目的特点调整代码实现
4. **测试验证**：迁移后进行充分测试，确保功能正常

### 常见问题

- **依赖冲突**：如果迁移的代码依赖于原项目中的特定模块，需要在当前项目中找到对应的替代方案
- **命名冲突**：检查变量名、函数名等是否与当前项目冲突
- **API 差异**：确认 API 调用方式是否与当前项目一致

## 注意事项

- 个人访问令牌具有访问您账户的权限，请妥善保管，不要泄露
- 大型 commit 可能包含大量变更，分析时请耐心处理
- 迁移代码时应遵循当前项目的编码规范和架构设计
