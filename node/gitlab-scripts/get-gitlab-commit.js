/**
 * GitLab Commit 获取工具
 * 用于获取指定commit的详细信息和变更内容
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

// 配置信息
let token = '';
try {
  // Read GitLab token from local file
  token = fs.readFileSync('/Users/<USER>/Desktop/令牌/gitlab-token.txt', 'utf8').trim();
  if (!token) {
    throw new Error('GitLab 令牌文件内容为空');
  }
} catch (err) {
  console.error('读取 GitLab 令牌失败:', err.message);
  process.exit(1);
}
const config = {
  // GitLab API基础URL
  baseUrl: 'https://g.ktvsky.com/api/v4',
  // 项目ID或路径（URL编码）
  projectId: 'web/tesla_ktv',
  // Commit SHA
  commitSha: '7c869ffb6901d60d9bac8851b750a7ec421bdefe',
  // 个人访问令牌（自动读取）
  privateToken: token,
  // 输出目录
  outputDir: path.join(__dirname, '../commit-analysis')
};

// 创建输出目录
if (!fs.existsSync(config.outputDir)) {
  fs.mkdirSync(config.outputDir, { recursive: true });
}

// API请求头
const headers = {
  'PRIVATE-TOKEN': config.privateToken
};

/**
 * 获取commit详情
 * @returns {Promise<Object>} commit详情
 */
async function getCommitDetails() {
  try {
    const response = await axios.get(
      `${config.baseUrl}/projects/${encodeURIComponent(config.projectId)}/repository/commits/${config.commitSha}`,
      { headers }
    );
    return response.data;
  } catch (error) {
    console.error('获取commit详情失败:', error.message);
    if (error.response) {
      console.error('错误详情:', error.response.data);
    }
    throw error;
  }
}

/**
 * 获取commit变更的文件
 * @returns {Promise<Array>} 变更文件列表
 */
async function getCommitDiff() {
  try {
    const response = await axios.get(
      `${config.baseUrl}/projects/${encodeURIComponent(config.projectId)}/repository/commits/${config.commitSha}/diff`,
      { headers }
    );
    return response.data;
  } catch (error) {
    console.error('获取commit变更失败:', error.message);
    if (error.response) {
      console.error('错误详情:', error.response.data);
    }
    throw error;
  }
}

/**
 * 保存结果到文件
 * @param {string} filename 文件名
 * @param {Object} data 数据
 */
function saveToFile(filename, data) {
  const filePath = path.join(config.outputDir, filename);
  fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf8');
  console.log(`已保存到: ${filePath}`);
}

/**
 * 主函数
 */
async function main() {
  try {
    console.log('开始获取commit信息...');
    
    // 检查访问令牌是否已设置
    if (config.privateToken === '请在此处填入您的个人访问令牌') {
      console.error('错误: 请先在配置中设置您的个人访问令牌');
      return;
    }
    
    // 获取commit详情
    const commitDetails = await getCommitDetails();
    saveToFile('commit-details.json', commitDetails);
    console.log('commit详情:', commitDetails.title);
    
    // 获取变更文件
    const commitDiff = await getCommitDiff();
    saveToFile('commit-diff.json', commitDiff);
    console.log(`变更文件数量: ${commitDiff.length}`);
    
    // 生成分析报告
    const report = {
      commit: commitDetails,
      changedFiles: commitDiff.map(file => ({
        path: file.new_path,
        changeType: file.new_file ? '新增' : file.deleted_file ? '删除' : '修改',
        additions: file.diff.split('\n')
          .filter(line => line.startsWith('+') && !line.startsWith('+++')).length,
        deletions: file.diff.split('\n')
          .filter(line => line.startsWith('-') && !line.startsWith('---')).length
      }))
    };
    
    saveToFile('analysis-report.json', report);
    console.log('分析报告已生成');
    
    // 保存变更内容
    commitDiff.forEach(file => {
      const filename = file.new_path.replace(/\//g, '_');
      saveToFile(`diff_${filename}.txt`, file.diff);
    });
    
    console.log('所有文件已保存，请查看分析结果');
  } catch (error) {
    console.error('执行失败:', error);
  }
}

// 执行主函数
main();