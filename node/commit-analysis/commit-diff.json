[{"old_path": "src/pages/index/index.vue", "new_path": "src/pages/index/index.vue", "a_mode": "100644", "b_mode": "100644", "new_file": false, "renamed_file": false, "deleted_file": false, "diff": "@@ -37,9 +37,10 @@\n             <img :src=\"curTab.image\" v-img-fallback=\"imgFallback\" />\n             <p>{{ curTab.name }}</p>\n           </div>\n-          <div class=\"sheet-list-content-right\" @click.stop>\n+          <div class=\"sheet-list-content-right\">\n             <LoadMore\n               class=\"song-list\"\n+              :class=\"{ 'no-scroll': !canScroll }\"\n               v-if=\"dataList.length\"\n               @load-more=\"fetchData\"\n               safeAreaHeight=\"42vh\"\n@@ -119,6 +120,7 @@ export default {\n     const isExpired = computed(() => !isVip.value && vipInfo.value.expire)\n \n     const fixSongSheet = ref(false)\n+    const canScroll = ref(false)\n \n     let curTab = ref({\n       id: 0,\n@@ -312,6 +314,22 @@ export default {\n       })\n     }\n \n+    // 处理页面滚动事件\n+    const handlePageScroll = () => {\n+      const scrollArea = document.querySelector('.scroll-area')\n+      const tabElement = document.getElementById('refTab')\n+      \n+      if (scrollArea && tabElement) {\n+        // 获取tab元素相对于滚动区域的位置\n+        const tabRect = tabElement.getBoundingClientRect()\n+        const scrollAreaRect = scrollArea.getBoundingClientRect()\n+        \n+        // 当tab元素的顶部位置接近或超过滚动区域的顶部时，允许歌曲列表滚动\n+        canScroll.value = tabRect.top <= scrollAreaRect.top + 30\n+        console.log('canScroll.value', canScroll.value)\n+      }\n+    }\n+\n     let observer = null\n \n     onMounted(() => {\n@@ -326,6 +344,12 @@ export default {\n       if (sheetListContentRef.value) {\n         observer.observe(sheetListContentRef.value)\n       }\n+\n+      // 添加页面滚动监听\n+      const scrollArea = document.querySelector('.scroll-area')\n+      if (scrollArea) {\n+        scrollArea.addEventListener('scroll', handlePageScroll)\n+      }\n     })\n \n     onUnmounted(() => {\n@@ -333,6 +357,12 @@ export default {\n       if (observer) {\n         observer.disconnect()\n       }\n+\n+      // 移除页面滚动监听\n+      const scrollArea = document.querySelector('.scroll-area')\n+      if (scrollArea) {\n+        scrollArea.removeEventListener('scroll', handlePageScroll)\n+      }\n     })\n \n     onMounted(() => {\n@@ -426,6 +456,7 @@ export default {\n       isYSTipAccept,\n       handleSongItemClick,\n       sheetListContentRef,\n+      canScroll,\n     }\n   },\n }\n@@ -597,6 +628,8 @@ export default {\n         flex 1\n         height 100%\n         margin 0 auto\n+        .no-scroll\n+          overflow hidden\n         .song-list\n           width 100% !important\n           padding-bottom 20px\n"}]