import os

def create_api_file(api_doc):
    service_dir = 'service'
    if not os.path.exists(service_dir):
        os.makedirs(service_dir)
    
    for file_name, endpoints in api_doc.items():
        file_path = os.path.join(service_dir, f"{file_name}.js")
        
        with open(file_path, 'w') as f:
            f.write("import httpV2 from '@/utils/httpV2'\n")
            f.write("import get from 'lodash/get'\n")
            f.write("import config from '@/config'\n\n")
            
            for endpoint in endpoints:
                f.write(f"export async function {endpoint['name']}({endpoint['params']}) {{\n")
                if endpoint['method'] == 'GET':
                    url = endpoint['url']
                    if endpoint['params']:
                        url += f"?{endpoint['params']}"
                    f.write(f"  const res = await httpV2.get(`{url}`)\n")
                else:
                    f.write(f"  const res = await httpV2.{endpoint['method'].lower()}('{endpoint['url']}', {{\n")
                    if '_src' in endpoint:
                        f.write("      _src: config.src,\n")
                    for param in endpoint['params'].split(', '):
                        f.write(f"      {param},\n")
                    f.write("  })\n")
                f.write("  // console.log(res)\n")
                if 'return' in endpoint:
                    f.write(f"  return {endpoint['return']}\n")
                else:
                    f.write("  return res\n")
                f.write("}\n\n")

# 示例API文档
api_doc = {
    'signin': [
        {
            'name': 'postSignIn',
            'method': 'POST',
            'url': '/stb/v2/user/sign_in',
            'params': '{ unionid, mac_id }',
            '_src': True
        },
        {
            'name': 'getSignInData',
            'method': 'GET',
            'url': '/stb/v2/user/sign_in',
            'params': '{ unionid }',
            'return': "get(res, 'data', {})"
        },
        {
            'name': 'getSignInActivityStatus',
            'method': 'GET',
            'url': '/stb/v2/user/sign_in_rule',
            'params': '',
            'return': "{\n    errcode: get(res, 'errcode'),  // 200 时有签到活动\n    errmsg: get(res, 'errmsg', ''),\n    data: get(res, 'data', {})\n  }"
        }
    ]
}

create_api_file(api_doc)