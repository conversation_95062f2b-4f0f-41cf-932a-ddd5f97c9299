import os
import sys

def create_vue_component(component_name):
    component_path = f"src/components/{component_name}.vue"
    
    # 确保目录存在
    os.makedirs(os.path.dirname(component_path), exist_ok=True)
    
    template = f"""<template>
  <div class="{component_name.lower()}">
    <h1>{{ title }}</h1>
    <p>{{ message }}</p>
    <button @click="incrementCount">点击次数: {{ count }}</button>
    <ul>
      <li v-for="item in items" :key="item.id">{{ item.name }}</li>
    </ul>
  </div>
</template>

<script>
import {{ ref, reactive, onMounted }} from 'vue'

export default {{
  name: '{component_name}',
  props: {{
    propExample: {{
      type: String,
      default: '默认值'
    }}
  }},
  setup(props, {{ emit }}) {{
    const title = ref('{component_name} 组件')
    const message = ref('这是一个示例组件')
    const count = ref(0)
    const items = reactive([
      {{ id: 1, name: '项目 1' }},
      {{ id: 2, name: '项目 2' }},
      {{ id: 3, name: '项目 3' }}
    ])

    const incrementCount = () => {{
      count.value++
      emit('count-changed', count.value)
    }}

    onMounted(() => {{
      console.log('{component_name} 组件已挂载')
    }})

    return {{
      title,
      message,
      count,
      items,
      incrementCount
    }}
  }}
}}
</script>

<style lang="stylus" scoped>
.{component_name.lower()}
  padding 20px
  background-color #f0f0f0
  border-radius 8px

  h1
    color #333
    font-size 24px

  p
    color #666
    margin-bottom 10px

  button
    background-color #4CAF50
    color white
    padding 10px 15px
    border none
    border-radius 4px
    cursor pointer
    &:hover
      background-color #45a049

  ul
    list-style-type none
    padding 0
    li
      margin-bottom 5px
      padding 5px
      background-color #e0e0e0
      border-radius 4px
</style>
"""
    
    with open(component_path, 'w', encoding='utf-8') as file:
        file.write(template)
    
    print(f"组件 {component_name} 已创建在 {component_path}")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("使用方法: python factory_create_com.py <组件名称>")
        sys.exit(1)
    
    component_name = sys.argv[1]
    create_vue_component(component_name)