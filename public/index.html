<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=no">
    <link rel="icon" href="<%= BASE_URL %>favicon.ico">
    <title><%= htmlWebpackPlugin.options.title %></title>
    <script>
      var _hmt = _hmt || [];
      (function () {
        var hm = document.createElement("script");
        hm.src = "https://hm.baidu.com/hm.js?93a959fbd42b63538a86b19a9c2141cf";
        var s = document.getElementsByTagName("script")[0];
        s.parentNode.insertBefore(hm, s);
      })();
    </script>
    <style type="text/css">
      .skeleton-container {
          width: auto;
          height: 100vh;
          padding: 1.82292vw 4.16667vw;
      }
      .skeleton-top {
          height: 80px;
          margin-bottom: 2.66667vw ;
      }
      .skeleton-nav {
          display: flex;
          justify-content: space-between;
          color: #fff;
          margin-top: 0.83333vw;
          margin-bottom: 3.33333vw;
      }
      .skeleton-nav > div {
          width: 21.35417vw;
          height: 10.41667vw;
          margin-left: 2.08333vw;
      }
      .skeleton-nav > div:nth-child(1) {
          margin-left: 0;
      }
      .skeleton-content {
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
      }
      ._skn_c_box {
          width: 15.625vw;
          margin-bottom: 1.5625vw;
          font-size: 1.45833vw;
      }
      ._skn_c_box_img {
          height: 15.625vw;
          border-radius: 1.04167vw;
          margin-bottom: 1.5625vw;
      }
      ._skn_c_box_txt {
          width: 8vw;
          height: 2vh;
      }
      .skeleton-top > div,.skeleton-nav > div, ._skn_c_box_img, ._skn_c_box_txt {
          background: #ecf0f2;
      }
      @media screen and (max-width: 1200px) {
          .skeleton-container {
              padding: 2.91667vw 6.66667vw;
          }
          .skeleton-nav {
              flex-flow: wrap;
          }
          .skeleton-nav > div {
              width: 41.66667vw;
              height: 16.66667vw;
              margin-left: 0;
          }
          ._skn_n_1, ._skn_n_2 {
              margin-bottom: 2.33333vw;
          }
          ._skn_c_box {
              width: 25vw;
              margin-bottom: 2.5vw;
              height: 372px;
          }
          ._skn_c_box_img {
              width: 25vw;
              height: 25vw;
              margin-bottom: 2.5vw;
          }
          ._skn_c_box_txt {
              width: 12vw;
              height: 8%;
          }
      }
      #app-load {
        width: 70px;
        height: 70px;
        animation: rotating 1s linear infinite;
        position: fixed;
        top: 50%;
        left: 50%;
      }
      @keyframes rotating {
        0%   {transform:translate(-50%, -50%) rotate(0deg)}
        25%  {transform:translate(-50%, -50%) rotate(90deg)}
        50%  {transform:translate(-50%, -50%) rotate(180deg)}
        75%  {transform:translate(-50%, -50%) rotate(270deg)}
        100% {transform:translate(-50%, -50%) rotate(360deg)}
      }
    </style>
  </head>
  <body>
    <script>
      window.__bl = {
        config: {
          pid: "cgbd80rba0@5a2df2979b45fe0",
          appType: "web",
          imgUrl: "https://arms-retcode.aliyuncs.com/r.png?",
          sendResource: true,
          enableLinkTrace: true,
          behavior: true,
          useFmp:true,
          enableSPA:true,
          release: '<%= VUE_APP_BL_RELEASE %>',
          environment: '<%= VUE_APP_ENVIRONMENT %>'
        }
      }
    </script>
    <script type="text/javascript" src="https://retcode.alicdn.com/retcode/bl.js" crossorigin></script>
    <noscript>
      <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app">
        <!-- 骨架屏 -->
        <div class="skeleton-container">
            <div class="skeleton-top">
                <div style="width:15.75vw;height:100%;float: left;"></div>
                <div style="width:44.16667vw;height:100%;float: left;margin-left: 2.5vw;"></div>
                <div style="width:16.66667vw;height:100%;float: right;"></div>
            </div>
            <div class="skeleton-nav">
                <div class="_skn_n_1"></div>
                <div class="_skn_n_2"></div>
                <div class="_skn_n_3"></div>
                <div class="_skn_n_4"></div>
            </div>
            <div class="skeleton-content">
                <div class="_skn_c_box">
                    <div class="_skn_c_box_img"></div>
                    <div class="_skn_c_box_txt"></div>
                </div>
                <div class="_skn_c_box">
                    <div class="_skn_c_box_img"></div>
                    <div class="_skn_c_box_txt"></div>
                </div>
                <div class="_skn_c_box">
                    <div class="_skn_c_box_img"></div>
                    <div class="_skn_c_box_txt"></div>
                </div>
                <div class="_skn_c_box">
                    <div class="_skn_c_box_img"></div>
                    <div class="_skn_c_box_txt"></div>
                </div>
                <div class="_skn_c_box">
                    <div class="_skn_c_box_img"></div>
                    <div class="_skn_c_box_txt"></div>
                </div>
                <div class="_skn_c_box">
                    <div class="_skn_c_box_img"></div>
                    <div class="_skn_c_box_txt"></div>
                </div>
                <div class="_skn_c_box">
                    <div class="_skn_c_box_img"></div>
                    <div class="_skn_c_box_txt"></div>
                </div>
                <div class="_skn_c_box">
                    <div class="_skn_c_box_img"></div>
                    <div class="_skn_c_box_txt"></div>
                </div>
                <div class="_skn_c_box">
                    <div class="_skn_c_box_img"></div>
                    <div class="_skn_c_box_txt"></div>
                </div>
                <div class="_skn_c_box">
                    <div class="_skn_c_box_img"></div>
                    <div class="_skn_c_box_txt"></div>
                </div>
                <div class="_skn_c_box">
                    <div class="_skn_c_box_img"></div>
                    <div class="_skn_c_box_txt"></div>
                </div>
                <div class="_skn_c_box">
                    <div class="_skn_c_box_img"></div>
                    <div class="_skn_c_box_txt"></div>
                </div>
                <div class="_skn_c_box">
                    <div class="_skn_c_box_img"></div>
                    <div class="_skn_c_box_txt"></div>
                </div>
                <div class="_skn_c_box">
                    <div class="_skn_c_box_img"></div>
                    <div class="_skn_c_box_txt"></div>
                </div>
                <div class="_skn_c_box">
                    <div class="_skn_c_box_img"></div>
                    <div class="_skn_c_box_txt"></div>
                </div>
                <div class="_skn_c_box">
                    <div class="_skn_c_box_img"></div>
                    <div class="_skn_c_box_txt"></div>
                </div>
            </div>
        </div>
    </div>
    <!-- built files will be auto injected -->
    <!-- 启动loading -->
    <img id="app-load" src="https://qncweb.ktvsky.com/20211217/vadd/0c639d301d8cb12ef9058fef77643c61.png" alt="">
  </body>
</html>
