{"name": "tesla-ktv", "version": "0.1.0", "private": true, "scripts": {"build": "vue-cli-service build", "stage": "vue-cli-service build --mode stage", "lint": "vue-cli-service lint", "dev": "vue-cli-service serve"}, "dependencies": {"axios": "^0.24.0", "axios-retry": "^3.2.4", "babel-plugin-import": "^1.13.3", "core-js": "^3.6.5", "crypto-js": "^4.1.1", "css-init": "^1.1.9", "date-fns": "^2.26.0", "events": "^3.3.0", "hls.js": "^1.1.1", "lodash": "^4.17.21", "nanoid": "^3.1.30", "nats.ws": "^1.16.1", "qrcode": "^1.5.0", "query-string": "^7.0.1", "register-service-worker": "^1.7.1", "store2": "^2.12.0", "style-resources-loader": "^1.4.1", "ua-parser-js": "^1.0.2", "v3-infinite-loading": "^1.0.1", "vant": "3", "vue": "^3.0.0", "vue-loading-overlay": "^4.0", "vue-progressive-image": "^4.0.0", "vue-router": "^4.0.0-0", "vue3-carousel": "^0.3.3", "vue3-marquee": "^4.1.0", "vue3-perfect-scrollbar": "^1.6.1", "vue3-touch-events": "^4.1.8", "vuex": "^4.0.0-0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-pwa": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.0.0", "@vue/eslint-config-prettier": "^6.0.0", "autoprefixer": "8.0.0", "babel-eslint": "^10.1.0", "babel-plugin-transform-remove-console": "^6.9.4", "eruda": "^2.4.1", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^7.0.0", "git-rev-sync": "^3.0.2", "postcss": "^8.4.5", "postcss-aspect-ratio-mini": "^1.0.1", "postcss-cssnext": "^3.1.0", "postcss-import": "^12.0.1", "postcss-px-to-viewport": "^1.1.1", "postcss-url": "^8.0.0", "postcss-viewport-units": "^0.1.6", "prettier": "^2.2.1", "stylus": "^0.54.7", "stylus-loader": "^3.0.2", "vue-loader-v16": "^16.0.0-beta.5.4", "worker-plugin": "^5.0.1"}}