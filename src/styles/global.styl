*
  color inherit

.page
  background rgba(0, 0, 0, 1)
  padding 35px 80px
  padding-top 164px
  font-size 28px
  min-height 100vh
  @media screen and (max-width 1200px) and (min-height 1100px)
    padding-left 60px
    padding-right 60px
    padding-top 140px
video
  &:focus
    outline none !important
:root
  --van-warning-color #fff !important
  --van-button-warning-background-color #fff !important
  --van-button-warning-border-color #fff !important
@media screen and (max-width 1200px) and (min-height 1200px)
  .user-vip-year-num
    font-size 64px !important

.horizontal
  display block
  @media screen and (max-width 1200px) and (min-height 1200px)
    display none !important
.vertical
  display none
  @media screen and (max-width 1200px) and (min-height 1200px)
    display block !important

.dis_flex
  display flex
.align_center
  align-items center