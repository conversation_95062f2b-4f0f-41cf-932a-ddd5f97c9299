*
  color inherit
  ::-webkit-scrollbar
    display none
.page
  background rgba(0, 0, 0, 1)
  padding 35px 80px
  padding-top 164px
  font-size 28px
  min-height 100vh
  @media screen and (max-width 1200px) and (min-height 1100px)
    padding-left 60px
    padding-right 60px
    padding-top 140px
video
  &:focus
    outline none !important
:root
  --van-warning-color #fff !important
  --van-button-warning-background-color #fff !important
  --van-button-warning-border-color #fff !important
@media screen and (max-width 1200px) and (min-height 1200px)
  .user-vip-year-num
    font-size 64px !important
.themeDark
  .page
    // background rgb(238, 232, 232) !important
.themeLight
  .page
    background linear-gradient(to bottom, #f4f5f8 0%, #f4f5f8 30%, #e2e5ed 100%) !important
    &.mv
      background none !important
  .search-bar
    background linear-gradient(#fff 0%, #f4f5f8 100%) !important
    .search-bar-tool
      border 2px solid rgba(29, 29, 31, 0.2)
    .already-order
      border 2px solid rgba(29, 29, 31, 0.2) !important
      span
        background: #7A53E7 !important
        color #FFFFFF !important
      p
        color rgba(29, 29, 31, 0.9) !important
    .mv-enter
      border 2px solid rgba(29, 29, 31, 0.2) !important
      p
        color rgba(29, 29, 31, 0.9) !important
  .bottom-bar
    background #DFE2EB !important
    .bottom-bar-lyc
      color rgba(29, 29, 31, 0.2) !important
      .active-scan
        background rgba(29, 29, 31, 0.2) -webkit-linear-gradient(left, rgba(29, 29, 31, 1), rgba(29, 29, 31, 1)) no-repeat 0 0
        -webkit-text-fill-color transparent
        -webkit-background-clip text
        background-size 0 100%
  .section-container
    .section-container-header
      .section-container-header-title
        color rgba(29, 29, 31, 0.9) !important
      .sec-gusse-sing-change
        span
          color rgba(29, 29, 31, 0.9) !important
        img
          width 27px
          height 25px
  .song-item
    border-bottom 2px solid rgba(29,29,31,0.1)
    .sing-cnt
      span
        color #7A53E7!important
    .name
      color rgba(29, 29, 31, 0.9) !important
    .desc
      color rgba(29, 29, 31, 0.5) !important
      .author
        color rgba(29, 29, 31, 0.5) !important
      .divider, .album
        color #1D1D1F80 !important
      .clickable
        &::after
          background url('https://qncweb.ktvsky.com/20240221/vadd/6de21984c0d694c97fa35d7cc86c1cea.png') no-repeat !important
          background-size: 22px 22px !important
    .right
      .order-btn
        color rgba(255, 255, 255, 1)
        background rgba(122, 83, 231, 1)
  .song-block
    background rgba(255, 255, 255, 0.8) !important
    .sing-cnt
      span
        color #7A53E7!important
    .name
      color rgba(29, 29, 31, 0.9) !important
    .desc
      color rgba(29, 29, 31, 0.5) !important
      .author
        color rgba(29, 29, 31, 0.5) !important
      .divider
        color rgba(29, 29, 31, 0.9) !important
      .clickable
        &::after
          background url('https://qncweb.ktvsky.com/20240221/vadd/6de21984c0d694c97fa35d7cc86c1cea.png') no-repeat !important
          background-size: 22px 22px !important
  .order-song-item, .already-song-item
    .info
      .name
        color rgba(29, 29, 31, 0.9) !important
      .singer, .flag
        color rgba(29, 29, 31, 0.5) !important
      .divide
        background rgba(29, 29, 31, 0.5) !important
      .clickable
        &::after
          background url('https://qncweb.ktvsky.com/20240221/vadd/6de21984c0d694c97fa35d7cc86c1cea.png') no-repeat !important
          background-size: 22px 22px !important
  .order-song-item.playing .info, .already-song-item .info.ordered
    .name, .singer, .flag
      color rgba(122, 83, 231, 1) !important
    .clickable::after
      content ""
      background-image url('https://qncweb.ktvsky.com/20240221/vadd/eefeaa2f2cbf6e16fa5c2d9abd62818f.png') !important
      background-size: 22px 22px !important
    .divide
      background rgba(122, 83, 231, 1) !important
  .song-block.ordered, .song-item .ordered
    .name, .desc, .author, .singer, .divide, .divider, .flag, .album
      color rgba(122, 83, 231, 1) !important
    .sing-cnt
      color rgba(122, 83, 231, 1) !important
      span
        color rgba(122, 83, 231, 1) !important
    .clickable
      position relative
    .clickable::after
      content ""
      position: absolute
      right: 0
      top 50%
      margin-top: -11px !important
      width 22px
      height 22px
      background-image url('https://qncweb.ktvsky.com/20240221/vadd/eefeaa2f2cbf6e16fa5c2d9abd62818f.png') !important
      background-size: 22px 22px !important
      background-position center center
  .order-song-control-panel
    background linear-gradient(180deg, #FFFFFF 0%, #E1E5EE 38.12%) !important
    .header
      border-bottom 2px solid rgba(29, 29, 31, 0.1)
      .tab
        .tab-item
          color rgba(29, 29, 31, 0.5) !important
        .actived
          color rgba(122, 83, 231, 1) !important
          border-bottom 2px solid rgba(122, 83, 231, 1) !important
.themeSystem
  .page
    background transparent !important
.thunder-modal-container
  @media screen and (max-height 650px)
    zoom 0.85
.net-error
  width 214px
  height 214px
  display flex
  flex-direction column
  justify-content center
  align-items center
  border-radius 12px
  img, svg
    width 90px !important
    height 90px !important
  .active
    animation rotate 1s linear infinite
  p
    height 28px
    line-height 28px
    font-size 24px
    color rgba(0, 0, 0, 0.40)!important
    &:nth-child(2)
      margin 20px 0 8px
  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
.avatar-default-wrapper
  background url('../assets/singer-dark.png') no-repeat
  background-size 100%
  border-radius 50%
  overflow hidden
  div
    width: 100%
    height: 100%
    background-size: 100% auto
.theme-themeLight
  .avatar-default-wrapper
    background-image url('../assets/singer-light.png')
.v-progressive-image, .v-progressive-image-placeholder
  width: 100%
  height: 100%
  overflow hidden
.v-progressive-image
  max-width 100vw!important
  & > div
    padding-bottom 0!important
    width: 100%
    height: 100%
  &-main
    width: 100%
    height: 100%

.order-song-item, .already-song-item, .song-item
  .flag
    white-space nowrap