import axios from 'axios';
import getHttpConfig from './request/config';
import getRequestCommonParams from './request/request-common-params';
import { showErrorToast, reportError } from './request/error-handler';
import store from '@/store'
import { Dialog } from 'vant';

const http = axios.create(
  getHttpConfig({
    urls: {
      pro: '//r.ktvsky.com',
      pre: '//x.ktvsky.com',
      test: '//r.stage.ktvsky.com',
    },
  })
);


http.interceptors.request.use((request) => {
  if (!store.state.base.net_status) {
    store.commit('base/SET_NET_LOADING', true)
  }
  if (request.method === 'POST') {
    if (!request.url.includes('/stb')) {
      request.headers['Content-Type'] = 'application/x-www-form-urlencoded';
    }
  }
  if (request.url.includes('/stb') || request.url.includes('carplay')) {
    const commonParams = getRequestCommonParams();
    request.params = {
      ...request.params,
      ...commonParams,
    };
  }
  return request;
});

http.interceptors.response.use(
  (response) => {
    store.commit('base/SET_NET_STATUS', true)
    store.commit('base/SET_NET_LOADING', false)
    const res = response.data;
    if (res.errcode === 200 || res.errcode === 21001 || res.code === 200) {
      return res;
    }
    showErrorToast(res.errmsg);
    return res;
  },
  (error) => {
    reportError(error);
    if (error.toJSON().message.includes('timeout of ')) {
      console.log('Network Error', error.toJSON().config.url, error.toJSON().message, error.toJSON())
      store.commit('base/SET_NET_STATUS', false)
      store.commit('base/SET_NET_LOADING', false)

      if (!store.state.mvIsHide) return

      Dialog({
        title: '提示',
        className: 'global-force-login net-error-dialog',
        confirmButtonText: '我知道了',
        showCancelButton: false,
        message: '当前网络状态不佳，请检查网络', // 歌曲下载出现了点问题，请重新加载
        zIndex: 10000,
        transition: false // 禁用动画效果
      })
      .then(() => {
        store.commit('base/SET_NET_LOADING', false)

        const isHomePage =  /^#\/(\?.*)?$/.test(window.location.hash)
        if (!document.querySelector('#home') && isHomePage) {
          window.TSNative.exit()
        }
      });
      return Promise.reject(error); // 确保所有的Promise错误都被正确处理
    }
    showErrorToast('网络异常, 请稍后重试');
    return Promise.reject(error); // 确保所有的Promise错误都被正确处理
  }
);

export default http;
