//后续看情况确定是否需要加密存储信息
import store from '@/store'
import store2 from 'store2'
import _ from 'lodash'
import { parseISO, differenceInDays, format } from 'date-fns'
// import { TSConfigInstance } from '@/packages/TSJsbridge/TSConfig'
import { openDatabase, setItem, getItem, deleteItem } from '@/utils/IndexedDB.js';

// 新增 storageYSTipAccept 存储方法
const setStorageYSTipAccept = (value) => {
  // 优先使用 window.TSConfig.setPreference 存储
  if (window.TSConfig && window.TSConfig.setPreference) {
    window.TSConfig.setPreference('storageYSTipAccept', JSON.stringify(value));
    console.log('storageYSTipAccept 数据已通过 TSConfig.setPreference 存储');
  } else {
    // 降级使用 localStorage 存储
    localStorage.setItem('storageYSTipAccept', JSON.stringify(value));
    console.log('storageYSTipAccept 数据已通过 localStorage 存储');
  }
};

// 获取 storageYSTipAccept 数据
function getStorageYSTipAccept() {
  try {
    let value = false; // 默认值为 false

    // 优先从 window.TSConfig.getPreference 获取数据
    if (window.TSConfig && window.TSConfig.getPreference) {
      const storedData = window.TSConfig.getPreference('storageYSTipAccept');
      if (storedData !== null && storedData !== undefined) {
        value = JSON.parse(storedData);
      } else {
        // 如果 TSConfig 中没有数据，尝试从 localStorage 获取
        const localStorageData = localStorage.getItem('storageYSTipAccept');
        if (localStorageData !== null && localStorageData !== undefined) {
          value = JSON.parse(localStorageData);
          localStorage.removeItem('storageYSTipAccept'); // 清理 localStorage 中的数据

          // 将获取到的数据保存到 TSConfig 中
          window.TSConfig.setPreference('storageYSTipAccept', JSON.stringify(value));
        }
      }
    } else {
      // 如果 TSConfig 不可用，直接从 localStorage 获取数据
      const localStorageData = localStorage.getItem('storageYSTipAccept');
      if (localStorageData !== null && localStorageData !== undefined) {
        value = JSON.parse(localStorageData);
      }
    }

    return value; // 返回布尔值
  } catch (error) {
    console.error('获取 storageYSTipAccept 数据时出错:', error);
    return false; // 出错时返回默认值 false
  }
}

const setAlreadyList = _.debounce((data) => {
  let time = new Date();
  let alreadyList = data || [...store.state.alreadyList];
  if (alreadyList.length >= 99) {
    alreadyList = alreadyList.slice(0, 99);
  }

  if (window.TSConfig && window.TSConfig.setPreference) {
    window.TSConfig.setPreference('alreadyData', JSON.stringify({ alreadyList, time }));
    console.log('Data saved using TSConfig.setPreference');
  } else {
    openDatabase().then(() => {
      setItem('alreadyData', { alreadyList, time }).then(console.log).catch(console.error);
    }).catch(console.error);
  }
}, 500);

const setOrderedList = _.debounce((data) => {
  let time = new Date();
  let orderedList = data || [...store.state.orderedList];
  if (orderedList.length >= 99) {
    orderedList = orderedList.slice(0, 99);
  }

  if (window.TSConfig && window.TSConfig.setPreference) {
    window.TSConfig.setPreference('orderedData', JSON.stringify({ orderedList, time }));
    console.log('Data saved using TSConfig.setPreference');
  } else {
    openDatabase().then(() => {
      setItem('orderedData', { orderedList, time }).then(console.log).catch(console.error);
    }).catch(console.error);
  }
}, 500);

async function getAlreadyList() {
  try {
    let alreadyList = [];
    let time = null;

    if (window.TSConfig && window.TSConfig.getPreference) {
      const storedData = window.TSConfig.getPreference('alreadyData');
      if (storedData) {
        const parsedData = JSON.parse(storedData);
        alreadyList = parsedData.alreadyList || [];
        time = parsedData.time;
      } else {
        // If TSConfig.getPreference exists but no data, try to get from IndexedDB or localStorage
        const dbData = await getItem('alreadyData');
        const localStorageData = localStorage.getItem('alreadyData');

        if (dbData) {
          alreadyList = dbData.alreadyList || [];
          time = dbData.time;
          await deleteItem('alreadyData'); // Clear IndexedDB
        } else if (localStorageData) {
          const parsedLocalStorageData = JSON.parse(localStorageData);
          alreadyList = parsedLocalStorageData.alreadyList || [];
          time = parsedLocalStorageData.time;
          localStorage.removeItem('alreadyData'); // Clear localStorage
        }

        // Save the retrieved data to TSConfig
        if (alreadyList.length > 0) {
          window.TSConfig.setPreference('alreadyData', JSON.stringify({ alreadyList, time }));
        }
      }
    } else {
      const db = await openDatabase();
      const storedData = await getItem('alreadyData');
      alreadyList = storedData?.alreadyList || [];
      time = storedData?.time || 0;
    }

    // const currDate = new Date();
    // if (time && differenceInDays(currDate, parseISO(time)) > 30) {
    //   setAlreadyList([]);
    //   return [];
    // }
    return alreadyList.filter(item => item?.music_name !== '');
  } catch (error) {
    console.error('Error in getAlreadyList:', error);
    return [];
  }
}

async function getOrderedList() {
  try {
    let orderedList = [];
    let time = null;

    if (window.TSConfig && window.TSConfig.getPreference) {
      const storedData = window.TSConfig.getPreference('orderedData');
      if (storedData) {
        const parsedData = JSON.parse(storedData);
        orderedList = parsedData.orderedList || [];
        time = parsedData.time;
      } else {
        // If TSConfig.getPreference exists but no data, try to get from IndexedDB or localStorage
        const dbData = await getItem('orderedData');
        const localStorageData = localStorage.getItem('orderedData');

        if (dbData) {
          orderedList = dbData.orderedList || [];
          time = dbData.time;
          await deleteItem('orderedData'); // Clear IndexedDB
        } else if (localStorageData) {
          const parsedLocalStorageData = JSON.parse(localStorageData);
          orderedList = parsedLocalStorageData.orderedList || [];
          time = parsedLocalStorageData.time;
          localStorage.removeItem('orderedData'); // Clear localStorage
        }

        // Save the retrieved data to TSConfig
        if (orderedList.length > 0) {
          window.TSConfig.setPreference('orderedData', JSON.stringify({ orderedList, time }));
        }
      }
    } else {
      await openDatabase();
      const storedData = await getItem('orderedData');
      orderedList = storedData?.orderedList || [];
      time = storedData?.time || 0;
      console.log('1204', orderedList)
    }


    // const currDate = new Date();
    // if (time && differenceInDays(currDate, parseISO(time)) > 30) {
    //   setOrderedList([]);
    //   return [];
    // }
    return orderedList || [];
  } catch (error) {
    console.error('Error in getOrderedList:', error);
    return [];
  }
}

/**
 * 开屏弹窗销售麦克风
 * 一天只弹一次，每天第一次打开弹出
 */
const setMicActivityTag = (t) => {
  store2('micActivityTag', {
    time: t
  })
}
/**
 * 开屏弹窗销售VIP
 * 一天只弹一次，每天第二次打开弹出
 */
const setVipActivityTag = (t) => {
  store2('vipModalActivityTag', {
    time: t
  })
}

// 通过micActivityTag字段判断当天是否已打开一次
const getVipActivityShowTag = () => {
  const currDate = format(Date.now(), 'yyyy-MM-dd')
  if (!store2('vipModalActivityTag')) {
    setVipActivityTag(currDate)
    return true
  }
  const { time } = store2('vipModalActivityTag')
  if (currDate !== time) {
    setVipActivityTag(currDate)
    return true
  }
  return false
}

// A/B Test 本地存储规则：从2023/7/7往后推算每天的版本 - ABBAABBAA...
const setABTestTag = (str) => {
  store2('abTestTag', str)
}

const getABTestTag = () => {
  const abTest = store2('abTestTag')
  // 已标记过的用户不再标记
  if (abTest) {
    return abTest
  }
  const currDate = format(Date.now(), 'yyyy-MM-dd')
  const ruleStartTime = '2023-07-08'
  if (currDate === ruleStartTime) {
    setABTestTag('A')
    return 'A'
  }
  // 推算当天版本
  const verObj = {
    1: 'B',
    2: 'B',
    3: 'A',
    0: 'A'
  }
  const length = Math.abs(differenceInDays(parseISO(currDate), parseISO(ruleStartTime)))
  const currver = verObj[length % 4]
  setABTestTag(currver)
  console.log('how many day?', length, currver)

  return currver
}

const setSearchCache = (data) => {
  let searchCache = data || [...store.state.search.searchCache]
  if (searchCache.length > 10) {
    searchCache = searchCache.slice(0, 10)
  }
  store2('searchCache', searchCache || [])
}
const getSearchCache = () => {
  if (!store2('searchCache')) return []
  return store2('searchCache')
}

// 搜索词条 - 一键演唱歌曲
const setSearchSong = (data) => {
  store2('searchSong', data)
}

const getSearchSong = () => {
  if (!store2('searchSong')) return []
  return store2('searchSong')
}

// 过期会员提醒 - 一天只弹一次
const setVipExpireTag = () => {
  const currDate = format(Date.now(), 'yyyy-MM-dd')
  store2('vipExpireModalTag', {
    time: currDate
  })
}

const getVipExpireTag = () => {
  const currDate = format(Date.now(), 'yyyy-MM-dd')
  if (!store2('vipExpireModalTag')) {
    return true
  }
  const { time } = store2('vipExpireModalTag')
  if (currDate !== time) {
    return true
  }
  return false
}

// // 低价激活用户运营支付弹窗 - 一天只弹一次
// const setOperationPopupPay = () => {
//   const currDate = format(Date.now(), 'yyyy-MM-dd')
//   store2('OperationPopupPay', {
//     time: currDate
//   })
// }

// const getOperationPopupPay = () => {
//   const currDate = format(Date.now(), 'yyyy-MM-dd')
//   if (!store2('OperationPopupPay')) {
//     return true
//   }
//   const { time = '' } = store2('OperationPopupPay')
//   if (currDate !== time) {
//     return true
//   }
//   return false
// }

export {
  setAlreadyList,
  setOrderedList,
  getAlreadyList,
  getOrderedList,
  // setLoginSendVipActivityTag,
  // getLoginSendVipActivityTag,
  setMicActivityTag,
  // getMicActivityShowTag,
  setVipActivityTag,
  getVipActivityShowTag,
  getABTestTag,
  setSearchCache,
  getSearchCache,
  setSearchSong,
  getSearchSong,
  setVipExpireTag,
  getVipExpireTag,
  setStorageYSTipAccept,
  getStorageYSTipAccept,
}