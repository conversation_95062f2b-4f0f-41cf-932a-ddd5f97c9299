import store from '@/store';
import store2 from 'store2'
import { stringify, parse } from 'query-string';
import md5 from 'crypto-js/md5';
import appConfig from '@/config'
import get from 'lodash/get';


function getCurrentTime () {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}
export default function getRequestCommonParams(salt = '1111111111', handler) {
  const searchObj = parse(location.search);
  const hashObj = parse(location.hash)
  const _vc = appConfig.vc;
  const _vn = appConfig.vn;
  const _src = get(searchObj, '_src', '') || get(hashObj, '_src', '') || store.state.system.systemInfo.src || appConfig.src;
  const commonParams = {
    _m: store.state.system.systemInfo.model || '', // undefined时传空
    _mac: store.state.system.systemInfo.mac,
    _manuf: store.state.system.systemInfo.brand,
    _p: 1, // 服务端要求写死为1
    _src: String(_src).length !== 6 ? '600232' : _src,
    _session: store2('_boot_id'),
    _ts: Date.now(),
    _vc,
    _vn,
    ktv_id: '',
    t: Date.now() / 1000,
    // _tm: getCurrentTime(),
  };
  let queryStringStr = stringify(commonParams);
  if (typeof handler === 'function') {
    queryStringStr = handler(queryStringStr);
  }
  const cipertext = md5(salt + queryStringStr);
  return {
    ...commonParams,
    _s: cipertext.toString(),
    _lang: 'cn',
    _play_type: get(store.state, 'system.audioEffects.play_type', ''),
    _speaker_num: get(store.state, 'system.audioEffects.speaker_num', ''),
    _car_name: get(store.state, 'system.car_name', ''),
    _mic_type: get(store.state, 'system.audioEffects.mic_type', ''),
  };
}
