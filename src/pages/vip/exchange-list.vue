<template>
  <div class="page exchange-list-page">
    <HeadBar class="headbar" title="兑换记录"></HeadBar>
    <LoadMore
      class="exchange-list"
      v-if="exchangeHistoryList.length"
      @load-more="handleGetExchangeHistoryList"
      safeAreaHeight="14.6991vw"
    >
      <div
        class="exchange-list-item"
        v-for="(history, index) in exchangeHistoryList"
        :key="index"
      >
        <div class="up">
          <div class="cdkey">
            <div class="cdkey-value">{{ history.pkg_desc }}</div>
            <div class="cdkey-endtime">{{ history.create_time }}</div>
          </div>
          <div class="days">
            <span>{{ history.days }}</span>天
          </div>
        </div>
      </div>
    </LoadMore>
    <div v-else class="exchange-empty">
      您现在还没有记录哦～
    </div>
    <!-- <order-btn></order-btn>
    <order-list v-if="isOrderedListShow"></order-list>
    <LoginModal v-if="carplayInfo.login_qr"></LoginModal> -->
  </div>
</template>

<script>
import { getExchangeHistory } from '@/service/vip'
import { useStore } from 'vuex'
import { ref, computed, onBeforeMount } from 'vue'

export default {
  name: 'ExchangeList',
  setup() {
    const store = useStore()
    const userInfo = computed(() => store.state.userInfo)

    let exchangeHistoryList = ref([])
    let p = 1
    let isRequest = false

    const handleGetExchangeHistoryList = async () => {
      if (isRequest) {
        return
      }
      isRequest = true

      const historyResponseData = await getExchangeHistory(userInfo.value.unionid, p)
      if (historyResponseData.length) {
        exchangeHistoryList.value = exchangeHistoryList.value.concat(historyResponseData)
        p++
      }
      isRequest = false
    }

    onBeforeMount(handleGetExchangeHistoryList)
    
    return {
      exchangeHistoryList,
      handleGetExchangeHistoryList
    }
  }
}
</script>

<style lang="stylus" scoped>
.exchange-list-page
  color #ffffff
  .exchange-empty
    height: 90vh
    display: flex
    justify-content: center
    align-items: center
    // margin-top 384px
    color rgba(255, 255, 255, 0.4)
    font-size: 28px
  .exchange-list
    padding-top 35px
    &-item
      display flex
      align-content: center
      justify-content: space-between
      box-sizing: content-box
      height: 136px
      // &:not(:first-child)
      //   margin-top: 29px
      &:not(:last-child)
        border-bottom 1px solid rgba(255, 255, 255, 0.4)
      &:last-child
        padding-bottom 120px
      .up
        display: flex
        justify-content: space-between
        align-items: center
        width: 100%
        color rgba(255, 255, 255, .8)
        font-size: 28px
        .days
          span
            font-weight 900
        .cdkey
          &-value
            color rgba(255, 255, 255, .8)
            margin-bottom 2px
          &-endtime
            color rgba(255, 255, 255, .4)
            font-size: 24px
</style>
