<template>
  <div class="page exchange-page">
    <HeadBar class="headbar" title="兑换VIP"></HeadBar>
    <div class="exchange">
      <div class="exchange-input">
        <input
          v-model="cdkey"
          placeholder="请输入16位兑换码，注意区分大小写"
          maxlength="16"
          type="text"
          @input="handleFilterInput"
          @mousedown="disableDoubleClickSelection"
          @keydown="handleSearchKeydown($event)"
          autocapitalize="off"
          ref="exchangeInput"
        />
      </div>
      <div
        @click="handleExchange"
        class="exchange-btn"
        :class="enable && 'enable'"
      >
        确认兑换
      </div>
      <div class="exchange-rule">
        <div class="title">VIP兑换规则说明:</div>
        <div class="content">
          <div>1、兑换成功后可获得相应天数的雷石ktv会员服务。</div>
          <div>2、兑换码一次只能兑换一张，不可叠加兑换。</div>
        </div>
      </div>
      <Record from="exchange" />
    </div>
    <RenewVipModal v-if="isShowVipModal" @close="handleCloseVipModal" />
  </div>
</template>

<script>
import get from 'lodash/get'
import { useStore } from 'vuex'
import { ref, computed } from 'vue'
import { Dialog } from 'vant'
import Toast from '@/utils/toast'
import RenewVipModal from '@/components/teleport/renew-vip'
import Record from '@/pages/mine/components/record'
import eventBus from '@/utils/event-bus'
import { sendLog } from '@/directives/v-log/log'

export default {
  name: 'Exchange',
  components: {
    RenewVipModal,
    Record,
  },
  setup() {
    const store = useStore()

    let cdkey = ref('')
    const userInfo = computed(() => store.state.userInfo)
    const macAddress = computed(() => store.state.system.systemInfo.mac)
    const enable = ref(false)

    let isShowVipModal = ref(false)
    const exchangeInput = ref(null)

    const handleFilterInput = () => {
      if (!cdkey.value) {
        enable.value = false
        return
      }
      // 此处在特定输入情况下 会出现空格 所以在此处增加去掉空格操作
      cdkey.value = cdkey.value.trim();
      // eslint-disable-next-line
      if (!/^[A-Za-z0-9]+$/gi.test(cdkey.value)) {
        Toast('兑换码格式错误，请检查后重新输入')
        cdkey.value = ''
        enable.value = false
      } else if (cdkey.value.length === 16) {
        enable.value = true
      } else {
        enable.value = false
      }
    }

    const handleExchange = async () => {
      if (!enable.value) return
      if (cdkey.value.length != 16) {
        Toast('请输入16位兑换码')
        enable.value = false
        return
      }
      const res = await store.dispatch('exchangeVip', {
        cdkey: cdkey.value,
        mac_id: macAddress.value,
      })
      if (get(res, 'errmsg')) {
        enable.value = false
        Dialog.confirm({
          title: '兑换成功',
          confirmButtonText: '知道了',
          showCancelButton: false,
          message: `恭喜您，成功开通VIP\n会员有效期：${
            res.data.start_time.split(' ')[0]
          }-${res.data.end_time.split(' ')[0]}`,
        }).then(() => {
          handleShowVipModal()

          sendLog({
            event_type: '10000~50000',
            event_name: 6008,
            event_data: {
              str1: '我的页',
              str2: '兑换VIP',
              str3: '点击兑换VIP按钮',
              str4: 'click',
              str5: 1
            }
          })
        })
        cdkey.value = ''
        eventBus.emit('exchange-success')
      }else{
        sendLog({
          event_type: '10000~50000',
          event_name: 6008,
          event_data: {
            str1: '我的页',
            str2: '兑换VIP',
            str3: '点击兑换VIP按钮',
            str4: 'click',
            str5: 2
          },
        })
      }
      exchangeInput.value.blur()
    }

    const handleCloseVipModal = () => {
      isShowVipModal.value = false
    }

    const handleShowVipModal = () => {
      isShowVipModal.value = true
    }

    const disableDoubleClickSelection = (event) => {
      if (event.detail > 1) {
        event.preventDefault();
      }
    }

    const handleSearchKeydown = (e) => {
      if (e.keyCode == 13) {
        exchangeInput.value.blur()
        handleExchange()
      }
    }

    return {
      cdkey,
      userInfo,
      isShowVipModal,
      handleExchange,
      handleFilterInput,
      handleCloseVipModal,
      enable,
      disableDoubleClickSelection,
      handleSearchKeydown,
      exchangeInput,
    }
  },
}
</script>

<style lang="stylus" scoped>
.exchange-page
  color #ffffff
.exchange
  margin-top 35px
  height calc(100vh - 164px)
  overflow-y scroll
  ::-webkit-scrollbar
    display none
  @media screen and (max-width: 1950px) and (min-width: 1700px) and (max-height: 720px) and (min-height: 650px)
    margin-top -20px !important
  &-input
    display flex
    font-size 32px
    margin 0 auto
    color rgba(255, 255, 255, 0.8)
    @media screen and (max-width 1200px) and (min-height 1000px)
      padding-left 0 !important
    @media screen and (max-width: 1950px) and (min-width: 1700px) and (max-height: 720px) and (min-height: 650px)
      zoom 0.9
    input
      margin 0 auto
      width 1000px
      height 128px
      border-radius 4px
      border 2px solid rgba(255, 255, 255, 0.2)
      padding 0 30px
      @media screen and (max-width 1200px) and (min-height 1000px)
        width 832px
        font-size 26px
  &-btn
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.10);
    backdrop-filter: blur(100px);
    width: 300px;
    height: 80px;
    font-size: 28px;
    color rgba(255, 255, 255, 0.2)
    display flex
    justify-content center
    align-items center
    margin 60px auto 0
    &.enable
      background: #E3AB5D;
      color: rgba(0, 0, 0, 0.80);
    @media screen and (max-width 1200px)
      width: 240px;
      height: 64px;
      font-size: 22px;
      margin-top 48px;
  &-rule
    font-size 28px
    margin-top 120px
    color rgba(255, 255, 255, 0.4)
    @media screen and (max-width: 1950px) and (min-width: 1700px) and (max-height: 720px) and (min-height: 650px)
      margin-top 0px !important
    .title
      font-size: 32px;
      color rgba(255, 255, 255, 0.6)
    .content
      font-weight 400
    @media screen and (max-width 1200px)
      margin-top 160px
      font-size: 22px;
      .title
        font-size: 26px;
@media screen and (min-width: 1300px)
  .inputPositionFixed
    position fixed
    top 38vh
.page
  height 100vh
  overflow hidden
  background: radial-gradient(322.36% 100% at 50% 0%, #131C24 0%, rgba(19, 28, 36, 0.99) 8.07%, rgba(19, 28, 35, 0.98) 15.54%, rgba(18, 27, 34, 0.95) 22.5%, rgba(18, 26, 33, 0.92) 29.04%, rgba(17, 25, 31, 0.87) 35.26%, rgba(16, 23, 29, 0.82) 41.25%, rgba(14, 21, 27, 0.75) 47.1%, rgba(13, 19, 24, 0.68) 52.9%, rgba(12, 17, 22, 0.60) 58.75%, rgba(10, 15, 18, 0.52) 64.74%, rgba(8, 12, 15, 0.42) 70.96%, rgba(6, 9, 12, 0.33) 77.5%, rgba(4, 6, 8, 0.22) 84.46%, rgba(2, 3, 4, 0.11) 91.93%, #000 100%), #000;
  .headbar
    background: none
.theme-themeLight
  .exchange-page
    background: linear-gradient(180deg, #FFFFFF 0%, #E1E5EE 38.12%)!important
  .exchange
    color rgba(29, 29, 31, 0.9)
    .title
      color rgba(29, 29, 31, 0.9)
    input
      border: 2px solid rgba(29, 29, 31, 0.2)
      background rgba(255, 255, 255, 0.5)
      color #1D1D1FE5
    .exchange-btn
      background rgba(29, 29, 31, 0.1)
      color rgba(29, 29, 31, 0.2)
      &.enable
        background rgba(219, 174, 106, 1)
        color rgba(0, 0, 0, 0.8)
    .exchange-rule
      .content
        color rgba(29, 29, 31, 0.5)!important
</style>

