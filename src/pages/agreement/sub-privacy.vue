<template>
  <div class="page agreement">
    <HeadBar class="headbar" title="雷石 KTV 隐私权协议"></HeadBar>
      <SubPrivacy class="agreement-content" :isShowTitle="false" />
    <!-- <LoginModal v-if="carplayInfo.login_qr"></LoginModal> -->
  </div>
</template>

<script>
import { onMounted } from 'vue'
import SubPrivacy from '@/subMoudle/privacy/common.vue'
import { sendLog } from '@/directives/v-log/log'

export default {
  name: 'AgreementPrivacy',
  components: {
    SubPrivacy,
  },
  setup() {
    onMounted(() => {
      sendLog({
        event_type: '10000~50000',
        event_name: 10033,
        event_data: {
          str1: '我的',
          str2: '隐私权',
          str3: '进入隐私',
          str4: 'click',
        },
      })
    })

    return {}
  },
}
</script>
  <style lang="stylus" scoped>
  .page
    display flex
    flex-direction column
    height 100vh
    overflow hidden
    padding-top 0 !important
    padding-bottom 0 !important
    background: radial-gradient(322.36% 100% at 50% 0%, #131C24 0%, rgba(19, 28, 36, 0.99) 8.07%, rgba(19, 28, 35, 0.98) 15.54%, rgba(18, 27, 34, 0.95) 22.5%, rgba(18, 26, 33, 0.92) 29.04%, rgba(17, 25, 31, 0.87) 35.26%, rgba(16, 23, 29, 0.82) 41.25%, rgba(14, 21, 27, 0.75) 47.1%, rgba(13, 19, 24, 0.68) 52.9%, rgba(12, 17, 22, 0.60) 58.75%, rgba(10, 15, 18, 0.52) 64.74%, rgba(8, 12, 15, 0.42) 70.96%, rgba(6, 9, 12, 0.33) 77.5%, rgba(4, 6, 8, 0.22) 84.46%, rgba(2, 3, 4, 0.11) 91.93%, #000 100%), #000;
    .headbar
      position relative
      padding-left 0
    .agreement-content
      flex 1
      overflow-y scroll
      padding 0 100px 170px
      ::v-deep p
        margin-bottom 40px
  .agreement
    &-title
      text-align center
      color rgba(255, 255, 255, 0.8)
      font-size 48px
      margin-top -64px
      margin-bottom 49px
    &-content
      line-height 40px
      font-size 30px
      color rgba(255, 255, 255, 0.4)
      padding-bottom 100px
  .text-indent-2
    text-indent 62px
  
  .theme-themeLight
    .agreement
      background: linear-gradient(180deg, #FFFFFF 0%, #E1E5EE 38.12%)!important
    .agreement-content
      ::v-deep p
        color rgba(29, 29, 31, 1)!important
  </style>
