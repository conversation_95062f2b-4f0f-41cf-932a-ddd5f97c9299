<template>
  <div class="page">
    <SearchBar title="唱过的歌"></SearchBar>
    <div v-if="isLoading" class="loading-indicator">
    </div>
    <div v-else-if="isLogin && dataListNumber" class="list singing-list">
      <div class="list-left">
        <LoadMore
          class="song-list"
          ref="loadMoreRef"
          v-if="dataListNumber"
          @load-more="LoadMoreData"
          safeAreaHeight="11.6991vw"
        >
          <div class="inner-cover">
            <img :src="listSheet.image" v-img-fallback="imgFallback" />
            <p>{{ listSheet.name }}</p>
          </div>
          <div v-for="(songItem, index) in dataList" :key="index">
            <SongItem
              v-if="index < 100"
              :songItem="songItem"
              :log-from="{
                song_list_source: 5,
                str1: '2-singing-order',
              }"
              :ponitActionLog="{
                str1: '2-singing-order',
                event_type: '10000~50000',
                event_name: 10007,
                event_data: {
                  str1: '首页',
                  str2: '常唱',
                  str3: '进入常唱',
                  str4: 'click',
                },
              }"
            ></SongItem>
          </div>
        </LoadMore>
        <div v-else-if="!isRequest" class="list-right-empty">
          {{ hintTxt }}
        </div>
      </div>
      <div class="list-right">
        <img :src="listSheet.image" v-img-fallback="imgFallback" />
        <p>{{ listSheet.name }}</p>
      </div>
    </div>
    <div v-else class="guess-nodata">
      <div class="guess-nodata-top">
        <div class="guess-nodata-top-mask" v-if="!isLogin">
          <div class="guess-nodata-top-mask-button" @click.stop="handleShowVip">
            登录查看唱过的歌
          </div>
        </div>
        <p v-else>{{ hintTxt }}</p>
      </div>
      <GuessSonglist title="长城车主专享歌单" :song_list_source="13" :pageSize="pageSize" pageRoute="2-singing-nologin" renderType="block" showAllData />
    </div>
  </div>
</template>

<script setup>
import GuessSonglist from '@/components/guess-song/songlist.vue'
import SongItem from '@/components/song-item/index.vue'
import useLoading from "@/composables/useLoading"
// import useVip from '@/composables/useVip'
import useLoginValid from '@/composables/useLoginValid'
import { sendLog } from '@/directives/v-log/log'
import { getSingsingList } from '@/service/singing'
import { checkLandscapeOrPortrait } from '@/utils/device'
import { computed, onMounted, ref, watch } from 'vue'
import { useStore } from 'vuex'

const store = useStore()
const { showLoginQrcode } = useLoginValid()

// const { showVipQrcode } = useVip()
const unionid = computed(() => store.state.userInfo.unionid)
const isLogin = computed(() => !!unionid.value)
const { showLoading, hideLoading } = useLoading();

// 最多100条 - 需求紧 - 接口暂无分页逻辑
let loadMoreRef = ref(null)
const hintTxt = ref('')
const dataList = ref([])
const dataListNumber = computed(() => dataList.value.length)
const imgFallback = {
  loading:
    'https://qncweb.ktvsky.com/20231212/vadd/d5d0736cbbe6b173599584f72c6b27f4.png',
  error:
    'https://qncweb.ktvsky.com/20231212/vadd/d5d0736cbbe6b173599584f72c6b27f4.png',
}
let listSheet = ref({
  name: '唱过的歌',
  image:
    'https://qncweb.ktvsky.com/20230706/vadd/711a2703123dc343441dfd7bd2e85d95.png',
})
let isRequest = false
let pageSize = ref(6)
const isLoading = ref(true)

const requestSingingData = async () => {
  let responseData = []
  responseData = await getSingsingList({
    unionid: unionid.value,
  })
  return responseData
}

const fetchData = async () => {
  if (!unionid.value) {
    hideLoading();
    isLoading.value = false;
    return;
  }

  if (isRequest) {
    return;
  }

  isRequest = true;
  isLoading.value = true;

  try {
    const singingResponseData = await requestSingingData();
    dataList.value = singingResponseData;
  } catch (error) {
    console.error(error);
  } finally {
    isRequest = false;
    isLoading.value = false;
    hideLoading();
  }
};

// 此处暂无分页
const LoadMoreData = () => {
  console.log('没有更多数据了~')
}

const handleShowVip = () => {
  if (!isLogin.value) {
    showLoginQrcode({
      log: '登录查看唱过的歌',
    })
    // showVipQrcode()
    sendLog({
      event_type: '10000~50000',
      event_name: 10007,
      event_data: {
        str1: '首页',
        str2: '常唱',
        str3: '进入常唱',
        str4: 'click',
        str9: '点击登录',
      },
    })

    sendLog({
      event_type: '10000~50000',
      event_name: 6003,
      event_data: {
        str1: '唱过的歌',
        str2: '登录查看唱过的歌',
        str3: '点击',
        str4: 'click',
      },
    })

    sendLog({
      event_type: '10000~50000',
      event_name: 1021,
      event_data: {
        str1: '唱过的歌',
        str2: '登录查看唱过的歌',
        str3: '登录弹窗',
        str4: 'show',
        str5: 1,
        str9: '车机端',
        str10:  '1850'
      }
    })
  }
}

watch(unionid, (val) => {
  if (val) {
    fetchData()
  }
})

onMounted(() => {
  showLoading()
  fetchData()
  setTimeout(() => {
    hintTxt.value = '近一年没有点歌记录，快去点歌吧～'
  }, 800)

  if(!isLogin.value){
    sendLog({
      event_type: '10000~50000',
      event_name: 6003,
      event_data: {
        str1: '唱过的歌',
        str2: '猜你会唱',
        str3: '猜你会唱列表展现',
        str4: 'show'
      }
    })

     sendLog({
      event_type: '10000~50000',
      event_name: 1021,
      event_data: {
        str1: '唱过的歌',
        str2: '长城车主尊享歌单',
        str3: '点击VIP歌曲',
        str4: 'show',
        str5: 1,
        str9: '车机端',
        str10:  '1851'
      }
    })
  }
   sendLog({
    event_type: '10000~50000',
    event_name: 6003,
    event_data: {
      str1: '唱过的歌',
      str2: '唱过的歌页',
      str3: '进入唱过的歌页',
      str4: 'show'
    },
  })
  
  pageSize.value = checkLandscapeOrPortrait() === 'landscape' ? 6 : 9
})
</script>

<style lang="stylus" scoped>
.page
  background #000000
  @media screen and (max-width 1200px)
    padding-right 60px
    padding-left 78px
  .guess-nodata
    width 100%
    height 100%
    padding-bottom 200px
    overflow-y scroll
    &-top
      width 100%
      height 420px
      position relative
      display flex
      align-items center
      justify-content center
      @media screen and (max-width 1200px) and (min-height 1200px)
        height 452px
      p
        text-align center
        color: rgba(255, 255, 255, 0.40);
        font-size 28px
        background url(https://qncweb.ktvsky.com/20231211/vadd/3bc6fd2689cda7c52578140c345c6272.png) no-repeat top center
        background-size 90px auto
        padding-top 120px
        @media screen and (max-width 1200px)
          font-size 22px
          background-size 72px auto
      &-mask
        position absolute
        top 0
        right 0
        left 0
        bottom 0
        display flex
        justify-content center
        align-items center
        &-button
          width 380px
          height 100px
          text-align center
          line-height 100px
          color rgba(0,0,0,0.8)
          font-size 32px
          border-radius 14px
          background #dbae6a
.infinite-loading
  display flex
  justify-content center
.singing-list
  padding 0 150px 0 0
.list
  // padding 0 230px 0 80px
  display flex
  justify-content space-between
  &-right
    width 400px
    @media screen and (max-height 650px)
      zoom 0.8
    img
      width 400px
      height 400px
      margin-top 28px
      border-radius 4px
    p
      width 400px
      font-size 52px
      color: rgba(204, 230, 238, 0.80)
      margin-top 159px
      text-align center
    @media screen and (max-width: 1950px) and (min-width: 1700px) and (max-height: 720px) and (min-height: 650px)
      zoom 0.65
      p
        margin-top 30px !important
  &-left
    width 980px
    margin 0 !important
    padding 0 !important
    .song-list
      width 100% !important
      padding 0 20px 110px 20px !important
      &::-webkit-scrollbar
        display none
    .inner-cover
      display none
    &-empty
      margin-top 30vh
      font-size 28px
      color rgba(255, 255, 255, 0.5)
      text-align center
  @media screen and (max-width 1200px) and (min-height 1200px)
    flex-direction column-reverse
    padding-right 0
    &-left
      width 100%
      flex 1
      display flex
      flex-direction row
      align-items center
      padding-left 0
      .song-list
        padding 0!important
      img
        width 200px
        height 200px
        margin-top 4px
      p
        margin-left 50px
        min-width 300px
    .inner-cover
      display block
      width 100%
      padding-left 0
      margin 20px auto 60px
      width 240px
      img
        width 240px
        height 240px
        margin 0
      .song-list
        height 76.6vh !important
      p
        font-size 32px
        color: rgba(204, 230, 238, 0.80)
        text-align center
        margin-top 48px
        min-width auto
        margin-left 0
    .list-right
      display none
.theme-themeLight
  .guess-nodata-top-mask-button
    color rgba(255,255,255,0.8)
    background #7a53e7
  .list-right
    p
      color rgba(29, 29, 31, 1)
  .guess-nodata-top p
    color rgba(29, 29, 31, 0.4)!important
    background-image url('https://qncweb.ktvsky.com/20240222/other/46fc15d93d419bed654d41b52f253151.png')!important
</style>

