<template>
  <div class="page singer-page">
    <SearchBar
      title="歌手"
    ></SearchBar>
    <div class="singer-tab">
      <div
        class="singer-tab-item"
        v-for="(item, index) in tabList"
        :key="item"
        @click="handleChangeTab(item, index)"
        :id="'tab_' + index"
      >
        <div 
          class="singer-tab-item-txt"
          :class="{'singer-tab-item-active':curTab == item}"
        >
          {{ item }}
        </div>
      </div>
    </div>
    <div style="min-height: calc(100vh - 18vw);">
      <LoadMore
        class="singer-list-pad-32"
        ref="loadMoreRef"
        v-if="singerUnionList.length"
        @load-more="fetchSingerList"
        safeAreaHeight="18vw"
      >
        <div class="singer-list">
          <SingerItem
            v-for="(item, index) in singerUnionList"
            :singer="item"
            :key="index"
            @click="handleClickSinger(item)"
          ></SingerItem>
        </div>
      </LoadMore>
      <p class="no-data" v-if="isShowEmpty && net_status">抱歉，暂无歌手相关数据</p>
      <p class="hint" v-if="isEmpty && singerUnionList.length > 20">已加载全部</p>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue'
import { getSingerClassList, getSingerList } from '@/service/singer'
import { useRoute, useRouter, onBeforeRouteLeave } from 'vue-router'
import { useStore } from 'vuex'
import SingerItem from '@/components/singer-item/index.vue'
import { sendLog } from '@/directives/v-log/log'
import eventBus from '@/utils/event-bus'

export default {
  name: 'SingerList',
  components: {
    SingerItem,
  },
  activated () {
    const store = useStore()
    const {
      isUsed,
      position
    } = store.state.pageCacheData.singer
    if (!isUsed) {
      this.handleInitData()
    } else {
      if (this.$refs.loadMoreRef) {
        this.$refs.loadMoreRef.root.scrollTop = position
      }
      this.handleUpdateCachePosition(false, 0)
    }
  },
  setup() {
    const store = useStore()
    const router = useRouter()
    const route = useRoute()
    const tabList = ref([])
    let loadMoreRef = ref(null)
    let singerList = ref([])
    let curTab = ref('')
    let p = 1
    let version = {
      current: '',
      latest: ''
    }
    let isEmpty = ref(false)
    let isRequest = false
    let isShowEmpty = ref(false)

    const net_status = computed(() => store.state.base.net_status);

    const tabLogMap = {
      '飙升周榜': 10037,
      '热门歌星': 10038,
      '大陆男歌星': 10039,
      '中国组合': 10040,
      '大陆女歌星': 10041,
      '港台女歌星': 10042,
      '港台男歌星': 10043,
      '外国歌星': 10044,
    }

    // 歌手列表去重
    const singerUnionList = computed(() => {
      const idsMap = new Map();
      for(let i in singerList.value) {
        if(!idsMap.has(singerList.value[i].singerid)) {
          idsMap.set(singerList.value[i].singerid, singerList.value[i])
        }
      }
      return Array.from(idsMap.values())
    })

    const fetchSingerClassList = async () => {
      tabList.value = await getSingerClassList()
      handleInitTab(tabList.value[0])
    }

    const fetchSingerList = async () => {
      if (isRequest) {
        console.log('请求正在进行中，无法重复请求');
        return;
      }

      isRequest = true;
      console.log('开始获取歌手列表...', curTab.value);

      try {
        const response = await getSingerList({
          p,
          k: curTab.value,
          version: version.latest
        });

        console.log('获取到的歌手数据:', response);

        if (response.data.length > 0) {
          if (p === 1 && response.version) {
            version = response.version;
            console.log('更新版本信息:', version);
          }
          singerList.value = [...singerList.value, ...response.data]; // 使用扩展运算符
          console.log('更新后的歌手列表:', singerList.value);
          p++;
        } else if (p === 1) {
          isShowEmpty.value = true;
          console.log('暂无数据，显示空状态');
        }
      } catch (error) {
        console.error('获取歌手列表时出错:', error);
      } finally {
        isRequest = false;
        console.log('请求结束');
      }
    }

    const handleInitTab = (val) => {
      const params = route.query
      handleChangeTab(params.tab ? params.tab : val)
    }

    const handleInitData = () => {
      singerList.value = []
      curTab.value = ''
      p = 1
      version = {
        current: '',
        latest: ''
      }
      isEmpty.value = false
      isRequest = false
      fetchSingerClassList()
    }

    const handleChangeTab = (tab, index) => {
      curTab.value = tab
      isShowEmpty.value = false
      isRequest = false

      const element = document.getElementById(`tab_${index}`);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'start' }); // 使用smooth选项可以实现平滑滚动效果
      }

      if (tabLogMap[tab]) {
        sendLog({
          event_type: '10000~50000',
          event_name: tabLogMap[tab],
          event_data: {
            str1: '歌星',
            str2: '类型区',
            str3: tab,
            str4: 'click',
          },
        })

        sendLog({
          event_type: '10000~50000',
          event_name: 6005,
          event_data: {
            str1: '歌手列表页',
            str2: '歌手分类',
            str3: tab,
            str4: 'click',
          },
        })
      }
    }

    const handleClickSinger = ({singername, singerheader, singerid}) => {
      sendLog({
        event_type: '10000~50000',
        event_name: 10045,
        event_data: {
          str1: '歌星',
          str2: '歌手区',
          str3: singername,
          str4: 'click',
        },
      })

      sendLog({
        event_type: '10000~50000',
        event_name: 6006,
        event_data: {
          str1: '歌手详情页',
          str2: '歌手详情页',
          str3: '进入歌手详情页',
          str4: 'show',
          str5: singerid,
          str6: 1
        }
      })
      
      router.push({
        name: 'songList',
        query: {
          name: singername,
          image: singerheader,
          singerid,
          path: 'singer'
        },
      })
    }

    const handleUpdateCachePosition = (u, v) => {
      store.commit(
        'UPDATE_PAGE_CACHEDATA',
        {
          data: {
            isUsed: u,
            position: v
          },
          type: 'singer'
        }
      )
    }

    watch(curTab, (tab) => {
      if (tab) {
        p = 1
        singerList.value = []
        fetchSingerList()
      }
    })

    onBeforeRouteLeave((to, from, next) => {
      if (to.name === 'search' || to.name === 'songList') {
        const position = loadMoreRef.value ? loadMoreRef.value.root.scrollTop : 0
        handleUpdateCachePosition(true, position)
      }
      next()
    })

    onBeforeUnmount(() => {
      eventBus.off('singer-online', handleInitData)
    })

    onMounted(() => {
      eventBus.on('singer-online', handleInitData)
    })

    return {
      tabList,
      loadMoreRef,
      singerList,
      singerUnionList,
      curTab,
      p,
      isEmpty,
      isRequest,
      handleInitData,
      handleChangeTab,
      handleClickSinger,
      handleUpdateCachePosition,
      fetchSingerList,
      isShowEmpty,
      net_status,
    }
  }
}
</script>

<style lang="stylus" scoped>
.page
  min-height 100vh
.singer-page
  padding-right 184px
  padding-left 184px
  height 100vh
  overflow hidden
  ::v-deep .loadmore
    padding-bottom 200px !important
  @media screen and (max-width 1200px) and (min-height 1200px)
    padding-right 60px
    padding-left 60px
.singer-tab
  width calc(100vw - 364px)
  height 100px
  // position absolute
  // top 35px
  // z-index 9
  display flex
  align-items center
  overflow-x scroll
  margin-left -26px
  margin-bottom 60px
  @media screen and (max-width 1200px) and (min-height 1200px)
    width calc(100vw - 120px)
  &::-webkit-scrollbar
    display none
  &-item
    width auto
    height 64px
    display flex
    align-items center
    flex-shrink 0
    border-bottom 2px solid rgba(255, 255, 255, 0.10)
    span
      width 2px
      height 32px
      background rgba(255, 255, 255, 0.10)
    &-txt
      color rgba(255, 255, 255, 0.40)
      font-size 32px
      font-weight 400
      padding 0 16px
      width auto
      max-width 400px
      margin 0 26px
      white-space nowrap
      overflow hidden
      text-overflow ellipsis
      height 64px
      @media screen and (max-width 1200px) and (min-height 1200px)
        font-size 26px
    &-active
      color #DBAE6A
      border-bottom 2px solid #DBAE6A
      font-weight 400
.singer-list
  display grid
  text-align center
  grid-template-columns repeat(6, 216px)
  justify-content space-between
  // padding 0 0 350px 0 !important
  box-sizing border-box
  padding 0 !important
  &::-webkit-scrollbar
    display none
  @media screen and (max-width 1200px) and (min-height 1200px)
    grid-template-columns repeat(4, 200px)
.singer-list-pad-32
  @media screen and (max-width 1200px) and (min-height 1200px)
    padding-left 32px !important
    padding-right 32px !important
.no-data
  font-size 28px
  color rgba(255, 255, 255, 0.5)
  text-align center
  line-height 650px
  @media screen and (max-width: 1200px)
    line-height 1500px
.hint
  text-align center
  color #555555
.theme-themeLight
  .singer-tab-item
    border-bottom-color rgba(29, 29, 31, 0.1)
  .singer-tab-item-txt
    color rgba(29, 29, 31, 0.4)
  .singer-tab-item-active
    border-bottom-color #7A53E7
    color #7A53E7
  .no-data
    color rgba(29, 29, 31, 0.4)
</style>
