<template>
  <div class="page">
    <SearchBar :isShowVipActEnter="true" :isShowMic="true" title="我的" />
    <div class="mine">
      <MineVip />
      <div class="mine-singed" :class="!paginatedList.length && 'singed-empty'">
        <div class="mine-singed-title">
          <p :class="selectIndex === 0 && 'active'" @click="handleClickTab(0)">
            唱过的歌
          </p>
        </div>
        <div v-show="selectIndex === 0" class="mine-singed-list" @click.stop>
          <LoadMore
            class="song-list"
            v-if="paginatedList.length"
            @load-more="fetchData"
          >
            <SongItem
              className="sheet-list-song-item"
              v-for="(songItem, index) in paginatedList"
              :key="index"
              :songItem="songItem"
              :log-from="{
                str1: '2-mine-order',
                song_list_source: 32,
              }"
            />
          </LoadMore>
          <div v-else class="mine-empty">
            <h3>近一年没有点歌记录，快去点歌吧～</h3>
            <GuessSonglist :pageSize="12" renderType="block" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import GuessSonglist from '@/components/guess-song/songlist.vue'
import SongItem from '@/components/song-item/index.vue'
import MineVip from '@/components/vip/index.vue'
import { sendLog } from '@/directives/v-log/log'
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'

const router = useRouter()
const route = useRoute()

const store = useStore()

const unionid = computed(() => store.state.userInfo.unionid)
const mvIsHide = computed(() => store.state.mvIsHide)
const oftenSingList = computed(() => store.state.oftenSing.oftenSingList)

const selectIndex = ref(0)
let isRequest = ref(false)
let p = 1
let isEmpty = false
const currentPage = ref(1)
const pageSize = ref(10)
const paginatedList = ref([])

const fetchData = () => {
  console.log('mine fetchData start!')
  try {
    const start = (currentPage.value - 1) * pageSize.value
    const end = start + pageSize.value
    paginatedList.value = oftenSingList.value.slice(0, end)
    currentPage.value += 1
  } catch (error) {
    console.log('mine fetchData error!', error)
  }
  console.log('mine fetchData end!', paginatedList.value)

  sendLog({
    event_type: '10000~50000',
    event_name: 6001,
    event_data: {
      str1: '我的页',
      str2: '唱过的歌',
      str3: '唱过的歌tab',
      str4: 'show',
    },
  })
}

const handleClickTab = (index) => {
  if (index === selectIndex.value) return
  isRequest.value = false
  selectIndex.value = index
  p = 1
  fetchData()
}

watch(unionid, (val) => {
  if (val) {
    fetchData()
  }
})

watch(
  mvIsHide,
  (val) => {
    if (val && selectIndex.value == 1) {
      p = 1
      isEmpty = false
      isRequest.value = false
      fetchData()
    }
  },
  {
    immediate: true,
    deep: true,
  }
)

// 添加路由守卫
router.beforeEach((to, from, next) => {
  if (to.path === '/mine') {
    // 重置页面状态
    currentPage.value = 1
    paginatedList.value = []
    next()
  } else {
    next()
  }
})

// 移除原有的 onBeforeRouteEnter

onMounted(() => {
  console.log('mine onMounted! - navigator.onLine:', navigator.onLine)
  sendLog({
    event_type: '10000~50000',
    event_name: 10026,
    event_data: {
      str1: '我的',
      str2: '我的页',
      str3: '进入我的页',
      str4: 'show',
    },
  })

  sendLog({
    event_type: '10000~50000',
    event_name: 6008,
    event_data: {
      str1: '我的页',
      str2: '我的页',
      str3: '进入我的页',
      str4: 'click',
    },
  })
  fetchData()
})
</script>

<style lang="stylus" scoped>
.mine
  padding 0 100px
  height calc(100vh - 164px)
  overflow-y scroll
  audio
    visibility hidden
  ::-webkit-scrollbar
    display none
  &-singed
    margin-top 36px
    padding-bottom 250px
    height calc(100vh - 170px)
    overflow hidden
    ::v-deep .loadmore
      padding-bottom 300px!important
    &-title
      color: rgba(255, 255, 255, 0.60);
      padding 36px 0 25px
      border-bottom 2px solid rgba(255, 255, 255, 0.1)
      display flex
      justify-content flex-start
      color: rgba(255, 255, 255, 0.40);
      p
        position relative
        text-align center
        width 160px
        &.active
          color rgba(219, 174, 106, 1)
          &::after
            content ""
            width 160px
            height 2px
            background rgba(219, 174, 106, 1)
            position absolute
            left -16px
            bottom -25px
      span
        font-size 32px
        @media screen and (max-width 1200px)
          font-size 26px
    .song-list
      padding 40px 0 0
      &::-webkit-scrollbar
        display: none
      @media screen and (max-width 1200px)
        min-height 304px
        overflow hidden
        padding-top 32px
        ::v-deep .song-block, ::v-deep .song-item
          .name
            font-size 26px
          .desc
            font-size 20px
          .song-block-vip
            width 42px
            height 22px
  .singed-empty
    height calc(100vh - 170px)
    overflow hidden
    display flex
    flex-direction column
    padding-bottom 0px !important
    .mine-singed-list
      flex 1
      overflow-y scroll
  @media screen and (max-width 1200px)
    padding 0
  &-empty
    padding-bottom 140px
    h3
      width 480px
      text-align center
      color rgba(255, 255, 255, 0.4)
      font-size: 28px;
      padding-top 130px
      background url(https://qncweb.ktvsky.com/20231208/other/8735391456ed4d23857d801ce6c5482b.svg) no-repeat top center
      background-size 90px auto
      margin 96px auto 80px
    ::v-deep .sec-gusse-sing
      padding 0!important
      &-list
        display grid
        grid-template-columns repeat(3, 493px)
        justify-content space-between
        @media screen and (max-width 1200px)
          grid-template-columns repeat(3, 344px)
          min-height 304px
          overflow hidden
          ::v-deep .song-item
            width 344px
            height 140px
            margin-bottom 24px
            .name
              font-size 26px
            .desc
              font-size 20px
            .song-block-vip
              width 42px
              height 22px
        .song-block
          width 100%
.page
  height 100vh
  overflow hidden
  background: radial-gradient(322.36% 100% at 50% 0%, #131C24 0%, rgba(19, 28, 36, 0.99) 8.07%, rgba(19, 28, 35, 0.98) 15.54%, rgba(18, 27, 34, 0.95) 22.5%, rgba(18, 26, 33, 0.92) 29.04%, rgba(17, 25, 31, 0.87) 35.26%, rgba(16, 23, 29, 0.82) 41.25%, rgba(14, 21, 27, 0.75) 47.1%, rgba(13, 19, 24, 0.68) 52.9%, rgba(12, 17, 22, 0.60) 58.75%, rgba(10, 15, 18, 0.52) 64.74%, rgba(8, 12, 15, 0.42) 70.96%, rgba(6, 9, 12, 0.33) 77.5%, rgba(4, 6, 8, 0.22) 84.46%, rgba(2, 3, 4, 0.11) 91.93%, #000 100%), #000;
  .search-bar
    background: none
.theme-themeLight
  .mine-singed-title
    color #1D1D1FE5
    border-bottom-color #1D1D1F1A
  .mine-singed-empty, .mine-empty
    h3
      color #1D1D1FE5!important
      background-image url('https://qncweb.ktvsky.com/20240222/other/46fc15d93d419bed654d41b52f253151.png')
</style>
