<template>
  <div class="page">
    <SearchBar
      placeholder=""
      title="会员中心"
    ></SearchBar>
    <div class="profile" :class="isVip && 'vip'">
      <MineVip from="profile" />
      <VipOrders />
    </div>
  </div>
</template>

<script>
import { computed, ref, onMounted} from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import MineVip from '@/components/vip/index.vue'
import VipOrders from './components/vip-orders'
import { sendLog } from '@/directives/v-log/log'

export default {
  name: 'Profile',
  components: {
    MineVip,
    VipOrders,
  },
  setup() {
    const router = useRouter()
    const store = useStore()

    const userInfo = computed(() => store.state.userInfo)
    const vipInfo = computed(() => store.state.vipInfo)
    const isLogin = computed(() => !!userInfo.value.unionid)
    const isVip = computed(() => !!vipInfo.value.end_time)

    const records = ref([])

    const handleExchange = () => {
      sendLog({
        event_type: '10000~50000',
        event_name: 10030,
        event_data: {
          str1: '我的',
          str2: '开通VIP',
          str3: '点击进入',
          str4: 'click',
          str5: '会员兑换',
          str6: 'click',
        },
      })
      router.push({
        name: 'vipExchange',
      })
    }

    onMounted(() =>{
      sendLog({
        event_type: '10000~50000',
        event_name: 6008,
        event_data: {
          str1: '我的页',
          str2: '会员中心',
          str3: '会员中心',
          str4: 'click',
        },
      })
    })

    return {
      isLogin,
      isVip,
      vipInfo,
      userInfo,
      handleExchange,
      records,
    }
  },
}
</script>

<style lang="stylus" scoped>
.page
  height 100vh
  overflow hidden
  background: radial-gradient(322.36% 100% at 50% 0%, #131C24 0%, rgba(19, 28, 36, 0.99) 8.07%, rgba(19, 28, 35, 0.98) 15.54%, rgba(18, 27, 34, 0.95) 22.5%, rgba(18, 26, 33, 0.92) 29.04%, rgba(17, 25, 31, 0.87) 35.26%, rgba(16, 23, 29, 0.82) 41.25%, rgba(14, 21, 27, 0.75) 47.1%, rgba(13, 19, 24, 0.68) 52.9%, rgba(12, 17, 22, 0.60) 58.75%, rgba(10, 15, 18, 0.52) 64.74%, rgba(8, 12, 15, 0.42) 70.96%, rgba(6, 9, 12, 0.33) 77.5%, rgba(4, 6, 8, 0.22) 84.46%, rgba(2, 3, 4, 0.11) 91.93%, #000 100%), #000;
  .search-bar
    background: none
  .profile
    padding 0 100px
    height calc(100vh - 164px)
    overflow-y scroll
    ::v-deep .user-vip-entry
      .active
        margin-right 0
    @media screen and (max-width 1200px)
      padding 0
</style>

