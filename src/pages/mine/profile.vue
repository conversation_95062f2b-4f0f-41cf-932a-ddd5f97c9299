<template>
  <div class="page">
    <SearchBar placeholder="搜索歌曲、歌星"></SearchBar>
    <div class="profile" :class="isVip && 'vip'">
      <MineVip from="profile" />
    </div>
  </div>
</template>

<script>
import { computed, ref } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import MineVip from '@/components/vip/index.vue'
import { sendLog } from '@/directives/v-log/log'

export default {
  name: 'Profile',
  components: {
    MineVip,
  },
  setup() {
    const router = useRouter()
    const store = useStore()

    const userInfo = computed(() => store.state.userInfo)
    const vipInfo = computed(() => store.state.vipInfo)
    const isLogin = computed(() => !!userInfo.value.unionid)
    const isVip = computed(() => !!vipInfo.value.end_time)

    const records = ref([])

    const handleExchange = () => {
      sendLog({
        event_type: '10000~50000',
        event_name: 10030,
        event_data: {
          str1: '我的',
          str2: '开通VIP',
          str3: '点击进入',
          str4: 'click',
          str5: '会员兑换',
          str6: 'click',
        },
      })
      router.push({
        name: 'vipExchange',
      })
    }

    return {
      isLogin,
      isVip,
      vipInfo,
      userInfo,
      handleExchange,
      records,
    }
  },
}
</script>

<style lang="stylus" scoped>
.page
  height 100vh
  overflow hidden
  background: #000000B2
  .search-bar
    background: none
  .profile
    padding 0 100px
    height calc(100vh - 164px)
    overflow-y scroll
    @media screen and (max-width 1200px)
      padding 0
</style>

