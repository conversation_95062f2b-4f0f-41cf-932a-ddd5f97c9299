<template>
  <div class="order">
    <div class="order-title">购买记录</div>
    <div class="order-list">
      <LoadMore
        v-if="orders.length"
        @load-more="handleGetUserOrdersList"
        safeAreaHeight="12.6991vw"
      >
        <div class="order-item" v-for="(item, index) in orders" :key="index">
          <div class="left">
            <h3>{{ item.vip_desc }}</h3>
            <p>{{ item.vip_datetime }}</p>
          </div>
          <div class="right">¥<span>{{ formatPrice(item.vip_price) }}</span></div>
        </div>
        <div class="order-bottom">
          <p>{{ isEmpty ? '已加载全部' : '加载中～'}}</p>
        </div>
      </LoadMore>
      <div v-else-if="!isRequest" class="order-empty">
        <svg v-if="themeClass === 'themeDark'" xmlns="http://www.w3.org/2000/svg" width="90" height="90" viewBox="0 0 90 90" fill="none">
          <g opacity="0.2">
          <rect x="11" y="11" width="68" height="68" rx="34" stroke="white" style="stroke:white;stroke:white;stroke-opacity:1;" stroke-width="4"/>
          <circle cx="45" cy="59.9998" r="4" fill="white" style="fill:white;fill:white;fill-opacity:1;"/>
          <path d="M41.0528 31.4226C40.7852 29.0652 42.6293 27 45.0018 27C47.3743 27 49.2184 29.0652 48.9509 31.4226L47.0505 48.1668C46.9323 49.2088 46.0506 49.996 45.0019 49.996C43.9532 49.996 43.0716 49.2088 42.9533 48.1668L41.0528 31.4226Z" fill="white" style="fill:white;fill:white;fill-opacity:1;"/>
          </g>
        </svg>
        <svg v-else xmlns="http://www.w3.org/2000/svg" width="90" height="90" viewBox="0 0 90 90" fill="none">
          <g opacity="0.2">
            <rect x="11" y="11" width="68" height="68" rx="34" stroke="rgba(40,40,42,1)" style="stroke:rgba(40,40,42,1);stroke-opacity:1;" stroke-width="4"/>
            <circle cx="45" cy="59.9998" r="4" fill="rgba(40,40,42,1)" style="fill:rgba(40,40,42,1);fill-opacity:1;"/>
            <path d="M41.0528 31.4226C40.7852 29.0652 42.6293 27 45.0018 27C47.3743 27 49.2184 29.0652 48.9509 31.4226L47.0505 48.1668C46.9323 49.2088 46.0506 49.996 45.0019 49.996C43.9532 49.996 43.0716 49.2088 42.9533 48.1668L41.0528 31.4226Z" fill="rgba(40,40,42,1)" style="fill:rgba(40,40,42,1);fill-opacity:1;"/>
          </g>
        </svg>
        暂无购买记录
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onBeforeMount, computed, onUnmounted } from 'vue'
import { useStore } from 'vuex'
import { getUserOrders } from '@/service/vip'
import eventBus from '@/utils/event-bus'

const store = useStore()
const userInfo = computed(() => store.state.userInfo)
const themeClass = computed(() => store.state.themeClass)

let orders = ref([])
let p = 1
let isRequest = ref(false)
let isEmpty = ref(false)

const handleGetUserOrdersList = async (payload) => {
  console.log('handleGetUserOrdersList')
  try {
    if (payload === 'nats') {
      isRequest.value = false; // 确保使用 .value 访问响应式变量
      p = 1;
      orders.value = [];
    }

    if (isRequest.value) {
      return;
    }

    isRequest.value = true;

    const historyResponseData = await getUserOrders(
      userInfo.value.unionid,
      p
    );

    if (historyResponseData.length) {
      orders.value = orders.value.concat(historyResponseData);
      p++;
    } else {
      isEmpty.value = true;
    }
  } catch (error) {
    console.error('获取用户订单列表失败:', error);
    // 可以根据需要处理错误，例如显示错误提示
    // errorMessage.value = '获取数据失败，请重试';
  } finally {
    isRequest.value = false; // 无论成功或失败，都需要重置请求状态
  }
};

const formatPrice = (val) => {
  return val.replace('￥', '');
};

onBeforeMount(() => {
  handleGetUserOrdersList()
  eventBus.on('nats-vip-pay', handleGetUserOrdersList.bind(null, 'nats'))
})

onUnmounted(() => {
  eventBus.off('nats-vip-pay', handleGetUserOrdersList)
})
</script>

<style lang="stylus" scoped>
.order
  margin-top 36px
  &-list
    ::-webkit-scrollbar
      display none
  &-title
    padding 36px 0 25px
    margin-bottom 28px
    font-size: 32px;
    color rgba(255, 255, 255, 0.6)
    border-bottom 2px solid #FFFFFF1A
    @media screen and (max-width 1200px)
      padding-top 200px
      font-size 26px
  .order-item
    display flex
    justify-content space-between
    align-items center
    height 141px
    border-bottom 1px solid #FFFFFF1A
    h3
      font-size 32px
      color rgba(255, 255, 255, 0.7)
      margin-bottom 10px
    p
      font-size 24px
      color rgba(255, 255, 255, 0.4)
    .right
      font-size: 24px
      color: #FFFFFF99
      span
        font-size: 40px;
        color: rgba(255, 255, 255, 0.80)
    @media screen and (max-width 1200px)
      h3
        font-size 26px
      p
        font-size: 20px;
      .right
        font-size: 26px;
  &-bottom
    margin-top 22px
    color rgba(255, 255, 255, 0.4)
    font-size 28px
    text-align center
  &-empty
    width 150px
    color rgba(255, 255, 255, 0.2)
    font-size: 24px;
    padding 100px 0 188px
    margin 0px auto 0
    text-align center
    min-height 30vh
    svg
      width 72px
      height 72px
      margin-bottom 20px
      margin-left auto
      margin-right auto
.theme-themeLight
  .order-title
    border-bottom-color #1D1D1F1A
  .order-title, .order-empty
    color #1D1D1FE5
  .order-item
    border-color #1D1D1F1A!important
    h3, .right, .right span
      color #1D1D1FE5
    p
      color rgba(29,29,31,0.5) !important
  .order-bottom
    color rgba(29,29,31,0.5)
</style>

