<template>
  <div class="page setting">
    <HeadBar class="headbar" title="设置"></HeadBar>
    <div class="setting-content">
      <ul>
        <li>
          <div>清理缓存</div>
          <div>
            <p @click="handleClickStorage">清理缓存 {{ cacheSize }}</p>
          </div>
        </li>
        <li>
          <div>清除数据</div>
          <div>
            <p @click="handleClickData">清除数据 {{ dataSize }}</p>
          </div>
        </li>
        <li>
          <div>相关协议</div>
          <div class="agreement">
            <p @click="handleCheckAgreement('agreementUser')">《用户服务协议》</p>
            <p @click="handleCheckAgreement('agreementPrivacy')">《隐私权政策》</p>
          </div>
        </li>
         <li>
          <div class="left">注销账号</div>
          <div class="login-out off">
            <p @click="handleLoginoff">去注销</p>
          </div>
        </li>
        <li>
          <div>切换账号</div>
          <div class="login-out">
            <p @click="handleLoginout">退出当前账号</p>
          </div>
        </li>
      </ul>
      <ServiceLicence />
    </div>
  </div>
</template>

<script setup>
import { computed, onBeforeMount, ref } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import useLoading from '@/composables/useLoading';
import { sendLog } from '@/directives/v-log/log'
import { Dialog } from 'vant'
import { TSFileInstance, TSNativeInstance, TSBaseInfoInstance } from '@/packages/TSJsbridge';
import Toast from '@/utils/toast'
import store2 from 'store2';
import { clearAllData, openDatabase } from '@/utils/IndexedDB.js';

import { parseISO, format } from 'date-fns'
import { accountCancel } from '@/service/user'
// eslint-disable-next-line vue/no-unused-components
// const components = { 'van-switch': Switch }

const store = useStore()
const router = useRouter()

const { showLoading, hideLoading } = useLoading();

const macAddress = computed(() => store.state.system.systemInfo.mac)
const userInfo = computed(() => store.state.userInfo)
// const themeClass = computed(() => store.state.themeClass)

const cacheSize = ref('')
const dataSize = ref('')

const vipInfo = computed(() => store.state.vipInfo)
const isVip = computed(() => !!vipInfo.value.end_time)
const end_time = computed(() => (isVip.value ? vipInfo.value.end_time.split(' ')[0] : ''))
// const isEnabledScore = computed(() => {
//   return store.state.score.enabled;
// });
// const scoreTabs = [
//   { name: '入选新秀', id: 1, event: 30062 },
//   { name: '业余选手', id: 2, event: 30063 },
//   { name: '实力唱将', id: 3, event: 30064 },
// ]
// const activeScore = ref(0)

const handleCheckAgreement = (name) => {
  if(name == 'agreementUser'){
    sendLog({
      event_type: '10000~50000',
      event_name: 6009,
      event_data: {
        str1: '设置页',
        str2: '用户协议',
        str3: '点击用户协议',
        str4: 'click',
      },
    })
  }else{
     sendLog({
      event_type: '10000~50000',
      event_name: 6009,
      event_data: {
        str1: '设置页',
        str2: '隐私协议',
        str3: '点击隐私协议',
        str4: 'click',
      },
    })
  }

  router.push({
    name
  })
}

const handleLoginout = () => {
  Dialog.confirm({
    className: 'global-force-login',
    confirmButtonText: '退出账号',
    cancelButtonText: '取消',
    title: '确定退出当前账号？'
  }).then(() => {
    store.dispatch('loginout', {
      mac_id: macAddress.value,
      unionid: userInfo.value.unionid,
      cleardata: false
    })
    // 退出后 删除配置
    localStorage.removeItem('vip_pkg_data');
    localStorage.removeItem('vip_pkg_time');
    // store.dispatch('singTime/resetSingTimeData')
    // 收藏功能下线
    // store.dispatch('collect/resetUserCollectList')
    sendLog({
      event_type: '10000~50000',
      event_name: 10031,
      event_data: {
        str1: '我的',
        str2: '退出登录',
        str3: '点击退出',
        str4: 'click',
      },
    })

    sendLog({
      event_type: '10000~50000',
      event_name: 10031,
      event_data: {
        str1: '我的',
        str2: '退出登录',
        str3: '点击退出',
        str4: 'click',
      },
    })

     sendLog({
      event_type: '10000~50000',
      event_name: 6009,
      event_data: {
        str1: '设置页',
        str2: '退出账号',
        str3: '点击退出账号',
        str4: 'click',
      },
    })
    router.push({
      name: 'home'
    })
  })
}

const handleClickStorage = () => {
  Dialog.confirm({
    className: 'global-force-login',
    confirmButtonText: '清除',
    cancelButtonText: '取消',
    title: '删除应用数据',
    message: '确认要清除缓存吗？清除缓存不会对使用有任何影响。'
  }).then(() => {
    sendLog({
      event_type: '10000~50000',
      event_name: 6009,
      event_data: {
        str1: '设置页',
        str2: '清理缓存',
        str3: '点击清理缓存',
        str4: 'click',
      },
    })

    showLoading()
    const res = TSFileInstance.clearCache()
    hideLoading()
    if (res) {
      cacheSize.value = ''
    } else {
      Toast('删除缓存失败')
    }
  })
}

// const clearLocalStorage = async () => {
//   showLoading();

//   try {
//     // 注销登录
//     await store.dispatch('loginout', {
//       mac_id: macAddress.value,
//       unionid: userInfo.value.unionid,
//       cleardata: true
//     });
//     console.log('loginout dispatch successful');

//     await openDatabase();
//     const result = await clearAllData(); 
//     console.log(result, 'clearAllData'); // 输出: Successfully cleared all data
    
//     // 清除 localStorage
//     localStorage.clear();

//     // 清除 store2 数据
//     store2.clearAll(); // 确保 store2 数据被清除

//     console.log('All items removed from localStorage');
//   } catch (error) {
//     hideLoading()
//     Toast('删除数据失败');
//     console.error('Error during clearLocalStorage:', error);
//     throw error;
//   }
// };
const clearLocalStorage = (isLoginout = false) => {
      showLoading()
      return new Promise((resolve, reject) => {
        store2('orderedData', null)
        store2('alreadyData', null)
        store2('LOCALSONGS', null)
        store2('storageYSTipAccept', null)
        store.commit('CLEAR_ORDERED_LIST', [])
        store.commit('SAVE_ALREADY_LIST', [])
        if (!isLoginout) {
          return resolve()
        }
        setTimeout(() => {
          store.dispatch('loginout', {
            mac_id: macAddress.value,
            unionid: userInfo.value.unionid,
          })
            .then(() => {
              resolve()
            })
            .catch((error) => {
              reject(error)
            })
        }, 1000)
      })
    }

const handleClickData = () => {
  Dialog.confirm({
    className: 'global-force-login',
    confirmButtonText: '删除并退出应用',
    cancelButtonText: '取消',
    title: '删除应用数据',
    message: '应用的全部数据删除后不可恢复，包括所有伴奏、使用记录以及其他数据。'
  }).then(async () => {
    sendLog({
      event_type: '10000~50000',
      event_name: 6009,
      event_data: {
        str1: '设置页',
        str2: '清除数据',
        str3: '点击清除数据',
        str4: 'click',
      },
    })
    
    await clearLocalStorage(); // 确保清除操作完成

    const res = TSFileInstance.clearData();
    if (res) {
      // 确保清除 orderedData 和 alreadyData
      await Promise.all([
        localStorage.clear(),
        store.commit('CLEAR_ORDERED_LIST'),
        store.commit('CLEAR_ALREADY_LIST'),
        store2.remove('orderedData'), // 使用 remove 确保数据被清除
        store2.remove('alreadyData')  // 使用 remove 确保数据被清除
      ]);
      
      // 直接调用 exit()，确保所有操作已完成
      TSNativeInstance.exit(); // 退出应用
      return;
    }
    hideLoading();
    Toast('删除数据失败');
  });
}

// const handleButtonClick = () => {
//   router.push({
//     name: 'score-results'
//   })
// }

// const handleChangeTheme = (theme) => {
//   store.commit('SET_THEME', theme)
// }

const handleLoginoff = () => {
  const date = parseISO(end_time.value)
  const formattedDate = end_time.value ? format(date, 'yyyy/MM/dd') : ''

  const confirmMessage = isVip.value
    ? `您确认注销账号，并删除所有账号信息及应用数据，同时放弃VIP权益吗？\nVIP到期时间：${formattedDate}`
    : '您确认注销账号，并删除所有账号信息及应用数据吗？'

  TSBaseInfoInstance.notifyStatusBarPopMask(0)
  Dialog.confirm({
    className: 'global-force-login loginoff',
    title: '注销账号',
    confirmButtonText: `确认并注销`,
    cancelButtonText: '取消注销',
    messageAlign: 'left',
    message: confirmMessage,
    beforeClose: () => {
      TSBaseInfoInstance.notifyStatusBarPopMask(1)
      return true
    },
  }).then(() => {
    clearLocalStorage().then(async () => {
      await accountCancel(userInfo.value.unionid)
      // await store.dispatch('getCarplayInfo')
      store.commit('YS_TIP_ACCEPT', false)
      // router.push({ name: 'home' })
      const res = TSFileInstance.clearData()
      hideLoading()
      if (res) {
        dataSize.value = ''
        TSNativeInstance.exit()
      } else {
        Toast('删除数据失败')
      }
    })
  })
}

onBeforeMount(() => {
  cacheSize.value = TSFileInstance.getCacheSize()
  dataSize.value= TSFileInstance.getDataSize()
})
</script>

<style lang="stylus" scoped>
.setting
  height 100vh
  display flex
  overflow hidden
  background: radial-gradient(322.36% 100% at 50% 0%, #131C24 0%, rgba(19, 28, 36, 0.99) 8.07%, rgba(19, 28, 35, 0.98) 15.54%, rgba(18, 27, 34, 0.95) 22.5%, rgba(18, 26, 33, 0.92) 29.04%, rgba(17, 25, 31, 0.87) 35.26%, rgba(16, 23, 29, 0.82) 41.25%, rgba(14, 21, 27, 0.75) 47.1%, rgba(13, 19, 24, 0.68) 52.9%, rgba(12, 17, 22, 0.60) 58.75%, rgba(10, 15, 18, 0.52) 64.74%, rgba(8, 12, 15, 0.42) 70.96%, rgba(6, 9, 12, 0.33) 77.5%, rgba(4, 6, 8, 0.22) 84.46%, rgba(2, 3, 4, 0.11) 91.93%, #000 100%), #000;
  @media screen and (max-width 1200px)
    .setting-content
      padding-left 112px
      padding-right 112px
  .setting-content
    width 100%
    padding-left 335px
    padding-right 335px
    margin 0 auto
    flex 1
    overflow-y scroll
    li
      display flex
      justify-content space-between
      align-items center
      color rgba(255, 255, 255, 0.6)
      padding 55px 0 57px
      border-bottom 2px solid rgba(255, 255, 255, 0.1)
      .themes
        display flex
        p
          margin-left 40px
        .active
          border: 2px solid #DBAE6ACC
          color #DBAE6A
      .agreement
        display flex
        p:first-child
          margin-right 40px
      p
        border-radius: 100px;
        border: 2px solid rgba(255, 255, 255, 0.20);
        background: rgba(255, 255, 255, 0.08);
        padding 23px 52px
      .login-out
        p
          color: #BE2D35;
      &.score-control
        display block
        ::v-deep .van-switch
          width 110px
          height 60px
          border: 2px solid rgba(255,255,255,0.2)
          box-sizing border-box
          &__node
            left 12px
            top 8px
            background rgba(255,255,255,0.4)
        ::v-deep .van-switch--on
          border-color #DBAE6A
          .van-switch__node
            background #fff!important
        & > div
          display flex
          justify-content space-between
          align-items center
        .controls
          margin-top 40px
          .tabs
            display flex
          button
            width 200px
            height 80px
            display flex
            justify-content center
            align-items center
            font-size 28px
            border: 2px solid #FFFFFF33
            background #FFFFFF14
            border-radius 100px
            margin-right 40px
            &.active
              border-color rgba(219, 174, 106, 0.8)
              color rgba(219, 174, 106, 1)
              background rgba(219, 174, 106, 0.1)
            &.list
              margin-right 0
    @media screen and (max-width 1200px)
      font-size 26px
      p
        padding 20px 41px!important
.theme-themeLight
  .setting
    background: linear-gradient(180deg, #FFFFFF 0%, #E1E5EE 38.12%)!important
    button, .controls button
      border-color #1D1D1F33!important
    li
      color #1D1D1FE5!important
      border-bottom-color rgba(29, 29, 31, 0.1)!important
    p
      border: 2px solid rgba(29, 29, 31, 0.2)!important
    .off
      color rgba(225, 61, 61, 1)!important
    .service-licence
      color rgba(29, 29, 31, 0.5)
    .themes
      .active
        background #6341C31A!important
        border: 2px solid #7A53E7!important
        color #7A53E7!important
</style>
