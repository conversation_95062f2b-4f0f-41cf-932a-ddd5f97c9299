<template>
  <div class="back-home" @click.stop="back">
    <img src="https://qncweb.ktvsky.com/20231226/other/4ca25d2ac3821806e2fca3d97d725063.svg" alt="">
    <p>返回</p>
  </div>
</template>

<script>
import { useRouter } from 'vue-router'
import { sendLog } from '@/directives/v-log/log'

export default {
  name: 'BackHome',
  setup() {
    const router = useRouter()

    // 关闭mv页面
    const back = () => {
      // router.back()
      router.push('/')
      sendLog({
        event_type: 'click',
        event_name: 30217,
        event_data: {
          str1: '换脸mv页',
          str2: '返回',
          str3: '点击',
          str4: 'click',
        }
      })
    }

    return {
      back,
    }
  }
}
</script>

<style lang="stylus" scoped>
.back-home
  width 130px
  height 130px
  border-radius 10px
  background rgba(30, 31, 33, 0.70)
  backdrop-filter blur(15px)
  display flex
  flex-direction column
  align-items center
  // justify-content center
  position absolute
  top 40px
  left 40px
  z-index 11
  img
    width 80px
    height 80px
    margin-top 8px
  p
    height 28px
    line-height 28px
    font-size 22px
    color rgba(255, 255, 255, 0.80)
    margin-top -6px
  @media screen and (max-width 1200px) and (min-height 1200px)
    top 32px
    left 32px
    width 104px
    height 104px
    border-radius 8px
    backdrop-filter blur(12px)
    img
      width 64px
      height 64px
      margin-top 6px
    p
      height 23px
      line-height 23px
      font-size 19.2px
      color rgba(255, 255, 255, 0.80)
      margin-top -3px
</style>