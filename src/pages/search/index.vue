<template>
  <div class="page search">
    <SearchBar
      placeholder="搜索歌曲、歌手"
      @search="handleOnSearch"
      :isSearch="true"
      title=""
      :isShowVipActEnter="isShowVipActEnter"
      ref="searchRef"
    ></SearchBar>
    <div class="song-list song-list-padding" v-if="keyword">
      <div class="tabs">
        <div
          class="tab"
          :class="{ active: curSearchTab === 'song' }"
          @click="handleSwitchSearchTab('song')"
        >
          歌曲
        </div>
        <div
          class="tab"
          :class="{ active: curSearchTab == 'singer' }"
          @click="handleSwitchSearchTab('singer')"
        >
          歌手
        </div>
      </div>

      <div v-show="isShowEmpty" class="empty-wrapper">
        <div class="empty-block">
          <svg v-show="themeClass == 'themeLight'" width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g opacity="0.4" clip-path="url(#clip0_346_155436)">
            <circle cx="62.1964" cy="33.4815" r="18.5557" stroke="#1D1D1F" stroke-width="4"/>
            <path d="M45.0664 38.9043L21.6423 68.7885L28.1023 75.2485L57.9865 51.8244" stroke="#1D1D1F" stroke-width="4"/>
            <path d="M18.8096 77.4078L20.1455 78.8963L20.1455 78.8963L18.8096 77.4078ZM9.2533 68.1959L10.7898 69.4763L9.2533 68.1959ZM15.6039 60.5749L17.1404 61.8552L17.1404 61.8552L15.6039 60.5749ZM12.9925 54.9995L12.9925 52.9995L12.9925 52.9995L12.9925 54.9995ZM12.75 52.9995C11.6454 52.9995 10.75 53.8949 10.75 54.9995C10.75 56.1041 11.6454 56.9995 12.75 56.9995L12.75 52.9995ZM9.79607 77.293L11.1694 75.8391L11.1694 75.8391L9.79607 77.293ZM15.2047 55.8179L13.9031 57.3364L15.2047 55.8179ZM24.6137 69.5115L17.4738 75.9194L20.1455 78.8963L27.2854 72.4884L24.6137 69.5115ZM10.7898 69.4763L17.1404 61.8552L14.0675 59.2945L7.71683 66.9156L10.7898 69.4763ZM12.9925 52.9995L12.75 52.9995L12.75 56.9995L12.9925 56.9995L12.9925 52.9995ZM11.1694 75.8391C9.38241 74.1512 9.21616 71.3647 10.7898 69.4763L7.71683 66.9156C4.79078 70.427 5.09992 75.6084 8.42274 78.747L11.1694 75.8391ZM17.4738 75.9194C15.6715 77.5369 12.9299 77.502 11.1694 75.8391L8.42274 78.747C11.6964 81.8391 16.7942 81.904 20.1455 78.8963L17.4738 75.9194ZM16.5063 54.2993C15.5277 53.4606 14.2814 52.9995 12.9925 52.9995L12.9925 56.9995C13.3265 56.9995 13.6495 57.119 13.9031 57.3364L16.5063 54.2993ZM17.1404 61.8552C19.0306 59.5869 18.7481 56.2209 16.5063 54.2993L13.9031 57.3364C14.4841 57.8344 14.5573 58.7067 14.0675 59.2945L17.1404 61.8552Z" fill="#1D1D1F"/>
            </g>
            <defs>
            <clipPath id="clip0_346_155436">
            <rect width="90" height="90" fill="white"/>
            </clipPath>
            </defs>
          </svg>
          <svg v-show="themeClass == 'themeDark'" width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g opacity="0.4" clip-path="url(#clip0_346_155436)">
            <circle cx="62.1964" cy="33.4815" r="18.5557" stroke="rgba(255, 255, 255, 0.80)" stroke-width="4"/>
            <path d="M45.0664 38.9043L21.6423 68.7885L28.1023 75.2485L57.9865 51.8244" stroke="rgba(255, 255, 255, 0.80)" stroke-width="4"/>
            <path d="M18.8096 77.4078L20.1455 78.8963L20.1455 78.8963L18.8096 77.4078ZM9.2533 68.1959L10.7898 69.4763L9.2533 68.1959ZM15.6039 60.5749L17.1404 61.8552L17.1404 61.8552L15.6039 60.5749ZM12.9925 54.9995L12.9925 52.9995L12.9925 52.9995L12.9925 54.9995ZM12.75 52.9995C11.6454 52.9995 10.75 53.8949 10.75 54.9995C10.75 56.1041 11.6454 56.9995 12.75 56.9995L12.75 52.9995ZM9.79607 77.293L11.1694 75.8391L11.1694 75.8391L9.79607 77.293ZM15.2047 55.8179L13.9031 57.3364L15.2047 55.8179ZM24.6137 69.5115L17.4738 75.9194L20.1455 78.8963L27.2854 72.4884L24.6137 69.5115ZM10.7898 69.4763L17.1404 61.8552L14.0675 59.2945L7.71683 66.9156L10.7898 69.4763ZM12.9925 52.9995L12.75 52.9995L12.75 56.9995L12.9925 56.9995L12.9925 52.9995ZM11.1694 75.8391C9.38241 74.1512 9.21616 71.3647 10.7898 69.4763L7.71683 66.9156C4.79078 70.427 5.09992 75.6084 8.42274 78.747L11.1694 75.8391ZM17.4738 75.9194C15.6715 77.5369 12.9299 77.502 11.1694 75.8391L8.42274 78.747C11.6964 81.8391 16.7942 81.904 20.1455 78.8963L17.4738 75.9194ZM16.5063 54.2993C15.5277 53.4606 14.2814 52.9995 12.9925 52.9995L12.9925 56.9995C13.3265 56.9995 13.6495 57.119 13.9031 57.3364L16.5063 54.2993ZM17.1404 61.8552C19.0306 59.5869 18.7481 56.2209 16.5063 54.2993L13.9031 57.3364C14.4841 57.8344 14.5573 58.7067 14.0675 59.2945L17.1404 61.8552Z" fill="rgba(255, 255, 255, 0.80)"/>
            </g>
            <defs>
            <clipPath id="clip0_346_155436">
            <rect width="90" height="90" fill="white"/>
            </clipPath>
            </defs>
          </svg>
          <p>抱歉，暂无“{{ keyword }}”结果</p>
        </div>
        <GuessSonglist :pageSize="6" renderType="block" pageRoute="2-search-guess" />
      </div>

      <div v-if="curSearchTab === 'song'" ref="">
        <LoadMore
          class="song-list-content"
          v-if="resultData.song.length"
          ref="loadMoreRef"
          @load-more="getSearchResult"
          safeAreaHeight="16.6991vw"
        >
          <SongItem
            v-for="(songItem, index) in resultData.song"
            :key="index"
            :songItem="songItem"
            :log-from="{
              'song_list_source': isLogin ? 16 : 2,
              'str1': '2-search-order',
            }"
          ></SongItem>
        </LoadMore>
        <!-- <div v-else-if="!isInit" class="empty-wrapper">
          <div class="empty-block">
            <svg v-show="themeClass == 'themeLight'" width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g opacity="0.4" clip-path="url(#clip0_346_155436)">
              <circle cx="62.1964" cy="33.4815" r="18.5557" stroke="#1D1D1F" stroke-width="4"/>
              <path d="M45.0664 38.9043L21.6423 68.7885L28.1023 75.2485L57.9865 51.8244" stroke="#1D1D1F" stroke-width="4"/>
              <path d="M18.8096 77.4078L20.1455 78.8963L20.1455 78.8963L18.8096 77.4078ZM9.2533 68.1959L10.7898 69.4763L9.2533 68.1959ZM15.6039 60.5749L17.1404 61.8552L17.1404 61.8552L15.6039 60.5749ZM12.9925 54.9995L12.9925 52.9995L12.9925 52.9995L12.9925 54.9995ZM12.75 52.9995C11.6454 52.9995 10.75 53.8949 10.75 54.9995C10.75 56.1041 11.6454 56.9995 12.75 56.9995L12.75 52.9995ZM9.79607 77.293L11.1694 75.8391L11.1694 75.8391L9.79607 77.293ZM15.2047 55.8179L13.9031 57.3364L15.2047 55.8179ZM24.6137 69.5115L17.4738 75.9194L20.1455 78.8963L27.2854 72.4884L24.6137 69.5115ZM10.7898 69.4763L17.1404 61.8552L14.0675 59.2945L7.71683 66.9156L10.7898 69.4763ZM12.9925 52.9995L12.75 52.9995L12.75 56.9995L12.9925 56.9995L12.9925 52.9995ZM11.1694 75.8391C9.38241 74.1512 9.21616 71.3647 10.7898 69.4763L7.71683 66.9156C4.79078 70.427 5.09992 75.6084 8.42274 78.747L11.1694 75.8391ZM17.4738 75.9194C15.6715 77.5369 12.9299 77.502 11.1694 75.8391L8.42274 78.747C11.6964 81.8391 16.7942 81.904 20.1455 78.8963L17.4738 75.9194ZM16.5063 54.2993C15.5277 53.4606 14.2814 52.9995 12.9925 52.9995L12.9925 56.9995C13.3265 56.9995 13.6495 57.119 13.9031 57.3364L16.5063 54.2993ZM17.1404 61.8552C19.0306 59.5869 18.7481 56.2209 16.5063 54.2993L13.9031 57.3364C14.4841 57.8344 14.5573 58.7067 14.0675 59.2945L17.1404 61.8552Z" fill="#1D1D1F"/>
              </g>
              <defs>
              <clipPath id="clip0_346_155436">
              <rect width="90" height="90" fill="white"/>
              </clipPath>
              </defs>
            </svg>
            <svg v-show="themeClass == 'themeDark'" width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g opacity="0.4" clip-path="url(#clip0_346_155436)">
              <circle cx="62.1964" cy="33.4815" r="18.5557" stroke="rgba(255, 255, 255, 0.80)" stroke-width="4"/>
              <path d="M45.0664 38.9043L21.6423 68.7885L28.1023 75.2485L57.9865 51.8244" stroke="rgba(255, 255, 255, 0.80)" stroke-width="4"/>
              <path d="M18.8096 77.4078L20.1455 78.8963L20.1455 78.8963L18.8096 77.4078ZM9.2533 68.1959L10.7898 69.4763L9.2533 68.1959ZM15.6039 60.5749L17.1404 61.8552L17.1404 61.8552L15.6039 60.5749ZM12.9925 54.9995L12.9925 52.9995L12.9925 52.9995L12.9925 54.9995ZM12.75 52.9995C11.6454 52.9995 10.75 53.8949 10.75 54.9995C10.75 56.1041 11.6454 56.9995 12.75 56.9995L12.75 52.9995ZM9.79607 77.293L11.1694 75.8391L11.1694 75.8391L9.79607 77.293ZM15.2047 55.8179L13.9031 57.3364L15.2047 55.8179ZM24.6137 69.5115L17.4738 75.9194L20.1455 78.8963L27.2854 72.4884L24.6137 69.5115ZM10.7898 69.4763L17.1404 61.8552L14.0675 59.2945L7.71683 66.9156L10.7898 69.4763ZM12.9925 52.9995L12.75 52.9995L12.75 56.9995L12.9925 56.9995L12.9925 52.9995ZM11.1694 75.8391C9.38241 74.1512 9.21616 71.3647 10.7898 69.4763L7.71683 66.9156C4.79078 70.427 5.09992 75.6084 8.42274 78.747L11.1694 75.8391ZM17.4738 75.9194C15.6715 77.5369 12.9299 77.502 11.1694 75.8391L8.42274 78.747C11.6964 81.8391 16.7942 81.904 20.1455 78.8963L17.4738 75.9194ZM16.5063 54.2993C15.5277 53.4606 14.2814 52.9995 12.9925 52.9995L12.9925 56.9995C13.3265 56.9995 13.6495 57.119 13.9031 57.3364L16.5063 54.2993ZM17.1404 61.8552C19.0306 59.5869 18.7481 56.2209 16.5063 54.2993L13.9031 57.3364C14.4841 57.8344 14.5573 58.7067 14.0675 59.2945L17.1404 61.8552Z" fill="rgba(255, 255, 255, 0.80)"/>
              </g>
              <defs>
              <clipPath id="clip0_346_155436">
              <rect width="90" height="90" fill="white"/>
              </clipPath>
              </defs>
            </svg>
            <p>抱歉，暂无“{{ keyword }}”结果</p>
          </div>
          <GuessSonglist :pageSize="6" renderType="block" />
        </div> -->
      </div>
      <div v-if="curSearchTab !== 'song'" ref="">
        <LoadMore
          ref="loadMoreRef"
          v-if="resultData.singer.length"
          @load-more="getSearchResult"
          safeAreaHeight="16.6991vw"
        >
          <div class="singer-list">
            <SingerItem
                v-for="(item, index) in resultData.singer"
                :key="index"
                :singer="item"
                @click="handleClickSinger(item)"
            ></SingerItem>
          </div>
        </LoadMore>
        <!-- <div v-else-if="!isInit" class="empty-wrapper">
          <div class="empty-block">
            <svg v-show="themeClass == 'themeLight'" width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g opacity="0.4" clip-path="url(#clip0_346_155436)">
              <circle cx="62.1964" cy="33.4815" r="18.5557" stroke="#1D1D1F" stroke-width="4"/>
              <path d="M45.0664 38.9043L21.6423 68.7885L28.1023 75.2485L57.9865 51.8244" stroke="#1D1D1F" stroke-width="4"/>
              <path d="M18.8096 77.4078L20.1455 78.8963L20.1455 78.8963L18.8096 77.4078ZM9.2533 68.1959L10.7898 69.4763L9.2533 68.1959ZM15.6039 60.5749L17.1404 61.8552L17.1404 61.8552L15.6039 60.5749ZM12.9925 54.9995L12.9925 52.9995L12.9925 52.9995L12.9925 54.9995ZM12.75 52.9995C11.6454 52.9995 10.75 53.8949 10.75 54.9995C10.75 56.1041 11.6454 56.9995 12.75 56.9995L12.75 52.9995ZM9.79607 77.293L11.1694 75.8391L11.1694 75.8391L9.79607 77.293ZM15.2047 55.8179L13.9031 57.3364L15.2047 55.8179ZM24.6137 69.5115L17.4738 75.9194L20.1455 78.8963L27.2854 72.4884L24.6137 69.5115ZM10.7898 69.4763L17.1404 61.8552L14.0675 59.2945L7.71683 66.9156L10.7898 69.4763ZM12.9925 52.9995L12.75 52.9995L12.75 56.9995L12.9925 56.9995L12.9925 52.9995ZM11.1694 75.8391C9.38241 74.1512 9.21616 71.3647 10.7898 69.4763L7.71683 66.9156C4.79078 70.427 5.09992 75.6084 8.42274 78.747L11.1694 75.8391ZM17.4738 75.9194C15.6715 77.5369 12.9299 77.502 11.1694 75.8391L8.42274 78.747C11.6964 81.8391 16.7942 81.904 20.1455 78.8963L17.4738 75.9194ZM16.5063 54.2993C15.5277 53.4606 14.2814 52.9995 12.9925 52.9995L12.9925 56.9995C13.3265 56.9995 13.6495 57.119 13.9031 57.3364L16.5063 54.2993ZM17.1404 61.8552C19.0306 59.5869 18.7481 56.2209 16.5063 54.2993L13.9031 57.3364C14.4841 57.8344 14.5573 58.7067 14.0675 59.2945L17.1404 61.8552Z" fill="#1D1D1F"/>
              </g>
              <defs>
              <clipPath id="clip0_346_155436">
              <rect width="90" height="90" fill="white"/>
              </clipPath>
              </defs>
            </svg>
            <svg v-show="themeClass == 'themeDark'" width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g opacity="0.4" clip-path="url(#clip0_346_155436)">
              <circle cx="62.1964" cy="33.4815" r="18.5557" stroke="rgba(255, 255, 255, 0.80)" stroke-width="4"/>
              <path d="M45.0664 38.9043L21.6423 68.7885L28.1023 75.2485L57.9865 51.8244" stroke="rgba(255, 255, 255, 0.80)" stroke-width="4"/>
              <path d="M18.8096 77.4078L20.1455 78.8963L20.1455 78.8963L18.8096 77.4078ZM9.2533 68.1959L10.7898 69.4763L9.2533 68.1959ZM15.6039 60.5749L17.1404 61.8552L17.1404 61.8552L15.6039 60.5749ZM12.9925 54.9995L12.9925 52.9995L12.9925 52.9995L12.9925 54.9995ZM12.75 52.9995C11.6454 52.9995 10.75 53.8949 10.75 54.9995C10.75 56.1041 11.6454 56.9995 12.75 56.9995L12.75 52.9995ZM9.79607 77.293L11.1694 75.8391L11.1694 75.8391L9.79607 77.293ZM15.2047 55.8179L13.9031 57.3364L15.2047 55.8179ZM24.6137 69.5115L17.4738 75.9194L20.1455 78.8963L27.2854 72.4884L24.6137 69.5115ZM10.7898 69.4763L17.1404 61.8552L14.0675 59.2945L7.71683 66.9156L10.7898 69.4763ZM12.9925 52.9995L12.75 52.9995L12.75 56.9995L12.9925 56.9995L12.9925 52.9995ZM11.1694 75.8391C9.38241 74.1512 9.21616 71.3647 10.7898 69.4763L7.71683 66.9156C4.79078 70.427 5.09992 75.6084 8.42274 78.747L11.1694 75.8391ZM17.4738 75.9194C15.6715 77.5369 12.9299 77.502 11.1694 75.8391L8.42274 78.747C11.6964 81.8391 16.7942 81.904 20.1455 78.8963L17.4738 75.9194ZM16.5063 54.2993C15.5277 53.4606 14.2814 52.9995 12.9925 52.9995L12.9925 56.9995C13.3265 56.9995 13.6495 57.119 13.9031 57.3364L16.5063 54.2993ZM17.1404 61.8552C19.0306 59.5869 18.7481 56.2209 16.5063 54.2993L13.9031 57.3364C14.4841 57.8344 14.5573 58.7067 14.0675 59.2945L17.1404 61.8552Z" fill="rgba(255, 255, 255, 0.80)"/>
              </g>
              <defs>
              <clipPath id="clip0_346_155436">
              <rect width="90" height="90" fill="white"/>
              </clipPath>
              </defs>
            </svg>
            <p>抱歉，暂无“{{ keyword }}”结果</p>
          </div>
          <GuessSonglist :pageSize="6" renderType="block" />
        </div> -->
      </div>
    </div>
    <div v-else class="init">
      <SearchHistory @clickWord="handleChangeInput" @delete-word="handleDeleteSearchWord" />
      <GuessSonglist :pageSize="6" renderType="block" pageRoute="2-search-guess" />
    </div>
  </div>
</template>

<script>
import { ref, watch, computed, onMounted, nextTick } from 'vue'
import { useStore } from 'vuex'
import SongItem from '@/components/song-item/index.vue'
import SingerItem from '@/components/singer-item/index.vue'
import SearchHistory from '@/components/search-history/index.vue'
import GuessSonglist from '@/components/guess-song/songlist.vue'
import { search } from '@/service/search'
import { useRouter, onBeforeRouteLeave, useRoute } from 'vue-router'
import { sendLog } from '@/directives/v-log/log'
import { setSearchCache } from '@/utils/historyCache'
import { checkLandscapeOrPortrait } from '@/utils/device'
import Toast from '@/utils/toast'
import { TSVoiceInstance } from '@/packages/TSJsbridge';
import eventBus from '@/utils/event-bus'
import useSongItem from '@/composables/useSongItem'

export default {
  name: 'Search',
  components: {
    SongItem,
    SingerItem,
    GuessSonglist,
    SearchHistory,
  },
  activated() {
    const store = useStore()
    const route = useRoute()
    const { isUsed, position } = store.state.pageCacheData.search
    console.log('0410', isUsed)

    if (!isUsed) {
      this.handleInitData()
      this.handleClearInput()
      
      if (route.query.keyword) return
      console.log('search activated')
      this.handleFocusInput() //只要进入就锁定
    } else {
      if (this.$refs.loadMoreRef) {
        this.$refs.loadMoreRef.root.scrollTop = position
      }
      this.handleUpdateCachePosition(false, 0)
    }

    sendLog({
      event_type: '10000~50000',
      event_name: 10019,
      event_data: {
        str1: '搜索页',
        str2: '搜索页',
        str3: '进入搜索页',
        str4: 'show',
      },
    })

    sendLog({
      event_type: '10000~50000',
      event_name: 6002,
      event_data: {
        str1: '搜索页',
        str2: '搜索页',
        str3: '进入搜索页',
        str4: 'show',
      },
    })
  },
  methods: {
    handleClearInput() {
      this.$refs.searchRef.handleClearInput()
    },
    handleFocusInput() {
      this.$refs.searchRef.handleFocusInput()
    },
    handleChangeInput(e) {
      console.log('search word')
      sendLog({
        event_type: '10000~50000',
        event_name: 10020,
        event_data: {
          str1: '搜索页',
          str2: '搜索历史',
          str3: '点击歌曲',
          str4: 'click',
        },
      })
      this.handleChangeKeyword(e)
      this.$refs.searchRef.handleChangeKeyword(e)
    },
  },
  setup() {
    const router = useRouter()
    const route = useRoute()
    const store = useStore()
    const { orderSong } = useSongItem()

    const isLogin = computed(() => !!store.state.userInfo.unionid)
    const searchCacheList = computed(() => store.state.search.searchCache)
    const themeClass = computed(() => store.state.themeClass)
    const isShowEmpty = computed(() => {
      return !isInit.value && (
        curSearchTab.value === 'song' && !resultData.value.song.length ||
        curSearchTab.value === 'singer' && !resultData.value.singer.length
      )
    })

    const searchRef = ref(null)
    let curSearchTab = ref('song')
    let loadMoreRef = ref(null)
    let keyword = ref('')
    let resultData = ref({
      singer: [],
      song: [],
    })
    let paginationNumberRecord = {
      singer: 1,
      song: 1,
    }
    let isRequest = false
    let isShowVipActEnter = ref(false)
    let isInit = ref(true)
    let from = ref('')

    const handleInitData = () => {
      curSearchTab.value = 'song'
      keyword.value = ''
      resultData.value.singer = []
      resultData.value.song = []
    }

    const handleSwitchSearchTab = (tab) => {
      curSearchTab.value = tab
      // isInit.value = true
      // TODO
      if (!resultData.value[tab].length) {
        searchHandler[tab].call()
      }

      if(curSearchTab.value == 'singer') {
        sendLog({
          event_type: '10000~50000',
          event_name: 6003,
          event_data: {
            str1: '搜索页',
            str2: '搜索结果页',
            str3: '歌手tab',
            str4: 'click',
          },
        })
      }
    }
    const handleOnSearch = (k) => {
      if (!k.trim()) {
        Toast('请输入搜索内容')
        return
      }
      keyword.value = k
      document.querySelector('input').blur();
      console.log('handleOnSearch', k)
    }

    const handleClickSinger = ({ singer, singerhead, singerid }) => {
      sendLog({
        event_type: '10000~50000',
        event_name: 6003,
        event_data: {
          str1: '搜索页',
          str2: '搜索结果页',
          str3: '点击歌手',
          str4: 'click',
          str5: singerid
        }
      })

      sendLog({
        event_type: '10000~50000',
        event_name: 6006,
        event_data: {
          str1: '歌手详情页',
          str2: '歌手详情页',
          str3: '进入歌手详情页',
          str4: 'show',
          str5: singerid,
          str6: 2
        },
      })
      
      router.push({
        name: 'songList',
        query: {
          name: singer,
          image: singerhead,
          singerid
        }
      })
    }

    const getSearchReportStatus = (res) => {
      if (res.errcode !== 200) {
        return 12
      }
      return res.singer.length || res.song.length ? 10 : 11
    }

    const searchHandler = {
      async singer() {
        await handleSearch('singer');
      },
      async song() {
        await handleSearch('song');
      }
    };

    async function handleSearch(type) {
      try {
        if (paginationNumberRecord[type] === 1 && resultData.value[type].length) {
          paginationNumberRecord[type]++;
        }

        const responseData = await search(keyword.value, paginationNumberRecord[type], type);
        const status = getSearchReportStatus(responseData);
        reportSearchStatus('initial', status, responseData);

        handleDataMerge(type, responseData);

        console.log(1211, from.value, paginationNumberRecord.song )
        if (type === 'song' && from.value === 'voiceControl' && paginationNumberRecord.song <= 2) {
          from.value = ''
          if (resultData.value.song.length === 1) {
            orderSong(resultData.value.song[0], {
              immediate: false,
              from: '语音',
            })
            TSVoiceInstance.sendTtsMessage(0, '', '');
            return
          }
          const message = resultData.value.song.length ? '请手动选择一个你想唱的吧' : '未找到相关内容';
          TSVoiceInstance.sendTtsMessage(-1, 1001, message);
        }

        resetFlags();

        reportSearchStatus('final', 10021);
      } catch (error) {
        console.log(`Error in ${type} function:`, error);
        reportSearchStatus('error', 12);
      }
    }

    function handleDataMerge(type, responseData) {
      if (paginationNumberRecord[type] === 1 && !resultData.value[otherTypeMap[type]].length) {
        resultData.value[otherTypeMap[type]] = [
          ...resultData.value[otherTypeMap[type]],
          ...responseData[otherTypeMap[type]]
        ];
      }

      if (responseData[type].length) {
        resultData.value[type] = [...resultData.value[type], ...responseData[type]];
        paginationNumberRecord[type]++;
      }
    }

    function reportSearchStatus(phase, status, responseData) {
      const eventData = {
        key_words: keyword.value,
        status: status || getSearchReportStatus(responseData)
      };

      sendLog({
        event_type: phase === 'error' ? 'click' : '10000~50000',
        event_name: phase === 'error' ? 122 : 10021,
        event_data: phase === 'error' ? eventData : {
          str1: '搜索页',
          str2: '搜索栏',
          str3: '点击搜索',
          str4: 'click'
        }
      });
    }

    function resetFlags() {
      from.value = '';
      isRequest = false;
      isInit.value = false;
    }

    const otherTypeMap = { singer: 'song', song: 'singer' };

    const getSearchResult = async () => {
      if (isRequest) {
        return
      }
      isRequest = true
      console.log('getSearchResult', curSearchTab.value)
      searchHandler[curSearchTab.value].call()
    }

    const handleChangeKeyword = (e) => {
      if (e) keyword.value = e
    }

    const setSearchCacheList = (k) => {
      // 存储搜索历史
      const keyword = k.trim()
      let newSearchCacheList = [keyword, ...searchCacheList.value.filter(item => item !== keyword)]
      newSearchCacheList = newSearchCacheList.slice(0, 10)
      store.dispatch('search/updateSearchCache', newSearchCacheList)
      setSearchCache(newSearchCacheList)
    }

    const handleUpdateCachePosition = (u, v) => {
      store.commit('UPDATE_PAGE_CACHEDATA', {
        data: {
          isUsed: u,
          position: v,
        },
        type: 'search',
      })
    }
    
    const fetchInitData = () => {
      isInit.value = true
      resultData.value = {
        singer: [],
        song: [],
      }
      paginationNumberRecord = {
        singer: 1,
        song: 1,
      }
      isRequest = false
      getSearchResult()
    }
    const handleBlurInput = () => {
      console.log('handleBlurInput')
      document.querySelector('input').blur();
    }
    const onVoiceSearchSame = () => {
      if (resultData.value.song.length === 1) {
        orderSong(resultData.value.song[0], {
          immediate: false,
          from: '语音',
        })
        TSVoiceInstance.sendTtsMessage(0, '', '');
        return
      }
      const message = resultData.value.song.length ? '请手动选择一个你想唱的吧' : '未找到相关内容';
      TSVoiceInstance.sendTtsMessage(-1, 1001, message);
      console.log('语音搜索内容一2致' + new Date(), 0, 1001, message)
    }

    onMounted(() => {
      eventBus.on('voiceSearchSame', onVoiceSearchSame)
      // 新增eventBus监听
      eventBus.on('inputOutFocus', handleBlurInput)
      if (checkLandscapeOrPortrait() === 'landscape') {
        isShowVipActEnter.value = true
      }

      from.value = route.query.from
    })

    watch(keyword, (k) => {
      console.log('watch keyword', k)
      if (k) {
        fetchInitData()
        setSearchCacheList(k)
      }else{
        sendLog({
          event_type: '10000~50000',
          event_name: 6002,
          event_data: {
            str1: '搜索页',
            str2: '猜你会唱',
            str3: '猜你会唱列表展现',
            str4: 'show',
          }
        })
      }
    })

    watch(() => route.query.keyword, async (k) => {
      await nextTick()
      if (k === keyword.value) {
        onVoiceSearchSame()
        return
      }
      if (k) {
        keyword.value = k
        searchRef.value.keyword = k
        from.value = route.query.from
        await nextTick()
        document.querySelector('input').blur();
      }
    }, {
      immediate: true,
      deep: true, 
    })

    onBeforeRouteLeave((to, from, next) => {
      from.value = ''
      if (to.name === 'songList') {
        const position = loadMoreRef.value
          ? loadMoreRef.value.root.scrollTop
          : 0
        handleUpdateCachePosition(true, position)
      }
      next()
    })

    const handleDeleteSearchWord = (v) => {
      sendLog({
        event_type: '10000~50000',
        event_name: 6002, // 10085 单首删除 10086 全部删除
        event_data: {
            str1: '搜索页',
            str2: '搜索历史',
            str3: v !== -1 ? '删除任意歌曲/内容' : '全部删除',
            str4: 'click',
        }
      })
    }

    return {
      isLogin,
      curSearchTab,
      resultData,
      keyword,
      loadMoreRef,
      isShowVipActEnter,
      handleSwitchSearchTab,
      handleOnSearch,
      handleClickSinger,
      handleChangeKeyword,
      setSearchCacheList,
      getSearchResult,
      handleInitData,
      handleUpdateCachePosition,
      // imgs,
      themeClass,
      searchRef,
      isInit,
      isShowEmpty,
      handleBlurInput,
      handleDeleteSearchWord
    }
  },
}
</script>

<style lang="stylus" scoped>
.search
  height 100vh
  overflow hidden
  display flex
  flex-direction column
  background #000000
  padding-bottom 0px
  background: radial-gradient(322.36% 100% at 50% 0%, #131C24 0%, rgba(19, 28, 36, 0.99) 8.07%, rgba(19, 28, 35, 0.98) 15.54%, rgba(18, 27, 34, 0.95) 22.5%, rgba(18, 26, 33, 0.92) 29.04%, rgba(17, 25, 31, 0.87) 35.26%, rgba(16, 23, 29, 0.82) 41.25%, rgba(14, 21, 27, 0.75) 47.1%, rgba(13, 19, 24, 0.68) 52.9%, rgba(12, 17, 22, 0.60) 58.75%, rgba(10, 15, 18, 0.52) 64.74%, rgba(8, 12, 15, 0.42) 70.96%, rgba(6, 9, 12, 0.33) 77.5%, rgba(4, 6, 8, 0.22) 84.46%, rgba(2, 3, 4, 0.11) 91.93%, #000 100%), #000;
  .init
    flex 1
    overflow-y scroll
    padding-bottom 220px
  .search-bar
    background none
  .song-list-padding
    padding 0 !important
    height auto
    .song-list-content
      margin-top 0 !important
  .song-list
    flex 1
    overflow hidden
    width 100% !important
    box-sizing border-box
    display flex
    flex-direction column
    ::-webkit-scrollbar
      display none
    &-content
      margin-top 76px
      position relative
      padding-left 100px
      padding-right 100px
      padding-bottom 150px!important
      ::v-deep .song
        width 100%
        .song-item
          padding 0 20px
          .right
            margin-right 0
      @media screen and (max-width 1200px)
        margin-top 40px
        padding-left 0
        padding-right 0
    .tabs
      width calc(100% - 200px)
      height 100px
      margin-left 100px
      border-bottom 2px solid rgba(255, 255, 255, 0.10)
      display flex
      justify-content center
      align-items center
      .tab
        display flex
        align-items center
        justify-content center
        width 96px
        height 100px
        color rgba(255, 255, 255, 0.4)
        font-size 32px
        &:nth-child(1)
          margin-right 480px
      .active
        border-bottom 2px solid #DBAE6A
        color rgba(219, 174, 106, 1)
    .empty
      overflow-y scroll
    & > div:not(.tabs):not(.empty-wrapper)
      flex 1
      overflow hidden
  .singer-list
    flex 1
    margin 0 auto
    text-align center
    display flex
    flex-wrap wrap
    box-sizing border-box
    display grid
    grid-template-columns repeat(6, 216px)
    justify-content space-between
    padding 60px 100px 150px!important
    @media screen and (max-width 1200px)
      padding-top 40px !important
      padding-left 32px !important
      padding-right 32px !important
      grid-template-columns repeat(4, 200px)
  .empty-wrapper
    overflow-y scroll
    height 100%
    padding-bottom 200px
  .empty-block
    margin-top 100px
    font-size 28px
    color rgba(255, 255, 255, 0.4)
    text-align center
    display flex
    flex-direction column
    align-items center
    justify-content center
    svg
      margin-bottom 40px
      width 90px
      height 90px
    @media screen and (max-width 1200px)
      margin 320px 0 300px
  .hint
    text-align center
    color #555555
  ::v-deep .search-history
    padding 0 100px
    @media screen and (max-width 1200px)
      padding 0
  ::v-deep .sec-gusse-sing
    padding 0 100px
    margin-top 96px
    @media screen and (max-width 1200px)
      padding 0
    .section-container-header-title
      font-size 28px
      color rgba(255, 255, 255, 0.4)
      @media screen and (max-width 1200px)
        font-size 24px
    .sec-gusse-sing-change span
      color rgba(255, 255, 255, 0.4)
    .sec-gusse-sing-list
      grid-template-columns repeat(3, 493px)
      @media screen and (max-width 1200px)
        grid-template-columns repeat(3, 344px)
    .song-block, .song
      width 493px
      @media screen and (max-width 1200px)
        width 344px
.theme-themeLight
  .search
    .tabs
      border-bottom-color rgba(29, 29, 31, 0.1)!important
      .tab
        color rgba(29, 29, 31, 0.4)
      .active
        border-bottom-color #7A53E7!important
        color #7A53E7
    .empty-block
      color rgba(29, 29, 31, 0.4)!important
</style>

