<template>
  <div
    class="refresh-container"
    @touchstart="handleTouchStart"
    @touchend="handleTouchEnd"
    id="home"
  >
    <div
      v-if="isYSTipAccept"
      class="page"
      :style="{
        'pointer-events': pageCanpoint ? 'auto' : 'none'
      }"
    >
      <SearchBar
        placeholder="搜索歌曲、歌手"
        :isShowBack="false"
        :isShowVipActEnter="false"
        :isShowNotify="true"
      ></SearchBar>
      <div class="scroll-area" ref="scrollArea">
        <NavList
          :firstSong="firstSong"
          @click-nav="handleClickNav"
          :key="refreshKey"
        ></NavList>
        
        <template v-if="!isShowOffline">
          <GuessSonglist
            :pageSize="pageSize"
            renderType="block"
            pageRoute="1-guess"
          />

          <!-- 麦克风运营位  -->
          <div class="mic-operation">
            <!-- <Carousel :images="defaultImg" /> -->
            <img src="https://qncweb.ktvsky.com/20240529/other/f0ec2235e8f53b73c44ae52b2fd90a3a.png"  @click="handleClickOperation"> 
            <!-- <van-swipe :autoplay="3000" :show-indicators="false">
              <van-swipe-item v-for="image in defaultImg" :key="image.id">
                <img :src="image.cover" @click="handleClickOperation(image.id)" />
              </van-swipe-item>
            </van-swipe> -->
          </div>

          <div class="sheet-list" :class="[ fixSongSheet && 'sheet-list-fix-top' ]" id="sheet-scroll">
            <div class="sheet-list-tab" id="refTab" ref="refTab">
                <div class="sheet-list-tab-item"
                  v-for="(item, index) in rankList"
                  :key="item.id"
                  @click="handleChangeTab(item)"
                  :id="'tab_' + item.id"
                >
                  <span v-if="index != 0"></span>
                  <div class="sheet-list-tab-item-txt" :class="{'sheet-list-tab-active':curTab.id == item.id}">{{ item.name }}</div>
                </div>
            </div>
            <div class="sheet-list-content">
              <div v-if="curTab.image" class="sheet-list-content-left">
                <img :src="curTab.image" v-img-fallback="imgFallback">
                <p>{{ curTab.name }}</p>
              </div>
              <div class="sheet-list-content-right" @click.stop>
                <LoadMore
                  class="song-list"
                  :class="{ 'no-scroll': !canScroll }"
                  v-if="dataList.length"
                  @load-more="fetchData"
                  safeAreaHeight="40vh"
                >
                  <SongItem
                    className="sheet-list-song-item"
                    v-for="(songItem, index) in dataList"
                    :key="index"
                    :songItem="songItem"
                    :log-from="{
                      str1: '1-song-tab',
                      song_list_source: unionid ? 3 : 15,
                      song_list: curTab.name,
                      song_list_id: curTab.id
                    }"
                  />
                  <p v-if="isRequest" class="bot-hint">加载中～</p>
                  <p class="bot-hint" v-if="isEnd">到底啦～</p>
                </LoadMore>
                <div v-if="!isRequest && !dataList.length"  class="empty">
                  暂无歌曲
                </div>
              </div>
            </div>
          </div>
        </template>
        
        <Offline v-if="isShowOffline" @click-retry="handleRetry"/>
      </div>
    </div>
    <YSTip v-else />
  </div>
</template>

<script setup>
import { onMounted, onUnmounted, ref, computed, onActivated, watch, defineAsyncComponent } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useStore } from 'vuex'
import Toast from '@/utils/toast'
import useLoginValidComposables from '@/composables/useLoginValid'
import useMicOnline from '@/components/modal/global/mic-online/create.js'
import eventBus from '@/utils/event-bus'
import { getHotSongList } from '@/service/hotSongList'
import { getPlaylist, getPlaylistDetail } from '@/service/playlist'
import { sendLog } from '@/directives/v-log/log'
import _ from 'lodash'
import { TSBaseInfoInstance, TSNativeInstance } from '@/packages/TSJsbridge'
import NavList from '@/components/nav-list/index.vue'
// import Carousel from '@/components/carousel/index.vue'
// import { Swipe as VanSwipe, SwipeItem as VanSwipeItem } from 'vant';
// 组件定义
const SongItem = defineAsyncComponent(() => import('@/components/song-item/index.vue'))
const GuessSonglist = defineAsyncComponent(() => import('@/components/guess-song/songlist.vue'))
const YSTip = defineAsyncComponent(() => import('@/components/ys-tip/index.vue'))

// 使用 Vue 3 的组合式 API
const router = useRouter()
const route = useRoute()
const store = useStore()
const { checkUserLoginStatus } = useLoginValidComposables()
const $useMicOnline = useMicOnline()

// 计算属性
const unionid = computed(() => store.state.userInfo.unionid)
const firstSong = computed(() => {
  return _.get(store.state, 'oftenSing.oftenSingList[0]', {})
})

const themeClass = computed(() => store.state.themeClass)
const micMallInfo = computed(() => store.state.base.mallInfo)
const isYSTipAccept = computed(() => store.state.storageYSTipAccept)
const net_status = computed(() => store.state.base.net_status)

// 响应式状态
const fixSongSheet = ref(false)
const isShowOffline = ref(false)
const pageCanpoint = ref(isYSTipAccept.value)
const canScroll = ref(false)
const defaultImg = ref([
  {
    id: '1',
    cover:'https://qncweb.ktvsky.com/20240529/other/f0ec2235e8f53b73c44ae52b2fd90a3a.png'
  },
  // {
  //   id: '2',
  //   cover:'https://qncweb.ktvsky.com/20250313/vadd/8d6d1428e270b5bfdc4878c7e3774d4e.png'
  // } 
  {
    id: '2',
    cover:'https://qncweb.ktvsky.com/20250317/vadd/b287d52d7f41df040081aa8d88f79a3a.png'
  }
])
let startY = 0
let startScrollTop = 0
const scrollArea = ref(null)

const curTab = ref({ id: 0, name: '', img: '' })
let p = 1
const version = ref({ current: '', latest: '' })
const isRequest = ref(false)
const isEnd = ref(false)
const rankList = ref([])
const dataList = ref([])
const refTab = ref(null)
const refreshKey = ref(0)

const imgs = {
  themeDark: { imgFallback: { loading: require('@/assets/cover-dark.png'), error: require('@/assets/cover-dark.png') } },
  themeLight: { imgFallback: { loading: require('@/assets/cover-light.png'), error: require('@/assets/cover-light.png') } },
  themeSystem: { imgFallback: { loading: require('@/assets/cover-dark.png'), error: require('@/assets/cover-dark.png') } },
}

const imgFallback = computed(() => imgs[themeClass.value].imgFallback)

const pageSize = ref(6)

// 方法定义
const handleClickNav = (nav) => {
  if (nav.isCheckLogin && !checkUserLoginStatus()) return
  if (!nav.isSupport) {
    Toast('功能尚未完成迁移')
    return
  }
  if (nav.type === 'page') {
    if (nav.pathName === 'mine') store.dispatch('getCarplayInfo')
    router.push({ name: nav.pathName })
    return
  }
  if (nav.type === 'emit') eventBus.emit(nav.emit)
}

const handleChangeTab = (tab) => {
  if (curTab.value.id === tab.id) return
  sendLog({
    event_type: '10000~50000',
    event_name: 10065,
    event_data: { str1: '首页', str2: '歌单tab', str3: '切换歌单', str4: 'click', str5: tab.name || '', str7: tab.id || '' },
  })

  sendLog({
    event_type: '10000~50000',
    event_name: 6001,
    event_data: {
      str1: '首页',
      str2: '歌单',
      str3: '点击任意歌单列表',
      str4: 'click',
      str5: tab.id || '',
    },
  })

  sendLog({
    event_type: '10000~50000',
    event_name: 6001,
    event_data: {
      str1: '首页',
      str2: '歌单',
      str3: '任意歌单列表展现',
      str4: 'show',
      str5: tab.id || '',
    },
  })

  curTab.value = tab
  dataList.value = []
  p = 1
  version.value = { current: '', latest: '' }
  isRequest.value = false
  isEnd.value = false
  fetchData()

  const element = document.getElementById(`tab_${tab.id}`)
  if (element) {
    // Use requestAnimationFrame for smoother scrolling
    requestAnimationFrame(() => {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
    });
  }
}

const initRankList = async () => {
  try {
    const res = await getPlaylist()
    if (res.length) {
      rankList.value = res.filter((v, i) => i < 10)
      curTab.value = rankList.value[0]
      fetchData()
    }
  } catch (error) {
    console.error('initRankList error', error)
  }
}

const requestBussinessData = async () => {
  const responseData = await getPlaylistDetail({ p, type: curTab.value.name, version: version.value.latest })
  return responseData
}

const requestHotSongData = async () => {
  const res = await getHotSongList({ p, unionid: unionid.value, version: version.value.latest })
  if (res.data.length) {
    if (p === 1 && res.version) version.value = res.version
    dataList.value = dataList.value.concat(res.data).filter((v, i) => i < 100)
    p++
  }
}

const fetchData = async () => {
  if (isRequest.value || isEnd.value) return
  isRequest.value = true

  try {
    if (curTab.value.name === '热门歌曲，总有一首你会唱') {
      if (!dataList.value.length) await requestHotSongData()
    } else {
      const bussinessResponseData = await requestBussinessData()
      if (bussinessResponseData.data.length === 0) {
        isEnd.value = true
      } else {
        if (p === 1 && bussinessResponseData.version) version.value = bussinessResponseData.version
        dataList.value = dataList.value.concat(bussinessResponseData.data)
        p++
      }
    }
  } catch (error) {
    console.error(error)
  } finally {
    isRequest.value = false
  }
}

const handleClickOperation = (value) => {
  // if(value != 1) return
  $useMicOnline.show({ info: micMallInfo.value, title: micMallInfo.value.title })
  sendLog({
    event_type: '10000~50000',
    event_name: 30010,
    event_data: { str1: '运营横条', str2: '运营横条', str3: 'click' },
  })
}

const handleTouchStart = (event) => {
  startY = event.touches[0].clientY
  startScrollTop = scrollArea.value.scrollTop
}

const handleTouchEnd = (event) => {
  handleRefresh(event)
}

// 处理页面滚动事件
const handlePageScroll = () => {
  // console.log('handlePageScroll')
  if (scrollArea.value && refTab.value) {
    // 获取tab元素相对于滚动区域的位置
    const tabRect = refTab.value.getBoundingClientRect()
    const scrollAreaRect = scrollArea.value.getBoundingClientRect()
    
    // 当tab元素的顶部位置接近或超过滚动区域的顶部时，允许歌曲列表滚动
    canScroll.value = tabRect.top <= scrollAreaRect.top + 80
    console.log('handlePageScroll canScroll.value', canScroll.value)
  }
}

const handleRefresh = _.debounce(async (event) => {
  const endY = event.changedTouches[0].clientY
  const distance = endY - startY
  if (distance > 300 && startScrollTop === 0) {
    try {
      console.log('开始刷新')
      await router.replace({ path: route.path, query: { ...route.query, t: Date.now() } })
      handleRetry()
      refreshKey.value++
      console.log('下拉刷新成功')
      Toast('刷新页面成功')
    } catch (error) {
      console.log('下拉刷新error', error)
    }
  }
}, 300)

const handleRetry = async () => {
  try {
    store.commit('base/SET_NET_LOADING', true)

    await Promise.all([
      initRankList(),
      requestHotSongData()
    ])
    await store.dispatch('oftenSing/initOftenSingList', unionid.value)

    store.commit('base/SET_NET_LOADING', false)
  } catch (error) {
    store.commit('base/SET_NET_LOADING', false)
  } finally {
    store.commit('base/SET_NET_LOADING', false)
  }
}

// 生命周期钩子
onMounted(async () => {
  // 添加页面滚动监听
  console.log('onMounted scrollArea.value', scrollArea.value)
  if (scrollArea.value) {
    scrollArea.value.addEventListener('scroll', handlePageScroll)
  } else {
    console.log('scrollArea.value is null')
    canScroll.value = true
  }
  
  sendLog({
    event_type: '10000~50000',
    event_name: 6001,
    event_data: {
      str1: '首页',
      str2: '猜你会唱',
      str3: '猜你会唱列表展现',
      str4: 'show',
    },
  })

  sendLog({
    event_type: '10000~50000',
    event_name: 6001,
    event_data: {
      str1: '首页',
      str2: 'banner',
      str3: 'banner展现',
      str4: 'show',
    },
  })

  if(!unionid.value){
    sendLog({
      event_type: '10000~50000',
      event_name: 6012,
      event_data: {
        str1: '通用',
        str2: '顶部运营',
        str3: '点击',
        str4: 'show',
        str5: 2
      }
    })
  }

  initRankList()
  setTimeout(() => {
    TSBaseInfoInstance.notifyHomePageLoadCompleted()
    TSNativeInstance.showRefreshBtn()
  }, 200)
})

onActivated(() => {
  sendLog({
    event_type: '10000~50000',
    event_name: 10001,
    event_data: { str1: '首页', str2: '首页', str3: '进入首页', str4: 'show' },
  })

  sendLog({
    event_type: '10000~50000',
    event_name: 6001,
    event_data: { str1: '首页', str2: '智行出行计划banner', str3: '展示', str4: 'show' }
  })
})

watch([net_status, rankList, dataList], () => {
  if (!net_status.value && rankList.value.length === 0 && dataList.value.length === 0) {
    isShowOffline.value = true
  } else {
    isShowOffline.value = false
  }
}, { deep: true, immediate: true })

watch(isYSTipAccept, (newValue) => {
  if (newValue) {
    setTimeout(() => {
      pageCanpoint.value = true
    }, 500)
  } else {
    pageCanpoint.value = false
  }
})

// 组件卸载时移除滚动监听
onUnmounted(() => {
  if (scrollArea.value) {
    scrollArea.value.removeEventListener('scroll', handlePageScroll)
  }
})
</script>

<style lang="stylus">
.page
  display flex
  flex-direction column
  height 100vh
  overflow hidden
  height 100vh
  overflow-y scroll
  padding-bottom 50px
  background: linear-gradient(180deg, #12181E 0%, #0F1419 12.39%, #000000 76.78%);
  @media screen and (max-width 1200px) and (max-height 450px) and (min-width 1000px)
    padding-bottom 0px!important
  .search-bar
    background none
  .scroll-area
    flex 1
    overflow-y scroll
  .sec-gusse-sing
    margin-bottom 0px
  .mic-operation
    margin 8px 0 48px
    width 100%
    height 140px
    border-radius: 10px;
    overflow hidden
    @media screen and (max-width 1200px) and (min-height 1200px)
      height auto
      margin 8px 0 32px
  .sheet-list
    width 1760px
    // height 940px
    height auto
    display flex
    flex-direction column
    position relative
    margin-bottom 80px
    .left-shadow
      position absolute
      top 0
      left 0
      width 150px
      height 100px
    .right-shadow
      position absolute
      top 0
      right 0
      width 150px
      height 100px
    @media screen and (max-width 1200px) and (min-height 1200px)
      width 1080px
      margin-bottom 55px
    &-tab
      width 100%
      height auto!important
      border-radius 10px
      padding 0 18px
      display flex
      align-items center
      overflow-x scroll
      margin-bottom 0px
      &::-webkit-scrollbar
        display none
      &-item
        width auto
        height 64px
        display flex
        align-items center
        flex-shrink 0
        border-bottom 2px solid rgba(255, 255, 255, 0.10)
        span
          width 2px
          height 32px
          background rgba(255, 255, 255, 0.10)
        &-txt
          color rgba(255, 255, 255, 0.40)
          font-size 32px
          font-weight 400
          padding 0 38px
          width auto
          max-width 400px
          margin 0 26px
          white-space nowrap
          overflow hidden
          text-overflow ellipsis
          height 64px
          @media screen and (max-width 1200px)
            font-size 26px
      &-active
        color #DBAE6A
        border-bottom 2px solid #DBAE6A
        font-weight 400
      @media screen and (max-width 1200px) and (min-height 1200px)
        margin-bottom 28px
        padding-left 0
    &-content
      width 100%
      height 682px
      display flex
      justify-content space-between
      align-items center
      overflow hidden
      @media screen and (max-width 1200px) and (max-height 450px) and (min-width 1000px)
        height 350px!important
        &-left
          zoom 0.65
      @media screen and (max-width 980px) and (max-height 500px)
        height 630px!important
      @media screen and (max-width 980px) and (max-height 450px)
        height 780px!important
      @media screen and (max-width 980px) and (max-height 450px)
        height 535px!important
      @media screen and (max-width 1160px) and (max-height 450px) and (min-width 1000px) and (min-height 400px)
        height 410px!important
        &-left
          zoom 0.75
      @media screen and (max-width: 1950px) and (min-width: 1700px) and (max-height: 720px) and (min-height: 650px)
        height 220px!important
        &-left
          zoom 0.8
      @media screen and (max-width 1280px) and (max-height 608px)
        height 340px!important
      &-left
        width 510px
        padding-left 65px
        img
          width 300px
          height 300px
          margin-top 30px
          border-radius 20px
          @media screen and (max-width 1200px) and (max-height 450px) and (min-width 1000px)
            zoom 0.8
        p
          width 300px
          font-size 28px
          color rgba(255, 255, 255, 0.70)
          margin-top 30px
          text-align left
          @media screen and (max-width 1200px) and (min-height 1200px)
            font-size 26px
          @media screen and (max-width: 1950px) and (min-width: 1700px) and (max-height: 720px) and (min-height: 650px)
            text-align center
            font-size 36px
      &-right
        flex 1
        height 100%
        margin 0 auto
        .no-scroll
          overflow hidden
        .song-list
          width 100% !important
          padding-bottom 20px
          padding-right 0
          padding-left 50px
          padding-top 60px
          &::-webkit-scrollbar
            display none
        .empty
          margin-top 10vh
          font-size 28px
          color rgba(255, 255, 255, 0.5)
          text-align center
      @media screen and (max-width 1200px) and (min-height 1200px)
        margin-top 0
        height 1496px
        flex-direction row
        &-left
          width 300px
          display flex
          flex-direction column
          align-items top
          padding-left 0
          img
            width 240px
            height 240px
            margin-top 30px
          p
            margin-left 0
            max-width 240px
            text-align left
            color rgba(255, 255, 255, 0.7)
        &-right
          width 100%
          padding-left 0
          margin-top 0
          .song-list
            height 73.6vh !important
            padding 0 20px
            padding-bottom 0 !important
  .bot-hint
    font-size 24px
    color #FFF
    opacity 0.5
    text-align center
    margin-top 50px
    @media screen and (max-width 1200px)
      margin-top 50px
    @media screen and (max-width 1200px) and (max-height 450px)
      position relative
      top 20px
    @media screen and (max-width 980px) and (max-height 500px)
      position relative
      top 30px
  .sheet-list-fix-top
    position fixed
    top 150px
    background #000
.theme-themeLight
  .sheet-list-tab
    &-item
      border-bottom 2px solid rgba(29, 29, 31, 0.1)
      span
        background rgba(29, 29, 31, 0.1)
      &-txt
        color rgba(29, 29, 31, 0.5)
    &-active
      color rgba(99, 65, 195, 1)
      border-bottom 2px solid rgba(99, 65, 195, 1)
  .sheet-list-content-left
    p
      color #1D1D1FE5
  .bot-hint
    color #1D1D1FE5
  .empty
    color #1D1D1FE5!important
</style>
