<template>
  <div class="page songlist-page">
    <SearchBar :title="listSheet.name"></SearchBar>
    <div class="list songlist-page-list">
      <div class="list-left">
        <LoadMore
          class="song-list"
          ref="loadMoreRef"
          v-if="dataList.length"
          @load-more="fetchData"
          safeAreaHeight="11.6991vw"
        >
          <div class="list-left-bg" v-if="singerhead">
            <img :src="singerhead" v-img-fallback="imgs[themeClass].imgFallback"/>
            <p>{{ listSheet.name }}</p>
          </div>
          <SongItem
            v-for="(songItem, index) in dataList"
            :key="index"
            :songItem="songItem"
            :log-from="{
              str1: '2-songlist-singer',
              song_list_source: listSheet.path == 'singer' && !isLogin ? 36 : listSheet.path == 'singer' && isLogin ? 37 : isLogin ? 12 : 17,
              song_list: listSheet.name,
              song_list_id: listSheet.name,
              path: listSheet.path
            }"
            :ponitActionLog="ponitActionLog"
          />
          <!-- song_list_source: listSheet.singerid ? 6 : 3, -->
        </LoadMore>
        <template v-else>
          <div class="list-left-bg" v-if="singerhead">
            <img :src="singerhead" v-img-fallback="imgs[themeClass].imgFallback" />
            <p>{{ listSheet.name }}</p>
          </div>
          <div class="list-left-empty" v-if="!onFirstLoad">
            <img :src="imgs[themeClass].empty" alt="">
            <p>暂无歌曲</p>
          </div>
        </template>
      </div>
      <!-- <div class="list-right-bg" v-if="singerbg">
        <ProgressiveImage
          :placeholder-src="singerbg + '?imageView2/2/w/10'"
          :src="singerbg"
          class="singer-bg"
        />
        <p>{{ listSheet.name }}</p>
      </div> -->
      <div class="list-right" v-if="!onFirstLoad">
        <div class="avatar-default-wrapper">
          <div
            :class="`${className}-cover`"
            :style="{ backgroundImage: `url(${singerhead})` }"
          ></div>
        </div>
        <p>{{ listSheet.name }}</p>
      </div>
    </div>
    <div v-if="showOffline" class="offline-container">
      <Offline
        style="top: 30vh"
        @click-retry="handleRetry"
        tip="加载数据失败了，请检查网络后点击按钮重新尝试"
      />
    </div>
  </div>
</template>

<script>
import SongItem from '@/components/song-item/index.vue'
import { getHotSongList } from '@/service/hotSongList'
import { getPlaylistDetail } from '@/service/playlist'
import { searchSinger } from '@/service/search'
import { computed, onBeforeMount, ref, watch } from 'vue'
import { onBeforeRouteLeave, useRoute } from 'vue-router'
import { useStore } from 'vuex'
// import { ProgressiveImage } from 'vue-progressive-image';

export default {
  name: 'SongList',
  components: {
    SongItem,
    // ProgressiveImage,
  },
  activated() {
    const store = useStore()
    const { isUsed, position } = store.state.pageCacheData.playlist
    if (!isUsed) {
      this.handleInitData()
    } else {
      // 放在这里统一处理 - 防止记忆activated钩子与onBeforeMount刷新时加载重复
      this.fetchData()
      if (this.$refs.loadMoreRef) {
        this.$refs.loadMoreRef.root.scrollTop = position
      }
      this.handleUpdateCachePosition(false, 0)
    }
  },
  setup() {
    const route = useRoute()
    const store = useStore()
    const unionid = computed(() => store.state.userInfo.unionid)

    let loadMoreRef = ref(null)
    const dataList = ref([])
    let singerbg = ref('')
    let singerhead = ref('')
    const imgs = {
      themeDark: {
        imgFallback: {
          loading: require('@/assets/singer-dark.png'),
          error: require('@/assets/singer-dark.png')
        },
        empty: 'https://qncweb.ktvsky.com/20231211/vadd/3bc6fd2689cda7c52578140c345c6272.png',
      },
      themeLight: {
        imgFallback: {
          loading: require('@/assets/singer-light.png'),
          error: require('@/assets/singer-light.png'),
        },
        empty: 'https://qncweb.ktvsky.com/20240222/other/46fc15d93d419bed654d41b52f253151.png',
      },
      themeSystem: {
        imgFallback: {
          loading: require('@/assets/singer-dark.png'),
          error: require('@/assets/singer-dark.png')
        },
        empty: 'https://qncweb.ktvsky.com/20231211/vadd/3bc6fd2689cda7c52578140c345c6272.png',
      },
    };

    const themeClass = computed(() => store.state.themeClass);

    let listSheet = ref({
      name: '',
      image: '',
      singerid: '',
      path: ''
    })
    let p = 1
    let version = ref({
      current: '',
      latest: '',
    })
    let songType = ref('')
    let isRequest = false
    let onFirstLoad = ref(true)
    const isInit = true
    let showOffline = ref(false)
    const isLogin = computed(() => !!store.state.userInfo.unionid)
    const ponitActionLog = computed(() =>
      listSheet.value.singerid
        ? {
            event_type: '10000~50000',
            event_name: 10045,
            event_data: {
              str1: '歌星',
              str2: '歌手区',
              str3: '进入任意歌手',
              str4: 'click',
            },
          }
        : {}
    )

    const requestBussinessData = async () => {
      let responseData = []
      const { singerid, name } = listSheet.value
      console.log(singerid, name)
      if (singerid) {
        const { data } = await searchSinger(singerid, p)
        responseData = {
          data: data.song,
        }
        singerhead.value = data.singerhead ? data.singerhead : ''
      } else {
        responseData = await getPlaylistDetail({
          p,
          type: name,
          version: version.value.latest,
        })
      }
      return responseData
    }

    // bad logic - 歌单列表 - 热门歌曲 - 特殊接口
    const requestHotSongData = async () => {
      const res = await getHotSongList({
        p,
        unionid: unionid.value,
        version: version.value.latest,
      })
      if (res.data.length) {
        if (p === 1 && res.version) {
          version.value = res.version
        }
        dataList.value = dataList.value
          .concat(res.data)
          .filter((v, i) => i < 100)
        console.log('dataList.value', dataList.value.length)
        p++
      }
    }

    const fetchData = async () => {
      try {
        if (isRequest) {
          return
        }
        isRequest = true

        if (listSheet.value.name === '热门歌曲，总有一首你会唱') {
          if (!dataList.value.length) await requestHotSongData()
          isRequest = false
          return
        }

        const bussinessResponseData = await requestBussinessData()
        if (bussinessResponseData.data.length !== 0) {
          if (p === 1 && bussinessResponseData.version) {
            version.value = bussinessResponseData.version
          }
          dataList.value = dataList.value.concat(bussinessResponseData.data)
          p++
        }
        isRequest = false
        showOffline.value = false
        if (onFirstLoad.value) onFirstLoad.value = false
      } catch (error) {
        console.log('songlist fetchData error', error)
        isRequest = false
        if (!dataList.value.length) {
          showOffline.value = true
        }
      }
    }

    const handleInitData = () => {
      dataList.value = []
      listSheet.value = route.query
      p = 1
      version.value = {
        current: '',
        latest: '',
      }
      songType = ''
      isRequest = false
      fetchData()
    }

    const handleUpdateCachePosition = (u, v) => {
      store.commit('UPDATE_PAGE_CACHEDATA', {
        data: {
          isUsed: u,
          position: v,
        },
        type: 'playlist',
      })
    }

    const handleRetry = async () => {
      console.log('handleRetry')
      try {
        store.commit('base/SET_NET_LOADING', true)

        await fetchData()

        store.commit('base/SET_NET_LOADING', false)
      } catch (error) {
        store.commit('base/SET_NET_LOADING', false)
      }
    }

    onBeforeMount(() => {
      listSheet.value = route.query
      if (route.name === 'songList') {
        // 当页面是歌星的详情页时，不触发actived钩子，在这里处理
        fetchData()
      }
    })

    onBeforeRouteLeave((to, from, next) => {
      if (to.name === 'search' && from.name === 'playList') {
        const position = loadMoreRef.value
          ? loadMoreRef.value.root.scrollTop
          : 0
        handleUpdateCachePosition(true, position)
      }
      if (to.name !== 'search') {
        store.commit('UPDATE_PAGE_CACHEDATA', {
          data: {
            isUsed: false,
            position: 0,
          },
          type: 'search',
        })
      }
      next()
    })

    watch(
      () => [route.name, route.query],
      ([newName, newQuery]) => {
        if (newName !== 'songList') return
        p = 1
        listSheet.value = newQuery
        dataList.value = []
        onFirstLoad.value = true
        fetchData()
      }
    )

    return {
      onFirstLoad,
      imgs,
      themeClass,
      loadMoreRef,
      singerbg,
      singerhead,
      dataList,
      listSheet,
      ponitActionLog,
      songType,
      fetchData,
      handleInitData,
      handleUpdateCachePosition,
      isInit,
      showOffline,
      handleRetry,
      isLogin,
      route
    }
  },
}
</script>

<style lang="stylus" scoped>
.page
  background #000000
  .singer-bg
    position absolute
    top 110px
    right 0
    width 807px
    height auto
    z-index 0
  ::v-deep .v-progressive-image
    width 400px!important
    height 400px!important
    overflow hidden
    & > div
      padding-bottom 0!important
    .v-progressive-image-placeholder
      width 400px!important
      height 400px
  .empty-wrapper
    margin 0 auto
  .empty
    color: rgba(255, 255, 255, 0.40);
    font-size 28px
    background url('https://qncweb.ktvsky.com/20231211/vadd/3bc6fd2689cda7c52578140c345c6272.png') no-repeat top center
    background-size 80px auto
    padding-top 100px
    text-align center
    margin 245px auto 0
    @media screen and (max-width 1200px)
      margin-top 409px
.infinite-loading
  display flex
  justify-content center
.songlist-page-list
  padding 0 150px 0 0
  @media screen and (max-width 1200px)
    padding 0
.list
  display flex
  justify-content space-between
  &-right
    width 400px
    margin-top 56px
    @media screen and (max-height 650px)
      zoom 0.8
    .avatar-default-wrapper
      width 400px
      height 400px
      border-radius 50%!important
      margin-top 0!important
    p
      width 400px
      font-size 52px
      color rgba(209, 229, 237, 0.8)
      margin-top 100px
      text-align center
      text-overflow ellipsis
      white-space nowrap
      overflow hidden
    
    @media screen and (max-width: 1950px) and (min-width: 1700px) and (max-height: 720px) and (min-height: 650px)
      zoom 0.7
      p
        margin-top 30px !important
  &-right-bg
   .singer-bg
      top 300px
      right 200px
    p
      position absolute
      right 0
      bottom 235px
      width 807px
      font-size 52px
      color rgba(209, 229, 237, 0.8)
      margin-top 159px
      text-align center
    @media screen and (max-width 1200px)
      display none
  &-left
    width 980px
    margin 0 !important
    padding 0 !important
    &-bg
      display none
    .song-list
      width 100% !important
      padding 0 20px 110px 20px !important
      &::-webkit-scrollbar
        display none
    .list-left-empty
      display flex
      flex-direction column
      justify-content center
      align-items center
      margin-top 24vh
      font-size 28px
      color rgba(255, 255, 255, 0.40)
      text-align center
      img
        width 80px
        height 80px
        margin-bottom 40px
      p
        height 32px
        line-height 32px
        margin-left 0
      @media screen and (max-width 1200px)
        margin-top 20vh
  @media screen and (max-width 1200px) and (min-height 1200px)
    flex-direction column
    justify-content center
    &-left
      width 100%
      display flex
      flex-direction column
      align-items center
      padding-left 0
      img
        width 200px
        height 200px
        margin-top 4px
      p
        margin-left 50px
        min-width 300px
      .song-list
        padding 0 0 200px 0!important
      &-bg
        display flex
        flex-direction column
        justify-content center
        align-items center
        margin-top 20px
        img
          width 240px
          height 240px
          border-radius 50%
        p
          text-align center
          font-size: 32px;
          color: rgba(204, 230, 238, 0.80);
          margin 48px 0 18px
    &-right
      display none
      width 100%
      padding-left 0
      margin-top 8px
      .song-list
        height 76.6vh !important
  .hint
    text-align center
    color #555555
.offline-container
  width 100vw
  height 100vh
  position fixed
  top 50%
  transform translateY(-20%)
.theme-themeLight
  .list-right, .list-right-bg
    p
      color rgba(29, 29, 31, 1)
  .singer-bg, .list-right img
    border-radius 50%
  .list-left-empty p
    color #1D1D1FE5

</style>

