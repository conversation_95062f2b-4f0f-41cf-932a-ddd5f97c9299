import { createStore } from 'vuex';
import uaInfo from '../utils/ua';
import store2 from 'store2';
import get from 'lodash/get';
import throttle from 'lodash/throttle';
import { getMac } from '@/service/device';
import { getCarplayInfo } from '@/service/carplay-info';
import { getActivityInfo } from '@/service/activity'
import { loginout, resolutionRatio  } from '@/service/user';
import { exchangeVip, checkOrderPayStatus } from '@/service/vip';
import { sendLog } from '@/directives/v-log/log'
import base from './modules/base'
import abTest from './modules/abTest'
import songLog from './modules/songLog'
import search from './modules/search';
import oftenSing from './modules/oftenSing'
import searchTips from './modules/searchTips'
import system from './modules/system'
import carMonitor from './modules/carMonitor'
import mvMode from './modules/mvMode'
import download from './modules/download'
import storage from './modules/storage'
import score from './modules/score'
import config from './modules/config'
import vipAddSong from './modules/vipAddSong'
import { nanoid } from 'nanoid'
import { openDatabase, setItem, getItem } from '@/utils/IndexedDB.js';
import { getStorageYSTipAccept } from '@/utils/historyCache'

let checkPayStatusTimer = 0
let checkLoginStatusTimer = 0
let checkLoginKeepStatusTimer = 0

const store = createStore({
  state: {
    themeClass: 'themeDark', // themeDark / themeLight / themeSystem - 默认黑色背景主题
    controlFromType: 1, // "点歌类型：1：点歌屏操作，2：按键操作，3：扫描点歌, 4: 首页会员优化(特斯拉)"
    loginModalEnabled: false,
    isForceLogin: false,
    sing_tips: { // 云控提示
      duration: 6, // 默认提示内容显示6s
      img: 'https://qncweb.ktvsky.com/20231024/other/5b9ee37d2990e0ca7f64c18b929e3472.png', // 提示框的云控背景
      songs: 4, // 加入已点歌曲数量，当达到此数量时进行显示
      text: '提示您：已点{songs}首，立即欢唱吧~', // 提示框的文案
    },
    systemInfo: {
      brand: '',
      model: '',
      system: ''
    },
    macAddress: '',
    alreadyList: [],
    orderedList: [],
    orderedSongIdMap: {},
    carplayInfo: {},
    videoPlayerHistory: {
      songItem: {
        acc: 0,
        flag: [],
        music_name: '',
        org: 0,
        singer: '',
        songid: 0,
        m3u8: '',
        hls: '',
        token: '', // 新增token播放鉴权
        tokenExp: 300, // token过期时间
        serverTime: 0,
        lrc: 0, // 歌词类型 0-5 0为无歌词，其他为有歌词
        lrcData: {
          is_utf8: 1, // 是否是utf8格式文件 0:false 1:true
          down_url: '', // 歌词下载文件地址
        },
      },
      songItemLrc: [],
      defaultAccTrackOrder: 1,
      audioTrackMap: {
        acc: {
          id: 2,
        },
        org: {
          id: 1
        }
      },
      currentTime: 0,
      enabledAudioTrack: {
        id: 1 // 未决断出音轨id之前 使用999 表示同类型未知
      },
      recovery: false,
      supportsAcc: true,
      supportsOrg: true,
    },
    setting: {
      quality: '480'
    },
    lowerBeforeQ: '',
    userInfo: {
      username: '',
      avatar: '',
      unionid: '',
      openid: '',
      phone: ''
    },
    hasMic: false, // 初始化时置为undefined
    hasDongle: false,
    activityInfo: {
      loginSendVip: undefined, // 初始化时置为undefined
      openscreen_vip: {
        id: undefined,
        img: ''
      },
    },
    vipInfo: {
      expire: false, // 用户是否是过期会员
      end_time: '',
      vip_imgs: []
    },
    mvIsOpened: false,
    mvIsHide: true,
    videoPaused: true,
    playingMvQuality: 720, // 当前正播放歌曲的画质
    videoVolume: store2.get('videoVolume') || 75, // 播放器下发音量
    micVolume: 11, // 耳返程序下发麦克风设置
    reverbVolume: 11, // 耳返程序下发混响设置
    mediaVolume: 75, // 多媒体下发音量
    videoInitAutoPlay: 0, // 0 - 初次尚未进入过mv页， 1 - 检测到字段变化为1后需要自动播放
    currIrcIndex: -1, // 当前播放歌词索引， -1 代表未开始播放歌词
    isSingStatus: false, // 是否进入过mv演唱界面
    systemVolumeMuted: false, // 系统静音状态 true 静音；false 未静音
    pageCacheData: { // 页面keep-alive处理
      classify: {
        isUsed: false,
        position: 0
      },
      singer: {
        isUsed: false,
        position: 0
      },
      playlist: {
        isUsed: false,
        position: 0
      },
      search: {
        isUsed: false,
        position: 0
      },
    },
    appStartTime: 0,
    serviceLicenceInfo: {
      serviceLicence: '',
      serviceUrl: '',
    },
    microphones: store2('microphones') || [],
    isRecording: false,
    storageYSTipAccept: getStorageYSTipAccept() || false,
    showProgress: false,
    availableQualities: [], // 当前在播的可用画质数组
    currentPage: 'home',
    hasVideo: false,
    freeVipNumber: 0, // 已使用的免费VIP次数
  },
  mutations: {
    UPDATE_HAS_VIDEO(state, data) {
      state.hasVideo = data
    },
    UPDATE_CURRENT_PAGE(state, page) {
      console.log('UPDATE_CURRENT_PAGE 0411', page)
      store.currentPage = page
    },
    UPDATE_PROGRESS(state, payload) {
      state.showProgress = payload
    },
    UPDATE_RECORD_STATE(state, payload) {
      state.isRecording = payload
    },
    // payload: [SongDownloadState]
    UPDATE_SONG_DOWNLOAD_STATE(state, { songid, downloadState, downloadProgress, src }) {
      const updateSong = (song) => {
        if (song?.songid == songid) {
          console.log('更新歌曲:', song.songid, downloadState);
          song.downloadState = downloadState;
          song.downloadProgress = downloadProgress || 0;
          song.src = src;
        }
      }
    
      // 过滤掉 songid 为 null、undefined 或 0 的数据
      const filteredOrderedList = state.orderedList.filter(song => song?.songid && song.songid !== 0);
      const filteredAlreadyList = state.alreadyList.filter(song => song?.songid && song.songid !== 0);
    
      // 更新过滤后的列表
      filteredOrderedList.forEach(updateSong);
      filteredAlreadyList.forEach(updateSong);
    
      // 重新赋值给 state
      state.orderedList = filteredOrderedList;
      state.alreadyList = filteredAlreadyList;
    
      // 更新当前歌曲
      if (songid === state.videoPlayerHistory.songItem.songid) {
        state.videoPlayerHistory = {
          ...state.videoPlayerHistory,
          songItem: {
            ...state.videoPlayerHistory.songItem,
            src,
            downloadProgress,
            downloadState: downloadState || 0
          }
        };
      }
    },
    RESET_VIDEO_PLAYER(state) {
      const enabledAudioTrack = state.videoPlayerHistory.enabledAudioTrack
      state.videoPlayerHistory = {
        songItem: {
          acc: 0,
          flag: [],
          music_name: '',
          org: 0,
          singer: '',
          songid: 0,
          m3u8: '',
          hls: '',
          token: '', // 新增token播放鉴权
          tokenExp: 300, // token过期时间
          serverTime: 0,
          lrc: 0,
          lrcData: {
            is_utf8: 1, // 是否是utf8格式文件 0:false 1:true
            down_url: '', // 歌词下载文件地址
          },
        },
        songItemLrc: [],
        defaultAccTrackOrder: 1,
        audioTrackMap: {
          acc: {
            id: 2,
          },
          org: {
            id: 1
          }
        },
        currentTime: 0,
        enabledAudioTrack,
        recovery: false
      }
    },
    RESET_VIDEO_PLAYER_HLS(state) {
      state.videoPlayerHistory = {
        ...state.videoPlayerHistory,
        songItem: {
          ...state.videoPlayerHistory.songItem,
          hls: ''
        },
        supportsAcc: true,
        supportsOrg: true,
      }
    },
    SAVE_LOGINMODAL_ENABLED(state, enabled) {
      state.loginModalEnabled = enabled;
    },
    SAVE_MAC_ADDRESS(state, macAddress) {
      state.macAddress = macAddress;
    },
    SAVE_SYSTEM_INFO(state, systemInfo) {
      state.systemInfo = systemInfo;
    },
    SAVE_ORDERED_LIST(state, orderedList) {
      state.orderedList = orderedList.filter(item => item.songid && item.music_name)
    },
    SAVE_ALREADY_LIST(state, alreadyList) {
      console.log('保存已点', alreadyList)
      state.alreadyList = alreadyList.filter(item => item.songid && item.music_name).slice(0, 99);
    },
    SAVE_ORDERED_SONGIDMAP(state, id) {
      state.orderedSongIdMap[id] = true
    },
    PUSH_SONG_TO_ALREADY_LIST(state, song) {
      let newAList = [song, ...state.alreadyList.filter(item => item.songid !== song.songid)];
      state.alreadyList = newAList.filter(item => item.songid && item.music_name).slice(0, 99);
    },
    DELETE_SONG_ALREADY_LIST(state, index) {
      let newAList = [ ...state.alreadyList ]
      newAList.splice(index, 1)
      state.alreadyList = newAList
    },
    CLEAR_ORDERED_LIST(state) {
      state.orderedList = []
      state.orderedSongIdMap = {}
    },
    UPDATE_ALL_ORDERED_LIST_ITEMS_BY_SONGID(state, { songid, ...newData }) {
      state.orderedList = state.orderedList.map(song => {
        if (song.songid === songid) {
          return { ...song, ...newData };
        }
        return song;
      });
      console.log(`已根据songid更新所有匹配的歌曲属性`, state.orderedList);
    },
    CLEAR_ALREADY_LIST(state) {
      state.alreadyList = []
    },
    PUSH_SONG_TO_ORDERED_LIST(state, { song, from }) {
      console.log('加入已点', song.songid, song.music_name)
      delete song._i;
      state.orderedList = state.orderedList.concat([{
        ...song,
        ID: nanoid(32)
      }])
      // 同步存储已点map
      state.orderedSongIdMap[song.songid] = true
      
      sendLog({
        event_type: 'click',
        event_name: 121,
        event_data: {
          song_id: song.songid,
          song_name: song.music_name,
          singer: song.singer,
          song_isvip: song.is_vip, // 增加歌曲vip上报标识
        }
      })
    },
    SHIFT_SONG_ORDERED_LIST(state) {
      state.orderedSongIdMap = state.orderedList.reduce((acc, cur) => {
        if (cur) {
          acc[cur.songid] = true
        }
        return acc
      }, {})
      // delete state.orderedSongIdMap[_shiftSong.songid]
    },
    DELETE_SONG_ORDER_LIST(state, index) {
      const newOrderedList = [ ...state.orderedList ]
      const deletedItem = get(newOrderedList.splice(index, 1), '0', null)
      state.orderedList = newOrderedList
      if (deletedItem) {
        state.orderedSongIdMap = state.orderedList.reduce((acc, cur) => {
          if (cur) {
            acc[cur.songid] = true
          }
          return acc
        }, {})
        // delete state.orderedSongIdMap[deletedItem.songid]
      }
    },
    STICK_SONG_TO_TOP_ORDERED_LIST(state, index) {
      const newOrderedList = [ ...state.orderedList ]
      const deletedItem = get(newOrderedList.splice(index, 1), '0', null)
      if (deletedItem) {
        // 在第二位加入这首歌
        newOrderedList.splice(1, 0, deletedItem)
      }
      state.orderedList = newOrderedList
    },
    SAVE_VIDEO_PLAYER_HISTORY(state, videoPlayerHistory) {
      state.videoPlayerHistory = {
        ...videoPlayerHistory
      }
    },
    SAVE_VIDEO_PLAYER_HISTORY_SONG_ITEM(state, songItem) {
      console.log('SAVE_VIDEO_PLAYER_HISTORY_SONG_ITEM', songItem?.org, songItem?.acc);
    
      // 初始化支持原唱和伴唱的标志
      let supportsOrg = true;
      let supportsAcc = true;
    
      // 初始化 audioTrackMap
      let audioTrackMap = {
        org: { id: songItem?.org || 1 },
        acc: { id: songItem?.acc || 2 }
      };

      let enabledAudioTrack = state.videoPlayerHistory.enabledAudioTrack
    
      // 根据 songItem 中的 org 和 acc 值判断是否支持原唱和伴唱
      if (songItem?.org === 1 && songItem?.acc === 1) {
        supportsAcc = false;
        audioTrackMap.acc.id = 2;
      } else if (songItem?.org === 2 && songItem?.acc === 2) {
        supportsOrg = false;
        audioTrackMap.org.id = 1;
      }
    
      console.log('audioTrackMap', audioTrackMap);
      console.log('supportsOrg', supportsOrg, 'supportsAcc', supportsAcc);
    
      // 更新 state
      state.videoPlayerHistory = {
        ...state.videoPlayerHistory,
        songItem: {
          ...state.videoPlayerHistory.songItem,
          ...songItem
        },
        supportsOrg,
        supportsAcc,
        audioTrackMap,
        enabledAudioTrack,
      };
    },
    SAVE_VIDEO_PLAYER_HISTORY_AUDIO_TRACKS(state, audioTrackMap) {
      state.videoPlayerHistory.audioTrackMap = audioTrackMap
    },
    SAVE_VIDEO_PLAYER_HISTORY_CURRENT_TIME(state, currentTime) {
      state.videoPlayerHistory.currentTime = currentTime || 0
    },
    SAVE_VIDEO_PLAYER_HISTORY_ENABLED_AUDIO_TRACK(state, enabledAudioTrack) {
      state.videoPlayerHistory.enabledAudioTrack = enabledAudioTrack
    },
    SAVE_VIDEO_PLAYER_HISTORY_DEFAULT_ACC_TRACK_ORDER(state, defaultAccTrackOrder) {
      console.log('defaultAccTrackOrder', defaultAccTrackOrder)
      state.videoPlayerHistory.defaultAccTrackOrder = defaultAccTrackOrder
    },
    UPDATE_VIDEO_PLAYER_RECOVERY(state, recovery) {
      state.videoPlayerHistory.recovery = recovery
    },
    SAVE_SETTING(state, setting) {
      state.setting = setting
    },
    CHANGE_PLAYING_MV_QUALITY(state, val) {
      state.playingMvQuality = val
      console.log('CHANGE_PLAYING_MV_QUALITY', val)
    },
    CHANGE_PLAYING_MV_QUALITY_RESOURCE(state, hls) {
      state.videoPlayerHistory = {
        ...state.videoPlayerHistory,
        songItem: {
          ...state.videoPlayerHistory.songItem,
          hls,
        }
      }
    },
    SAVE_CARPLAY_INFO(state, carplayInfo) {
      state.carplayInfo = carplayInfo
    },
    SAVE_USER_INFO(state, userInfo) {
      state.userInfo = {
        ...state.userInfo,
        ...userInfo,
      }
    },
    SAVE_HAS_MIC(state, data) {
      state.hasMic = data
    },
    SAVE_HAS_DONGLE(state, data) {
      state.hasDongle = data
    },
    SAVE_ACTIVITY_INFO(state, activity) {
      state.activityInfo = {
        ...state.activityInfo,
        ...activity,
      }
    },
    SAVE_AUTH_INFO(state, authInfo) {
      state.userInfo = {
        ...state.userInfo,
        ...authInfo,
      }
    },
    SAVE_VIP_INFO(state, vipInfo) {
      state.vipInfo.end_time = vipInfo.end_time || ''
      state.vipInfo.vip_imgs = vipInfo.vip_imgs || []
      state.vipInfo.expire = vipInfo.expire || false
    },
    SAVE_IS_FORCE_LOGIN(state, data) {
      state.isForceLogin = data.isForceLogin
    },
    SAVE_SING_TIPS(state, data) {
      state.sing_tips = data
    },
    UPDATE_MV_ISHIDE(state, isHide) {
      state.mvIsHide = isHide
    },
    UPDATE_MV_VIDEO_PAUSE(state, videoPaused) {
      state.videoPaused = videoPaused
    },
    UPDATE_MV_VIDEO_VOLUME(state, volume) {
      state.videoVolume = volume
      store2.set('videoVolume', volume)
    },
    UPDATE_MV_VIDEO_MEDIA(state, volume) {
      state.mediaVolume = volume
    },
    UPDATE_MV_INIT_AUTOPLAY(state, num) {
      state.videoInitAutoPlay = num
    },
    UPDATE_IS_SING_STATUS(state, bol) {
      state.isSingStatus = bol
    },
    UPDATE_PAGE_CACHEDATA(state, payload) {
      const { data, type } = payload
      state.pageCacheData[type] = { ...state.pageCacheData[type], ...data }
    },
    UPDATE_CURR_SONG_HLS(state, data) {
      console.log('UPDATE_CURR_SONG_HLS', data)
      state.videoPlayerHistory = {
        ...state.videoPlayerHistory,
        songItem: {
          ...state.videoPlayerHistory.songItem,
          hls: data.hls,
          src: data.src,
          token: data.token,
          tokenExp: data.tokenExp,
          m3u8: data.m3u8,
          serverTime: data.serverTime,
          lrcData: data.lrc
        }
      }
      for (let q in data.m3u8) {
        // if (data.hls === data.m3u8[q]) state.playingMvQuality = q
        if (data.hls === data.m3u8[q]) {
          state.playingMvQuality = q
          // 更新本地及云端数据
          state.setting = {
            ...state.setting,
            quality: q
          }
          store2('setting', state.setting)
          if (state.userInfo.unionid && state.carplayInfo.resolution !== q) {
            resolutionRatio({ unionid: state.userInfo.unionid, quality: q})
          }
        }
      }
    },
    UPDATE_CURR_SONG_TOKEN(state, data) {
      state.videoPlayerHistory = {
        ...state.videoPlayerHistory,
        songItem: {
          ...state.videoPlayerHistory.songItem,
          token: data.token,
          tokenExp: data.tokenExp,
          serverTime: data.serverTime,
        }
      }
    },
    UPDATE_APP_START_TIME(state, data) {
      state.appStartTime = data
    },
    UPDATE_VIDEO_MIC_VOLUME(state, data) {
      state.micVolume = data
      openDatabase().then(() => {
        setItem('micVolume', data).then(console.log);
      }).catch(console.error);
    },
    UPDATE_VIDEO_REVERB_VOLUME(state, data) {
      state.reverbVolume = data
      openDatabase().then(() => {
        setItem('reverbVolume', data).then(console.log);
      }).catch(console.error);
    },
    UPDATE_CONTROL_FROM_TYPE(state, data) {
      console.log('UPDATE_CONTROL_FROM_TYPE', data)
      state.controlFromType = data
    },
    RESET_CURR_SONG_LRC(state) {
      state.videoPlayerHistory.songItemLrc = []
      state.videoPlayerHistory.songItemLrcEmpty = false
      state.currIrcIndex = -1
    },
    UPDATE_CURR_SONG_LRC(state, data) {
      state.videoPlayerHistory.songItemLrc = data.lrc
      state.videoPlayerHistory.songItemLrcEmpty = !get(data, 'lrc.length', 0)
    },
    UPDATE_CURR_IYRIC_INDEX(state, data) {
      state.currIrcIndex = data
    },
    UPDATE_LOWERBEFOREQ(state, data) {
      state.lowerBeforeQ = data
    },
    UPDATE_MV_IS_OPENED(state, data) {
      state.mvIsOpened = data
    },
    SAVE_SERVICE_INFO(state, data) {
      state.serviceLicenceInfo = data
    },
    SET_MICROPHONES(state, microphones) {
      console.log('microphones:', microphones)
      state.microphones = microphones;
    },
    // SYNC_SCREENDATA(state, data) {
    //   const localSDataArr = [
    //     '_boot_id',
    //     'searchCache',
    //     '_session_id',
    //     'showMvControlGuide',
    //     'alreadyData',
    //     'vipModalActivityTag',
    //     'mac_address',
    //     'orderedData',
    //     'micActivityTag',
    //     'abTestTag',
    //     'OperationPopupPay',
    //     'microphones',
    //   ]
    //   const resData = data
    //   Object.keys(resData).forEach(v => {
    //     if (localSDataArr.includes(v)) {
    //       store2(v, JSON.parse(resData[v]))
    //     } else {
    //       state[v] = JSON.parse(resData[v])
    //     }
    //   })
    // },
    // TYPE_DATA_SCREENDATA(state, data) {
    //   state[data.name] = data.data
    //   console.log(`TYPE_DATA_SCREENDATA:${data.name}-${data.data}`)
    // },
    SET_THEME(state, theme) {
      state.themeClass = theme
    },
    UPDATE_SYSTEM_MUTED(state, data) {
      console.log('UPDATE_SYSTEM_MUTED', data)
      state.systemVolumeMuted = data
    },
    YS_TIP_ACCEPT(state, val) {
      state.storageYSTipAccept = val
    },
    UPDATE_SONG_AVAILABLE_QUALITIES(state, qualities) {
      state.availableQualities = qualities
    },
    SAVE_FREE_VIP_COUNT(state, freeVipNumber) {
      state.freeVipNumber = freeVipNumber;
    },
  },
  actions: {
    setVideoPaused: throttle(function({ commit }, payload) {
      // console.log('setVideoPaused', payload)
      commit('UPDATE_MV_VIDEO_PAUSE', payload);
    }, 300, {
    }),
    saveVideoPlayerSongItem({ commit }, payload) {
      commit('SAVE_VIDEO_PLAYER_HISTORY_SONG_ITEM', payload)
    },
    stickSongToTopOrderedList({ dispatch, commit }, payload) {
      commit('STICK_SONG_TO_TOP_ORDERED_LIST', payload)
      dispatch('download/checkAutoDownload')
    },
    shiftOrderedSongList({ dispatch, commit }) {
      commit('SHIFT_SONG_ORDERED_LIST')
      dispatch('download/checkAutoDownload')
    },
    addSongToOrderedList({ dispatch, commit }, payload) {
      commit('PUSH_SONG_TO_ORDERED_LIST', { song: payload})
      dispatch('download/checkAutoDownload')
    },
    updateMicrophones({ commit, state }, { id, status, reset }) {
      if (reset) {
        // 重置所有麦克风的status为false
        state.microphones.forEach(mic => {
          mic.status = false;
        });
      } else {
        const microphone = state.microphones.find(mic => mic.id === id);
        if (microphone) {
          microphone.status = status;
        } else if (id >= 0) {
          state.microphones.push({ id, status });
        }
      }
      // 根据status排序，优先将status为true的元素放在数组前面
      state.microphones.sort((a, b) => (b.status - a.status));
      const online = state.microphones.filter(item => item.status)

      store2('microphones', state.microphones)
      commit('SET_MICROPHONES', state.microphones);
      commit('SAVE_HAS_MIC', !!online.length);
    },
    updateCurrSongToken({ commit }, payload) {
      commit('UPDATE_CURR_SONG_TOKEN', payload)
    },
    startCheckLoginKeepStatus({ dispatch }) {
      function checkLoginKeepStatus() {
        if (checkLoginKeepStatusTimer) {
          clearTimeout(checkLoginKeepStatusTimer)
        }
        dispatch('getCarplayInfo')
        checkLoginKeepStatusTimer = setTimeout(checkLoginKeepStatus, 60 * 1000)
      }
      checkLoginKeepStatus()
    },
    // TODO 先这么写，不太对
    async startCheckPayStatus({ dispatch }, { order_id }) {
      let checkPayStatusLimit = 0
      async function checkPayStatus() {
        if (checkPayStatusTimer) {
          clearTimeout(checkPayStatusTimer)
        }
        if (checkPayStatusLimit >= 20) {
          checkPayStatusLimit = 0
          return
        }
        checkPayStatusLimit++
        const res = await checkOrderPayStatus(order_id)
        if (get(res, 'errmsg') !== '支付成功') {
          checkPayStatusTimer = setTimeout(checkPayStatus, 5000)
        } else {
          dispatch('getCarplayInfo')
        }
      }
      checkPayStatus()
    },
    setLoginModalEnabled({ commit }, enabled) {
      commit('SAVE_LOGINMODAL_ENABLED', enabled)
    },
    async loginout({ dispatch }, { mac_id, unionid,cleardata }) {
      const res = await loginout({
        mac_id,
        unionid,
      })
      if (get(res, 'errcode') == 200) {
        if (!cleardata) {
          dispatch('getCarplayInfo')
        }
        // 埋点224
        sendLog({
          event_type: 'click',
          event_name: 224,
          event_data: {
            login_status: 1,
            login_msg: get(res, 'errmsg')
          }
        })
      } else {
        // 埋点224
        sendLog({
          event_type: 'click',
          event_name: 224,
          event_data: {
            login_status: 2,
            login_msg: get(res, 'errmsg')
          }
        })
      }
    },
    stopCheckLoginStatus(){
      if (checkLoginStatusTimer) {
        clearTimeout(checkLoginStatusTimer)
      }
    },
    async exchangeVip({ commit }, { cdkey, mac_id }) {
      const exchangeVipRes = await exchangeVip({
        cdkey,
        mac_id
      })
      if (exchangeVipRes.data) {
        commit('SAVE_VIP_INFO', exchangeVipRes.data)
        return exchangeVipRes
      }
    },
    getMacAddress({ commit }) {
      const mac = getMac()
      commit('SAVE_MAC_ADDRESS', mac)
    },
    getSystemInfo({ commit }) {
      commit('SAVE_SYSTEM_INFO', {
        brand: uaInfo.browser.name,
        model: uaInfo.device.model || uaInfo.browser.name,
        system: uaInfo.os.name,
      })
    },
    async getCarplayInfo({ commit, dispatch }, { noLoading = false } = {}) {
      try {
        const { data } = await getCarplayInfo(noLoading)
        if (data) {
          commit('SAVE_CARPLAY_INFO', data)
          if (get(data, 'user.unionid', '')) {
            dispatch('setLoginModalEnabled', false)
          }
          commit('SAVE_IS_FORCE_LOGIN', {
            isForceLogin: get(data, 'isForceLogin', false) // 0：不启用 1：启用
          })
          commit('SAVE_USER_INFO', {
            username: get(data, 'user.nickname', ''),
            avatar: get(data, 'user.headimgurl', ''),
            unionid: get(data, 'user.unionid', ''),
            openid: get(data, 'user.openid', ''),
            phone: get(data, 'user.phone', ''),
          })
          // commit('SAVE_HAS_MIC', get(data, 'has_mic', false))
          commit('SAVE_ACTIVITY_INFO', {
            loginSendVip: get(data, 'event.free_vip', ''),
          })
          commit('SAVE_VIP_INFO', {
            vip_imgs: data.vip_imgs,
            end_time: data.vip_info.end_time,
            expire: data.vip_info.expire,
          })
          commit('SAVE_SING_TIPS', {
            duration: get(data, 'sing_tips.duration', 6), // 默认提示内容显示6s
            img: get(data, 'sing_tips.img', 'https://qncweb.ktvsky.com/20231024/other/5b9ee37d2990e0ca7f64c18b929e3472.png'), // 提示框的云控背景
            songs: get(data, 'sing_tips.songs', 4), // 加入已点歌曲数量，当达到此数量时进行显示
            text: get(data, 'sing_tips.text', '提示您：已点{songs}首，立即欢唱吧~'), // 提示框的文案
          })
        }
      } catch (error) {
        console.log('接口请求错误')
      }
    },
    saveSetting({ commit }, setting) {
      store2('setting', setting)
      commit('SAVE_SETTING', setting)
    },
    getSetting({ commit }) {
      let setting = store2('setting')
      if (!setting) {
        setting = this.state.setting
      }
      commit('SAVE_SETTING', setting)
      console.log('启动应用，获取本地画质', setting.quality)
      commit('CHANGE_PLAYING_MV_QUALITY', setting.quality)
    },
    async setActivityInfo({ commit }) {
      try {
        const data = await getActivityInfo()
        if (get(data, 'tp') == 10) {
          // vip二维码弹窗
          commit('SAVE_ACTIVITY_INFO', {
            openscreen_vip: {
              id: get(data, 'id', false),
              img: get(data, 'imgurl', ''),
            },
          })
        } else {
          // 空值
          commit('SAVE_ACTIVITY_INFO', {
            openscreen_vip: {
              id: false,
              img: '',
            },
          })
          if (get(data, 'tp') == 12) {
            // 歌曲推荐弹窗
            console.log('12')
          }
        }
      } catch (err) {
        console.log('接口请求错误')
      }
    },
    orderSongType({ state }, data) {
      data.forEach((item) => {
        const curIndex = state.orderedList.findIndex((data) => {
          return item.songid === data.songid
        })
        if (curIndex > -1) {
          item.type = 'order'
        } else {
          delete item.type
        }
      })
    },
  },
  modules: {
    base,
    abTest,
    songLog,
    search,
    oftenSing,
    searchTips,
    system,
    carMonitor,
    mvMode,
    download,
    storage,
    score,
    config,
    vipAddSong,
  }
});

export default store