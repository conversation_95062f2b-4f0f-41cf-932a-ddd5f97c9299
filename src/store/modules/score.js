const state = {
  enabled: true,
  scoreSongInfo: {
    songId: '',
    musicName: '',
    scoreSourceUrl: '',
    isScore: 0,
  },
  result: {
    name: '',
    score: 0,
  }
}

const getters = {}

const actions = {
  updateCurSongIsSupport({ commit }, payload) {
    commit('SET_SCORE_SONG_INFO', payload)
  },
  open({ commit }) {
    commit('SET_SCORE_ENABLED', true);
  },
  close({ commit }) {
    commit('SET_SCORE_ENABLED', false)
  },
}

const mutations = {
  SET_SCORE_ENABLED(state, data) {
    state.enabled = data 
  },
  SET_SCORE_SONG_INFO(state, data) {
    state.scoreSongInfo = data
  },
  SET_SCORE_RESULT(state, data) {
    state.result = data
  },
  RESET_SCORE_RESULT(state) {
    state.result = {
      name: '',
      score: 0
    }
  }
}

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations,
}
