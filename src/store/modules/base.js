import { getBaseInfo, reportSession, getConfig } from '@/service/base'
import { getMall } from '@/service/base';

const state = {
  loginModalEnabled: false, // 此字段未使用，暂时保留
  phone_control_qr: '',
  nats: {
    eip: '',
    iip: '',
    port: '',
    PingInterval: 15,
    reconnectdelay: 3,
    subs: '',
  },
  icon: {},
  score: {},
  launch_img: {},
  mallInfo: null,
  net_status: true,
  needLoading: false,
  isRouterBack: false,
  appStatus: true, // 是否在前台
}

const getters = {
  getLevelAndDesc: (state) => (totalScore) => {
    const result = state.score.level.find((item) => totalScore >= item.score);
    return {
      level: result ? result.level : '',
      desc: result ? result.desc : '',
    };
  },
}

const actions = {
  reportSession() {
    reportSession()
  },
  setLoginModalEnabled({ commit }, enabled) {
    commit('SAVE_LOGINMODAL_ENABLED', enabled)
  },
  async getBaseInfo({ commit }) {
    try {
      const { data } = await getBaseInfo()
      console.log(data)
      data && commit('SAVE_BASE_INFO', data)
    } catch (error) {
      console.log('base info')
    }
  },
  async getConfig({ commit }) {
    try {
      const { data } = await getConfig()
      console.log(data)
      data && commit('SAVE_BASE_CONFIG', data)
    } catch (error) {
      console.log('base info')
    }
  },
  async fetchMallInfo({ commit }) {
    try {
      const res = await getMall();
      commit('setMallInfo', res.data);
    } catch (error) {
      // 处理请求错误
    }
  },
}

const mutations = {
  SAVE_LOGINMODAL_ENABLED(state, enabled) {
    state.loginModalEnabled = enabled
  },
  SAVE_BASE_INFO(state, { phone_control_qr, nats }) {
    state.phone_control_qr = phone_control_qr
    state.nats = nats
  },
  SAVE_BASE_CONFIG(state, { score, icon, launch_img }) {
    state.score = score
    state.icon = icon
    state.launch_img = launch_img
  },
  setMallInfo(state, payload) {
    state.mallInfo = payload;
  },
  SET_NET_STATUS(state, status) {
    state.net_status = status
    console.log('SET_NET_STATUS', state.net_status)
  },
  SET_NET_LOADING(state, status) {
    state.needLoading = status
  },
  SET_IS_ROUTER_BACK(state, status) {
    state.isRouterBack = status
  },
  SET_APP_STATUS(state, status) {
    state.appStatus = status
  }
}

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations,
}
