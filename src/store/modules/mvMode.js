
const state = {
    mvMode: {
        mode: 'mv', // mv or 歌词
        modeSwitch: 'open', // open or close
    },
}

const getters = {}

const actions = {
    saveMvMode({ commit }, setting) {
        commit('SAVE_MV_MODE', setting)
    },
}

const mutations = {
    SAVE_MV_MODE(state, setting) {
        state.mvMode = setting
    },
}

export default {
    namespaced: true,
    state,
    getters,
    actions,
    mutations,
}