const state = {
  carSpeed: 0,
  carGear: 1,
  car<PERSON><PERSON>: 1,
  notShowAgain: false,
}

const getters = {}

const actions = {
  setCarSpeed({ commit }, data) {
    commit('SAVE_CAR_SPEED', data)
  },
  setCarGear({ commit }, data) {
    commit('SAVE_CAR_GEAR', data)
  },
  setCarKey({ commit }, data) {
    commit('SAVE_CAR_KEY', data)
  },
}

const mutations = {
  SAVE_CAR_SPEED(state, data) {
    state.carSpeed = data
  },
  SAVE_CAR_GEAR(state, data) {
    state.carGear = data
  },
  SAVE_CAR_KEY(state, data) {
    state.carKey = data
  },
  SET_DIALOG_SHOWN(state, value) {
    state.notShowAgain = value;
  },
}

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations,
}
