import store from '@/store'
import store2 from 'store2'
import { stringify } from 'query-string';
import { getClientWidth, getClientHeight, checkLandscapeOrPortrait } from '@/utils/device'
import { format } from 'date-fns'
import config from '@/config/index'
import getBaseUrl from '@/utils/request/base-url'
import { postOftensing } from '@/service/singing'
import { TSNativeInstance } from '@/packages/TSJsbridge'

function getCurrentTime () {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

function report(reqUrl) {
  try {
    let img = new Image(1, 1);
    img.src = reqUrl;
    img.onload = () => {
      img = null;
    };
  } catch (err) {
    // eslint-disable-next-line
    console.log(err);
  }
}

export function sendHLSReport({ sid, scale, error_msg, error_type}) {
  if (!sid) return;
  const params = {
    uid: store.state.userInfo.unionid || '-',
    sid,
    scale,
    error_msg,
    error_type,
    event_name: 'hls-report',
    event_type: 'custom',
  }
  const host = process.env.NODE_ENV === 'production'
    ? '//hlsreport.ktvsky.com'
    : '';
  const sendUrl = `${host}?${stringify(params)}`;
  report(sendUrl);
}
 
export function sendLog({event_type, event_name, event_data}) {
  if (!event_type || !event_name) return;
  const now = Date.now()
  const commonParams = {
    _src: store.state.system.systemInfo.src,
    _m: store.state.system.systemInfo.model,
    _h: checkLandscapeOrPortrait() === 'landscape' ? 1 : 2,
    _r1: `${getClientWidth()},${getClientHeight()}`,
    _mac: store.state.system.systemInfo.mac,
    _user_id: store.state.userInfo.unionid,
    // _tm: format(now, 'yyyy-mm-dd HH:MM:SS'),
    _tm: getCurrentTime(),
    _ts: now,
    _boot_id: store2('_boot_id'),
    _session: store2('_boot_id'),
    androidid: store.state.system.systemInfo.androidid,
    _did: store.state.system.systemInfo._did,
    _car_id: store.state.system.systemInfo._car_id,
    _mike_bind: store.state.hasDongle ? 2 : 1,
    _type_src: '2',
    _car_model: store.state.system.car_name
  }
  const params = {
    ...commonParams,
    event_name,
    ...event_data,
    _car_model: window.TSVehicle && window.TSVehicle?.getVehicleModel  ? window.TSVehicle?.getVehicleModel()  : '未知',
    _vn: window.TSNative && window.TSNative?.getValue ? window.TSNative?.getValue('_vn') : '未知'
  }
  const host = getBaseUrl({
    pro: '//log3.ktvsky.com',
    pre: '//log3pre.ktvsky.com',
    test: '//log3pre.ktvsky.com'
  })
  const sendUrl = `${host}/log/event?${stringify(params)}`;
  report(sendUrl);
}

export async function sendSongLog(event_data) {
  const commonParams = {
    _src: store.state.system.systemInfo.src || 600232,
    _m: store.state.system.systemInfo.model,
    _h: checkLandscapeOrPortrait() === 'landscape' ? 1 : 2,
    _vn: `v${config.vn}_${store.state.abTest.abTestVersion || store2('abTestTag')}`,
    mac: store.state.macAddress,
    // end_type: '', // 1:正常播放结束，2：切歌
    // song_id: '',
    // song_name: '',
    // singer: '',
    // start_time: '', // 格式："2020-07-27 14:02:20"
    // end_time: '',
    // play_time: '', // 歌曲播放真实的总时长（去除暂停），单位秒(看打分结果页怎么拿时长)
    unionid: store.state.userInfo.unionid,
  }
  const params = {
    ...commonParams,
    ...event_data,
    _car_model: window.TSVehicle && window.TSVehicle?.getVehicleModel  ? window.TSVehicle?.getVehicleModel()  : '未知',
     _vn: window.TSNative && window.TSNative?.getValue ? window.TSNative?.getValue('_vn') : '未知'
  }
  const host = getBaseUrl({
    pro: '//log3.ktvsky.com',
    pre: '//log3pre.ktvsky.com',
    test: '//log3pre.ktvsky.com'
  })
  const sendUrl = `${host}/song/playex?${stringify(params)}`;
  report(sendUrl);
  try {
    // 实时上报唱过的歌
    await postOftensing(params)
    store.dispatch('oftenSing/initOftenSingList', store.state.userInfo.unionid)
  } catch (error) {
    console.log('postOftensing error', error)
  }

}