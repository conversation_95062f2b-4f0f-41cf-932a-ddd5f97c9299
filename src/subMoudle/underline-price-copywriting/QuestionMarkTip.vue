<template>
  <span class="question-mark-tip" @click.stop="showTip">
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M11.2075 13.6774V13.5323C11.2075 13.0655 11.2866 12.662 11.4693 12.3349C11.6401 11.9822 12.1379 11.4653 12.9495 10.7723L13.1724 10.533C13.4084 10.2429 13.539 9.94022 13.539 9.61318C13.539 9.17142 13.4084 8.83186 13.1592 8.57934C12.8974 8.32747 12.5176 8.20153 12.0462 8.20153C11.4561 8.20153 11.0242 8.37824 10.7625 8.74351C10.5284 9.04285 10.411 9.46615 10.4084 10.0127C10.4111 10.0373 10.4124 10.0619 10.4124 10.0866C10.4124 10.4545 10.1038 10.7525 9.72269 10.7525C9.34159 10.7525 9.03302 10.4545 9.03302 10.0866C9.03304 10.0672 9.03392 10.0479 9.03566 10.0286H9.03302C9.03302 9.10879 9.30797 8.39076 9.85852 7.86131C10.4084 7.31934 11.168 7.05494 12.1379 7.05494C12.9759 7.05494 13.657 7.26923 14.1812 7.72285C14.7053 8.16395 14.9671 8.76857 14.9671 9.52483C14.9671 10.142 14.797 10.6589 14.4693 11.0618C14.3513 11.2002 13.9847 11.5286 13.3688 12.0448C13.1427 12.2234 12.9556 12.4465 12.819 12.7002C12.6766 12.9539 12.6043 13.2408 12.6093 13.5316V13.7459C12.5928 14.1066 12.2842 14.3941 11.9071 14.3941C11.5201 14.3941 11.2062 14.0908 11.2062 13.7163C11.2062 13.7031 11.2062 13.6905 11.2075 13.6774ZM12.0001 21.2308C6.90203 21.2308 2.76929 17.098 2.76929 12C2.76929 6.90197 6.90203 2.76923 12.0001 2.76923C17.0981 2.76923 21.2308 6.90197 21.2308 12C21.2308 17.098 17.0981 21.2308 12.0001 21.2308ZM12.0001 19.9121C16.3695 19.9121 19.9121 16.3694 19.9121 12C19.9121 7.63055 16.3695 4.08791 12.0001 4.08791C7.63061 4.08791 4.08797 7.63055 4.08797 12C4.08797 16.3694 7.63061 19.9121 12.0001 19.9121ZM11.9018 15.1431C12.1768 15.1431 12.4128 15.2189 12.5961 15.3949C12.7794 15.5591 12.871 15.7734 12.871 16.0378C12.871 16.3022 12.7662 16.5165 12.5829 16.6932C12.3952 16.8591 12.1523 16.9489 11.9018 16.945C11.6401 16.9451 11.404 16.8567 11.2207 16.6807C11.1323 16.5988 11.0622 16.4992 11.0148 16.3885C10.9675 16.2777 10.9439 16.1582 10.9458 16.0378C10.9458 15.7734 11.0374 15.5591 11.2207 15.3949C11.404 15.2189 11.6401 15.1431 11.9018 15.1431Z" fill="#CBCBCB"/>
    </svg>
  </span>
</template>
<script setup>
import Toast from '@/utils/toast'

const showTip = () => {
    // console.log('??????')
  Toast({
    message: '划线价：促销活动前产品标价 ，促销活动截止日期：2027-06-17',
    position: 'center',
    className: 'toast-zoom-max'
  })
}
</script>
<style lang="stylus" scoped>
.question-mark-tip
  display inline-flex
  align-items center
  cursor pointer
  margin-left 4px
  margin-top 0
  height 1em
  line-height 1em
  svg
    vertical-align middle
    height 1.2em
    width auto
</style> 