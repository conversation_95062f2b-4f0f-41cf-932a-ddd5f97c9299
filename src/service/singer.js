// import http from '@/utils/http'
import httpV2 from '@/utils/httpV2'
import get from 'lodash/get'

export async function getSingerClassList() {
  const res = await httpV2.get('/stb/v2/top/class/singer')
  return get(res, 'data', []);
}

export async function getSingerList({k, p, version}, options = {}) {
  // /stb/carplay/detail/class
  if (!k) return []

  let params = {
    k,
    p,
    size: 18,
  }
  if (version && p > 1) {
    params = {
      ...params,
      ver: version
    }
  }
  const res = await httpV2.get('/stb/v2/detail/class/singer', { 
    params,
    cancelToken: options.cancelToken,
  })
  if (p <= 1) {
    return {
      data: [...get(res, 'data', [])],
      version: get(res, 'version', {})
    }
  }
  return {
    data: get(res, 'data', []),
    version: get(res, 'version', {})
  }
}

export async function getSongInfo(songid) {
  const params = {
    songid,
  }
  const res = await httpV2.get('/stb/v2/detail/song/info', { params })
  return get(res, 'data', []);
}