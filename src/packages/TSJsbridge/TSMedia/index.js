import Toast from '@/utils/toast'
import store from '@/store'
import { DownloadStatus } from '@/utils/download'

const noopMethodList = [
  'startPlay',
  'playOnline',
  'replay',
  'playNext',
  'pause',
  'resume',
  'getIsPlaying',
  'getSelectedTrack',
  'selectTrack',
  'getDuration',
  'getCurrentPosition',
  'getStreamMaxVolume',
  'getStreamVolume',
  'setStreamVolume',
  'stop',
  'updateToken',
  'setResolution'
]

function transMicoroVolumeToSlide(val, max) {
  const result = Math.floor(val / max * 100)
  return Number.isNaN(result) ? 5 : result
}

export class TSMediaBridge {
  song = null
  songJsonString = JSON.stringify({})
  maxStreamVolume = 0

  constructor() {
    if (!window.TSMedia) {
      console.log('TSMedia interface not found!')
      Toast({
        message: 'TSMedia interface not found!',
        className: 'not-support',
        position: 'top',
      })
      noopMethodList.forEach(method => {
        this[method] = (payload) => {
          console.log('call TSMedia fun:', method)
          console.log('call payload', JSON.stringify(payload))
        };
      });
    } else {
      console.log('TSMedia', 'interface', 'installed')
      this.init()
    }
  }

  init() {
    try {
      console.log('TSMedia', 'init')
      this.maxStreamVolume = this.getStreamMaxVolume()
      console.log('TSMedia', 'maxStreamVolume', this.maxStreamVolume)
      // const initStreamVolume = this.getStreamVolume()
      const initStreamVolume = localStorage.getItem('videoVolume') || store.state.videoVolume
      const transVolume = transMicoroVolumeToSlide(initStreamVolume, this.maxStreamVolume)
      const resolution = this.getResolution()
      store.commit('UPDATE_MV_VIDEO_VOLUME', transVolume)
      console.log('UPDATE_MV_VIDEO_VOLUME', transVolume, this.maxStreamVolume)
      store.commit('CHANGE_PLAYING_MV_QUALITY', resolution)
    } catch (error) {
      console.log('TSMedia init error:', error)
    }
  }

  setSong(song) {
    this.song = song
    this.songJsonString = JSON.stringify(song)
    return this.songJsonString
  }

  startPlay(song) { // 播放（边下边播/在线播放）
    if (!song) {
      console.log('TSMedia', 'startPlay', '未传入歌曲', song)
      return
    }
    if (song.downloadState === DownloadStatus.SUCCESS) {
      this.play(song)
      return
    }
    try {
      console.log('TSMedia', 'startPlay', song.music_name, '边下边播', JSON.stringify(song))
      song.acc = song.acc || 2
      song.org = song.org || 1
      this.songJsonString = JSON.stringify(song)
      window.TSMedia.playOnline(this.songJsonString, true)
    } catch (error) {
      console.log(error)
    }
  }

  play(song) {
    if (!song) {
      console.log('TSMedia', 'play', '未传入歌曲')
      return
    }
    try {
      console.log('TSMedia', 'play', song.music_name, '打分', song.is_score, '本地播放')
      song.acc = song.acc || 2
      song.org = song.org || 1
      if (!song.score_source_url && store.state.config.score_url_format) {
        song.score_source_url = store.state.config.score_url_format.replace('{songid}', song.songid);
      }
      this.songJsonString = JSON.stringify(song)
      window.TSMedia.play(this.songJsonString)
    } catch (error) {
      console.log(error)
    }
  }

  replay(song) {
    if (!song) {
      console.log('TSMedia', 'replay', '未传入歌曲')
      return
    }
    try {
      console.log('TSMedia', 'replay', song.music_name, JSON.stringify(song))
      song.acc = song.acc || 2
      song.org = song.org || 1
      this.songJsonString = JSON.stringify(song)
      window.TSMedia.replay(this.songJsonString)
    } catch (error) {
      console.log(error)
    }
  }

  stop() {
    try {
      console.log('TSMedia.stop')
      window.TSMedia.stop()
    } catch (error) {
      console.log(error)
    }
  }

  playNext(song, isLocal) {
    if (!song) {
      console.log('TSMedia', 'playNext', '未传入歌曲')
      return
    }
    try {
      console.log('TSMedia', 'playNext', song.music_name, song.downloadState)
      song.acc = song.acc || 2
      song.org = song.org || 1
      this.songJsonString = JSON.stringify(song)
      if (isLocal || song.downloadState === DownloadStatus.SUCCESS) {
        this.play(song)
      } else {
        this.startPlay(song)
      }
    } catch (error) {
      console.log(error)
    }
  }
  pause() {
    console.log('pause')
    try {
      window.TSMedia.pause()
    } catch (error) {
      console.log('pause error', error)
    }
  }
  resume() {
    try {
      const result = window.TSMedia.resume()
      console.log('resume', result)
      return JSON.parse(result)
    } catch (error) {
      return {
        resultCode: 1,
      }
    }
  }
  getIsPlaying() {
    try {
      const res = window.TSMedia.isPlaying();
      return res;
    } catch (error) {
      console.log('getIsPlaying error', error);
      
    }
  }

  getIsPlayingFinish() {
    try {
      const res = window.TSMedia.isPlayFinish();
      return res;
    } catch (error) {
      console.log('isPlayFinish error', error);
      
    }
  }
  
  getSelectedTrack() {
    try {
      const res = window.TSMedia.getSelectedTrack();
      console.log('getSelectedTrack', res);
      return res;
    } catch (error) {
      console.log('getSelectedTrack error', error);
      
    }
  }
  
  // 1原唱 2伴唱
  selectTrack(index) {
    try {
      window.TSMedia.selectTrack(index);
    } catch (error) {
      console.log('selectTrack error', error);
      
    }
  }
  
  getDuration() {
    try {
      const res = window.TSMedia.getDuration();
      console.log(res);
      return res;
    } catch (error) {
      console.log('getDuration error', error);
      
    }
  }
  
  getCurrentPosition() {
    try {
      const res = window.TSMedia.getCurrentPosition();
      console.log(res);
      return res;
    } catch (error) {
      console.log('getCurrentPosition error', error);
      
    }
  }
  
  getStreamMaxVolume() {
    try {
      const res = window.TSMedia.getMaxVolume();
      console.log('getStreamMaxVolume', res);
      return res;
    } catch (error) {
      console.log('getStreamMaxVolume error', error);
      
    }
  }
  
  getStreamVolume() {
    try {
      const res = window.TSMedia.getVolume();
      console.log('getStreamVolume', res);
      return res;
    } catch (error) {
      console.log('getStreamVolume error', error);
      
    }
  }
  
  setStreamVolume(volume) {
    try {
      console.log('setStreamVolume', volume);
      const _volume = Math.floor(volume / 100 * this.maxStreamVolume);
      console.log('setStreamVolume _volume', _volume);
      window.TSMedia.setVolume(_volume);
    } catch (error) {
      console.log('setStreamVolume error', error);
      
    }
  }
  
  updateToken() {
    try {
      // 根据需要实现具体的逻辑
    } catch (error) {
      console.log('updateToken error', error);
      
    }
  }
  
  setResolution(resolution) {
    try {
      console.log('setResolution', resolution);
      window.TSConfig.setResolution(Number(resolution));
    } catch (error) {
      console.log('setResolution error', error);
      
    }
  }
  
  getResolution() {
    try {
      const res = window.TSConfig.getResolution();
      console.log('getResolution', res);
      return res;
    } catch (error) {
      console.log('getResolution error', error);
      
    }
  }
}

export const TSMediaInstance = new TSMediaBridge()