import Toast from '@/utils/toast'
import store from "@/store"
import eventBus from '@/utils/event-bus'
import throttle from 'lodash/throttle'
import store2, { local } from 'store2'
import { getSoundfile } from '@/service/carplay-info'
import { sendLog } from '@/directives/v-log/log';
import { openDatabase, setItem, getItem } from '@/utils/IndexedDB.js';

const noopMethodList = [
  'getMicMaxVolume',
  'getMicVolume',
  'setMicVolume',
  'getReverbMaxVolume',
  'getReverbVolume',
  'setReverbVolume',
  'hasThunderMic',
  'setAudioEffect',
  'pauseLoopback',
  'getMicConnect',
  'hasDongleConnect',
]

export class TSMicrophoneBridge {
  maxMicVolume = 0
  maxEchoVolume = 0

  constructor() {
    if (!window.TSMicrophone) {
      console.log('TSMicrophone interface not found!')
      Toast({
        message: 'TSMicrophone interface not found!',
        className: 'not-support',
        position: 'top',
      })
      noopMethodList.forEach(method => {
        this[method] = (payload) => {
          console.log('call TSMicrophone fun:', method)
          console.log('call payload', JSON.stringify(payload))
        };
      });
    } else {
      console.log('TSMicrophone', 'interface', 'installed')
      this.init()
    }
  }

  init() {
    const maxMicVolume = this.getMicMaxVolume();
    store2.set('maxMicVolume', maxMicVolume);
    const maxEchoVolume = this.getReverbMaxVolume();
    store2.set('maxEchoVolume', maxEchoVolume);
  
    (async () => {
      try {
        await openDatabase();
        const savedMicVolume = await getItem('micVolume');
        const initMicVolume = savedMicVolume !== null && !isNaN(savedMicVolume) ? parseFloat(savedMicVolume) : parseFloat(maxMicVolume * 0.75);
        const initEchoVolume = await getItem('reverbVolume') || 11;
        const hasDongle = this.getDongleConnect();
        const hasMic = this.getMicConnect()
        
        console.log('HAS_MIC', hasMic, savedMicVolume, initMicVolume, initEchoVolume);
        store.commit('SAVE_HAS_MIC', hasMic);
        store.commit('SAVE_HAS_DONGLE', hasDongle);
        
        this.updateMicVolume(initMicVolume);
        this.updateEchoVolume(initEchoVolume);
        this.updateMicConnect(hasDongle);
      } catch (error) {
        console.error(error);
      }
    })();
  }

  updateMicVolume(volume) {
    store.commit('UPDATE_VIDEO_MIC_VOLUME', volume);
    this.setMicVolume(volume);
  }

  updateEchoVolume(volume) {
    store.commit('UPDATE_VIDEO_REVERB_VOLUME', volume);
    this.setReverbVolume(volume);
  }

  updateMicConnect(hasMic) {
    if (hasMic) {
      [0, 1].forEach(id => {
        const res = this.getMicConnect(id);
        store.dispatch('updateMicrophones', { id, status: res });
      });
    }
  }
  
  calculateVolume(volume, maxValue) {
    return Math.floor((volume / 100) * maxValue);
  }

  handleMicphoneNotification(payload) {
    const { type, action } = payload;
    console.log('handleMicphoneNotification', JSON.stringify(payload))
    if (type == '4') {
      sendLog({
        event_type: '10000~50000',
        event_name: 6011,
        event_data: {
          str1: '麦克风',
          str2: '音量调节',
          str3: '麦克风音量调节',
          str4: 'click',
        },
      })
    }

    if (payload.type == '5') {
      sendLog({
        event_type: '10000~50000',
        event_name: 6011,
        event_data: {
          str1: '麦克风',
          str2: '音量调节',
          str3: '混响音量调节',
          str4: 'click',
        },
      })
    }
    // 切歌
    if (type == '160' && action == 0) {
      sendLog({
        event_type: '10000~50000',
        event_name: 6011,
        event_data: {
          str1: '麦克风',
          str2: '按键操作',
          str3: '切歌',
          str4: 'click',
        },
      })
      
      eventBus.emit('ts-microphone-control-next')
      return
    }
    if (type === '0') {
      const shouldUpdateMicrophones = ['0', '-1'].includes(action);
      store.dispatch('updateMicrophones', { reset: shouldUpdateMicrophones });
      store.commit('system/SAVE_AUDIO_EFFECTS', {});
      console.log('handleMicphoneNotification shouldUpdateMicrophones:', shouldUpdateMicrophones)
    
      store.commit('SAVE_HAS_DONGLE', !shouldUpdateMicrophones);
      
      if (action == '1') { // dongle接入主板
        this.setMicVolume(store.state.micVolume)
        this.setReverbVolume(store.state.reverbVolume)
      }
    }
  }

  handleMicphoneDongleEvent(payload) {
    console.log('handleMicphoneDongleEvent', payload);
    const { arg1, arg2, type } = payload;
    const typeNumber = Number(type);
    let id, status;
    
    switch (typeNumber) {
      case 1:
        id = Number(arg1);
        status = Number(arg2) === 1;
        store.dispatch('updateMicrophones', { id, status });
        break;
      case 4:
        store.commit('UPDATE_VIDEO_MIC_VOLUME', Number(arg2));
        break;
      case 5:
        store.commit('UPDATE_VIDEO_REVERB_VOLUME', Number(arg2));
        break;
      default:
        console.log(`Unsupported type: ${typeNumber}`);
    }
  }  

  async handleAudioEffectParams(type,payload) {
    try {
      const audioEffectParams = JSON.parse(payload);
      const { powerAmplifierType, speakerCount, vehicleModel, micModel } = audioEffectParams;
      const audioEffects = {
        play_type: powerAmplifierType,
        speaker_num: speakerCount,
        // car_name: vehicleModel, // 启动就需要获取，不在从此处获取
        mic_type: micModel,
      };
      sendLog({
        event_type: '10000~50000',
        event_name: 30150,
        event_data: {
          str1: '音效配置信息',
          str2: '插入dongle',
          str3: micModel,
          str4: 'click',
          str5: powerAmplifierType,
          str7: speakerCount,
          str9: vehicleModel
        },
      })
      await store.commit('system/SAVE_AUDIO_EFFECTS', audioEffects);
      const { data = [] } = await getSoundfile(audioEffects);
      window.TSMicrophone.onAudioEffectParams(null, JSON.stringify(data));
    } catch (error) {
      console.log('handleAudioEffectParams error', error);
      window.TSMicrophone.onAudioEffectParams(error, null);
    }
  }

  getMicMaxVolume() {
    try {
      const res = window.TSMicrophone.getMicMaxVolume()
      console.log('getMicMaxVolume', res)
      return res
    } catch (error) {
      console.log('getMicMaxVolume error', error)
    }
  }
  getMicVolume(index = 0) {
    try {
      let res = -1
      res = window.TSMicrophone.getMicVolume(index)
      console.log('getMicVolume', res)
      return res
    } catch (error) {
      console.log('getMicVolume error', error)
    }
  }
  setMicVolume(volume) {
    try {
      window.TSMicrophone.setMicVolume(volume)
    } catch (error) {
      console.log('setMicVolume error', error)
    }
  }
  getReverbMaxVolume() {
    try {
      const res = window.TSMicrophone.getReverbMaxVolume()
      console.log('getReverbMaxVolume', res)
      return res
    } catch (error) {
      console.log('getReverbMaxVolume error', error)
    }
  }
  getReverbVolume() {
    try {
      let res = 10
      res = window.TSMicrophone.getReverbVolume()
      console.log('getReverbVolume', res)
      return res
    } catch (error) {
      console.log('getReverbVolume error', error)
    }
  }
  setReverbVolume(volume) {
    try {
      console.log('setReverbVolume', volume)
      // const _volume = Math.floor(volume / 100 * this.maxEchoVolume)
      // console.log('setReverbVolume _volume', _volume)
      const res = window.TSMicrophone.setReverbVolume(volume)
      console.log(res)
    } catch (error) {
      console.log('setReverbVolume error', error)
    }
  }
  hasThunderMic() {
    try {
      const res = window.TSMicrophone.hasThunderMic()
      console.log('hasThunderMic', res)
      store.commit('SAVE_HAS_MIC', res > -1)
      if (res > -1) {
        this.init()
      } else {
        store2('hasConnectedMicrophone', true)
        // store.commit('UPDATE_VIDEO_MIC_VOLUME', Math.floor(50 / 100 * this.maxMicVolume))
        // store.commit('UPDAT E_VIDEO_REVERB_VOLUME', Math.floor(50 / 100 * this.maxEchoVolume))
      }
      this.init()
    } catch (error) {
      console.log('hasThunderMic error', error)
    }
  }
  setAudioEffect(fileStr) {
    try {
      const res = window.TSMicrophone.loadAudioEffect(fileStr)
      console.log('loadAudioEffect', res)
      if (res) {
        // 更新音效成功
        console.log('set loadAudioEffect success!')
      }
    } catch (error) {
      console.log('setAudioEffect error', error)
    }
  }
  pauseLoopback(pause = 1) { // 控制耳返的暂停恢复
    try {
      const res = window.TSMicrophone.pauseLoopback(pause)
      console.log('pauseLoopback', res)
      if (res) {
        console.log('set pauseLoopback success!')
      }
    } catch (error) {
      console.log('pauseLoopback error', error)
    }
  }
  getMicConnect(type) { // 获取AB双麦的连接状态
    try {
      const res = window.TSMicrophone.getMicConnect(type)
      console.log(`getMicConnect ${type}麦：${res > 0 ? '已连接' : '未连接'}`);
      return res > 0
    } catch (error) {
      console.log('getMicConnect error', error)
    }
  }
  getDongleConnect() {
    try {
      const res = window.TSMicrophone.hasDongleConnect()
      console.log(`getDongleConnect ${res ? '连接dongle' : '没有连接dongle'}`);
      return !!res
    } catch (error) {
      console.log('getDongleConnect error', error)
    }
  }
}

export const TSMicrophoneInstance = new TSMicrophoneBridge()