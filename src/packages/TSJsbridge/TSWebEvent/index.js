import Toast from '@/utils/toast'
import { TSMicrophoneInstance } from '../TSMicrophone'
import { TSDownloaderInstance } from '../TSDownloader'
import store2 from 'store2'

const noopMethodList = [
  'onWebPageChanged',
]

function transMicoroVolumeToSlide(val, max) {
  return Math.floor(val / max * 100)
}

export class TSWebEventBridge {
  event = {}

  constructor() {
    if (!window.TSWebEvent) {
      console.log('TSWebEvent interface not found!')
      Toast({
        message: 'TSWebEvent interface not found!',
        className: 'not-support',
        position: 'top',
      })
      noopMethodList.forEach(method => {
        this[method] = (payload) => {
          console.log('call TSWebEvent fun:', method)
          console.log('call payload', JSON.stringify(payload))
        };
      });
      window.tsTSWebEvent = this
    } else {
      console.log('TSWebEvent', 'interface', 'installed')
      this.init()
    }
  }

  init() {
    TSDownloaderInstance.attachTSWebEvent(this)
    this.on('handleMicrophoneEvent', (jsonString) => {
      console.log('handleMicrophoneEvent', jsonString)
      try {
        const dongleData = JSON.parse(jsonString)
        TSMicrophoneInstance?.handleMicrophoneEvent(dongleData)
        // eventEmitter.emit('onMicrophoneEvent', JSON.parse(jsonString))
      } catch (error) {
        console.log('handleMicrophoneEvent json parse error', error)
      }
    })
  }

  on(name, handler) {
    window[name] = handler
  }

  onWebPageChanged(to, from) {
    try {
      // Android接口重新释放
      window.TSWebEvent.onWebPageChanged(to, from)
      store2.set('currentPage', to)
      // store.commit('UPDATE_CURRENT_PAGE', to)
      console.log('onWebPageChanged', to)
    } catch (error) {
      console.log('onWebPageChanged error', error)
    }
  }
}

export const TSWebEventInstance = new TSWebEventBridge()