<template>
  <div
    class="control-container"
    :class="{
      'short-mv-control-left': shortMode === 1,
      'short-mv-control-right': shortMode === 2,
    }"
    @click="$emit('video-control-close')"
  >
    <div class="control-container-top">
      <div
        class="control-container-item"
        @click.stop="$emit('video-control-replay')"
      >
        <!-- 重唱 -->
        <img
          src="https://qncweb.ktvsky.com/20231214/other/1357d97c550111adc5a8bc4c0eaeb625.svg"
        />
        重唱
      </div>
      <div class="control-container-item" @click.stop="handleClickPlayPause">
        <!-- 播放 -->
        <img
          v-show="paused"
          src="https://qncweb.ktvsky.com/20231214/other/29460641498a84c23abc647d79958bef.svg"
        />
        <img
          v-show="!paused"
          src="https://qncweb.ktvsky.com/20231214/other/e81dfa7401137aa23a717f92b78e5bcd.svg"
          alt=""
        />
        {{ paused ? '播放' : '暂停' }}
      </div>
      <div v-for="(track, key) in audioTrackMap" :key="key">
        <div
          v-show="
            (enabledAudioTrackId !== 999 && enabledAudioTrackId !== track.id) ||
            (enabledAudioTrackId === 999 && key === 'org')
          "
          class="control-container-item"
          data-action="update-control"
          @click.stop="handleSwitchAudioTrack(track)"
        >
          <img :src="iconConfig[key]" />
          <div>{{ key === 'org' ? '原唱' : '伴唱' }}</div>
        </div>
      </div>
    </div>
    <div class="control-container-item next" @click.stop="$emit('video-control-next')">
      <img
        src="https://qncweb.ktvsky.com/20231214/other/f19b7818143607bd0bc178256bbc5cd9.svg"
      />
      <div v-if="nextOrderSong" class="next-song">
        下一首：{{ nextOrderSong.music_name }} - {{ nextOrderSong.singer }}
      </div>
      <div v-else class="next-song">没有歌曲啦，快去点歌吧</div>
    </div>
  </div>
</template>

<script>
import { computed, toRefs, ref, watch } from 'vue'
import { useStore } from 'vuex'
import useOrder from '@/composables/useOrder'
import { debounce } from 'lodash'

export default {
  name: 'VideoControl',
  props: {
    paused: {
      type: Boolean,
      default: true,
    },
    shortMode: {
      // 适配mv页小屏幕播放模式：0：播控正常显示 1：播控左侧显示 2：播控右侧显示
      type: Number,
      default: 0,
    },
    audioTrackMap: {
      type: Object,
      default() {
        return {
          acc: {},
          org: {},
        }
      },
    },
    enabledAudioTrackId: {
      type: Number,
    },
  },
  setup(props, { emit }) {
    const { shortMode, paused } = toRefs(props)

    const size = computed(() => (shortMode.value > 0 ? 120 : 140))
    const { orderedList } = useOrder()
    const nextOrderSong = ref({})

    const store = useStore()
    const currPlayingSongIsAI = computed(() => store.state.orderedList[0]?.isAIMV)

    const iconConfig = {
      org: 'https://qncweb.ktvsky.com/20231214/other/2d680ecf3ee5faf2edd30c46c02d5ef1.svg',
      acc: 'https://qncweb.ktvsky.com/20231214/other/8686717d1d0f8e06c2d90668cef93b50.svg',
    }

    const handleSwitchAudioTrack = debounce((audioTrack) => {
      emit('switch-audio-track', audioTrack)
    }, 500)

    const handleClickPlayPause = () => {
      if (paused.value) {
        emit('video-control-play')
      } else {
        emit('video-control-pause')
      }
    }

    watch(orderedList.value, (val) => {
      nextOrderSong.value = val[1]
    })

    return {
      size,
      handleSwitchAudioTrack,
      currPlayingSongIsAI,
      handleClickPlayPause,
      iconConfig,
      nextOrderSong,
    }
  },
}
</script>

<style lang="stylus" scoped>
.control-container
  z-index 10
  width fit-content
  margin 0 auto
  @media screen and (max-width 1200px)
    position absolute
    bottom 511px
    left calc(50vw - 286px)
  &-top
    display flex
    justify-content center
    align-items center
  &-item
    width auto
    height 120px
    padding 0 40px
    display flex
    justify-content center
    align-items center
    border-radius 100px
    background rgba(30, 31, 33, 0.70)
    backdrop-filter blur(15px)
    color rgba(255, 255, 255, 0.80)
    font-size 36px
    img
      margin-right 20px
      width 48px
    &:nth-child(2)
      margin 0 24px
    @media screen and (max-width 1200px) and (min-height 1200px)
      height 96px
      padding 0 33px
      border-radius 80px
      background rgba(255, 255, 255, 0.10)
      backdrop-filter blur(12px)
      font-size 28px
      img
        margin-right 16px
        width 40px
      &:nth-child(2)
        margin 0 22px
    &.next
      margin 40px 0 0
      justify-content flex-start
      img
        margin-right 40px
      .next-song
        flex 1
        max-width 540px
        padding 0 60px
        height 80px
        line-height 80px
        color: rgba(255, 255, 255, 0.60);
        text-align center
        white-space nowrap
        overflow hidden
        text-overflow ellipsis
        border-left 2px solid rgba(255, 255, 255, 0.20)
        @media screen and (max-width 1200px)
          max-width 450px
      .marquee-container
        width 450px
        overflow: hidden;
        margin 0 auto
        display flex
      .marquee-content
        white-space: nowrap;
        margin-right 10px
      @media screen and (max-width 1200px)
        margin-top 32px
        img
          margin-right 26px
.short-mv-control-left
  width 100% !important
  margin 0 !important
  padding-right 800px
  .control-container-item
    &:nth-child(2)
      margin 0 110px !important
    &.next
      margin 40px 120px 0 !important
      white-space nowrap
.short-mv-control-right
  width 100% !important
  margin 0 !important
  padding-left 800px
  .control-container-item
    &:nth-child(2)
      margin 0 110px !important
    &.next
      margin 40px 120px 0 !important
</style>

