<template>
  <div
    class="control-container"
    :class="{
      'short-mv-control-left': shortMode === 1,
      'short-mv-control-right': shortMode === 2,
    }"
    @click="$emit('video-control-close')"
  >
    <div class="control-container-top">
      <div
        class="control-container-item"
        @click.stop="$emit('video-control-replay')"
      >
        <!-- 重唱 -->
        <svg width="49" height="49" viewBox="0 0 49 49" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M25.5564 4C36.8735 4 46 13.4288 46 25C46 36.5712 36.8735 46 25.5564 46C16.3787 46 8.63808 39.7945 6.04345 31.2819C5.88242 30.7537 6.18015 30.1949 6.70844 30.0338C7.23673 29.8728 7.79553 30.1705 7.95655 30.6988C10.3114 38.4246 17.3149 44 25.5564 44C35.7161 44 44 35.5202 44 25C44 14.4798 35.7161 6 25.5564 6C20.5044 6 15.6976 8.31675 12.3158 11.7791L12.323 11.7855L11.8099 12.3174C11.6917 12.4481 11.5755 12.5802 11.4613 12.7138C11.3965 12.7896 11.3229 12.8533 11.2432 12.9047L6.83354 17.4753C6.21523 18.1162 5.13073 17.69 5.11406 16.7997L4.93858 7.42851C4.92223 6.55514 5.95488 6.0821 6.60558 6.66487L10.8248 10.4437C14.5449 6.60487 19.8761 4 25.5564 4Z" fill="white" style="fill:white;fill-opacity:1;"/>
        </svg>
        重唱
      </div>
      <div style="position: relative;top: 0px;" class="control-container-item" @click.stop="handleClickPlayPause">
        <!-- 播放 -->
        <svg v-show="paused" width="53" height="52" viewBox="0 0 53 52" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M44.8967 26L12.2008 47.0188L12.2008 4.98114L44.8967 26Z" stroke="white" style="stroke:white;stroke-opacity:1;" stroke-width="2.6"/>
        </svg>
        <svg v-show="!paused" width="53" height="52" viewBox="0 0 53 52" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect x="9.60156" y="3.8999" width="2.6" height="44.2" fill="white" style="fill:white;fill-opacity:1;"/>
          <rect x="40.8008" y="3.8999" width="2.6" height="44.2" fill="white" style="fill:white;fill-opacity:1;"/>
        </svg>
        {{ paused ? '播放' : '暂停' }}
      </div>
      <div
        class="control-container-item"
        data-action="update-control"
        @click.stop="handleSwitchAudioTrack"
        :class="supportsOrg ? '' : 'disable'"
      >
        <svg v-show="enabledAudioTrackId == 1 && supportsOrg" width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g clip-path="url(#clip0_981_23781)">
          <path d="M46.7992 24C46.7992 36.592 36.5913 46.8 23.9992 46.8C11.4071 46.8 1.19922 36.592 1.19922 24C1.19922 11.4079 11.4071 1.19995 23.9992 1.19995C36.5913 1.19995 46.7992 11.4079 46.7992 24Z" stroke="white" style="stroke:white;stroke-opacity:1;" stroke-width="2.4"/>
          <circle cx="23.9992" cy="24" r="4.8" fill="white" style="fill:white;fill-opacity:1;"/>
          <rect x="35.0312" y="11.2722" width="2.4" height="8.4" transform="rotate(45 35.0312 11.2722)" fill="white" style="fill:white;fill-opacity:1;"/>
          <rect x="17.2109" y="29.0916" width="2.4" height="8.4" transform="rotate(45 17.2109 29.0916)" fill="white" style="fill:white;fill-opacity:1;"/>
          </g>
          <defs>
          <clipPath id="clip0_981_23781">
          <rect width="48" height="48" fill="white" style="fill:white;fill-opacity:1;"/>
          </clipPath>
          </defs>
        </svg>
        <svg v-show="enabledAudioTrackId != 1 || !supportsOrg" width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g clip-path="url(#clip0_980_23659)">
          <circle cx="23.9992" cy="24" r="4.8" fill="white" style="fill:white;fill-opacity:1;"/>
          <circle cx="31.1984" cy="43.2" r="3.6" stroke="white" style="stroke:white;stroke-opacity:1;" stroke-width="2.4"/>
          <circle cx="43.8223" cy="40.2207" r="3.02069" stroke="white" style="stroke:white;stroke-opacity:1;" stroke-width="2.4"/>
          <path d="M34.8008 42V31.2L46.8008 27.6V38.4" stroke="white" style="stroke:white;stroke-opacity:1;" stroke-width="2.4" stroke-linecap="square"/>
          <path fill-rule="evenodd" clip-rule="evenodd" d="M2.4 24C2.4 12.0706 12.0706 2.4 24 2.4C33.9388 2.4 42.3098 9.11258 44.8267 18.2514L47.1775 17.7477C44.4265 7.52459 35.092 0 24 0C10.7452 0 0 10.7452 0 24C0 36.0313 8.85297 45.9949 20.4 47.7318V45.3014C10.1842 43.5875 2.4 34.7028 2.4 24Z" fill="white" style="fill:white;fill-opacity:1;"/>
          </g>
          <defs>
          <clipPath id="clip0_980_23659">
          <rect width="48" height="48" fill="white" style="fill:white;fill-opacity:1;"/>
          </clipPath>
          </defs>
        </svg>
        <div>{{ (enabledAudioTrackId != 1 || !supportsOrg) ? '原唱' : '伴唱' }}</div>
      </div>
    </div>
    <div class="control-container-item next" @click.stop="handleClickNext">
      <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M35.1455 23.9999L8.39922 43.2572L8.39922 4.74258L35.1455 23.9999Z" stroke="white" style="stroke:white;stroke-opacity:1;" stroke-width="2.4"/>
        <path d="M39.6016 6H42.0016L42.0016 42H39.6016L39.6016 6Z" fill="white" style="fill:white;fill-opacity:1;"/>
      </svg>

      <div v-if="nextOrderSong" class="next-song">
        下一首：{{ nextOrderSong.music_name }} - {{ nextOrderSong.singer }}
      </div>
      <div v-else class="next-song">没有歌曲啦，快去点歌吧</div>
    </div>
  </div>
</template>

<script>
import { computed, toRefs, ref, watch, onMounted } from 'vue'
import useOrder from '@/composables/useOrder'
import { debounce } from 'lodash'

export default {
  name: 'VideoControl',
  props: {
    paused: {
      type: Boolean,
      default: true,
    },
    shortMode: {
      // 适配mv页小屏幕播放模式：0：播控正常显示 1：播控左侧显示 2：播控右侧显示
      type: Number,
      default: 0,
    },
    audioTrackMap: {
      type: Object,
      default() {
        return {
          acc: {},
          org: {},
        }
      },
    },
    enabledAudioTrackId: {
      type: Number,
    },
    isRecording: {
      type: Boolean,
      default: false,
    },
    supportsOrg: {
      type: Boolean,
      default: true,
    },
  },
  setup(props, { emit }) {
    const { shortMode, paused, supportsOrg } = toRefs(props)

    const size = computed(() => (shortMode.value > 0 ? 120 : 140))
    const { orderedList } = useOrder()
    const nextOrderSong = ref({})

    const handleSwitchAudioTrack = debounce(() => {
      emit('switch-audio-track')
    }, 500)

    const handleClickPlayPause = () => {
      if (paused.value) {
        emit('video-control-play')
      } else {
        emit('video-control-pause')
      }
    }

    const handleClickNext = debounce(() => {
      emit('video-control-next')
    }, 1000, { leading: true, trailing: false })

    const getClassNames = () => {
      const classNames = [];

      if (!supportsOrg.value) {
        classNames.push('disable');
      }

      return classNames;
    };

    watch(orderedList, (val) => {
      nextOrderSong.value = val[1]
    })


    onMounted(() => {
      nextOrderSong.value = orderedList.value[1]
    })

    return {
      size,
      handleSwitchAudioTrack,
      handleClickPlayPause,
      nextOrderSong,
      handleClickNext,
      getClassNames,
    }
  },
}
</script>

<style lang="stylus" scoped>
.control-container
  z-index 10
  width fit-content
  margin 0 auto
  @media screen and (max-width 1200px)
    position absolute
    bottom 511px
    left calc(50vw - 286px)
  @media screen and (max-width: 1950px) and (min-width: 1700px) and (max-height: 720px) and (min-height: 650px)
    zoom 0.8
  &-top
    display flex
    justify-content center
    align-items center
  &-item
    width auto
    height 120px
    padding 0 40px
    display flex
    justify-content center
    align-items center
    border-radius 100px
    background rgba(30, 31, 33, 0.8)
    backdrop-filter none
    color rgba(255, 255, 255, 0.80)
    font-size 36px
    &.disable
      opacity 0.5
    svg
      margin-right 20px
      width 48px
    &:nth-child(2)
      margin 0 24px
    &.next
      margin 40px 0 0
      justify-content flex-start
      svg
        margin-right 40px
      .next-song
        flex 1
        max-width 540px
        padding 0 60px
        height 80px
        line-height 80px
        color: rgba(255, 255, 255, 0.60);
        text-align center
        white-space nowrap
        overflow hidden
        text-overflow ellipsis
        border-left 2px solid rgba(255, 255, 255, 0.20)
        @media screen and (max-width 1200px)
          max-width 450px
      .marquee-container
        width 450px
        overflow: hidden;
        margin 0 auto
        display flex
      .marquee-content
        white-space: nowrap;
        margin-right 10px
      @media screen and (max-width 1200px)
        margin-top 32px
        img
          margin-right 26px
.short-mv-control-left
  width 57vw !important
  margin 0 !important
  padding-right 0px !important
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  transform: scale(0.85);
  transform-origin: center center;

  @media screen and (max-width: 1950px) and (min-width: 1700px) and (max-height: 720px) and (min-height: 650px)
    width 80vw !important
    margin-right 0vw !important

  .control-container-top
    width 86%
    justify-content space-around

    @media screen and (max-width: 1950px) and (min-width: 1700px) and (max-height: 720px) and (min-height: 650px)
      width 60%
  .control-container-item
    &:nth-child(2)
      margin 0 110px !important
    &.next
      margin 40px 0px 0 !important
      white-space nowrap
      width 82%

      @media screen and (max-width: 1950px) and (min-width: 1700px) and (max-height: 720px) and (min-height: 650px)
        width 60%

      .next-song
        max-width 90% !important
.short-mv-control-right
  width 57vw !important
  margin 0 !important
  margin-left 43vw !important
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  transform: scale(0.85);
  transform-origin: center center;

  @media screen and (max-width: 1950px) and (min-width: 1700px) and (max-height: 720px) and (min-height: 650px)
    width 80vw !important
    margin-left 47vw !important

  .control-container-top
    width 86%
    justify-content space-around

    @media screen and (max-width: 1950px) and (min-width: 1700px) and (max-height: 720px) and (min-height: 650px)
      width 60%
  .control-container-item
    &:nth-child(2)
      margin 0 110px !important
    &.next
      margin 40px 0px 0 !important
      white-space nowrap
      width 82%
      
      @media screen and (max-width: 1950px) and (min-width: 1700px) and (max-height: 720px) and (min-height: 650px)
        width 60%
      .next-song
        max-width 90% !important
</style>

