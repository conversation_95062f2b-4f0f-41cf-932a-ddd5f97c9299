<template>
  <div class="top-menu">
    <div class="menu">
      <!-- 音轨 -->
      <div
        v-for="(track, key) in audioTrackMap"
        :key="key"
      >
        <div
          v-show="(enabledAudioTrackId !== 999 && enabledAudioTrackId !== track.id) || (enabledAudioTrackId === 999 && key === 'org')"
          class="menu-item"
          @click.stop="handleSwitchAudioTrack(track)"
        >
          <img class="menu-item-icon" :src="iconConfig[key]"/>
          <div>{{ key === 'org' ? '原唱' : '伴唱' }}</div>
        </div>
      </div>
      <div v-if="isTuner" class="menu-item" @click.stop="$emit('show-effect-adjust')">
        <!-- 气氛 https://qncweb.ktvsky.com/20230113/vadd/5bc69ba4eaec9baf882ed75784e6c273.png -->
        <img class="menu-item-icon" src="https://qncweb.ktvsky.com/20211228/vadd/89d9b54edc1c7c9ed1aeb4e10cab5929.png"/>
        <div>调音</div>
      </div>
      <div v-if="isOrdered" class="menu-item" @click.stop="$emit('show-ordered')">
        <!-- 已点 -->
        <img class="menu-item-icon" src="https://qncweb.ktvsky.com/20210922/vadd/dcd8d22b949a55069ef2959e8fc5e6fe.png"/>
        <div>已点</div>
      </div>
    </div>
    <!-- <div class="scan-code">
      <img class="scan-code-icon" src="https://qncweb.ktvsky.com/20210922/vadd/8505708055752b11856f7a8211e09036.png"/>
      <div>手机点歌</div>
    </div> -->
  </div>
</template>

<script>
import Toast from '@/utils/toast'

export default {
  name: 'VideoPlayerTopMenu',
  props: {
    audioTrackMap: {
      type: Object,
      default() {
        return {
          acc: {},
          org: {}
        }
      }
    },
    enabledAudioTrackId: {
      type: Number,
    },
    isTuner: Boolean,
    isOrdered: Boolean
  },
  setup(props, {emit}) {
    const iconConfig = {
      org: 'https://qncweb.ktvsky.com/20210922/vadd/941e3d66542ecdd0bb72413f20c17185.png',
      acc: 'https://qncweb.ktvsky.com/20210924/vadd/a15968b552c82e75573f9d2bea752a2a.png'
    };

    const handleSwitchAudioTrack = (audioTrack) => {
      console.log(audioTrack, 'handleSwitchAudioTrack')
      if (audioTrack.id === 999) {
        Toast('抱歉，当前歌曲缺少原唱资源')
        return
      }
      emit('switch-audio-track', audioTrack)
    }

    return {
      iconConfig,
      handleSwitchAudioTrack,
    }
  }
  
}
</script>

<style lang="stylus" scoped>
.top-menu
  position: absolute
  top 35px
  width: 100%
  display flex
  align-items center
  justify-content center
  color #ffffff
  font-size 24px
  z-index 11
  @media screen and (max-width 1200px) and (min-height 1421px)
    top 608px
  @media screen and (max-width 1180px) and (min-height 1200px)
    top 413px
.menu
  height 130px
  background rgba(30, 31, 33, 0.8)
  border-radius 14px
  display flex
  align-items center
  // margin-right 30px
  &-item
    width 140px
    height 130px
    display flex
    flex-direction column
    justify-content center
    align-items center
    margin-right 40px
    &:nth-child(1)
      margin-left 40px
    @media screen and (max-width: 1200px)
      margin 0 20px !important
    &-icon
      width 60px
      height 60px
.scan-code
  width 200px
  height 140px
  background rgba(30, 31, 33, 0.8)
  border-radius 14px
  text-align center
  display flex
  flex-direction column
  justify-content center
  align-items center
  &-icon
    width 60px
    height 60px
</style>
