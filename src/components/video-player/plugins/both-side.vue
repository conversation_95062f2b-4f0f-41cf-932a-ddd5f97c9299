<template>
  <div class="both-sides-menu">
    <div
      class="menu"
      :class="item.pos"
      v-for="(item, i) in bothSides"
      :key="i"
    >
      <div class="menu-item-header" @click.stop="handleShowSideBar('我的特权', item.pos)">
        <!-- 个人中心 -->
        <div class="menu-header-img">
          <img v-if="headerimg" :src="headerimg"/>
          <img v-else style="background-color: #CFCFCF;" src="https://qncweb.ktvsky.com/20230424/vadd/608c9e8b386f6493599d2cc79ba361d4.png" class="header-img" alt="">
        </div>
      </div>
      <!-- 快速点歌 -->
      <div class="menu-item" @click.stop="handleShowSideBar('快速点歌', item.pos, 10070)">
        <img class="menu-item-icon" src="https://qncweb.ktvsky.com/20240131/other/fe6a1fb414456f4274c71df747a3c4a4.png"/>
        <div class="menu-item-text">快速点歌</div>
      </div>
      <!-- 调音 -->
      <div class="menu-item" @click.stop="handleShowSideBar('调音', item.pos, 10072)">
        <img class="menu-item-icon" src="https://qncweb.ktvsky.com/20240131/other/ba6d3128f6e4a8c9f52ed9ebb81f83fc.png"/>
        <div class="menu-item-text">调音</div>
      </div>
      <!-- 画质 -->
      <div class="menu-item" @click.stop="handleShowSideBar('画质', item.pos, 10076)">
        <img class="menu-item-icon" src="https://qncweb.ktvsky.com/20240131/other/15836e466ffdfcd42ed465c66124c817.png"/>
        <div class="menu-item-text">画质</div>
      </div>
      <!-- 已点 -->
      <div class="menu-item" @click.stop="handleShowSideBar('已点', item.pos, 10078)">
        <img class="menu-item-icon" src="https://qncweb.ktvsky.com/20240131/other/bc9fe847ad90aa026aa6f2eebf5ad5b4.png"/>
        <div class="menu-item-text">已点</div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import { sendLog } from '@/directives/v-log/log'
import useMvMode from '@/composables/useMvMode';
import Toast from '@/utils/toast'
import { TSBaseInfoInstance } from "@/packages/TSJsbridge"
import { useStore } from 'vuex'
import { setTimeout } from 'timers';
export default {
  name: 'VideoBothSidesMenu',
  props: {
    headerimg: {
      type: String,
      default: ''
    },
    audioTrackMap: {
      type: Object,
      default() {
        return {
          acc: {},
          org: {}
        }
      }
    },
    enabledAudioTrackId: {
      type: Number,
    },
    isRecording: {
      type: Boolean,
      default: false,
    },
  },
  setup(props, {emit}) {
    const { isMvMode } = useMvMode()
    const store = useStore()
    const iconConfig = {
      org: 'https://qncweb.ktvsky.com/20231102/vadd/535f3a6eda6ecaab4ed3f1b1540e66b8.png',
      acc: 'https://qncweb.ktvsky.com/20231102/vadd/6fbdc34d53bffc3f6f2394d3f0a8a94f.png'
    };

    const bothSides = ref([{
      value: 0,
      label: '左侧',
      pos: 'left',
    },{
      value: 1,
      label: '右侧',
      pos: 'right',
    }])
    
    const handleSwitchAudioTrack = (audioTrack, pos) => {
      emit('switch-audio-track', audioTrack)

      sendLog({
        event_type: '10000~50000',
        event_name: pos === 'left' ? 10074 : 10075,
        event_data: {
          str1: 'MV',
          str2: '播控',
          str3: `原唱${pos === 'left' ? '左' : '右'}`,
          str4: 'click',
        },
      })
    }

    const handleShowSideBar = (name, pos, logname) => {
      try {
        if (!isMvMode.value &&name == '画质') {
          Toast('行车时，不可选择画质')
          return
        }
        emit('show-side-bar', { name, pos })

        setTimeout(() => {
          TSBaseInfoInstance.controlMVScaling(pos === 'left' ? 1 : 0, true)
        }, 100)

        sendLog({
          event_type: '10000~50000',
          event_name: pos === 'left' ? logname : logname + 1,
          event_data: {
            str1: 'MV',
            str2: '播控',
            str3: `${name}${pos === 'left' ? '左' : '右'}`,
            str4: 'click',
          },
        })
      } catch (error) {
        console.log('handleShowSideBar', error)
      }
    }

    return {
      bothSides,
      iconConfig,
      handleShowSideBar,
      handleSwitchAudioTrack,
    }
  }
  
}
</script>

<style lang="stylus" scoped>
.both-sides-menu
  position absolute
  top 0
  width 100%
  height 100%
  display flex
  align-items center
  justify-content center
  z-index 9
  @media screen and (max-width 1200px) and (min-height 1421px)
    display none
  @media screen and (max-width 1180px) and (min-height 1200px)
    display none
  .menu
    width 130px
    height auto
    padding 0px 0px 16px
    background rgba(30, 31, 33, 0.7)
    backdrop-filter blur(15px)
    position absolute
    top 130px
    border-radius 20px
    display flex
    flex-direction column
    align-items center
    @media screen and (min-aspect-ratio: 1920/720) and (max-aspect-ratio: 1792/660)
      zoom 0.8
      top 100px !important
    &.left
      left 70px
    &.right
      right 70px
    &-item
      width 130px
      height 120px
      margin-bottom 10px
      margin-right 0
      display flex
      flex-direction column
      justify-content center
      align-items center
      &.disable
        opacity 0.8
        img
          opacity 0.2
      &-icon
        width 80px
        height 80px
      &-text
        font-size 22px
        color rgba(255, 255, 255, 0.50)
        height 25px
        line-height 25px
    &-header-img
      width 80px
      height 80px
      margin-bottom 30px
      border-radius 100%
      overflow hidden
    .menu-item-header
      margin-bottom 0px
      padding-top 30px
      width 100%
      display flex
      justify-content center
      align-items center
</style>
