<template>
  <div :class="className">
    <div class="wrapper avatar-default-wrapper">
      <div
        :class="`${className}-cover`"
        :style="{ backgroundImage: `url(${singerHead})` }"
      ></div>
    </div>
    <p>{{ singerName }}</p>
  </div>
</template>
<script>
import { computed, toRefs } from 'vue'
import { useStore } from 'vuex'

export default {
  props: {
    singer: {
      type: Object,
      default() {
        return {
          musiccount: 0,
          singerheader: '',
          singerhead: '',
          singerid: 0,
          singerjp: '',
          singer: '',
          singername: '',
          singerqp: '',
          singertypename: '',
        }
      }
    },
    className: {
      type: String,
      default: 'singer-item'
    }
  },
  name: 'SingerItem',
  setup(props) {
    const { singer } = toRefs(props)
    const store = useStore()

    const imgs = {
      themeDark: {
        imgFallback: {
          loading: require('@/assets/singer-dark.png'),
          error: require('@/assets/singer-dark.png')
        }
      },
      themeLight: {
        imgFallback: {
          loading: require('@/assets/singer-light.png'),
          error: require('@/assets/singer-light.png'),
        }
      },
      themeSystem: {
        imgFallback: {
          loading: require('@/assets/singer-dark.png'),
          error: require('@/assets/singer-dark.png')
        }
      },
    };

    const themeClass = computed(() => store.state.themeClass);

    const singerHead = computed(() => singer.value.singerheader || singer.value.singerhead)
    const singerName = computed(() => singer.value.singername || singer.value.singer)

    return {
      singerName,
      singerHead,
      imgs,
      themeClass,
    }
  },
}
</script>
<style lang="stylus" scoped>
.singer-item
  width 216px
  margin-bottom 60px
  .wrapper
    width 100%
    height auto
  &-cover
    width 216px
    height 216px
    border-radius 50%
    margin-bottom 20px
    background-size 100%
    background-repeat no-repeat
  p
    width 216px
    font-size 28px
    color rgba(255, 255, 255, 0.8)
    white-space nowrap
    overflow hidden
    text-overflow ellipsis
    text-align center
  @media screen and (max-width 1200px) and (min-height 1200px)
    width 200px
    p
      width 200px
      font-size 22px
    .singer-item-cover
      width 200px
      height 200px
.theme-themeLight
  .singer-item
    p
      color rgba(29, 29, 31, 1)
</style>