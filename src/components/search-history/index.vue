<template>
  <div class="search-history" v-show="isShow">
    <div class="search-history-title">
      <span>搜索历史</span>
      <!-- <img
        @click="handleDeleteSearchCache(-1)"
        :src="imgs[themeClass].icon"
      /> -->
      <div @click="handleDeleteSearchCache(-1)">
        <svg v-show="themeClass === 'themeLight'" width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g opacity="0.5">
          <path d="M12.8008 3H19.2008V5H12.8008V3Z" fill="#1D1D1F" fill-opacity="0.9" style="fill:#1D1D1F;fill:color(display-p3 0.1137 0.1137 0.1216);fill-opacity:0.9;"/>
          <path d="M4 7.19995H28V9.19995H4V7.19995Z" fill="#1D1D1F" fill-opacity="0.9" style="fill:#1D1D1F;fill:color(display-p3 0.1137 0.1137 0.1216);fill-opacity:0.9;"/>
          <path d="M10.1559 27.8L7.54257 8.19995H24.4582L21.8449 27.8H10.1559Z" stroke="#1D1D1F" stroke-opacity="0.9" style="stroke:#1D1D1F;stroke:color(display-p3 0.1137 0.1137 0.1216);stroke-opacity:0.9;" stroke-width="2"/>
          </g>
        </svg>
        <svg v-show="themeClass === 'themeDark'" width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g opacity="0.5">
          <path d="M12.8008 3H19.2008V5H12.8008V3Z" fill="white" style="fill:white;fill-opacity:1;"/>
          <path d="M4 7.19995H28V9.19995H4V7.19995Z" fill="white" style="fill:white;fill-opacity:1;"/>
          <path d="M10.1559 27.8L7.54257 8.19995H24.4582L21.8449 27.8H10.1559Z" stroke="white" style="stroke:white;stroke-opacity:1;" stroke-width="2"/>
          </g>
        </svg>
      </div>
    </div>
    <div class="search-history-content">
      <div
        class="search-history-content-item"
        :class="{
          'search-history-content-item-active': false, // keyword === searchItem
        }"
        :key="index"
        @click="handleClickSearchTab(searchItem)"
        v-for="(searchItem, index) in searchCacheList"
      >
        <span>{{ searchItem }}</span>
        <!-- <img
          class="search-history-content-item-d-img"
          @click.stop="handleDeleteSearchCache(searchItem, index)"
          :src="imgs[themeClass].delete"
        /> -->
        <div
          class="search-history-content-item-d-img"
          @click.stop="handleDeleteSearchCache(searchItem, index)"
        >
          <svg v-show="themeClass === 'themeLight'" width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g opacity="0.4">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M3.74219 26.3708L5.62781 28.2564L15.9987 17.8855L26.3698 28.2566L28.2554 26.371L17.8843 15.9999L28.2552 5.62903L26.3696 3.74341L15.9987 14.1143L5.628 3.7436L3.74238 5.62922L14.1131 15.9999L3.74219 26.3708Z" fill="#1D1D1F" style="fill:#1D1D1F;fill:color(display-p3 0.1137 0.1137 0.1216);fill-opacity:1;"/>
            </g>
          </svg>
          <svg v-show="themeClass === 'themeDark'" width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g opacity="0.4">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M3.74219 26.3708L5.62781 28.2564L15.9987 17.8855L26.3698 28.2566L28.2554 26.371L17.8843 15.9999L28.2552 5.62903L26.3696 3.74341L15.9987 14.1143L5.628 3.7436L3.74238 5.62922L14.1131 15.9999L3.74219 26.3708Z" fill="white" style="fill:white;fill-opacity:1;"/>
            </g>
          </svg>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useStore } from 'vuex'
import { setSearchCache } from '@/utils/historyCache'
import { defineEmits } from 'vue'
import { sendLog } from '@/directives/v-log/log'

const emit = defineEmits(['click-word', 'delete-word'])

const store = useStore()
const searchCacheList = computed(() => store.state.search.searchCache)
const isShow = computed(() => !!searchCacheList.value.length)

const imgs = {
  themeDark: {
    icon: 'https://qncweb.ktvsky.com/20231211/vadd/c7a95033db409eb1d99b967e0d102d39.png',
  },
  themeLight: {
    icon: 'https://qncweb.ktvsky.com/20240222/other/75a8cbede13fd2f3734e34839e42f986.png',
  },
  themeSystem: {
    icon: 'https://qncweb.ktvsky.com/20231211/vadd/c7a95033db409eb1d99b967e0d102d39.png',
  },
}

const themeClass = computed(() => store.state.themeClass)
const mvIsHide = computed(() => store.state.mvIsHide)

const handleClickSearchTab = (item) => {
  // 搜索
  emit('click-word', item)

  if(mvIsHide.value){
    sendLog({
      event_type: '10000~50000',
      event_name: 6002,
      event_data: {
        str1: '搜索页',
        str2: '搜索历史',
        str3: '点击任意歌曲/内容',
        str4: 'click',
      },
    })
  }else{
    sendLog({
      event_type: '10000~50000',
      event_name: 6007, // 10085 单首删除 10086 全部删除
      event_data: {
        str1: '欢唱页',
        str2: '快速点歌-搜索历史',
        str3: '点击任意内容',
        str4: 'click',
      }
    })
  }
}

const handleDeleteSearchCache = (searchItem, index) => {
  if (searchItem === -1) {
    store.dispatch('search/clearSearchCache')
    emit('delete-word', searchItem)
    return
  }
  let newSearchCacheList = [...searchCacheList.value]
  newSearchCacheList.splice(index, 1)
  store.dispatch('search/updateSearchCache', newSearchCacheList)
  store.dispatch('search/deleteSearchSong', searchItem)
  setSearchCache(newSearchCacheList)
  emit('delete-word', searchItem)
}
</script>

<style lang="stylus" scoped>
.search-history
  width 100%
  margin-top 60px
  @media screen and (max-width 1200px)
    margin-top 40px
  &-title
    width 100%
    height 33px
    display flex
    flex-direction row
    align-items center
    color rgba(255, 255, 255, 0.4)
    span
      font-size 28px
      margin-right 12px
    div
      width 32px
      height 32px
    @media screen and (max-width 1200px)
      span
        font-size 24px
      img
        width 26px
        height 26px
  &-content
    width 100%
    margin-top 40px
    display flex
    flex-direction row
    flex-wrap wrap
    &-item
      max-width 700px
      height 70px
      display flex
      align-items center
      justify-content space-between
      border-radius: 4px;
      background: rgba(255, 255, 255, 0.08);
      padding 0 24px
      margin-right 40px
      margin-bottom 24px
      &-active
        background rgba(255, 255, 255, 0.15)
      span
        max-width 447px
        height 70px
        line-height 70px
        font-size 28px
        color rgba(255, 255, 255, 0.6)
        white-space nowrap
        overflow hidden
        text-overflow ellipsis
        margin-right 24px
      &-s-img
        width 30px
        height 30px
        margin-right 15px
      &-d-img
        width 24px
        height 24px
    @media screen and (max-width 1200px)
      margin-top 32px
      &-item
        padding 0 20px
        height 56px
        span
          font-size 22px
          margin-right 20px
        &-d-img
          width 15px
          height 15px
.theme-themeLight
  .search-history
    &-title
      color rgba(29,29,31,0.9)!important
    &-content
      &-item
        background rgba(255, 255, 255, 0.8)!important
        span
          color rgba(29, 29, 31, 0.7)!important
</style>
