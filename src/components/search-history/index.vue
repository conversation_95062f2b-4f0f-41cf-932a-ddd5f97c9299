<template>
  <div class="search-history" v-show="isShow">
    <div class="search-history-title">
      <span>搜索历史</span>
      <img
        @click="handleDeleteSearchCache(-1)"
        src="https://qncweb.ktvsky.com/20231211/vadd/c7a95033db409eb1d99b967e0d102d39.png"
        alt=""
      />
    </div>
    <div class="search-history-content">
      <div
        class="search-history-content-item"
        :class="{
          'search-history-content-item-active': false, // keyword === searchItem
        }"
        :key="index"
        @click="handleClickSearchTab(searchItem)"
        v-for="(searchItem, index) in searchCacheList"
      >
        <span>{{ searchItem }}</span>
        <img
          class="search-history-content-item-d-img"
          @click.stop="handleDeleteSearchCache(searchItem, index)"
          src="https://qncweb.ktvsky.com/20231211/vadd/0b3a737b87cca221b0d2fa3b6f805a47.png"
          alt=""
        />
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useStore } from 'vuex'
import useOrder from '@/composables/useOrder'
import useSongItem from '@/composables/useSongItem'
import { setSearchCache } from '@/utils/historyCache'
import { sendLog } from '@/directives/v-log/log'
import { Dialog } from 'vant'

export default {
  name: 'SearchHistory',
  setup(props, { emit }) {
    const store = useStore()
    const { orderedListNumber, orderSong, addSong } = useOrder()
    const { validSong } = useSongItem()
    const searchCacheList = computed(() => store.state.search.searchCache)
    const searchSongList = computed(() => store.state.search.searchSong)
    const searchSong = computed(() => {
      let res = {}
      searchSongList.value.some((v) => {
        if (searchCacheList.value.includes(v.searchname)) {
          res = v
          return true
        }
        return false
      })
      return res
    })
    const isShow = computed(() => !!searchCacheList.value.length)

    const isCanOrder = (song) => {
      if (!song || !Object.keys(song).length || !song.songid) {
        // 无歌曲
        return false
      }
      return validSong(song)
    }

    const goSing = (song) => {
      // 歌曲数据校验
      if (isCanOrder(song)) {
        addSong(song, {
          from: {
            song_list_source: 5,
          },
        })
        orderSong(song, orderedListNumber.value - 1)
      }
    }

    const handleGoSing = (item) => {
      // 一键演唱
      if (searchSong.value.searchname === item) {
        goSing(searchSong.value)
      }
    }

    const handleClickSearchTab = (item) => {
      // 搜索
      emit('click-word', item)
    }

    const handleDeleteSearchCache = (searchItem, index) => {
      if (searchItem === -1) {
        Dialog.confirm({
          message: '您确定要删除所有的历史记录吗？',
        }).then(() => {
          store.dispatch('search/clearSearchCache')
          emit('delete-word', searchItem)
        })
        return
      }
      let newSearchCacheList = [...searchCacheList.value]
      newSearchCacheList.splice(index, 1)
      store.dispatch('search/updateSearchCache', newSearchCacheList)
      store.dispatch('search/deleteSearchSong', searchItem)
      setSearchCache(newSearchCacheList)
      emit('delete-word', searchItem)
    }

    return {
      isShow,
      searchSong,
      searchCacheList,
      handleGoSing,
      handleClickSearchTab,
      handleDeleteSearchCache,
    }
  },
}
</script>

<style lang="stylus" scoped>
.search-history
  width 100%
  margin-top 60px
  @media screen and (max-width 1200px)
    margin-top 40px
  &-title
    width 100%
    height 33px
    display flex
    flex-direction row
    align-items center
    color rgba(255, 255, 255, 0.4)
    span
      font-size 28px
      margin-right 12px
    img
      width 32px
      height 32px
    @media screen and (max-width 1200px)
      span
        font-size 24px
      img
        width 26px
        height 26px
  &-content
    width 100%
    margin-top 40px
    display flex
    flex-direction row
    flex-wrap wrap
    &-item
      max-width 700px
      height 70px
      display flex
      align-items center
      justify-content space-between
      border-radius: 4px;
      background: rgba(255, 255, 255, 0.08);
      padding 0 24px
      margin-right 40px
      margin-bottom 24px
      &-active
        background rgba(255, 255, 255, 0.15)
      span
        max-width 447px
        height 70px
        line-height 70px
        font-size 28px
        color rgba(255, 255, 255, 0.6)
        white-space nowrap
        overflow hidden
        text-overflow ellipsis
        margin-right 24px
      &-s-img
        width 30px
        height 30px
        margin-right 15px
      &-d-img
        width 24px
        height 24px
    @media screen and (max-width 1200px)
      margin-top 32px
      &-item
        padding 0 20px
        height 56px
        span
          font-size 22px
          margin-right 20px
        &-d-img
          width 15px
          height 15px
</style>
