<template>
  <div class="user-vip" :class="[
    isVip && 'vip',
    from === 'mv' && 'mv',
    ['profile', 'mine'].includes(from) && 'mine-user-vip'
  ]">
    <div class="user-vip-top">
      <div class="user-vip-info">
        <div class="avatar-default-wrapper">
          <div class="avatar" :style="{ backgroundImage: `url(${userInfo.avatar || avatar})` }"></div>
        </div>
        <div class="user-vip-info-user">
          <div class="username">
            {{ userInfo.username }}
          </div>
          <div v-if="isLogin" class="desc">{{ vipDesc }}</div>
        </div>
      </div>
      <div v-if="from !== 'mv'" class="user-vip-entry">
        <template v-if="from === 'profile'">
          <div class="active" @click="handleExchange">兑换VIP</div>
        </template>
        <template v-else>
          <div class="active" @click="handleProfile">会员中心</div>
          <div @click="handleSetting">设置</div>
        </template>
      </div>
      <div v-if="from === 'mv'" class="packages-title">
        <img :src="imgs[themeClass].title" />
      </div>
    </div>
    <div class="user-vip-openvip">
      <div class="packages">
        <div v-if="from !== 'mv'" class="packages-title">
          <h3><span>VIP权益</span></h3>
          <img :src="imgs[themeClass].title" />
        </div>
        <div class="vip-packages">
          <div class="ps">
            <div
              class="vip-packages-item"
              v-for="(item, index) in packages"
              :key="item.id"
              :class="[
                item.id === selectItem.id && 'active',
                packages.length - 1 === index && 'last',
              ]"
              @click="handleClickItem(item)"
            >
              <div
                v-if="item.tips"
                class="tips"
                :class="item.isOrange && 'orange'"
              >
                {{ item.tips }}
              </div>
              <div class="days">{{ item.title }}</div>
              <div class="day-price">
                <span>¥</span>{{ formatValue(item.day_fee) }}<span>元/天</span>
              </div>
              <div class="price">
                <span>¥{{ formatValue(item.fee) }}</span>
                <span v-if="item.fee !== item.old_fee" class="origin"
                  >¥{{ formatValue(item.old_fee) }}</span
                >
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="pay-info">
        <div class="left">
          <div class="price" v-html="formatPayTitle"></div>
          <h3>微信扫码支付</h3>
          <p>有效期至-{{ expirationDate }}</p>
        </div>
        <div class="code">
          <img :src="qrCodeURL" alt="" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { watch, ref, computed, onBeforeMount, toRefs } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import useQRCode from '@/composables/useQRCode'
import get from 'lodash/get'
import { Toast } from 'vant'
import { addDays, format } from 'date-fns'
import {
  getVipPkg,
  getVipPkgQr,
} from '@/service/vip'
import { sendLog } from '@/directives/v-log/log'

export default {
  name: 'vipComponent',
  props: {
    from: {
      type: String,
      default: 'mine',
    },
  },
  setup(props, { emit }) {
    const store = useStore()
    const router = useRouter()
    const { getQRCodeURL } = useQRCode()
    const userInfo = computed(() => store.state.userInfo)
    const vipInfo = computed(() => store.state.vipInfo)
    const isLogin = computed(() => !!userInfo.value.unionid)
    const isVip = computed(() => !!vipInfo.value.end_time)
    const setting = computed(() => store.state.setting)
    const loginSendVip = computed(() => store.state.activityInfo.loginSendVip)
    const isShowSignIn = computed(() => store.state.signin.isShowSignIn)
    const unionid = computed(() => store.state.userInfo.unionid)
    const isExpired = computed(() => store.state.vipInfo.expire)
    const { from } = toRefs(props)
    let qrCodeURL = ref(
      'https://qncweb.ktvsky.com/20211130/vadd/82b806b8bf560e49df69162835879c65.png'
    )

    const packages = ref([])
    const selectItem = ref({})
    
    const imgs = {
      themeDark: {
        // title: require('@/assets/titleLight.png'),
        title: 'https://qncweb.ktvsky.com/20250219/vadd/46ef052f2f70d8c610df83b4c595e3ba.png',
      },
      themeLight: {
        // title: require('@/assets/titleDark.png'),
        title: 'https://qncweb.ktvsky.com/20250219/vadd/ba9ce05005b6cf60705d0af0628cfe74.png',
      },
      themeSystem: {
        // title: require('@/assets/titleDark.png'),
        title: 'https://qncweb.ktvsky.com/20250219/vadd/ba9ce05005b6cf60705d0af0628cfe74.png',
      },
    }
    const themeClass = computed(() => store.state.themeClass)

    const vipDesc = computed(() => {
      return isVip.value
        ? `会员有效期至: ${vipInfo.value.end_time
            .split(' ')[0]
            .replaceAll('-', '.')}`
        : '未开通 VIP'
    })

    const expirationDate = computed(() => {
      if (!selectItem.value.days) return ''
      const currentDate = isVip.value ? new Date(vipInfo.value.end_time) : new Date()
      const expirationDate = addDays(currentDate, selectItem.value.days)

      return format(expirationDate, 'yyyy.MM.dd')
    })

    const formatValue = (value) => {
      if (value === undefined || isNaN(value)) {
        return 'N/A'
      }
      return value / 100
    }

    const getVipQrcode = async () => {
      try {
        const data = await getVipPkgQr({
          unionid: unionid.value,
          pkg_id: selectItem.value.id,
          fr: from.value == 'mv' ? 1834 : from.value == 'profile' && packages.value.length == 3  ? 1839 :  from.value == 'profile' && packages.value.length == 5 ? 1838 : packages.value.length == 3 ? 1837 : 1836,
        })

        const qr = get(data, 'qr', '')
        if (qr) {
          const qrCodeData = await getQRCodeURL(qr)
          if (qrCodeData) {
            qrCodeURL.value = qrCodeData
          }
        }
      } catch (error) {
        console.log(error)
      }
    }

    const formatPayTitle = computed(() => {
      const regex = /(\d+)(年|天)/g
      if (!selectItem.value.title) return ''

      const matches = selectItem.value.title.match(regex)
      if (!matches) return ''

      const formatted = matches
        .map((match) => {
          const [_, number, unit] = match.match(/(\d+)(年|天)/)
          return `<span class="user-vip-year-num">${number}</span>${unit}`
        })
        .join(' ')

      return formatted
    })

    const fetchConfig = async () => {
      if (!unionid.value) return

      const res = await getVipPkg(unionid.value)
      packages.value = res.data
      
      if (isVip.value) {
        const index = packages.value.findIndex((item) => !!item.tips)
        packages.value[index].isOrange = true
      } else {
        const reversedIndex = packages.value
          .slice()
          .reverse()
          .findIndex((item) => !!item.tips)
        const index = packages.value.length - 1 - reversedIndex
        packages.value[index].isOrange = true
      }

      const active =
        packages.value.find((item) => item.id === res.recommend_pkg) ||
        packages.value[0]
      selectItem.value = active

      getVipQrcode()
    }

    const handleClickItem = (item) => {
      try {
        if(packages.value.length === 5){
          sendLog({
            event_type: '10000~50000',
            event_name: 1021,
            event_data: {
              str1: from.value === 'mine' ? '我的' : '会员中心页',
              str2: from.value === 'mine' ? '我的-会员档位展示-5 档' : '会员中心-会员档位展示-5 档',
              str3: '点击档位',
              str4: 'click',
              str5: 2,
              str7: isVip.value ? 2 : isExpired.value ? 4 : 1,
              str9: '手机端',
              str10: from.value === 'mine' ? 1836 : 1838
            }
          })
        }else{
          sendLog({
            event_type: '10000~50000',
            event_name: 1021,
            event_data: {
              str1: from.value === 'mine' ? '我的' : '会员中心页',
              str2: from.value === 'mine' ? '我的-会员档位展示-3 档' : '会员中心-会员档位展示-3 档',
              str3: '点击档位',
              str4: 'click',
              str5: 2,
              str7: isVip.value ? 2 : isExpired.value ? 4 : 1,
              str9: '手机端',
              str10: from.value === 'mine' ? 1837 : 1839
            }
          })
        }

        qrCodeURL.value =
          'https://qncweb.ktvsky.com/20211130/vadd/82b806b8bf560e49df69162835879c65.png'
        selectItem.value = item
        getVipQrcode()

        if(from.value == 'mv'){
          sendLog({
            event_type: '10000~50000',
            event_name: 1021,
            event_data: {
              str1: 'MV 页面',
              str2: '我的-VIP 支付',
              str3: '任意支付档位',
              str4: 'click',
              str5: 2,
              str6: isVip.value ? 2 : isExpired.value ? 4 : 1,
              str9: '车机端',
              str10: '1834'
            }
          })
          return
        }

        

        if(packages.value.length == 3){
          sendLog({
            event_type: '10000~50000',
            event_name: 1021,
            event_data: {
              str1: from.value == 'profile' ? '会员中心页' : '我的',
              str2: from.value == 'profile' ? '会员中心-会员档位展示-3 档' : '我的-会员档位展示-3 档',
              str3: '点击档位',
              str4: 'click',
              str5: 2,
              str6: isVip.value ? 2 : isExpired.value ? 4 : 1,
              str9: '车机端',
              str10: from.value == 'profile' ? '1839' : '1837'
            }
          })
        }else{
          sendLog({
            event_type: '10000~50000',
            event_name: 1021,
            event_data: {
              str1: from.value == 'profile' ? '会员中心页' : '我的',
              str2: from.value == 'profile' ? '会员中心-会员档位展示-5 档' : '我的-会员档位展示-5 档',
              str3: '点击档位',
              str4: 'click',
              str5: 2,
              str6: isVip.value ? 2 : isExpired.value ? 4 : 1,
              str9: '车机端',
              str10: from.value == 'profile' ? '1838' : '1836'
            }
          })
        }
      } catch (error) {
        console.log('handleClickItem error', error)
      }
    }

    const handleProfile = () => {
      try {
        router.push({
          name: 'profile',
        })
        sendLog({
          event_type: '10000~50000',
          event_name: 10030,
          event_data: {
            str1: '我的',
            str2: '开通VIP',
            str3: '点击进入',
            str4: 'click',
          },
        })
      } catch (error) {
        console.log('handleProfile error', error)
      }
    }

    const handleExchange = () => {
      try {
        router.push({
          name: 'vipExchange',
        })
        sendLog({
          event_type: '10000~50000',
          event_name: 10030,
          event_data: {
            str1: '我的',
            str2: '开通VIP',
            str3: '点击进入',
            str4: 'click',
            str5: '会员兑换',
            str6: 'click',
          },
        })

        sendLog({
          event_type: '10000~50000',
          event_name: 6008,
          event_data: {
            str1: '我的页',
            str2: '兑换VIP',
            str3: '点击兑换VIP按钮',
            str4: 'click'
          },
        })
      } catch (error) {
        console.log('handleExchange error', error)
      }
    }

    const handleSetting = () => {
      sendLog({
        event_type: '10000~50000',
        event_name: 6008,
        event_data: {
          str1: '我的页',
          str2: '设置',
          str3: '设置',
          str4: 'click',
        },
      })
      try {
        router.push({
          name: 'setting',
        })
      } catch (error) {
        console.log('handleSetting error', error)
      }
    }

    watch(unionid, (val) => {
      if (val) {
        fetchConfig()
      }
    })

    onBeforeMount(() => {
      fetchConfig()
    })

    return {
      vipInfo,
      userInfo,
      isLogin,
      isVip,
      setting,
      isShowSignIn,
      loginSendVip,
      vipDesc,
      formatValue,
      packages,
      expirationDate,
      selectItem,
      handleClickItem,
      qrCodeURL,
      handleProfile,
      handleExchange,
      handleSetting,
      formatPayTitle,
      imgs,
      themeClass,
    }
  },
}
</script>

<style lang="stylus" scoped>
.user-vip
  &-top
    display flex
    justify-content space-between
    flex-wrap wrap
    align-items center
    margin-bottom 60px
    @media screen and (max-width 1200px)
      margin-bottom 40px
  &-info
    display flex
    align-items center
    @media screen and (max-width 1200px)
      margin-bottom 30px
    .avatar-default-wrapper
      width 120px
      height 120px
      border-radius 100%
      margin-right 24px
      overflow hidden
      div
        background-size 100%
    &-user
      .username
        max-width 439px
        white-space nowrap
        overflow hidden
        text-overflow ellipsis
        color rgba(255, 255, 255, 1)
        font-size 32px
        margin-bottom 16px
      .desc
        color rgba(255, 255, 255, 0.4)
        font-size 24px
        line-height 1
        padding-left 66px
        background-repeat no-repeat
        background-size 50px auto
        background-image url(https://qncweb.ktvsky.com/20231206/vadd/342350516d9b0eef7c2a5989dfcec1c5.png)
        background-position left center
  &-entry
    display flex
    div
      display flex
      justify-content center
      align-items center
      width: 200px;
      height: 80px;
      border-radius: 100px;
      border: 2px solid rgba(255, 255, 255, 0.10);
      background: rgba(255, 255, 255, 0.08);
      color: rgba(255, 255, 255, 0.60);
      font-size 28px
    .active
      color rgba(219, 174, 106, 1)
      margin-right 32px
  &.vip
    .username
      color: #E3AB5D;
    .desc
      background-image url(https://qncweb.ktvsky.com/20231206/vadd/56dc0bc6477bf2c7a6c4fcdc8360804e.png)
  &-openvip
    background rgba(255, 255, 255, 0.06)
    display flex
    border-radius 5px
    @media screen and (max-width 1200px)
      height 500px
    .packages
      width 890px
      flex 1
      background url('https://qncweb.ktvsky.com/20231208/other/b2d860aba32a9a1fdf2d277e579be3d1.png') no-repeat right center
      background-size 143px auto
      padding 0 70px 0 48px
      @media screen and (max-width 1200px)
        padding-left 30px
        padding-right 30px
      &-title
        display flex
        align-items center
        justify-content space-between
        flex-wrap wrap
        border-bottom 1px solid rgba(255, 255, 255, 0.1)
        padding 40px 0
        h3
          font-size: 30px;
          font-weight: 500;
          padding-left 67px
          background url('https://qncweb.ktvsky.com/20231208/other/d87306bc759edd6d14444eb2459f4716.png') no-repeat left center
          background-size 55px auto
          span
            background: linear-gradient(180deg, #FFDAB3 0%, #C69156 100%);
            background: linear-gradient(180deg, color(display-p3 0.9765 0.8588 0.7216) 0%, color(display-p3 0.7451 0.5765 0.3725) 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        img
          width 563px
          height 32px
      .vip-packages
        min-width 640px
        .ps
          display flex
          padding-top 65px
          transform: translateZ(0)
          padding-right 0
          ::v-deep .ps__thumb-x, ::v-deep .ps__rail-x
            opacity 0
        &-item
          width: 200px;
          height: 260px;
          border-radius: 10px;
          background: linear-gradient(217deg, rgba(255, 237, 219, 0.12) 0%, rgba(255, 255, 255, 0.02) 100%), rgba(255, 255, 255, 0.04);
          backdrop-filter: blur(50px);
          border 2px solid transparent
          position relative
          text-align center
          margin-right 20px
          flex-shrink: 0;
          &.active
            background: linear-gradient(322deg, #FFE093 5.61%, #FFEBB5 34.88%, #FFECB9 97.2%);
            border: 2px solid #FFD871
            .days
              background: linear-gradient(270deg, #625F66 0%, #222124 100%);
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            .day-price
              color: #933000;
            .price
              color rgba(172, 98, 0, 1)
              .origin
                color rgba(172, 98, 0, 0.5)
          .tips
            padding 0 10px
            height 40px
            line-height 40px
            font-size 22px
            color #fff
            position absolute
            left 0
            top -20px
            background linear-gradient(90deg, #FF3D6B 0%, #8C1AFF 100%)
            border-radius 10px 10px 10px 0
            &.orange
              background linear-gradient(90deg, #ff3d3d 0%, #ff881a 100%)
          .days
            color: #FFD7AE;
            font-size: 28px;
            font-weight: 300;
            margin-top 40px
          .day-price
            color: #FFD7AE
            font-size: 52px;
            margin 12px 0 9px
            span
              font-size: 24px;
          .price
            font-size: 24px;
            color rgba(255, 255, 255, 1)
            display flex
            justify-content center
            font-weight: 300;
            .origin
              color: rgba(255, 255, 255, 0.5)
              font-size: 24px;
              text-decoration line-through
              margin-left 8px
    .pay-info
      width 300px
      margin-top 35px
      padding-bottom 60px
      .left
        width 100%
        text-align center
        margin 0 auto
        ::v-deep .price
          color #FFD67D
          font-size 28px
          margin-bottom 20px
          min-height 80px
          span
            font-size 80px
            line-height 85px
        h3
          color rgba(255, 255, 255, 0.8)
          font-size 28px
          margin-bottom 6px
        p
          color rgba(255, 255, 255, 0.6)
          font-size 22px
          white-space nowrap
      .code
        width 180px
        height 180px
        background #fff
        margin 20px auto 0
        overflow hidden
        img
          width 170px
          height 170px
          margin 5px
  &.mine-user-vip
    .ps
      justify-content center
  &.mv
    .ps
      overflow-x scroll !important
      &::-webkit-scrollbar
        display none
    .user-vip-top
      border-radius: 10px;
      padding 32px
      margin 0 48px
      background: rgba(255, 255, 255, 0.05);
      .packages-title
        width 100%
        margin-top 32px
        padding-top 33px
        background url('https://qncweb.ktvsky.com/20231212/other/7d7396163a83a861d8da23594838a424.png') no-repeat left top
        background-size 100% auto
    .user-vip-openvip
      background none
      flex-wrap wrap
      justify-content center
      .vip-packages
        display flex
        justify-content center
      .ps
        padding-top 60px
      .packages
        background none
        padding-right 10px
        width 700px
      .pay-info
        width auto
        display flex
        justify-content space-between
        align-items center
        ::v-deep .price
          min-height 40px
        .left
          flex 1
          width auto
        p
          margin-left 0!important
          white-space nowrap
        .code
          margin 0 0 0 60px
    @media screen and (max-width 1200px) and (min-height 1200px)
      .user-vip-top
        margin 0 112px
        padding 40px 40px 32px 40px
        .avatar
          width 96px
          height 96px
          margin-right 20px
        .username
          font-size 26px
        .desc
          font-size 20px
          background-size 40px auto
          padding-left 52px
        .packages-title
          margin-top 10px
          // background-size auto 26px
      .user-vip-openvip
        height 608px
        flex-direction column
        align-items center
        flex-wrap nowrap!important
        zoom calc(180 / 200)
        .last
          margin-right 0
        .vip-packages-container
          padding-top 52px
          justify-content center
          .vip-packages-item
            width 182px
            height 234px
            margin-right 16px
            .days
              font-size 22px
            .day-price
              font-size 48px
              span
                font-size 22px
            .price
              font-size 20px
              .origin
                font-size 20px
            .days
              font-size 20px
        .packages
          padding-left 0
        .pay-info
          margin-top 79px
          .left
            .price
              font-size 22px
              span
                font-size 22px
            .num
              font-size 64px !important
            h3
              font-size 22px
            p
              font-size 18px
          .code
            margin 0 0 0 40px
  @media screen and (max-width 1200px)
    zoom 0.74
  @media screen and (max-width 1200px) and (min-height 1200px)
    &.mv
      zoom normal
.theme-themeLight
  .user-vip-top
    .username
      color #1D1D1FE5!important
    .desc
      color #1D1D1F66!important
      background-image url('https://qncweb.ktvsky.com/20240226/other/3cfac1df1c19e0078cf57e2b32f72570.png')
  .vip
    .username
      color #DD9949!important
    .desc
      background-image url('https://qncweb.ktvsky.com/20231206/vadd/56dc0bc6477bf2c7a6c4fcdc8360804e.png')
  .user-vip-entry
    div
      border: 2px solid #1D1D1F33
      color #1D1D1FE5
    .active
      background #fff
      color #DD9949
  .user-vip-openvip
    background #FFFFFF80
  .packages-title
    border-bottom-color #1D1D1F1A!important
    h3 span
      color #1D1D1FE5
      background none
      -webkit-text-fill-color #1D1D1FE5
  .vip-packages-item
    background: #fff!important
    .days
      color: #DD9949!important
    .day-price
      color #DD9949!important
    .price span
      color #1D1D1F!important
      &.origin
        color #1D1D1F80!important
    &.active
      background: linear-gradient(321.82deg, #FFDF87 5.61%, #FFEAAE 34.88%, #FFEBB2 96.2%)!important
      .days
        color #615F66!important
      .day-price
        color #883700!important
      .price span
        color #AC6200!important
        &.origin
          color #AC620080!important
  .pay-info
    .price
      color #DD9949!important
    h3
      color #1D1D1FCC!important
    p
      color #1D1D1F99!important
  .mv
    .user-vip-top
      background #FFFFFF
      .packages-title
        background-image url(https://qncweb.ktvsky.com/20240226/other/24c7036f3bb4145b7465adbd48ee80bf.png)
    .vip-packages
      &-item
        background: #FFFFFF
</style>

