<template>
  <CommonModal ref="root">
    <div class="grade-result-modal-content" :class="level">
      <div class="top">
        <img @click="handleCancel" src="https://qncweb.ktvsky.com/20231207/vadd/72ff0b114ee2cb3153ce901af19bc813.png" alt="">
        <van-count-down
          ref="countDown"
          :time="11 * 1000"
          @finish="handleConfirm"
        >
          <template #default="timeData">
            <button @click="handleConfirm" class="next-button">下一首({{timeData.seconds}}s)</button>
          </template>
        </van-count-down>
      </div>
      <div class="info">
        <div
          class="info-score"
          :style="{ backgroundImage: `url(${currentConfig.icon})`}"
        >
          <i class="score">{{ score }}</i>
          <span>分</span>
        </div>
        <p v-html="desc"></p>
      </div>
    </div>
  </CommonModal>
</template>
<script>
import { ref, toRefs, onMounted } from 'vue';
import CommonModal from '@/components/modal/common/component.vue';
import { CountDown } from 'vant';
import { sendLog } from "@/directives/v-log/log";

const obj = {
  A: {
    icon: 'https://qncweb.ktvsky.com/20240206/other/5aab84d30376184f0d7f2033d2a36f6c.png',
  },
  B: {
    icon: 'https://qncweb.ktvsky.com/20240206/other/eedef22be79e8828e37c244712b669b3.png',
  },
  C: {
    icon: 'https://qncweb.ktvsky.com/20240206/other/36b01654f5b0f2d46e5ed9a2ec4e5af8.png',
  },
  S: {
    icon: 'https://qncweb.ktvsky.com/20240206/other/c0d7e6453aa958ea84f6d1c9cb63cd43.png',
  },
  SS: {
    icon: 'https://qncweb.ktvsky.com/20240206/other/c5f43aced793dc50131fe1d49125a2c1.png',
  },
  SSS: {
    icon: 'https://qncweb.ktvsky.com/20240206/other/a2a7495fcdfb02385be8d96c597e39a9.png',
  },
}
export default {
  name: 'GradeResultModal',
  props: {
    name: String,
    score: Number,
    level: String,
    desc: String,
    onConfirm: Function,
    onCancel: Function,
  },
  components: {
    CommonModal,
    [CountDown.name]: CountDown,
  },
  setup(props) {
    const root = ref(null);
    const countDown = ref(null);
    const { onConfirm, onCancel, level } = toRefs(props);
    const currentConfig = obj[level.value]

    const handleConfirm = () => {
      onConfirm.value && onConfirm.value.call();
    };
    const handleCancel = () => {
      onCancel.value && onCancel.value.call();
    };

    onMounted(()=>{
      sendLog({
        event_type: '10000~50000',
        event_name: 6007,
        event_data: {
          str1: '欢唱页',
          str2: '评分结果',
          str3: '评分结果展示',
          str4: 'click',
        }
      })
    })
    return {
      root,
      handleConfirm,
      handleCancel,
      currentConfig,
      countDown,
    };
  },
};
</script>
<style lang="stylus" scoped>
.grade-result-modal {
  &-content {
    position: relative;
    width: 100vw;
    height: 100vh;
    background: #1E1F21;
    border-radius: 20px;
    color: rgba(255, 255, 255, 0.8);
    .top {
      height 164px
      display flex
      justify-content space-between
      align-items center
      padding 0 96px 0 80px
      img {
        width 40px
      }
      ::v-deep .next-button {
        border: 2px solid rgba(255, 255, 255, 0.2)!important
        font-size 28px!important
        width 212px!important
        height 85px!important
        border-radius 4px!important
        color: #fff!important
      }
    }

    .info {
      display: flex;
      flex-direction column
      justify-content: center;
      align-items: center;
      margin-bottom: 101px;
      position relative
      top -50px

      &-title {
        color: #fff;
        margin-bottom: 64px;
      }

      &-score {
        color: #FEE074;
        font-weight: 900;
        font-size: 100px;
        background-size 620px auto
        background-repeat no-repeat
        background-position left top
        padding-top 158px
        padding-left 430px
        width 850px
        height 460px
        i {
          font-size: 150px;
          font-style: italic;
          font-weight: 700;
        }
        span {
          font-weight: 700;
          font-size 30px
        }
      }
      p {
        font-size: 80px;
        color rgba(255, 255, 255, 1)
        font-weight 300
        text-align center
        margin 0 auto
      }
    }

    .control {
      display: flex;
      justify-content: center;

      &-item {
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 14px;
        background: #383A3E;
        color: rgba(255, 255, 255, 0.8);
        width: 300px;
        height: 90px;

        &:first-child {
          margin-right: 40px;
        }

        &.confirm {
          background: rgba(255, 255, 255, 0.8);
          color: #000000;
        }
      }
    }

    .close {
      position: absolute;
      top: 30px;
      left: 30px;
      width: 90px;
      height: 90px;
    }

    .title {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      font-size: 40px;

      img {
        width: 44px;
        height: 44px;
        margin-right: 10px;
      }
    }
  }
}
.A {
  .info-score {
    i, span {
      background: linear-gradient(171deg, #979ED3 24.22%, #ECF3FE 75.84%)
      background: linear-gradient(171deg, color(display-p3 0.5958 0.6194 0.8125) 24.22%, color(display-p3 0.9294 0.9529 0.9922) 75.84%)
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
}
.B, .C {
  .info-score {
    i, span {
      background: linear-gradient(177deg, #E6967E 24.23%, #FFE9D2 76.05%)
      background: linear-gradient(177deg, color(display-p3 0.8583 0.6017 0.5114) 24.23%, color(display-p3 0.9843 0.9176 0.8353) 76.05%)
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
}
.S {
  .info-score {
    i, span {
      background: linear-gradient(175deg, #FFD06B 21.9%, #FFF6D9 76.01%)
      background: linear-gradient(175deg, color(display-p3 1 0.8248 0.4833) 21.9%, color(display-p3 1 0.9656 0.8625) 76.01%)
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
}
.SS {
  .info-score {
    padding-left 465px
    i, span {
      background: linear-gradient(164deg, #FF1DFF 10.03%, #FFD289 44.34%, #FFF 78.9%)
      background: linear-gradient(164deg, color(display-p3 0.9392 0.2402 1) 10.03%, color(display-p3 1 0.8311 0.5777) 44.34%, color(display-p3 1 1 1) 78.9%)
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
}
.SSS {
  .info-score {
    padding-left 541px
    i, span {
      background: linear-gradient(350deg, #FFF 22.19%, #89E0FF 52.97%, #FFC0FF 84.41%)
      background: linear-gradient(350deg, color(display-p3 1 1 1) 22.19%, color(display-p3 0.6157 0.8706 0.9961) 52.97%, color(display-p3 1 0.7647 1) 84.41%)
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
}
</style>
