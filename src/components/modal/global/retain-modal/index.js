// src/components/modal/global/retain-modal/index.js
import RetainModalComponent from './index.vue'
import createRetainModal from './create.js' // 引入 createRetainModal 函数

const Plugin = (app, props = {}, slots = {}) => {
    const retainModal = createRetainModal(props, slots) // 使用 createRetainModal 函数创建API
    app.config.globalProperties.$retainModal = retainModal
    app.provide('$retainModal', retainModal)
}

RetainModalComponent.install = Plugin

export default RetainModalComponent