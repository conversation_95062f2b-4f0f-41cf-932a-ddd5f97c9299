<template>
  <CommonModal ref="root">
    <div class="vip-loggedin-modal-content"
      :style="{
        backgroundImage: `url(${vipLoginedBg})`
      }"
    >
      <div @click="handleCloseModal" class="close"></div>

      <div class="vip-packages">
        <div
          class="vip-packages-item"
          v-for="item in packages"
          :key="item.id"
          :class="{ active: item.id === selectItem.id }"
          @click="handleClickItem(item)"
        >
          <div v-if="item.tips" :class="['tips', { orange: item.isOrange }]">{{ item.tips }}</div>
          <div class="days">{{ item.title }}</div>
          <div class="day-price">¥<span>{{ formatValue(item.day_fee) }}</span>元/天</div>
          <div class="price">
            <span>¥{{ formatValue(item.fee) }}</span>
            <span v-if="item.old_fee !== item.fee" class="origin">¥{{ formatValue(item.old_fee) }}</span>
          </div>
        </div>
      </div>

      <div class="pay-info">
        <div class="left">
          <div v-if="packages.length == 3" class="price"><span>{{ formatValue(selectItem.fee) }}</span>元</div>
          <div v-else class="price" v-html="formatPayTitle"></div>
          <h3>微信扫码支付</h3>
          <p>有效期至-<span>{{ expirationDate }}</span></p>
        </div>
        <div class="code">
          <img :src="qrCodeURL" alt="QR Code">
        </div>
      </div>
    </div>
  </CommonModal>
</template>

<script>
import { onBeforeMount, ref, toRefs, computed, onUnmounted, watch } from 'vue'
import store from '@/store'
import CommonModal from '@/components/modal/common/component.vue'
import useQRCode from '@/composables/useQRCode'
import get from 'lodash/get'
import { addDays, format } from 'date-fns'
import { sendLog } from '@/directives/v-log/log'
import { getVipPkg, getVipPkgQr } from '@/service/vip'
import eventBus from '@/utils/event-bus'
import { openDatabase, getItem } from '@/utils/IndexedDB';
import { cacheImages } from '@/constants'
import useLoading from '@/composables/useLoading';

export default {
  name: 'VipModalQRcode',
  components: { CommonModal },
  props: {
    fr: { type: Number, default: 0 }
  },
  setup(props) {
    const { fr } = toRefs(props)
    const { getQRCodeURL } = useQRCode()
    const { showLoading, hideLoading } = useLoading();
    const vipInfo = computed(() => store.state.vipInfo)
    const isMvMode = computed(() => store.state.mvMode.mvMode.mode === 'mv');
    const isVip = computed(() => !!vipInfo.value.end_time)
    const unionid = computed(() => store.state.userInfo.unionid)
    const root = ref(null)
    const qrCodeURL = ref('https://qncweb.ktvsky.com/20211130/vadd/82b806b8bf560e49df69162835879c65.png')
    const packages = ref([])
    const selectItem = ref({})
    const vipLoginedBg = ref(null);
    const imgs = {
      themeDark: {
        vip: 'https://qncweb.ktvsky.com/20231201/other/557b93bf52141eddfdf7fb47449e9597.png',
        notVip: 'https://qncweb.ktvsky.com/20231201/other/8a88f9898a033f58ab9ed0f4d07d7be8.png'
      },
      themeLight: {
        vip: 'https://qncweb.ktvsky.com/20240229/other/40ae4730bde72a0904a823ad34dcab7c.png',
        notVip: 'https://qncweb.ktvsky.com/20240229/other/5638c79462f59efef6ea8245d27cccb0.png'
      },
      themeSystem: {
        vip: 'https://qncweb.ktvsky.com/20231201/other/557b93bf52141eddfdf7fb47449e9597.png',
        notVip: 'https://qncweb.ktvsky.com/20231201/other/8a88f9898a033f58ab9ed0f4d07d7be8.png'
      }
    }

    const themeClass = computed(() => store.state.themeClass)

    const expirationDate = computed(() => {
      if (!selectItem.value.days) return ''
      const currentDate = isVip.value ? new Date(vipInfo.value.end_time) : new Date()
      return format(addDays(currentDate, selectItem.value.days), 'yyyy.MM.dd')
    })

    const formatPayTitle = computed(() => {
      const regex = /(\d+)(年|天)/g
      const matches = selectItem.value.title ? selectItem.value.title.match(regex) : []
      return matches.map(match => {
        const [_, number, unit] = match.match(/(\d+)(年|天)/)
        return `<span>${number}</span>${unit}`
      }).join(' ')
    })

    const formatValue = (value) => {
      return (value === undefined || isNaN(value)) ? 'N/A' : value / 100
    }

    const getVipQrcode = async () => {
      try {
        showLoading('', true)
        const data = await getVipPkgQr({ unionid: unionid.value, pkg_id: selectItem.value.id, fr: fr.value })
        const qr = get(data, 'qr', '')
        if (qr) {
          qrCodeURL.value = await getQRCodeURL(qr) || qrCodeURL.value
        }
      } catch (error) {
        store.dispatch('getCarplayInfo', {
          noLoading: true
        })
      } finally {
        hideLoading()
      }
    }

    const handleCloseModal = () => {
      store.dispatch('getCarplayInfo', {
        noLoading: true
      })
      root.value.hide()
      logEvent('关闭弹窗')
      store.commit('base/SET_NET_LOADING', false)
    }

    const handleCancel = () => {
      store.dispatch('getCarplayInfo', {
        noLoading: true
      })
      logEvent('关闭弹窗')
    }

    const handleClickItem = (item) => {
      if (item.id !== selectItem.value.id) {
        qrCodeURL.value = 'https://qncweb.ktvsky.com/20211130/vadd/82b806b8bf560e49df69162835879c65.png'
        selectItem.value = item
        getVipQrcode()
      }
    }

    const logEvent = (action) => {
      sendLog({
        event_type: '10000~50000',
        event_name: 10094,
        event_data: {
          str1: '任意页',
          str2: 'VIP弹窗',
          str3: action,
          str4: 'click'
        }
      })
    }

    // 监视 isMvMode 的变化
    watch(isMvMode, (newValue) => {
      console.log('监听isMvMode变化', newValue)
      if (!newValue) {
        handleCloseModal();
      }
    });

    onBeforeMount(async () => {
      try {
        showLoading('', true)
        await openDatabase(); // 确保数据库已打开
        vipLoginedBg.value = await getItem('vipLoginedBg') || cacheImages.vipLoginedBg;
        if (vipLoginedBg.value) {
          console.log('vipLoginedBg loaded from IndexedDB');
        } else {
          console.log('vipLoginedBg not found in IndexedDB');
        }
        
        const res = await getVipPkg(unionid.value)
        packages.value = res.data

        const index = isVip.value 
          ? packages.value.findIndex(item => !!item.tips) 
          : packages.value.slice().reverse().findIndex(item => !!item.tips)

        if (index >= 0) {
          packages.value[index].isOrange = true
        }

        selectItem.value = packages.value.find(item => item.id === res.recommend_pkg) || packages.value[0]
        getVipQrcode()
      } catch (error) {
        console.error('Error during onBeforeMount:', error);
      } finally {
        hideLoading()
      }

      eventBus.on('nats-vip-pay', handleCloseModal)
    })

    onUnmounted(() => {
      eventBus.off('nats-vip-pay', handleCloseModal)
      hideLoading()
    })

    return {
      handleCloseModal,
      handleClickItem,
      handleCancel,
      isVip,
      packages,
      selectItem,
      expirationDate,
      qrCodeURL,
      root,
      formatValue,
      formatPayTitle,
      imgs,
      themeClass,
      vipLoginedBg,
    }
  }
}
</script>

<style lang="stylus" scoped>
.vip-loggedin-modal
  &-content
    position: relative
    width: 1260px
    height: 780px
    background-repeat: no-repeat
    background-size: 100% auto
    overflow hidden
    @media screen and (aspect-ratio: 16/9)
      zoom: 1
    @media screen and (aspect-ratio: 4/3)
      zoom: 0.8
    @media screen and (aspect-ratio: 3/2)
      zoom: 0.6
    @media screen and (max-height 800px) and (min-width: 1600px)
      zoom: 0.6
      
    .close
      width 40px
      height 40px
      position absolute
      left unset
      right 55px
      top 25px

    .vip-packages
      display: flex
      justify-content: center
      align-items: center
      margin-top: 240px
      min-height 228px
      &-item
        width: 200px
        height: 228px
        border-radius: 20px
        background: #FFFFFF
        text-align center
        color #DD9949
        position relative
        padding-top 36px
        font-weight 300
        margin 0 12px
        border 2px solid transparent
        &:first-child
          margin-left 0
        &:last-child
          margin-right 0
        .tips
          position absolute
          height 40px
          line-height 40px
          color #FFFFFF
          left 0
          top -20px
          background: linear-gradient(90deg, #FF3D6B 0%, #8C1AFF 100%);
          padding 0 11px
          border-radius 10px
          font-size 22px
        .orange
          background: linear-gradient(90deg, #FF3D3D 0%, #FF881A 100%);
        .days
          font-size 28px
        .day-price
          font-size 24px
          font-weight 500
          span
            font-size 48px
            font-weight 700
        .price
          color #1D1D1F
          font-size #1D1D1F
          margin-top 18px
          .origin
            opacity 0.5
            margin-left 10px

      .active
        background: linear-gradient(321.82deg, #FFE093 5.61%, #FFEBB5 34.88%, #FFECB9 96.2%);
        border: 2px solid #FFD871
        .days
          color #615F66
        .day-price
          color #FF0000
        .price
          color #AC6200CC
          .origin
            opacity 1
            color #AC620080

    .pay-info
      display flex
      margin 90px auto 0
      justify-content center
      align-items center
      color: #FFFFFFCC;
      text-align center
      .left
        min-width 220px
      .price
        color #DD9949
        font-size 28px
        height 122px
        span
          font-size 80px
          font-weight bold
      h3
        font-size 28px
      p
        font-size 22px
        color: #FFFFFF99;
        span
          display inline-block
          width 110px
      .code
        width 180px
        height 180px
        display flex
        justify-content center
        align-items center
        margin-left 60px
        background #fff
        margin-top 10px
        img
          width 170px
          height 170px
</style>
