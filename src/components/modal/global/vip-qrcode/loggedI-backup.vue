<template>
  <CommonModal ref="root" :zIndex="10">
    <div class="vip-loggedin-modal-content">
      <svg @click="handleCloseModal" class="close" width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect width="90" height="90" rx="14" fill="#1E1F21" style="fill:#1E1F21;fill:color(display-p3 0.1176 0.1216 0.1294);fill-opacity:1;"/>
        <path opacity="0.800874" fill-rule="evenodd" clip-rule="evenodd" d="M31.0607 30.0604C30.4749 29.4746 29.5251 29.4746 28.9393 30.0604C28.3536 30.6462 28.3536 31.596 28.9393 32.1818L42.3788 45.6212L28.948 59.052C28.3622 59.6378 28.3622 60.5876 28.948 61.1734C29.5337 61.7591 30.4835 61.7591 31.0693 61.1734L44.5001 47.7425L57.9307 61.1731C58.5165 61.7589 59.4662 61.7589 60.052 61.1731C60.6378 60.5873 60.6378 59.6376 60.052 59.0518L46.6214 45.6212L60.0607 32.182C60.6464 31.5962 60.6464 30.6464 60.0607 30.0607C59.4749 29.4749 58.5251 29.4749 57.9393 30.0607L44.5001 43.4999L31.0607 30.0604Z" fill="white" style="fill:white;fill-opacity:1;"/>
      </svg>

      <div class="bg">
        <div class="top">
          <img v-if="isVip" :src="imgs[themeClass].vip" alt="VIP Image">
          <img v-else :src="imgs[themeClass].notVip" alt="Non-VIP Image">
        </div>
        <div class="vip-packages">
          <div
            class="vip-packages-item"
            v-for="item in packages"
            :key="item.id"
            :class="{ active: item.id === selectItem.id }"
            @click="handleClickItem(item)"
          >
            <div v-if="item.tips" :class="['tips', { orange: item.isOrange }]">{{ item.tips }}</div>
            <div class="days">{{ item.title }}</div>
            <div class="day-price">¥<span>{{ formatValue(item.day_fee) }}</span>元/天</div>
            <div class="price">
              <span>¥{{ formatValue(item.fee) }}</span>
              <span v-if="item.old_fee !== item.fee" class="origin">¥{{ formatValue(item.old_fee) }}</span>
            </div>
          </div>
        </div>
        <div class="pay-info">
          <div class="left">
            <div class="price">
              <div v-if="packages.length == 3" class="price"><span>{{ formatValue(selectItem.fee) }}</span>元</div>
              <div v-else class="price" v-html="formatPayTitle"></div>
            </div>
            <h3>微信扫码支付</h3>
            <p>有效期至-{{ expirationDate }}</p>
          </div>
          <div class="code">
            <img :src="qrCodeURL" alt="QR Code">
          </div>
        </div>
      </div>

      <svg v-show="themeClass === 'themeDark'" width="1200" height="780" viewBox="0 0 1200 780" fill="none" xmlns="http://www.w3.org/2000/svg">
        <mask id="mask0_3248_21665" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="1200" height="780">
        <rect width="1200" height="780" rx="20" fill="url(#paint0_linear_3248_21665)" style=""/>
        </mask>
        <g mask="url(#mask0_3248_21665)">
        <path d="M0 32C0 20.799 0 15.1984 2.17987 10.9202C4.09734 7.15695 7.15695 4.09734 10.9202 2.17987C15.1984 0 20.799 0 32 0H1168C1179.2 0 1184.8 0 1189.08 2.17987C1192.84 4.09734 1195.9 7.15695 1197.82 10.9202C1200 15.1984 1200 20.799 1200 32V748C1200 759.201 1200 764.802 1197.82 769.08C1195.9 772.843 1192.84 775.903 1189.08 777.82C1184.8 780 1179.2 780 1168 780H32C20.7989 780 15.1984 780 10.9202 777.82C7.15695 775.903 4.09734 772.843 2.17987 769.08C0 764.802 0 759.201 0 748V32Z" fill="#191919" style="fill:#191919;fill:color(display-p3 0.0970 0.0970 0.0970);fill-opacity:1;"/>
        <g filter="url(#filter0_f_3248_21665)">
        <ellipse cx="837.5" cy="7.61911" rx="228.5" ry="147.936" fill="#AF6C2E" fill-opacity="0.21" style="fill:#AF6C2E;fill:color(display-p3 0.6875 0.4238 0.1805);fill-opacity:0.21;"/>
        </g>
        <g filter="url(#filter1_f_3248_21665)">
        <ellipse cx="1136.55" cy="145.619" rx="203.553" ry="131.651" fill="#AF6C2E" fill-opacity="0.17" style="fill:#AF6C2E;fill:color(display-p3 0.6875 0.4238 0.1805);fill-opacity:0.17;"/>
        </g>
        </g>
        <defs>
        <filter id="filter0_f_3248_21665" x="309" y="-440.317" width="1057" height="895.873" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
        <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
        <feGaussianBlur stdDeviation="150" result="effect1_foregroundBlur_3248_21665"/>
        </filter>
        <filter id="filter1_f_3248_21665" x="633" y="-286.032" width="1007.11" height="863.302" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
        <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
        <feGaussianBlur stdDeviation="150" result="effect1_foregroundBlur_3248_21665"/>
        </filter>
        <linearGradient id="paint0_linear_3248_21665" x1="600" y1="0" x2="600" y2="780" gradientUnits="userSpaceOnUse">
        <stop stop-color="#FFEBBF" style="stop-color:#FFEBBF;stop-color:color(display-p3 1.0000 0.9231 0.7500);stop-opacity:1;"/>
        <stop offset="1" stop-color="#FFCE6F" style="stop-color:#FFCE6F;stop-color:color(display-p3 1.0000 0.8073 0.4333);stop-opacity:1;"/>
        <stop offset="1" stop-color="#FFCE6F" style="stop-color:#FFCE6F;stop-color:color(display-p3 1.0000 0.8073 0.4333);stop-opacity:1;"/>
        </linearGradient>
        </defs>
      </svg>
      <svg v-show="themeClass === 'themeLight'" width="1200" height="780" viewBox="0 0 1200 780" fill="none" xmlns="http://www.w3.org/2000/svg">
        <mask id="mask0_51_4" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="1200" height="780">
        <rect width="1200" height="780" rx="20" fill="url(#paint0_linear_51_4)"/>
        </mask>
        <g mask="url(#mask0_51_4)">
        <path d="M0 8C0 3.58172 3.58172 0 8 0H1192C1196.42 0 1200 3.58172 1200 8V772C1200 776.418 1196.42 780 1192 780H7.99999C3.58171 780 0 776.418 0 772V8Z" fill="url(#paint1_linear_51_4)"/>
        <g filter="url(#filter0_f_51_4)">
        <ellipse cx="1136.55" cy="145.619" rx="203.553" ry="131.651" fill="#FF7B01" fill-opacity="0.17"/>
        </g>
        </g>
        <defs>
        <filter id="filter0_f_51_4" x="633" y="-286.032" width="1007.11" height="863.302" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
        <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
        <feGaussianBlur stdDeviation="150" result="effect1_foregroundBlur_51_4"/>
        </filter>
        <linearGradient id="paint0_linear_51_4" x1="600" y1="0" x2="600" y2="780" gradientUnits="userSpaceOnUse">
        <stop stop-color="#FFEBBF"/>
        <stop offset="1" stop-color="#FFCE6F"/>
        <stop offset="1" stop-color="#FFCE6F"/>
        </linearGradient>
        <linearGradient id="paint1_linear_51_4" x1="600" y1="0" x2="600" y2="780" gradientUnits="userSpaceOnUse">
        <stop stop-color="white" stop-opacity="0.99"/>
        <stop offset="0.381179" stop-color="#E2E5ED"/>
        </linearGradient>
        </defs>
      </svg>

    </div>
  </CommonModal>
</template>

<script>
import { onBeforeMount, ref, toRefs, computed, onUnmounted } from 'vue'
import store from '@/store'
import CommonModal from '@/components/modal/common/component.vue'
import useQRCode from '@/composables/useQRCode'
import get from 'lodash/get'
import { addDays, format } from 'date-fns'
import { sendLog } from '@/directives/v-log/log'
import { getVipPkg, getVipPkgQr } from '@/service/vip'
import eventBus from '@/utils/event-bus'

export default {
  name: 'VipModalQRcode',
  components: { CommonModal },
  props: {
    fr: { type: Number, default: 0 }
  },
  setup(props) {
    const { fr } = toRefs(props)
    const { getQRCodeURL } = useQRCode()
    const vipInfo = computed(() => store.state.vipInfo)
    const isVip = computed(() => !!vipInfo.value.end_time)
    const unionid = computed(() => store.state.userInfo.unionid)
    const root = ref(null)
    const qrCodeURL = ref('https://qncweb.ktvsky.com/20211130/vadd/82b806b8bf560e49df69162835879c65.png')
    const packages = ref([])
    const selectItem = ref({})
    
    const imgs = {
      themeDark: {
        vip: 'https://qncweb.ktvsky.com/20231201/other/557b93bf52141eddfdf7fb47449e9597.png',
        notVip: 'https://qncweb.ktvsky.com/20231201/other/8a88f9898a033f58ab9ed0f4d07d7be8.png'
      },
      themeLight: {
        vip: 'https://qncweb.ktvsky.com/20240229/other/40ae4730bde72a0904a823ad34dcab7c.png',
        notVip: 'https://qncweb.ktvsky.com/20240229/other/5638c79462f59efef6ea8245d27cccb0.png'
      },
      themeSystem: {
        vip: 'https://qncweb.ktvsky.com/20231201/other/557b93bf52141eddfdf7fb47449e9597.png',
        notVip: 'https://qncweb.ktvsky.com/20231201/other/8a88f9898a033f58ab9ed0f4d07d7be8.png'
      }
    }

    const themeClass = computed(() => store.state.themeClass)

    const expirationDate = computed(() => {
      if (!selectItem.value.days) return ''
      const currentDate = isVip.value ? new Date(vipInfo.value.end_time) : new Date()
      return format(addDays(currentDate, selectItem.value.days), 'yyyy.MM.dd')
    })

    const formatPayTitle = computed(() => {
      const regex = /(\d+)(年|天)/g
      const matches = selectItem.value.title ? selectItem.value.title.match(regex) : []
      return matches.map(match => {
        const [_, number, unit] = match.match(/(\d+)(年|天)/)
        return `<span>${number}</span>${unit}`
      }).join(' ')
    })

    const formatValue = (value) => {
      return (value === undefined || isNaN(value)) ? 'N/A' : value / 100
    }

    const getVipQrcode = async () => {
      try {
        const data = await getVipPkgQr({ unionid: unionid.value, pkg_id: selectItem.value.id, fr: fr.value })
        const qr = get(data, 'qr', '')
        if (qr) {
          qrCodeURL.value = await getQRCodeURL(qr) || qrCodeURL.value
        }
      } catch (error) {
        store.dispatch('getCarplayInfo', {
          noLoading: true
        })
      }
    }

    const handleCloseModal = () => {
      store.dispatch('getCarplayInfo', {
        noLoading: true
      })
      root.value.hide()
      logEvent('关闭弹窗')
      store.commit('base/SET_NET_LOADING', false)
    }

    const handleCancel = () => {
      store.dispatch('getCarplayInfo', {
        noLoading: true
      })
      logEvent('关闭弹窗')
    }

    const handleClickItem = (item) => {
      if (item.id !== selectItem.value.id) {
        qrCodeURL.value = 'https://qncweb.ktvsky.com/20211130/vadd/82b806b8bf560e49df69162835879c65.png'
        selectItem.value = item
        getVipQrcode()
      }
    }

    const logEvent = (action) => {
      sendLog({
        event_type: '10000~50000',
        event_name: 10094,
        event_data: {
          str1: '任意页',
          str2: 'VIP弹窗',
          str3: action,
          str4: 'click'
        }
      })
    }

    onBeforeMount(async () => {
      const res = await getVipPkg(unionid.value)
      packages.value = res.data

      const index = isVip.value 
        ? packages.value.findIndex(item => !!item.tips) 
        : packages.value.slice().reverse().findIndex(item => !!item.tips)

      if (index >= 0) {
        packages.value[index].isOrange = true
      }

      selectItem.value = packages.value.find(item => item.id === res.recommend_pkg) || packages.value[0]
      getVipQrcode()

      eventBus.on('nats-vip-pay', handleCloseModal)
    })

    onUnmounted(() => {
      eventBus.off('nats-vip-pay', handleCloseModal)
    })

    return {
      handleCloseModal,
      handleClickItem,
      handleCancel,
      isVip,
      packages,
      selectItem,
      expirationDate,
      qrCodeURL,
      root,
      formatValue,
      formatPayTitle,
      imgs,
      themeClass,
    }
  }
}
</script>

<style lang="stylus" scoped>
.vip-loggedin-modal
  &-content
    width 1200px
    height 780px
    border-radius 20px
    background: #1E1F21
    position relative
    & > svg
      position absolute
      left 0
      top 0
      width 100%
      height 100%
    @media screen and (max-width 1200px)
      zoom 0.8
    @media screen and (max-width: 1950px) and (min-width: 1700px) and (max-height: 720px) and (min-height: 650px)
      zoom 0.7
    .bg
      background url(https://qncweb.ktvsky.com/20231201/other/4639196bfaaab6bf2b1aea62b87d8ef4.png) no-repeat
      background-size 261px 183px
      background-position 880px 16px
      position relative
      z-index 10
    .close
      position absolute
      top -100px
      left 0px
      width 90px
      height 90px
    .top
      height 200px
      border-bottom 1px solid rgba(255, 255, 255, 0.1)
      margin 0 48px
      display flex
      align-items center
      img
        width auto
        height 116px
    .vip-packages
      display flex
      justify-content center
      margin-top 60px
      height 228px
      &-item
        width: 200px;
        height: 228px;
        border-radius: 20px;
        background: linear-gradient(217deg, rgba(255, 238, 222, 0.12) 0%, rgba(255, 255, 255, 0.02) 100%), rgba(255, 255, 255, 0.04);
        backdrop-filter: blur(50px);
        position relative
        text-align center
        margin 0 12px
        &.active
          background: linear-gradient(322deg, #FFE093 5.61%, #FFEBB5 34.88%, #FFECB9 96.2%);
          .days
            background: linear-gradient(270deg, #615F66 0%, #222124 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          .price
            color rgba(172, 98, 0, 0.8)
            .origin
              color rgba(172, 98, 0, 0.5)
          .day-price
            color rgba(255, 0, 0, 1)
        .tips
          padding 0 10px
          height 40px
          line-height 40px
          font-size 22px
          color #fff
          position absolute
          left 0
          top -20px
          background linear-gradient(90deg, #FF3D6B 0%, #8C1AFF 100%)
          border-radius 10px 10px 10px 0
          &.orange
            background linear-gradient(90deg, #ff3d3d 0%, #ff881a 100%)
        .days
          color: #FAD9B3;
          font-size: 28px;
          font-weight: 300;
          margin 39px 0 3px
        .price
          color rgba(255, 255, 255, 0.6)
          font-size: 24px;
          .origin
            color rgba(255, 255, 255, 0.3)
            text-decoration line-through
            margin-left 8px
        .day-price
          color rgba(250, 217, 179, 1)
          font-size: 24px;
          margin 8px 0
          span
            font-size: 48px;
    .pay-info
      display flex
      justify-content center
      align-items center
      margin-top 40px
      .left
        width 250px
        text-align center
        .price
          color #FFD67D
          font-size 28px
          margin-bottom 20px
          span
            font-size 80px
            line-height 85px
        h3
          color rgba(255, 255, 255, 0.8)
          font-size 28px
          margin-bottom 6px
        p
          color rgba(255, 255, 255, 0.6)
          font-size 22px
      .code
        width 180px
        height 180px
        background #fff
        margin-left 60px
        img
          width 170px
          height 170px
          margin 5px
.theme-themeLight
  .vip-loggedin-modal-content
    background-color #E2E5ED
    .top
      border-bottom-color rgba(29, 29, 31, 0.1)
    .vip-packages-item
      border none
      background #fff
      .days, .day-price
        color #DD9949
      .price
        color #1D1D1F
      .origin
        color #1D1D1F80
      &.active
        background: linear-gradient(321.82deg, #FFE093 5.61%, #FFEBB5 34.88%, #FFECB9 96.2%);
        border none
        .day-price
          color #FF0000
        .price
          color #AC6200CC
    .pay-info
      .price
        color #DD9949
      h3
        color #1D1D1FCC
      p
        color #1D1D1F99
    .bg
      background-image url(https://qncweb.ktvsky.com/20240229/other/54f7176210b17a9878bcb6dd4951acd2.png)
</style>
