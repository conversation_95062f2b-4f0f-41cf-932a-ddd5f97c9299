<template>
  <CommonModal ref="root" zIndex="110">
    <div class="vip-modal-content">
      <div class="background">
        <img :src="vipBg" alt="" />
      </div>
      <div @click="handleCloseModal" class="close" :key="themeClass"></div>
      <div class="qrcode">
        <div v-if="!needReload" class="qrcode-content">
          <img :src="qrCodeURL" :key="qrCodeURL" />
        </div>

        <div v-else class="net-error" @click.stop="getVipQrcode('reload')">
          <svg
            :class="isRequest && 'active'"
            width="90"
            height="90"
            viewBox="0 0 90 90"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g opacity="0.4" clip-path="url(#clip0_1270_101510)">
              <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M50.9798 10.5088C49.0366 10.1743 47.0387 10 45 10C25.67 10 10 25.67 10 45C10 56.1164 15.1825 66.0224 23.2632 72.4337L27.1902 70.3766C19.2129 64.7677 14 55.4926 14 45C14 27.8792 27.8792 14 45 14C45.4133 14 45.8247 14.0081 46.2341 14.0241L50.9798 10.5088ZM62.6381 19.5035C70.7122 25.0996 76 34.4323 76 45C76 62.1208 62.1208 76 45 76C44.4595 76 43.9222 75.9862 43.3885 75.9588L38.697 79.434C40.7416 79.8058 42.8481 80 45 80C64.33 80 80 64.33 80 45C80 33.8105 74.7491 23.8474 66.577 17.4403L62.6381 19.5035Z"
                fill="black"
                style="fill: black; fill-opacity: 1"
              />
              <path
                d="M45.0424 80L48.3127 72.3843C48.3712 72.2479 48.4006 72.1796 48.4134 72.1425C48.7162 71.2627 47.8202 70.4473 46.9728 70.8315C46.9371 70.8477 46.8719 70.8833 46.7416 70.9545L37.0398 76.2512C36.3988 76.6012 36 77.2732 36 78.0035C36 78.5837 36.5272 78.9725 37.0924 79.103C39.6336 79.6899 42.2806 80 45 80C45.0141 80 45.0283 80 45.0424 80Z"
                fill="black"
                style="fill: black; fill-opacity: 1"
              />
              <path
                d="M44.5523 10.0028L41.2832 17.6158C41.2246 17.7522 41.1953 17.8204 41.1825 17.8575C40.8796 18.7373 41.7757 19.5527 42.623 19.1685C42.6588 19.1523 42.7239 19.1167 42.8543 19.0455L52.5561 13.7488C53.1971 13.3988 53.5959 12.7268 53.5959 11.9965C53.5959 11.3276 52.9615 10.9029 52.3073 10.7639C49.9505 10.2634 47.506 10 45 10C44.8505 10 44.7013 10.0009 44.5523 10.0028Z"
                fill="black"
                style="fill: black; fill-opacity: 1"
              />
            </g>
            <defs>
              <clipPath id="clip0_1270_101510">
                <rect
                  width="90"
                  height="90"
                  fill="white"
                  style="fill: white; fill-opacity: 1"
                />
              </clipPath>
            </defs>
          </svg>
          <p>网络异常</p>
          <p>点击刷新</p>
        </div>
      </div>
    </div>
  </CommonModal>
</template>

<script setup>
import CommonModal from '@/components/modal/common/component.vue'
import useLoading from '@/composables/useLoading'
import useQRCode from '@/composables/useQRCode'
import { cacheImages } from '@/constants'
import { vipLogFrom } from '@/constants/index'
import { sendLog } from '@/directives/v-log/log'
import { getCarplayInfo } from '@/service/carplay-info'
import store from '@/store'
import { getItem, openDatabase } from '@/utils/IndexedDB'
import { withTimeoutHandling } from '@/utils/promiseUtils'
import { debounce } from 'lodash'
import { computed, defineProps, onBeforeMount, ref, watch } from 'vue'

const props = defineProps({
  songid: {
    type: Number,
    default: 0,
  },
  log: {
    type: String,
    default: '',
  },
})
const isMvMode = computed(() => store.state.mvMode.mvMode.mode === 'mv')
const net_status = computed(() => store.state.base.net_status)
const userInfo = computed(() => store.state.userInfo)
const isLogin = computed(() => !!userInfo.value.unionid)
const carplayInfo = computed(() => store.state.carplayInfo)

const { getQRCodeURL } = useQRCode()
const { showLoading, hideLoading } = useLoading()

const root = ref(null)
const qrCodeURL = ref(
  'https://qncweb.ktvsky.com/20211130/vadd/82b806b8bf560e49df69162835879c65.png'
)
const needReload = ref(false)
const isRequest = ref(false)
// const vipLoginedBg = ref(null);
const vipBg = ref('')

// const imgs = {
//   themeDark: {
//     title: 'https://qncweb.ktvsky.com/20231226/vadd/a4e8232a41055a42361d438ecb297d65.png',
//   },
//   themeLight: {
//     title: 'https://qncweb.ktvsky.com/20240222/other/2d49d3d87044995eac64e3a9eaf0e8c1.png',
//   },
//   themeSystem: {
//     title: 'https://qncweb.ktvsky.com/20231226/vadd/a4e8232a41055a42361d438ecb297d65.png',
//   },
// }

const themeClass = computed(() => store.state.themeClass)

const getVipQrcode = debounce(
  async (payload) => {
    if (isRequest.value || (!net_status.value && payload !== 'reload')) return
    isRequest.value = true

    try {
      const pay_qr =
        payload === 'reload'
          ? (await getCarplayInfo(true)).data.pay_qr
          : carplayInfo.value.pay_qr ||
            (await withTimeoutHandling(getCarplayInfo())).data.pay_qr

      console.log('getVipQrcode:', pay_qr)

      if (pay_qr) {
        const qrCodeData = await withTimeoutHandling(
          getQRCodeURL(
            `${pay_qr}&songid=${props.songid || ''}&log=${vipLogFrom.get(
              props.log || '其他'
            )}`
          )
        )
        if (qrCodeData) {
          qrCodeURL.value = qrCodeData
          // console.log('getVipQrcode qrCodeData:', qrCodeURL.value);
        }
        needReload.value = false
        return
      }

      needReload.value = true
    } catch (error) {
      console.error('getVipQrcode error:', error)
      needReload.value = true
    } finally {
      isRequest.value = false
      hideLoading()
    }
  },
  500,
  { leading: true, trailing: false }
)

const handleCloseModal = () => {
  // store.dispatch('getCarplayInfo')
  root.value.hide()
  sendLog({
    event_type: '10000~50000',
    event_name: 10094,
    event_data: {
      str1: '任意页',
      str2: 'VIP弹窗',
      str3: '关闭弹窗',
      str4: 'click',
    },
  })

  sendLog({
    event_type: '10000~50000',
    event_name: 6012,
    event_data: {
      str1: '通用',
      str2: '会员弹窗',
      str3: '会员弹窗关闭',
      str4: 'click',
      str5: isLogin.value ? 2 : 1,
    },
  })
}

onBeforeMount(async () => {
  getVipQrcode()

  await openDatabase() // 确保数据库已打开
  vipBg.value = (await getItem('vipBg')) || cacheImages.vipBg
  if (vipBg.value) {
    console.log('vipBg loaded from IndexedDB')
  } else {
    console.log('vipBg not found in IndexedDB')
  }
})
// 监视 isMvMode 的变化
watch(isMvMode, (newValue) => {
  console.log('监听isMvMode变化', newValue)
  if (!newValue) {
    handleCloseModal()
  }
})
watch(
  net_status,
  async (val) => {
    if (!val) {
      console.log('net_status', val)
      // qrCodeURL.value = 'https://qncweb.ktvsky.com/20211130/vadd/82b806b8bf560e49df69162835879c65.png'
      // needReload.value = true
      // if (root.value) {
      //   root.value.hide()
      // }
    } else {
      needReload.value = false
      getVipQrcode()
    }
  },
  {
    immediate: true,
    deep: true,
  }
)
</script>

<style lang="stylus" scoped>
.vip-modal
  &-content
    position relative
    width 540px
    height 654px
    padding-top 0 !important
    border-radius 10px
    background url('../../../../assets/vip-modal.png')
    background-size 100% auto
    // background linear-gradient(0deg, #1E1F21 0%, #1E1F21 100%), linear-gradient(168deg, #4D4843 -82.51%, #17181B 138.48%)
    font-size 24px
    color rgba(255, 255, 255, 1)
    display flex
    flex-direction column
    align-items center
    .background
      width 100%
      height 100%
      img
        width 100%
    @media screen and (max-width: 1950px) and (min-width: 1700px) and (max-height: 720px) and (min-height: 650px)
      zoom 0.7
    @media screen and (max-width 1200px)
      zoom 0.8
    .close
      position absolute
      top 22px
      right 30px
      left unset!important
      width 50px
      height 50px
      display flex
      justify-content center
      align-items center
      svg
        width 40px
        height  40px
    .vip-top-img
      width 242px
      height 138px
      position absolute
      top -70px
      left 379px
    .title
      margin 120px 0 47px
      width 1000px
      height 309px
      display flex
      justify-content center
      img
        width 700px
        height 309px
    .qrcode
      width 140px
      height auto!important
      display flex
      justify-content center
      align-items center
      border-radius 10px
      position absolute
      left 50%
      margin-left -70px
      bottom 55px
      margin-bottom 0px
      &-content
        width 140px
        height 140px
        display flex
        align-items center
        flex-direction column
        margin-top 10px
      .offline
        left 8px
        top 8px
      &-txt
        width 226px
        height 49px
        position absolute
        bottom -42px
        left -18px
        background linear-gradient(90deg, #FF3D3D 0%, #FF881A 100%)
        font-size 24px
        color #fff
        text-align center
        line-height 49px
        border-radius 30px
        padding-left 10px
    .tip
      font-size 32px
      text-align center
      display flex
      flex-direction column
      align-items center
      span
        display flex
        align-items center
      img
        width 45px
        height 45px
        margin-right 10px

    .net-error
      font-size 20px !important
      position relative
      top 40px
      p
        margin 0px
      svg
        width 70px !important
        height 70px !important
        margin-bottom 10px
</style>
