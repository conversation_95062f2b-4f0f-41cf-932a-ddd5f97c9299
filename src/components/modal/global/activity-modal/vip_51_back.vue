<template>
  <CommonModal ref="root">
    <!-- 五一活动 -->
    <div class="activity-modal-vip">
      <img @click="handleCloseModal" class="activity-modal-vip-close" src="https://qncweb.ktvsky.com/20230426/vadd/bb4c5c9684cfc9526bcfbec0a1ee2402.png"/>
      <div class="activity-modal-vip-title">
        <span>五一出游唱</span>
        <span>年卡立减</span>
        <img src="https://qncweb.ktvsky.com/20230426/vadd/b0201b7ceb5a3989243cd358d45fa04a.png" alt="">
        <span>元</span>
      </div>
      <p class="activity-modal-vip-p"><span>开通VIP</span>享专属优质曲库，高清MV等特权</p>
      <div class="activity-modal-vip-content">
        <span>每日仅需</span>
        <img src="https://qncweb.ktvsky.com/20230426/vadd/d06a7cd11bc3c537127a87cb12f64dec.png" alt="">
        <span>元</span>
      </div>
      <div  class="activity-modal-vip-bottom">
        <div class="activity-modal-vip-code">
          <img :src="qrCodeURL" alt="">
          <span>微信扫码 立享优惠</span>
        </div>
      </div>
    </div>
  </CommonModal>
</template>
<script>
import { computed, ref, onBeforeMount } from 'vue'
import store from '@/store'
import CommonModal from '@/components/modal/common/component.vue'
import { format } from 'date-fns'
import { getCarplayInfo } from '@/service/carplay-info'
import useQRCode from '@/composables/useQRCode'
import { Toast } from 'vant'
import get from 'lodash/get'
import { sendLog } from '@/directives/v-log/log'
export default {
  name: 'ActivityVipModal',
  components: {
    CommonModal,
  },
  props: {
    closeEvent: {
      type: Function,
      default: () => {
      }
    },
  },
  setup(props) {
    const root = ref(null)
    const appStartTime = computed(() => store.state.appStartTime)
    const { getQRCodeURL } = useQRCode()
    let qrCodeURL = ref('https://qncweb.ktvsky.com/20211130/vadd/82b806b8bf560e49df69162835879c65.png')
    const getVipQrcode = async () => {
      const { data } = await getCarplayInfo()
      if (get(data, 'pay_qr', '')) {
        const qrCodeData = await getQRCodeURL(data.pay_qr)
        if (qrCodeData) {
          qrCodeURL.value = qrCodeData
        }
        return
      }
      Toast('未获取到登录二维码')
    }
    const handleCloseModal = () => {
      const now = Date.now()
      props.closeEvent.call()
      sendLog({
        event_type: 'show',
        event_name: 1723,
        event_data: {
          start_time: format(appStartTime.value, 'yyyy-MM-dd HH:mm:ss'),
          end_time: format(now, 'yyyy-MM-dd HH:mm:ss'),
          stay_time: Math.round((now - appStartTime.value) / 1000),
          key_words: '销售VIP'
        }
      })
      root.value.hide()
    }
    onBeforeMount(getVipQrcode)
    return {
      root,
      qrCodeURL,
      handleCloseModal,
    }
  }
}
</script>
<style lang="stylus" scoped>
.activity-modal-vip
  padding-top 79px
  position relative
  width 1000px
  height 730px
  // background url() no-repeat
  // background-size 100% 100%
  // background-position center
  background linear-gradient(158.72deg, #585450 -44.7%, #1E1F21 91.57%)
  border-radius 20px
  color rgba(255, 255, 255, 1)
  display flex
  flex-direction column
  align-items center
  &-close
    width 90px
    height 90px
    position absolute
    top 30px
    left 30px
  &-title
    margin-bottom 21px
    font-size 50px
    height 60px
    display flex
    flex-direction row
    align-items center
    justify-content center
    span
      height 60px
      font-weight 500
      &:nth-child(1)
        width 250px
        margin-right 40px
      &:nth-child(2)
        width 200px
      &:nth-child(4)
        width 55px
    img
      width 88px
      height 66px
      margin 0 10px
  &-p
    width 670px
    height 39px
    font-size 28px
    font-weight 400
    text-align center
    line-height 39px
    letter-spacing 6px
    color rgba(255, 255, 255, 0.5)
    span
      letter-spacing 6px
      margin-right 32px
  &-content
    margin 43px 22px
    font-size 28px
    height 233px
    display flex
    flex-direction row
    align-items flex-end
    justify-content center
    span
      height 60px
      color #FFBF5E
      height 39px
      line-height 39px
      font-weight 400
      margin-bottom 37px
      &:nth-child(1)
        width 120px
      &:nth-child(3)
        width 30px
    img
      width 337px
      height 233px
      margin 0 -15px 0 10px
  &-bottom
    width 100%
    height 193px
    display flex
    justify-content center
    position absolute
    bottom 40px
    left 0
  &-code
    width 162px
    height 193px
    background #fff
    border-radius 8px
    display flex
    flex-direction column
    align-items center
    img
      width 150px
      height 150px
      margin-top 6px
    span
      height 21px
      line-height 21px
      font-weight 400
      font-size 15px
      color rgba(0, 0, 0, 0.8)
      margin-top 8px
</style>