import { createComponent } from '@/components/modal/utils/index.js';
import ActivityModal from './index.vue';
import ActivityModalAlert from './alert.vue';
import ActivityMicModal from './mic.vue';
import ActivityVipModal from './vip.vue';
import VipExpireModal from './vipExpire.vue';
import ActivityRecommendModal from './recommendSong.vue';
// import ActivitySignInModal from './signIn.vue';
// import ActivityZeroBuyVipModal from './zerobuyvip.vue';
import ActivityKSongModal from './ksong.vue';

const creatCommonUseModal = (component, _forceProps = {}) => {

  return function (globalProps = {}, globalSlots = {}) {

    return {
      show(props = globalProps, slots = globalSlots) {
        const forceProps = {
          programmatic: true,
          lockScroll: true,
          isFullPage: true,
          active: true,
          canCancel: false,
          ..._forceProps,
        }
  
        const propsData = Object.assign({}, globalProps, props, forceProps);
        const container = document.body;
  
        const mergedSlots = Object.assign({}, globalSlots, slots);
        const instance = createComponent(component, propsData, container, mergedSlots);
        return {
          hide: instance.refs.root.hide
        }
      },
    }
  }
}

const useActivity = creatCommonUseModal(ActivityModal)

const useActivityAlert = creatCommonUseModal(ActivityModalAlert)

const useActivityMic = creatCommonUseModal(ActivityMicModal)

const useActivityVip = creatCommonUseModal(ActivityVipModal)

const useVipExpire = creatCommonUseModal(VipExpireModal, { canCancel: true })

const useActivityRecommendSong = creatCommonUseModal(ActivityRecommendModal)

// const useActivitySignIn = creatCommonUseModal(ActivitySignInModal)

// const useZeroBuyVip = creatCommonUseModal(ActivityZeroBuyVipModal)

const useActivityKSong = creatCommonUseModal(ActivityKSongModal)

export default useActivity

export {
  useActivityAlert,
  useActivityMic,
  useActivityVip,
  useVipExpire,
  useActivityRecommendSong,
  // useActivitySignIn,
  // useZeroBuyVip,
  useActivityKSong,
}

