import { createComponent } from '@/components/modal/utils/index.js'
import modalAI from './index.vue'

export default function useAIModal(globalProps = {}, globalSlots = {}) {
  function show(Component, props = globalProps, slots = globalSlots) {
    const forceProps = {
      programmatic: true,
      lockScroll: true,
      isFullPage: false,
      active: true,
    }

    const propsData = { ...globalProps, ...props, ...forceProps }
    const container = document.body

    const mergedSlots = { ...globalSlots, ...slots }
    const instance = createComponent(
      Component,
      propsData,
      container,
      mergedSlots
    )

    return {
      hide: instance.refs.root.hide,
    }
  }
  return {
    show(props = globalProps, slots = globalSlots) {
      return show(modalAI, props, slots)
    },
  }
}
