import { createComponent } from '@/components/modal/utils/index.js';
import LoginModalVue from "./index.vue";
import ForceLoginVue from "./forceLogin.vue";

export default function useLoginQrcode(globalProps = {}, globalSlots = {}) {

  return {
    show(props = globalProps, slots = globalSlots) {
      const forceProps = {
        programmatic: true,
        lockScroll: true,
        isFullPage: true,
        active: true,
      }

      const propsData = Object.assign({}, globalProps, props, forceProps);
      const container = document.body;

      const mergedSlots = Object.assign({}, globalSlots, slots);
      const instance = createComponent(LoginModalVue, propsData, container, mergedSlots);
      return {
        hide: instance.refs.root.hide
      }
    },
  }
}

export function useForceLoginModal(globalProps = {}, globalSlots = {}) {
  return {
    show(props = globalProps, slots = globalSlots) {
      const forceProps = {
        programmatic: true,
        lockScroll: true,
        isFullPage: true,
        active: true,
        canCancel: false,
      }

      const propsData = Object.assign({}, globalProps, props, forceProps);
      const container = document.body;

      const mergedSlots = Object.assign({}, globalSlots, slots);
      const instance = createComponent(ForceLoginVue, propsData, container, mergedSlots);
      return {
        hide: instance.refs.root.hide
      }
    },
  }
}