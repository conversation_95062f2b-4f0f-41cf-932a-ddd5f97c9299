<template>
  <CommonModal
    ref="root"
  >
    <div
      class="login-modal-content"
    >
      <div class="background">
        <img :src="bgImg1" alt="">

        <!-- <img v-show="themeClass === 'themeLight'" :src="bgImg1" alt="">
        <img v-show="themeClass === 'themeDark'" :src="bgImg2" alt=""> -->
      </div>
      <div @click="handleCloseLogin" class="close"></div>
      <div class="qrcode">
        <img v-if="!needReload" :src="qrCodeURL" />
        <div v-else class="net-error" @click.stop="getLoginQrcode('reload')">
          <svg :class="isRequest && 'active'" width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g opacity="0.4" clip-path="url(#clip0_1270_101510)">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M50.9798 10.5088C49.0366 10.1743 47.0387 10 45 10C25.67 10 10 25.67 10 45C10 56.1164 15.1825 66.0224 23.2632 72.4337L27.1902 70.3766C19.2129 64.7677 14 55.4926 14 45C14 27.8792 27.8792 14 45 14C45.4133 14 45.8247 14.0081 46.2341 14.0241L50.9798 10.5088ZM62.6381 19.5035C70.7122 25.0996 76 34.4323 76 45C76 62.1208 62.1208 76 45 76C44.4595 76 43.9222 75.9862 43.3885 75.9588L38.697 79.434C40.7416 79.8058 42.8481 80 45 80C64.33 80 80 64.33 80 45C80 33.8105 74.7491 23.8474 66.577 17.4403L62.6381 19.5035Z" fill="black" style="fill:black;fill-opacity:1;"/>
            <path d="M45.0424 80L48.3127 72.3843C48.3712 72.2479 48.4006 72.1796 48.4134 72.1425C48.7162 71.2627 47.8202 70.4473 46.9728 70.8315C46.9371 70.8477 46.8719 70.8833 46.7416 70.9545L37.0398 76.2512C36.3988 76.6012 36 77.2732 36 78.0035C36 78.5837 36.5272 78.9725 37.0924 79.103C39.6336 79.6899 42.2806 80 45 80C45.0141 80 45.0283 80 45.0424 80Z" fill="black" style="fill:black;fill-opacity:1;"/>
            <path d="M44.5523 10.0028L41.2832 17.6158C41.2246 17.7522 41.1953 17.8204 41.1825 17.8575C40.8796 18.7373 41.7757 19.5527 42.623 19.1685C42.6588 19.1523 42.7239 19.1167 42.8543 19.0455L52.5561 13.7488C53.1971 13.3988 53.5959 12.7268 53.5959 11.9965C53.5959 11.3276 52.9615 10.9029 52.3073 10.7639C49.9505 10.2634 47.506 10 45 10C44.8505 10 44.7013 10.0009 44.5523 10.0028Z" fill="black" style="fill:black;fill-opacity:1;"/>
            </g>
            <defs>
            <clipPath id="clip0_1270_101510">
            <rect width="90" height="90" fill="white" style="fill:white;fill-opacity:1;"/>
            </clipPath>
            </defs>
          </svg>
          <p>网络异常</p>
          <p>点击刷新二维码</p>
        </div>
      </div>
      
    </div>
  </CommonModal>
</template>

<script setup>
import CommonModal from '@/components/modal/common/component.vue'
import useQRCode from '@/composables/useQRCode'
import { vipLogFrom } from '@/constants/index'
import { sendLog } from '@/directives/v-log/log'
import { getCarplayInfo } from '@/service/carplay-info'
import store from '@/store'
import { withTimeoutHandling } from '@/utils/promiseUtils'
import { computed, defineProps, onBeforeMount, ref, watch } from 'vue'

const props = defineProps({
  songid: {
    type: Number,
    default: 0
  },
  log: {
    type: String,
    default: ''
  },
})

const { getQRCodeURL } = useQRCode()

const root = ref(null)
const qrCodeURL = ref('')
const needReload = ref(false)
const isRequest = ref(false)
const net_status = computed(() => store.state.base.net_status);
const themeClass = computed(() => store.state.themeClass)
const carplayInfo = computed(() => store.state.carplayInfo);

const bgImg1 = ref(localStorage.getItem('https://qncweb.ktvsky.com/20250319/vadd/2ae9b39a614ea511d068f9c92ba55d0e.png') || 'https://qncweb.ktvsky.com/20250319/vadd/2ae9b39a614ea511d068f9c92ba55d0e.png');

// const bgImg1 = ref(localStorage.getItem('https://qncweb.ktvsky.com/20240222/other/832cf5435e0716312f21e1e2a0206dc5.png') || 'https://qncweb.ktvsky.com/20240222/other/832cf5435e0716312f21e1e2a0206dc5.png');
// const bgImg2 = ref(localStorage.getItem('https://qncweb.ktvsky.com/20231219/other/8ca99b075df0166fdac8a96c0c41fb43.png') || 'https://qncweb.ktvsky.com/20231219/other/8ca99b075df0166fdac8a96c0c41fb43.png');
const isLogin = computed(() => !!store.state.userInfo.unionid)
const getLoginQrcode = async (payload) => {
  sendLog({
    event_type: '10000~50000',
    event_name: 6012,
    event_data: {
      str1: '通用',
      str2: '扫码登录弹窗',
      str3: '弹窗展示',
      str4: 'show'
    },
  })

  console.log('请求登录二维码，当前状态:', { isRequest: isRequest.value, net_status: net_status.value });
  
  // 检查请求状态和网络状态
  if (isRequest.value || !net_status.value && payload !== 'reload') return;

  isRequest.value = true;

  try {
    // 获取支付二维码
    const pay_qr = payload === 'reload' 
      ? (await getCarplayInfo(true)).data.pay_qr 
      : carplayInfo.value.pay_qr || (await withTimeoutHandling(getCarplayInfo())).data.pay_qr;

    // 处理二维码数据
    if (pay_qr) {
      needReload.value = false;
      const qrCodeData = await withTimeoutHandling(getQRCodeURL(`${pay_qr}&songid=${props.songid || ''}&log=${vipLogFrom.get(props.log || '其他')}`));
      if (qrCodeData) {
        qrCodeURL.value = qrCodeData;
      }
    } else {
      needReload.value = true;
    }
  } catch (error) {
    console.error('获取登录二维码失败:', error);
    needReload.value = true;
  } finally {
    isRequest.value = false;
  }
}

const handleCloseLogin = () => {
  root.value.hide()
  sendLog({
    event_type: '10000~50000',
    event_name: 10098,
    event_data: {
      str1: '任意页',
      str2: '登录弹窗',
      str3: '关闭弹窗',
      str4: 'click',
    },
  })

  sendLog({
    event_type: '10000~50000',
    event_name: 6012,
    event_data: {
      str1: '通用',
      str2: '扫码登录弹窗',
      str3: '弹窗关闭',
      str4: 'click',
      str5: 1
    },
  })
}

watch(isLogin,(val)=>{
  if(val){
    if (root.value) {
      root.value.hide()
    }
    
    sendLog({
      event_type: '10000~50000',
      event_name: 6012,
      event_data: {
        str1: '通用',
        str2: '扫码登录弹窗',
        str3: '弹窗关闭',
        str4: 'click',
        str5: 2
      },
    })
  }
})

onBeforeMount(getLoginQrcode)

watch(net_status, async(val) => {
  console.log('net_status', val)
  if (val) {
    getLoginQrcode()
  } else {
    needReload.value = true
  }
}, {
  immediate: true,
  deep: true
})

</script>

<style lang="stylus" scoped>
.login-modal
  &-content
    position relative
    width 1000px
    height 100%
    border-radius 20px
    display flex
    flex-direction column
    align-items center
    overflow hidden
    .background
      width 100%
      height 100%
      img
        width 100%
    @media screen and (max-width: 1950px) and (min-width: 1700px) and (max-height: 720px) and (min-height: 650px)
      zoom 0.7
    @media screen and (max-width 1200px)
      zoom 0.8
    .close
      position absolute
      top 27px
      right 10px
      left unset!important
      width 100px
      height 70px
    .qrcode
      position absolute
      bottom 90px
      left 50%
      margin-left -116px
      width 230px
      height 230px
      display flex
      justify-content center
      align-items center
      background #ffffff
      border-radius 20px
      img
        width 214px
        height 214px
</style>
