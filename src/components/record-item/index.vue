<template>
  <div class="recording-item" @click="tooglePlayRecording">
    <div class="left">
      <div class="song-info">
        <div class="song-name">
          {{ recording.music_name }}
        </div>
        <p>
          <span
            class="artist"
            :class="recording.singerid > 0 && 'clickable'"
            @click.stop="handleClickSinger"
          >{{ recording.singer }}</span>
          <span
            v-if="recording.score"
            :class="['score', recording.level]">
            {{ recording.level }} 
            {{ recording.score }}
          </span>
          <span class="record-time">录音时间：{{ recording.record_time }}</span>
        </p>
      </div>
    </div>
    <div class="right">
      <img v-show="isPlaying" class="icon" :src="imgs[themeClass].pause" alt="暂停" @click.stop="pauseRecording" />
      <img v-show="!isPlaying" class="icon" :src="imgs[themeClass].play" alt="播放" @click.stop="playRecording" />
      <img class="icon" :src="imgs[themeClass].share" alt="分享" @click.stop="shareRecording" />
      <img class="icon" :src="imgs[themeClass].delete" alt="删除录音" @click.stop="handleClickDelete" />
    </div>
  </div>
</template>

<script>
import { ref, toRefs, computed } from 'vue';
import eventBus from '@/utils/event-bus'
import debounce from 'lodash/debounce';
import { Dialog } from 'vant';
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import split from 'lodash/split'
import { sendLog } from '@/directives/v-log/log'

export default {
  props: {
    recording: {
      type: Object,
      required: true
    },
    isPlaying: {
      type: Boolean,
    }
  },
  setup(props) {
    const store = useStore()
    const router = useRouter()
    
    const { recording, isPlaying } = toRefs(props)
    const audioPlayer = ref(null)

    const imgs = {
      themeDark: {
        pause: 'https://qncweb.ktvsky.com/20240202/other/383da1c4383e16b18aa36414b8888dc8.png',
        play: 'https://qncweb.ktvsky.com/20240202/other/155c208865f2c907a12a9678386fcf61.png',
        share: 'https://qncweb.ktvsky.com/20240202/other/30ad1e0ee96905133a79561cc1ecadda.png',
        delete: 'https://qncweb.ktvsky.com/20240202/other/3e6eaf6b5ba7c0d869a265afb648311b.png',
      },
      themeLight: {
        pause: 'https://qncweb.ktvsky.com/20240311/other/d36784073629b7c44f1875c7b2bfc485.png',
        play: 'https://qncweb.ktvsky.com/20240311/other/445a56b6432ea4f3da6c752e3fc29482.png',
        share: 'https://qncweb.ktvsky.com/20240311/other/fe7bf2f23b2c6fe44e687834e5a2c44d.png',
        delete: 'https://qncweb.ktvsky.com/20240311/other/e35bb8a3d953096a4151158dc4818622.png',
      },
      themeSystem: {
        pause: 'https://qncweb.ktvsky.com/20240202/other/383da1c4383e16b18aa36414b8888dc8.png',
        play: 'https://qncweb.ktvsky.com/20240202/other/155c208865f2c907a12a9678386fcf61.png',
        share: 'https://qncweb.ktvsky.com/20240202/other/30ad1e0ee96905133a79561cc1ecadda.png',
        delete: 'https://qncweb.ktvsky.com/20240202/other/3e6eaf6b5ba7c0d869a265afb648311b.png',
      },
    }

    const themeClass = computed(() => store.state.themeClass)

    const tooglePlayRecording = debounce(() => {
      if (isPlaying.value) {
        eventBus.emit('handle-pause-record', recording.value)
      } else {
        eventBus.emit('handle-play-record', recording.value)
      }
      isPlaying.value = !isPlaying.value;
    }, 300, { immediate: true });

    const pauseRecording = debounce(() => {
      isPlaying.value = false;
      eventBus.emit('handle-pause-record', recording.value)
    }, 300, { immediate: true });

    const playRecording = debounce(async () => {
      isPlaying.value = true;
      eventBus.emit('handle-play-record', recording.value)
    }, 300, { immediate: true });

    const shareRecording = debounce(() => {
      // 处理分享录音的逻辑
      eventBus.emit('handle-share-record', recording.value) 
    }, 300, { immediate: true });

    const handleClickDelete = debounce(() => {
      // 处理删除录音的逻辑
      Dialog.confirm({
        title: '删除作品确认',
        className: 'global-force-login',
        confirmButtonText: '确定删除',
        cancelButtonText: '再想想',
        message: `确定删除《${recording.value.music_name}》吗？删除后将无法找回`,
      }).then(async () => {
        eventBus.emit('handle-delete-record', recording.value) 
        // try {
        //   showLoading()
        //   await deleteRecording({
        //     id: recording.value.id,
        //     unionid: unionid.value
        //   })
        //   hideLoading()
        // } catch (error) {
        //   hideLoading()
        // }
      });
    }, 300);

    const handleClickSinger = () => {
      console.log(recording.value)
      if (recording.value.singerid <=0 || !recording.value.singerid) return
      router.push({
        name: 'songList',
        query: {
          name: split(recording.value.singer, ',')[0],
          image: recording.value.singer_head,
          singerid: recording.value.singerid,
        },
      })
      sendLog({
        event_type: '10000~50000',
        event_name: 10108,
        event_data: {
          str1: '任意点歌页',
          str2: '歌曲列表',
          str3: '点击任意歌手',
          str4: 'click',
        },
      })
    }

    return {
      pauseRecording,
      playRecording,
      shareRecording,
      handleClickDelete,
      audioPlayer,
      tooglePlayRecording,
      handleClickSinger,
      imgs,
      themeClass,
    };
  }
}
</script>

<style lang="stylus">
.recording-item
  display flex
  justify-content space-between
  align-items center
  height 157px
  padding 10px
  color rgba(255, 255, 255, 0.7)
  .clickable
    position relative
    padding-right 26px
    &::after
      content ""
      position absolute
      right 0
      top 50%
      margin-top -10px
      width 22px
      height 22px
      background url('https://qncweb.ktvsky.com/20231212/vadd/70dbe52816b882ae1f6871b3a509f375.png') no-repeat
      background-size 100% 100%
  .divider
    margin -2px 16px 0 16px

  .left
    flex 1
    .song-info
      margin-bottom 5px
    .song-name
      font-size 32px
      margin-bottom 10px
    p
      color rgba(255, 255, 255, 0.4)
      display flex
      align-items center
      .record-time
        margin-left 12px
      .score
        height 28px
        border-radius 30px
        text-align center
        padding 0 12px
        color rgba(118, 113, 105, 1)
        border: 2px solid rgba(118, 113, 105, 1)
        margin-left 10px
        font-size 18px
        &.S
          color rgba(226, 132, 45, 1)
          border-color rgba(226, 132, 45, 1)
        &.A
          color rgba(226, 170, 85, 1)
          border-color rgba(226, 170, 85, 1)
        &.B
          color rgba(206, 175, 130, 1)
          border-color rgba(206, 175, 130, 1)
  .right
    display flex
    align-items center
    .icon
      width 40px
      height 40px
      margin-left 100px
      cursor pointer
.theme-themeLight
  .recording-item
    color #1D1D1FE5
  .song-info
    p
      color #1D1D1F80
</style>
