<template>
  <div class="search-bar">
    <div class="search-bar-left">
      <template v-if="isShowBack">
        <div class="back" @click="handleBack">
          <svg v-if="themeClass === 'themeLight'" class="back-icon" width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M19.6759 3.70701L20.383 2.99991L18.9688 1.58569L18.2617 2.2928L1.29109 19.2634L0.583984 19.9705L1.29109 20.6776L18.2617 37.6481L18.9688 38.3552L20.383 36.941L19.6759 36.2339L4.44185 20.9999H37V18.9999H4.38297L19.6759 3.70701Z" fill="#1D1D1F" style="fill:#1D1D1F;fill:color(display-p3 0.1137 0.1137 0.1216);fill-opacity:1;"/>
          </svg>

          <svg v-else class="back-icon" width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M19.6759 3.70714L20.383 3.00003L18.9688 1.58582L18.2617 2.29292L1.29109 19.2635L0.583984 19.9706L1.29109 20.6777L18.2617 37.6483L18.9688 38.3554L20.383 36.9412L19.6759 36.234L4.44185 21H37V19H4.38297L19.6759 3.70714Z" fill="white" style="fill:white;fill-opacity:1;"/>
          </svg>
        </div>
        <span v-if="title" class="title">{{ title }}</span>
      </template>

      <Logo v-else />
      <!-- <button @click="mockHighFrequencyReport">模拟高频上报</button> -->
      <div v-show="isShowNotify" class="notify-wrapper">
        <div @click="handleToSearch"
          class="search-bar-tool"
          :class="{
            'search-input': isSearch
          }"
        >
          <svg v-if="themeClass === 'themeLight'" class="search-bar-tool-icon" width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M26.5865 24.464L20.6502 18.5252C22.4301 16.2058 23.2611 13.2962 22.9746 10.3866C22.6881 7.47708 21.3056 4.78543 19.1075 2.85771C16.9095 0.929981 14.0604 -0.0894648 11.1383 0.00616771C8.21628 0.1018 5.43999 1.30535 3.37267 3.37267C1.30535 5.43999 0.1018 8.21628 0.00616771 11.1383C-0.0894648 14.0604 0.929981 16.9095 2.85771 19.1075C4.78543 21.3056 7.47708 22.6881 10.3866 22.9746C13.2962 23.2611 16.2058 22.4301 18.5252 20.6502L24.4665 26.5927C24.606 26.7322 24.7716 26.8429 24.9539 26.9184C25.1363 26.994 25.3316 27.0328 25.529 27.0328C25.7263 27.0328 25.9217 26.994 26.104 26.9184C26.2863 26.8429 26.4519 26.7322 26.5915 26.5927C26.731 26.4532 26.8417 26.2875 26.9172 26.1052C26.9927 25.9229 27.0316 25.7275 27.0316 25.5302C27.0316 25.3329 26.9927 25.1375 26.9172 24.9552C26.8417 24.7729 26.731 24.6072 26.5915 24.4677L26.5865 24.464ZM3.02522 11.5252C3.02522 9.84408 3.52374 8.20069 4.45773 6.80287C5.39172 5.40505 6.71924 4.31559 8.27241 3.67224C9.82558 3.0289 11.5346 2.86057 13.1835 3.18854C14.8323 3.51652 16.3469 4.32607 17.5356 5.51481C18.7244 6.70356 19.5339 8.21811 19.8619 9.86695C20.1899 11.5158 20.0215 13.2249 19.3782 14.778C18.7348 16.3312 17.6454 17.6587 16.2476 18.5927C14.8497 19.5267 13.2064 20.0252 11.5252 20.0252C9.27159 20.0229 7.11093 19.1266 5.51737 17.5331C3.92381 15.9395 3.02754 13.7788 3.02522 11.5252Z" fill="#1D1D1F" fill-opacity="0.5" style="fill:#1D1D1F;fill:color(display-p3 0.1137 0.1137 0.1216);fill-opacity:0.5;"/>
          </svg>

          <svg v-else class="search-bar-tool-icon" width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path opacity="0.5" d="M26.5865 24.464L20.6502 18.5252C22.4301 16.2058 23.2611 13.2962 22.9746 10.3866C22.6881 7.47708 21.3056 4.78543 19.1075 2.85771C16.9095 0.929981 14.0604 -0.0894648 11.1383 0.00616771C8.21628 0.1018 5.43999 1.30535 3.37267 3.37267C1.30535 5.43999 0.1018 8.21628 0.00616771 11.1383C-0.0894648 14.0604 0.929981 16.9095 2.85771 19.1075C4.78543 21.3056 7.47708 22.6881 10.3866 22.9746C13.2962 23.2611 16.2058 22.4301 18.5252 20.6502L24.4665 26.5927C24.606 26.7322 24.7716 26.8429 24.9539 26.9184C25.1363 26.994 25.3316 27.0328 25.529 27.0328C25.7263 27.0328 25.9217 26.994 26.104 26.9184C26.2863 26.8429 26.4519 26.7322 26.5915 26.5927C26.731 26.4532 26.8417 26.2875 26.9172 26.1052C26.9927 25.9229 27.0316 25.7275 27.0316 25.5302C27.0316 25.3329 26.9927 25.1375 26.9172 24.9552C26.8417 24.7729 26.731 24.6072 26.5915 24.4677L26.5865 24.464ZM3.02522 11.5252C3.02522 9.84408 3.52374 8.20069 4.45773 6.80287C5.39172 5.40505 6.71924 4.31559 8.27241 3.67224C9.82558 3.0289 11.5346 2.86057 13.1835 3.18854C14.8323 3.51652 16.3469 4.32607 17.5356 5.51481C18.7244 6.70356 19.5339 8.21811 19.8619 9.86695C20.1899 11.5158 20.0215 13.2249 19.3782 14.778C18.7348 16.3312 17.6454 17.6587 16.2476 18.5927C14.8497 19.5267 13.2064 20.0252 11.5252 20.0252C9.27159 20.0229 7.11093 19.1266 5.51737 17.5331C3.92381 15.9395 3.02754 13.7788 3.02522 11.5252Z" fill="white" style="fill:white;fill-opacity:1;"/>
          </svg>
          <!-- <div class="search-bar-tool-input">
            {{placeholder}}
          </div> -->
          <input
            :style="{
              'z-index': isSearch ? 2 : 'initial',
              'pointer-events': isSearch ? 'auto' : 'none'
            }"
            type="text"
            :placeholder="placeholder"
            v-model="keyword"
            class="search-bar-tool-input"
            :disabled="!isSearch"
            v-focus
            ref="searchInputDom"
            maxlength="20"
            autocapitalize="off"
            style="-webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none;"
          />

        </div>
        <Notify v-if="!isLogin" />
      </div>

      <div
        v-show="!isShowNotify && !!placeholder"
        class="search-bar-tool"
        :class="{
          'search-input': isSearch
        }"
        @click="handleClickInput"
      >
        <svg v-if="themeClass === 'themeLight'" class="search-bar-tool-icon" width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M26.5865 24.464L20.6502 18.5252C22.4301 16.2058 23.2611 13.2962 22.9746 10.3866C22.6881 7.47708 21.3056 4.78543 19.1075 2.85771C16.9095 0.929981 14.0604 -0.0894648 11.1383 0.00616771C8.21628 0.1018 5.43999 1.30535 3.37267 3.37267C1.30535 5.43999 0.1018 8.21628 0.00616771 11.1383C-0.0894648 14.0604 0.929981 16.9095 2.85771 19.1075C4.78543 21.3056 7.47708 22.6881 10.3866 22.9746C13.2962 23.2611 16.2058 22.4301 18.5252 20.6502L24.4665 26.5927C24.606 26.7322 24.7716 26.8429 24.9539 26.9184C25.1363 26.994 25.3316 27.0328 25.529 27.0328C25.7263 27.0328 25.9217 26.994 26.104 26.9184C26.2863 26.8429 26.4519 26.7322 26.5915 26.5927C26.731 26.4532 26.8417 26.2875 26.9172 26.1052C26.9927 25.9229 27.0316 25.7275 27.0316 25.5302C27.0316 25.3329 26.9927 25.1375 26.9172 24.9552C26.8417 24.7729 26.731 24.6072 26.5915 24.4677L26.5865 24.464ZM3.02522 11.5252C3.02522 9.84408 3.52374 8.20069 4.45773 6.80287C5.39172 5.40505 6.71924 4.31559 8.27241 3.67224C9.82558 3.0289 11.5346 2.86057 13.1835 3.18854C14.8323 3.51652 16.3469 4.32607 17.5356 5.51481C18.7244 6.70356 19.5339 8.21811 19.8619 9.86695C20.1899 11.5158 20.0215 13.2249 19.3782 14.778C18.7348 16.3312 17.6454 17.6587 16.2476 18.5927C14.8497 19.5267 13.2064 20.0252 11.5252 20.0252C9.27159 20.0229 7.11093 19.1266 5.51737 17.5331C3.92381 15.9395 3.02754 13.7788 3.02522 11.5252Z" fill="#1D1D1F" fill-opacity="0.5" style="fill:#1D1D1F;fill:color(display-p3 0.1137 0.1137 0.1216);fill-opacity:0.5;"/>
        </svg>

        <svg v-else class="search-bar-tool-icon" width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path opacity="0.5" d="M26.5865 24.464L20.6502 18.5252C22.4301 16.2058 23.2611 13.2962 22.9746 10.3866C22.6881 7.47708 21.3056 4.78543 19.1075 2.85771C16.9095 0.929981 14.0604 -0.0894648 11.1383 0.00616771C8.21628 0.1018 5.43999 1.30535 3.37267 3.37267C1.30535 5.43999 0.1018 8.21628 0.00616771 11.1383C-0.0894648 14.0604 0.929981 16.9095 2.85771 19.1075C4.78543 21.3056 7.47708 22.6881 10.3866 22.9746C13.2962 23.2611 16.2058 22.4301 18.5252 20.6502L24.4665 26.5927C24.606 26.7322 24.7716 26.8429 24.9539 26.9184C25.1363 26.994 25.3316 27.0328 25.529 27.0328C25.7263 27.0328 25.9217 26.994 26.104 26.9184C26.2863 26.8429 26.4519 26.7322 26.5915 26.5927C26.731 26.4532 26.8417 26.2875 26.9172 26.1052C26.9927 25.9229 27.0316 25.7275 27.0316 25.5302C27.0316 25.3329 26.9927 25.1375 26.9172 24.9552C26.8417 24.7729 26.731 24.6072 26.5915 24.4677L26.5865 24.464ZM3.02522 11.5252C3.02522 9.84408 3.52374 8.20069 4.45773 6.80287C5.39172 5.40505 6.71924 4.31559 8.27241 3.67224C9.82558 3.0289 11.5346 2.86057 13.1835 3.18854C14.8323 3.51652 16.3469 4.32607 17.5356 5.51481C18.7244 6.70356 19.5339 8.21811 19.8619 9.86695C20.1899 11.5158 20.0215 13.2249 19.3782 14.778C18.7348 16.3312 17.6454 17.6587 16.2476 18.5927C14.8497 19.5267 13.2064 20.0252 11.5252 20.0252C9.27159 20.0229 7.11093 19.1266 5.51737 17.5331C3.92381 15.9395 3.02754 13.7788 3.02522 11.5252Z" fill="white" style="fill:white;fill-opacity:1;"/>
        </svg>
          
        <input
          :style="{
            'z-index': isSearch ? 2 : 'initial'
          }"
          type="text"
          :placeholder="placeholder"
          v-model="keyword"
          class="search-bar-tool-input"
          :disabled="!isSearch"
          v-focus
          ref="searchInputDom"
          @mousedown="disableDoubleClickSelection"
          @keydown="handleSearchKeydown($event)"
          maxlength="20"
          autocapitalize="off"
          style="-webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none;"
        />

        <div
          v-show="keyword"
          class="clear"
          ref="clearButton"
        >
          <img v-show="themeClass === 'themeLight'" src="@/assets/close.png" />
          <img v-show="themeClass !== 'themeLight'" src="@/assets/close_dark.png" />
        </div>
        <div v-show="!isSearch" class="search-bar-tool-mask"></div>
      </div>
      <div class="search-btn" @click="handleSearch" v-if="isSearch">搜索</div>

      <!-- <button @click="handleExit">退出应用</button> -->
      <!-- <button @click="handleToggleTheme">切换皮肤</button> -->
    </div>
    <div class="search-bar-right">
      <div
        v-if="isShowVipActEnter && !isVipUser"
        class="operate-position"
        :class="[
          route_page === 'search' && 'operate-position-search',
          route_page === 'singer' && 'operate-position-singer'
        ]"
        @click="handleShowVip"
      ></div>

      <div
        v-if="micMallInfo && !!isShowMic"
        class="mic"
      >
        <div class="mic-main" @click="handleClickMic">
          <svg v-if="themeClass === 'themeLight'" width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clip-path="url(#clip0_1256_35812)">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M20.5853 20.4142C24.1 23.9289 29.7985 23.9289 33.3132 20.4142C36.8279 16.8995 36.8279 11.201 33.3132 7.68628C29.7985 4.17157 24.1 4.17157 20.5853 7.68628C17.0705 11.201 17.0705 16.8995 20.5853 20.4142ZM19.171 21.8284C20.1066 22.764 21.1687 23.4957 22.3023 24.0238L9.83722 33.4652L7.53411 31.1621L16.9756 18.697C17.5036 19.8307 18.2355 20.8928 19.171 21.8284ZM34.7274 21.8284C31.9769 24.5789 28.1321 25.5683 24.5944 24.7966L10.3501 35.5857L9.65543 36.1119L9.03921 35.4957L5.50368 31.9601L4.88747 31.3439L5.41364 30.6492L16.2028 16.4049C15.4312 12.8672 16.4206 9.02254 19.171 6.27207C23.4668 1.9763 30.4316 1.9763 34.7274 6.27207C39.0232 10.5678 39.0232 17.5327 34.7274 21.8284ZM21.9995 11.929C21.0283 11.6907 21.0282 11.6911 21.0281 11.6915L21.0279 11.6924L21.0274 11.6943L21.0263 11.6992L21.0231 11.7127C21.0206 11.7233 21.0175 11.7372 21.0137 11.7543C21.0062 11.7885 20.9965 11.8354 20.9854 11.8939C20.9633 12.0106 20.9359 12.1742 20.9114 12.3755C20.8626 12.7764 20.824 13.3359 20.8649 13.9774C20.9462 15.2505 21.3488 16.9351 22.7066 18.2929C24.0644 19.6507 25.749 20.0533 27.0222 20.1346C27.6636 20.1755 28.2231 20.1369 28.624 20.0881C28.8253 20.0636 28.9889 20.0362 29.1057 20.0141C29.1641 20.0031 29.211 19.9933 29.2452 19.9858C29.2623 19.9821 29.2762 19.9789 29.2869 19.9764L29.3004 19.9733L29.3052 19.9721L29.3072 19.9716L29.308 19.9714C29.3084 19.9713 29.3088 19.9712 29.0706 19C28.8324 18.0288 28.8327 18.0287 28.8331 18.0286L28.8337 18.0285L28.8346 18.0283L28.8183 18.0319C28.8015 18.0356 28.773 18.0416 28.7339 18.049C28.6555 18.0638 28.5354 18.0841 28.3822 18.1028C28.0739 18.1403 27.6411 18.17 27.1496 18.1387C26.1567 18.0753 25.0129 17.7708 24.1208 16.8787C23.2288 15.9866 22.9243 14.8428 22.8609 13.85C22.8295 13.3584 22.8592 12.9256 22.8968 12.6174C22.9154 12.4641 22.9357 12.344 22.9506 12.2656C22.958 12.2265 22.964 12.198 22.9676 12.1812L22.9713 12.1649C22.9714 12.1643 22.9715 12.1638 22.9716 12.1637L22.9716 12.1637L22.9713 12.1648L22.9713 12.1649L22.971 12.1659L22.9709 12.1665C22.9708 12.1668 22.9707 12.1672 21.9995 11.929Z" fill="#1D1D1F" style="fill:#1D1D1F;fill:color(display-p3 0.1137 0.1137 0.1216);fill-opacity:1;"/>
            </g>
            <defs>
            <clipPath id="clip0_1256_35812">
            <rect width="40" height="40" fill="white" style="fill:white;fill-opacity:1;"/>
            </clipPath>
            </defs>
          </svg>

          <svg v-else width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clip-path="url(#clip0_1016_51347)">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M20.5853 20.4142C24.1 23.9289 29.7985 23.9289 33.3132 20.4142C36.8279 16.8995 36.8279 11.201 33.3132 7.68628C29.7985 4.17157 24.1 4.17157 20.5853 7.68628C17.0705 11.201 17.0705 16.8995 20.5853 20.4142ZM19.171 21.8284C20.1066 22.764 21.1687 23.4957 22.3023 24.0238L9.83722 33.4652L7.53411 31.1621L16.9756 18.697C17.5036 19.8307 18.2355 20.8928 19.171 21.8284ZM34.7274 21.8284C31.9769 24.5789 28.1321 25.5683 24.5944 24.7966L10.3501 35.5857L9.65543 36.1119L9.03921 35.4957L5.50368 31.9601L4.88747 31.3439L5.41364 30.6492L16.2028 16.4049C15.4312 12.8672 16.4206 9.02254 19.171 6.27207C23.4668 1.9763 30.4316 1.9763 34.7274 6.27207C39.0232 10.5678 39.0232 17.5327 34.7274 21.8284ZM21.9995 11.929C21.0283 11.6907 21.0282 11.6911 21.0281 11.6915L21.0279 11.6924L21.0274 11.6943L21.0263 11.6992L21.0231 11.7127C21.0206 11.7233 21.0175 11.7372 21.0137 11.7543C21.0062 11.7885 20.9965 11.8354 20.9854 11.8939C20.9633 12.0106 20.9359 12.1742 20.9114 12.3755C20.8626 12.7764 20.824 13.3359 20.8649 13.9774C20.9462 15.2505 21.3488 16.9351 22.7066 18.2929C24.0644 19.6507 25.749 20.0533 27.0222 20.1346C27.6636 20.1755 28.2231 20.1369 28.624 20.0881C28.8253 20.0636 28.9889 20.0362 29.1057 20.0141C29.1641 20.0031 29.211 19.9933 29.2452 19.9858C29.2623 19.9821 29.2762 19.9789 29.2869 19.9764L29.3004 19.9733L29.3052 19.9721L29.3072 19.9716L29.308 19.9714C29.3084 19.9713 29.3088 19.9712 29.0706 19C28.8324 18.0288 28.8327 18.0287 28.8331 18.0286L28.8337 18.0285L28.8346 18.0283L28.8183 18.0319C28.8015 18.0356 28.773 18.0416 28.7339 18.049C28.6555 18.0638 28.5354 18.0841 28.3822 18.1028C28.0739 18.1403 27.6411 18.17 27.1496 18.1387C26.1567 18.0753 25.0129 17.7708 24.1208 16.8787C23.2288 15.9866 22.9243 14.8428 22.8609 13.85C22.8295 13.3584 22.8592 12.9256 22.8968 12.6174C22.9154 12.4641 22.9357 12.344 22.9506 12.2656C22.958 12.2265 22.964 12.198 22.9676 12.1812L22.9713 12.1649C22.9714 12.1643 22.9715 12.1638 22.9716 12.1637L22.9716 12.1637L22.9713 12.1648L22.9713 12.1649L22.971 12.1659L22.9709 12.1665C22.9708 12.1668 22.9707 12.1672 21.9995 11.929Z" fill="white" style="fill:white;fill-opacity:1;"/>
            </g>
            <defs>
            <clipPath id="clip0_1016_51347">
            <rect width="40" height="40" fill="white" style="fill:white;fill-opacity:1;"/>
            </clipPath>
            </defs>
          </svg>
          <p>麦克风</p>
        </div>
        <div v-if="microphones.length" class="mic-spots">
          <p
            v-for="item in microphones"
            :key="item"
            :class="item.status && 'active'"></p>
        </div>
        <div v-if="showMicInfo" class="mic-info">
          <ul>
            <li
              v-for="(item, index) in microphones"
              :key="item"
              :class="item.status && 'active'"
            >
              <span></span>麦克风 {{index + 1}}
            </li>
          </ul>
          <span class="triangle"></span>
        </div>
      </div>
      
<!--      <button @click="handleClickReload" style="color: #fff">reload</button>-->

      <!-- 已点模块 && 全屏mv模块 改版 -->
      <div
        class="already-order"
        v-if="isShowOrder"
        @click="handleOpenOrderControl"
      >
        <span v-if="orderedSongNum < 100">{{ orderedSongNum }}</span>
        <span class="already-order-span" v-else>99</span>

        <svg v-if="themeClass === 'themeLight'" width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M2 4L22 4V6L2 6L2 4Z" fill="#1D1D1F" style="fill:#1D1D1F;fill:color(display-p3 0.1137 0.1137 0.1216);fill-opacity:1;"/>
          <path d="M2 18L17 18V20L2 20L2 18Z" fill="#1D1D1F" style="fill:#1D1D1F;fill:color(display-p3 0.1137 0.1137 0.1216);fill-opacity:1;"/>
          <path d="M2 32H12V34H2L2 32Z" fill="#1D1D1F" style="fill:#1D1D1F;fill:color(display-p3 0.1137 0.1137 0.1216);fill-opacity:1;"/>
          <circle cx="26.5" cy="27.5" r="7.5" stroke="#1D1D1F" style="stroke:#1D1D1F;stroke:color(display-p3 0.1137 0.1137 0.1216);stroke-opacity:1;" stroke-width="2"/>
          <path d="M34 26V4L38 7.56522" stroke="#1D1D1F" style="stroke:#1D1D1F;stroke:color(display-p3 0.1137 0.1137 0.1216);stroke-opacity:1;" stroke-width="2" stroke-linecap="square"/>
        </svg>

        <svg v-else width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M2 4L22 4V6L2 6L2 4Z" fill="white" style="fill:white;fill-opacity:1;"/>
          <path d="M2 18L17 18V20L2 20L2 18Z" fill="white" style="fill:white;fill-opacity:1;"/>
          <path d="M2 32H12V34H2L2 32Z" fill="white" style="fill:white;fill-opacity:1;"/>
          <circle cx="26.5" cy="27.5" r="7.5" stroke="white" style="stroke:white;stroke-opacity:1;" stroke-width="2"/>
          <path d="M34 26V4L38 7.56522" stroke="white" style="stroke:white;stroke-opacity:1;" stroke-width="2" stroke-linecap="square"/>
        </svg>

        <p>已点</p>
      </div>
      <div
        v-if="isShowMvEnter"
        class="mv-enter"
        @click="handleOpenMv"
      >
        <div :style="{
          opacity: isSinging ? 1 : 0
        }" class="mv-enter-active"></div>
        <svg v-if="themeClass === 'themeLight'" class="mv-enter-icon" width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="20" cy="21" r="5" stroke="#1D1D1F" style="stroke:#1D1D1F;stroke:color(display-p3 0.1137 0.1137 0.1216);stroke-opacity:1;" stroke-width="2"/>
          <rect x="31" y="25" width="2" height="12" fill="#1D1D1F" style="fill:#1D1D1F;fill:color(display-p3 0.1137 0.1137 0.1216);fill-opacity:1;"/>
          <rect x="25" y="29" width="2" height="5" fill="#1D1D1F" style="fill:#1D1D1F;fill:color(display-p3 0.1137 0.1137 0.1216);fill-opacity:1;"/>
          <rect x="36" y="29" width="2" height="5" fill="#1D1D1F" style="fill:#1D1D1F;fill:color(display-p3 0.1137 0.1137 0.1216);fill-opacity:1;"/>
          <rect x="24" y="4" width="2" height="17" fill="#1D1D1F" style="fill:#1D1D1F;fill:color(display-p3 0.1137 0.1137 0.1216);fill-opacity:1;"/>
          <path fill-rule="evenodd" clip-rule="evenodd" d="M3 19.8891C3 11.4693 9.04197 4.46988 17 3.0437V5.0806C10.1609 6.47988 5 12.569 5 19.8891C5 28.2469 11.728 35 20 35C20.7212 35 21.4307 34.9487 22.125 34.8494V36.8676C21.4289 36.955 20.7197 37 20 37C10.6112 37 3 29.3392 3 19.8891ZM28.2335 7.25643C26.9464 6.40346 25.5214 5.74482 24 5.32166V3.25446C25.9032 3.71645 27.6816 4.50074 29.2749 5.54673L28.2335 7.25643ZM32.8092 12.021C34.1987 14.3115 35 17.0047 35 19.8891C35 20.2486 34.9875 20.6052 34.9631 20.9584H36.9673C36.989 20.6048 37 20.2482 37 19.8891C37 16.2601 35.8776 12.895 33.9632 10.1263L32.8092 12.021Z" fill="#1D1D1F" style="fill:#1D1D1F;fill:color(display-p3 0.1137 0.1137 0.1216);fill-opacity:1;"/>
        </svg>

        <svg v-else class="mv-enter-icon" width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="20" cy="21" r="5" stroke="white" style="stroke:white;stroke-opacity:1;" stroke-width="2"/>
          <rect x="31" y="25" width="2" height="12" fill="white" style="fill:white;fill-opacity:1;"/>
          <rect x="25" y="29" width="2" height="5" fill="white" style="fill:white;fill-opacity:1;"/>
          <rect x="36" y="29" width="2" height="5" fill="white" style="fill:white;fill-opacity:1;"/>
          <rect x="24" y="4" width="2" height="17" fill="white" style="fill:white;fill-opacity:1;"/>
          <path fill-rule="evenodd" clip-rule="evenodd" d="M3 19.8889C3 11.4692 9.04197 4.46976 17 3.04358V5.08048C10.1609 6.47976 5 12.5689 5 19.8889C5 28.2468 11.728 34.9999 20 34.9999C20.7212 34.9999 21.4307 34.9485 22.125 34.8493V36.8675C21.4289 36.9549 20.7197 36.9999 20 36.9999C10.6112 36.9999 3 29.339 3 19.8889ZM28.2335 7.2563C26.9464 6.40334 25.5214 5.7447 24 5.32154V3.25434C25.9032 3.71633 27.6816 4.50061 29.2749 5.5466L28.2335 7.2563ZM32.8092 12.0208C34.1987 14.3113 35 17.0046 35 19.8889C35 20.2485 34.9875 20.6051 34.9631 20.9583H36.9673C36.989 20.6046 37 20.2481 37 19.8889C37 16.26 35.8776 12.8949 33.9632 10.1261L32.8092 12.0208Z" fill="white" style="fill:white;fill-opacity:1;"/>
        </svg>
          
        <p>进入欢唱</p>
      </div>
      <div v-show="isShowOrderToast" class="order-tips">
        <span>请先点播歌曲</span>
        <svg width="228" height="99" viewBox="0 0 228 99" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g filter="url(#filter0_b_1205_65928)">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M116.23 0.978178C115.038 -0.326061 112.962 -0.326059 111.77 0.97818L102.637 10.9732C101.501 12.2172 99.8932 12.9259 98.208 12.9259H43.6962C19.5635 12.9259 0 32.1953 0 55.9653C0 79.7354 19.5635 99.0048 43.6962 99.0048H184.304C208.437 99.0048 228 79.7354 228 55.9653C228 32.1953 208.437 12.9259 184.304 12.9259H129.792C128.107 12.9259 126.502 12.2206 125.366 10.9765C122.5 7.84045 117.098 1.92876 116.23 0.978178Z" fill="url(#paint0_linear_1205_65928)" style=""/>
          <path fill-rule="evenodd" clip-rule="evenodd" d="M116.23 0.978178C115.038 -0.326061 112.962 -0.326059 111.77 0.97818L102.637 10.9732C101.501 12.2172 99.8932 12.9259 98.208 12.9259H43.6962C19.5635 12.9259 0 32.1953 0 55.9653C0 79.7354 19.5635 99.0048 43.6962 99.0048H184.304C208.437 99.0048 228 79.7354 228 55.9653C228 32.1953 208.437 12.9259 184.304 12.9259H129.792C128.107 12.9259 126.502 12.2206 125.366 10.9765C122.5 7.84045 117.098 1.92876 116.23 0.978178Z" fill="url(#paint1_linear_1205_65928)" style=""/>
          </g>
          <defs>
          <filter id="filter0_b_1205_65928" x="-100" y="-100" width="428" height="299.005" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
          <feFlood flood-opacity="0" result="BackgroundImageFix"/>
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="50"/>
          <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1205_65928"/>
          <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1205_65928" result="shape"/>
          </filter>
          <linearGradient id="paint0_linear_1205_65928" x1="-2.069e-06" y1="55.9624" x2="228" y2="56.9847" gradientUnits="userSpaceOnUse">
          <stop stop-color="#793EDA" style="stop-color:#793EDA;stop-color:color(display-p3 0.4761 0.2451 0.8530);stop-opacity:1;"/>
          <stop offset="1" stop-color="#673EDA" style="stop-color:#673EDA;stop-color:color(display-p3 0.4031 0.2451 0.8530);stop-opacity:1;"/>
          </linearGradient>
          <linearGradient id="paint1_linear_1205_65928" x1="114" y1="0" x2="114" y2="99.0048" gradientUnits="userSpaceOnUse">
          <stop stop-color="#A07BCF" style="stop-color:#A07BCF;stop-color:color(display-p3 0.6275 0.4820 0.8126);stop-opacity:1;"/>
          <stop offset="1" stop-color="#8B5CED" style="stop-color:#8B5CED;stop-color:color(display-p3 0.5440 0.3626 0.9294);stop-opacity:1;"/>
          </linearGradient>
          </defs>
        </svg>
      </div>
    </div>

    <!-- 免费VIP次数提示 -->
    <FreeVipTips v-if="!isShowBack" />
  </div>
</template>

<script>
import useMicOnline from '@/components/modal/global/mic-online/create.js'
import useDownload from '@/composables/useDownload'
import useLoginValid from '@/composables/useLoginValid'
import useSongItem from '@/composables/useSongItem'
import useVip from '@/composables/useVip'
import { sendLog, sendSongLog } from '@/directives/v-log/log'
import { TSNativeInstance, TSVehicleInstance } from '@/packages/TSJsbridge'
import eventBus from '@/utils/event-bus'
import store2 from 'store2'
import {
  computed,
  inject,
  nextTick,
  onBeforeMount,
  onMounted,
  ref,
  toRefs,
  watch,
} from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'
import Logo from './logo.vue'
import Notify from './notify.vue'
import FreeVipTips from './free-vip-tips.vue'
import Toast from '@/utils/toast'

export default {
  name: 'SearchBar',
  props: {
    placeholder: String,
    isImmediately: Boolean,
    isSearch: Boolean,
    isShowBack: {
      type: Boolean,
      default: true,
    },
    title: {
      type: String,
      default: '',
    },
    isShowOrder: {
      type: Boolean,
      default: true,
    },
    isShowMvEnter: {
      type: Boolean,
      default: true,
    },
    isShowSingTaskEnter: {
      type: Boolean,
      default: false,
    },
    isShowVipActEnter: {
      type: Boolean,
      default: true,
    },
    isShowMic: {
      type: Boolean,
      default: false,
    },
    isShowNotify: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    Logo,
    Notify,
    FreeVipTips,
  },
  methods: {
    handleFocusInput() {
      if (this.$route.query.keyword) return
      const inputDom = this.$refs.searchInputDom
      console.log('handleFocusInput')
      inputDom.focus()
    },
    handleBlurInput() {
      const inputDom = this.$refs.searchInputDom
      inputDom.blur()
    },
  },
  setup(props, { emit }) {
    const withTimeoutHandling = inject('withTimeoutHandling');

    const router = useRouter()
    const route = useRoute()
    const store = useStore()
    const { isShowMic, isSearch } = toRefs(props)
    const { orderSong } = useSongItem()
    const { showLoginQrcode, isLogin } = useLoginValid()
    const { showVipQrcode, isVipUser } = useVip()
    const $useMicOnline = useMicOnline()
    const { getIsLocalSong } = useDownload();
    const micCount = ref(4)
    const micMallInfo = computed(() => store.state.base.mallInfo)
    const showMicInfo = ref(false)
    const mediaVolume = computed(() => store.state.mediaVolume)

    const themeClass = computed(() => store.state.themeClass)

    const clearButton = ref('clearButton');  // 获取按钮的ref

    // 搜索页和歌星页的来源埋点
    const frObj = {
      search: 1845,
      songList: 1847,
    }
    const route_page = computed(() => route.name)

    let videoPlayerHistory = computed(() => store.state.videoPlayerHistory)
    const orderedSongIdMap = computed(() => store.state.orderedSongIdMap)
    const initControlPlay = computed(() => store.state.videoInitAutoPlay)
    const orderedList = computed(() => store.state.orderedList)
    const orderedSongNum = computed(() => store.state.orderedList.length)
    const microphones = computed(() => store.state.microphones)
    const isExpired = computed(() => store.state.vipInfo.expire)
    const videoPaused = computed(() => store.state.videoPaused)
    const isSinging = computed(
      () =>
        orderedSongNum.value && !videoPaused.value
    )
    let keyword = ref('')

    let isShowOrderToast = ref(false)
    let showOrderToastTimer = null
    let micOnlineInstance = ref(null)

    const handleBack = () => {
      store.commit('base/SET_IS_ROUTER_BACK', true)

      // if (route.name === 'search') {
      //   router.replace({ name: 'home' });
      //   return
      // }

      if (window.history.state && window.history.state.back === null) {
        // 如果没有上一页，跳转到首页
        console.log('没有上一页，跳转到首页')
        router.push({ name: 'home' });
      } else {
        console.log('有上一页，返回上一页')
        router.back()
      }
    }

    const showOrderToast = () => {
      isShowOrderToast.value = true
      showOrderToastTimer = setTimeout(() => {
        isShowOrderToast.value = false
      }, 3 * 1000)
    }

    // 只有这里可以进mv页
    const handleOpenMv = async () => {
      console.log('开始处理打开MV');

      try {
        if (!videoPlayerHistory.value.songItem.songid && !orderedSongNum.value) {
          console.log('未点播歌曲且未登录');
          if (isLogin.value) {
            // Toast('请点播歌曲')
            if (isShowOrderToast.value) clearTimeout(showOrderToastTimer);
            showOrderToast();
            console.log('显示点播提示');
          } else {
            showLoginQrcode();
            console.log('显示登录二维码');
          }
          return;
        }

        if ((!isLogin.value || !isVipUser.value)) {
          console.log('未登录或非VIP用户');
          const isLocal = await withTimeoutHandling(getIsLocalSong(orderedList.value[0]));
          if (orderedList.value[0].is_vip && !isLocal) {
            showVipQrcode();
            console.log('显示VIP二维码');
            return;
          }
        }

        // 进入mv使用记忆的多媒体值
        const storageMediaVolume = mediaVolume.value ? mediaVolume.value : store2.get('storageMediaVolume');
        if (typeof storageMediaVolume === 'number' && TSVehicleInstance) {
          store.commit('UPDATE_MV_VIDEO_MEDIA', storageMediaVolume);
          TSVehicleInstance.setMediaVolume(storageMediaVolume);
          console.log('设置多媒体音量:', storageMediaVolume);
        }

        sendLog({
          event_type: '10000~50000',
          event_name: 10066,
          event_data: {
            str1: '首页',
            str2: '进入欢唱',
            str3: '点击进入欢唱',
            str4: 'click',
          },
        });

        sendLog({
          event_type: '10000~50000',
          event_name: 6007,
          event_data: {
            str1: '欢唱页',
            str2: '欢唱页',
            str3: '进入欢唱页',
            str4: 'click',
          },
        });

        sendLog({
          event_type: '10000~50000',
          event_name: 6012,
          event_data: {
            str1: '通用',
            str2: '欢唱页',
            str3: '进入欢唱页',
            str4: 'show',
            str5: route.name || ''
          },
        })
        console.log('发送日志');

        if (
          Object.keys(orderedSongIdMap.value).length &&
          !initControlPlay.value
        ) {
          console.log('恢复播放已点歌曲');
          orderSong(orderedList.value[0], {
            position: 'recovery',
            isPushOrdered: false,
            enabledMvShow: true,
            useFirstSongAutoPlay: true,
          });
          store.commit('UPDATE_IS_SING_STATUS', true);
          store.dispatch('searchTips/updateIsShowSingTips', false); // 关闭tips弹窗
          console.log('更新唱歌状态并关闭提示弹窗');
          return;
        }

        console.log('恢复播放历史歌曲');
        orderSong(videoPlayerHistory.value.songItem, {
          position: 'recovery',
          isPushOrdered: false,
          enabledMvShow: true,
          from: 'searchBar'
        });
        store.commit('UPDATE_IS_SING_STATUS', true);
        store.dispatch('searchTips/updateIsShowSingTips', false); // 关闭tips弹窗
        console.log('更新唱歌状态并关闭提示弹窗');
      } catch (error) {
        console.error('handleOpenMv 错误:', error);
      }

      console.log('处理打开MV结束');
    }
    
    const handleToSearch = () => {
      try {
        console.log('handleToSearch')
        router.push({
          name: 'search',
        })
        sendLog({
          event_type: '10000~50000',
          event_name: 10003,
          event_data: {
            str1: '首页',
            str2: '搜索栏',
            str3: '进入搜索',
            str4: 'click',
          },
        })
        sendLog({
          event_type: '10000~50000',
          event_name: 6001,
          event_data: {
            str1: '首页',
            str2: '搜索栏',
            str3: '搜索栏',
            str4: 'click',
          },
        })
      } catch (error) {
        console.log('handleToSearch error', error)
      }
    }

    const handleClearKeyword = () => {
      keyword.value = ''
    }

    const handleChangeKeyword = (e) => {
      keyword.value = e
    }

    const handleFormSubmit = () => {
      return false
    }

    const handleSearch = () => {
      sendLog({
        event_type: '10000~50000',
        event_name: 6002,
        event_data: {
          str1: '搜索页',
          str2: keyword.value ? '搜索结果页' :'搜索栏',
          // str3: keyword.value ? '搜索button' : '搜索页-搜索',
          str3: '搜索button',
          str4: 'click',
        },
      })

      emit('search', keyword.value)
    }

    const handleSearchKeydown = (e) => {
      if (e.keyCode == 13) {
        handleSearch()
      }
    }

    const handleOpenOrderControl = () => {
      eventBus.emit('show-order-song-control-popup')
      sendLog({
        event_type: '10000~50000',
        event_name: 10005,
        event_data: {
          str1: '首页',
          str2: '已点',
          str3: '打开已点',
          str4: 'click',
        },
      })

      sendLog({
        event_type: '10000~50000',
        event_name: 6012,
        event_data: {
          str1: '通用',
          str2: '顶部已点',
          str3: '已点按钮点击',
          str4: 'click',
        },
      })
    }

    const handleShowVip = () => {
      if(!isLogin.value) {
        // if(route.name == 'home') {
          sendLog({
            event_type: '10000~50000',
            event_name: 1021,
            event_data: {
              str1: route.name == 'home' ? '通用' : '歌星详情页',
              str2: route.name == 'home' ?  '顶部运营位' : '歌星详情页运营位',
              str3: route.name == 'home' ?  '顶部运营位' : '',
              str4: 'click',
              str5: 1,
              str9: '车机端',
              str10: route.name == 'home' ? '1800' : route.name == 'search' ? '1844' :  '1846'
            }
          })
        // }
      }else{
        sendLog({
          event_type: '10000~50000',
          event_name: 1021,
          event_data: {
            str1: route.name == 'search' ? '搜索' : '歌星详情页',
            str2: route.name == 'search' ? '顶部搜索结果运营位' : '歌星详情页运营位',
            str3: '',
            str4: 'show',
            str5: 2,
            str9: '车机端',
            str10:  route.name == 'search' ? '1845' : '1847'
          }
        })
      }

      sendLog({
        event_type: 'custom',
        event_name: 1733,
      })
      showVipQrcode({
        log: frObj[route.name] ? `${route.name}-顶部运营位` : '首页-顶部运营位',
        isLogin: isLogin.value,
        fr: frObj[route.name] ? frObj[route.name] : 1845
      })
      
      sendLog({
        event_type: '10000~50000',
        event_name: 10004,
        event_data: {
          str1: '首页',
          str2: '顶部运营位',
          str3: '点击运营位',
          str4: 'click',
        },
      })

      sendLog({
        event_type: '10000~50000',
        event_name: 6012,
        event_data: {
          str1: '通用',
          str2: '顶部运营位',
          str3: '点击',
          str4: 'click',
          str5: isVipUser.value ? 2 : isExpired.value ? 4 : 1
        }
      })
    }

    const handleClickMic = () => {
      sendLog({
        event_type: '10000~50000',
        event_name: 6008,
        event_data: {
          str1: '我的页',
          str2: '麦克风',
          str3: '点击',
          str4: 'click',
          str5: microphones.value.length ? 2 : 1
        }
      })

      if (microphones.value.length) {
        showMicInfo.value = !showMicInfo.value
      } else {
        sendLog({
          event_type: '10000~50000',
          event_name: 30053,
          event_data: {
            str1: '麦克风',
            str2: '购买弹窗',
            str3: 'show',
          },
        })
        micOnlineInstance.value = $useMicOnline.show({
          info: micMallInfo.value
        })
      }
    }

    const handleExit = () => {
      console.log('handleExit')
      TSNativeInstance.exit()
    }

    watch(() => microphones.value.length, () => {
      if (micOnlineInstance.value) {
        micOnlineInstance.value.hide()
      }
    })

    const handleClearInput = () => {
      keyword.value = ''
    }

    onBeforeMount(async () => {
      if (!isShowMic.value) return
      if (micMallInfo.value.model) {
        sendLog({
          event_type: '10000~50000',
          event_name: 30052,
          event_data: {
            str1: '麦克风',
            str2: '展示入口',
            str3: 'show',
          },
        })

        sendLog({
          event_type: '10000~50000',
          event_name: 6008,
          event_data: {
            str1: '我的页',
            str2: '麦克风',
            str3: '展示入口',
            str4: 'show',
          }
        })
      }
    })

    const disableDoubleClickSelection = (event) => {
      if (event.detail > 1) {
        event.preventDefault();
      }
    }

    const handleToggleTheme = () => {
      store.commit('SET_THEME', themeClass.value === 'themeDark' ? 'themeLight' : 'themeDark')
    }

    const handleClickInput= (event) => {
      console.log(event)
      if (isSearch.value) {
        clearKeyword(event)
      } else {
        handleToSearch()
      }
    }

    const clearKeyword = (event) => {
      try {
        if (!clearButton.value) return
        console.log(clearButton)

        const rect = clearButton.value.getBoundingClientRect();  // 获取按钮的位置和尺寸
        const padding = 20;  // 增加额外的点击区域范围

        // 获取点击位置相对于视口的坐标
        const clickX = event.clientX;
        const clickY = event.clientY;

        // 判断点击位置是否在清除按钮的扩大区域内
        if (
          clickX >= (rect.left - padding) &&
          clickX <= (rect.left + rect.width + padding) &&
          clickY >= (rect.top - padding) &&
          clickY <= (rect.top + rect.height + padding)
        ) {
          // 如果是在清除按钮上点击，执行清除操作
          console.log('清除按钮被点击');
          keyword.value = '';  // 清除关键字
        } else {
          // 如果不是在清除按钮上点击，什么也不做或者处理其他逻辑
          console.log('点击位置不在清除按钮上');
        }
      } catch (error) {
        console.error('清除关键字时发生错误:', error);
      }
    }

    onMounted(nextTick(() => {
      setTimeout(() => {
        const refSearchInput = document.getElementById('searchInputId')
        if (!refSearchInput) return
        refSearchInput.addEventListener('copy', (event) => event.preventDefault())
        refSearchInput.addEventListener('mousedown', (e) => {
          if (e.detail > 1) {
            e.preventDefault()
          }
        })
      }, 1000)
    }))

    const handleClickReload = () => {
      window.location.reload()
    }

    return {
      themeClass,
      keyword,
      route_page,
      orderedSongNum,
      orderedList,
      isShowOrderToast,
      isSinging,
      micCount,
      micMallInfo,
      showMicInfo,
      microphones,
      handleBack,
      handleOpenMv,
      handleToSearch,
      handleSearch,
      handleChangeKeyword,
      handleClearKeyword,
      handleSearchKeydown,
      handleFormSubmit,
      handleOpenOrderControl,
      handleShowVip,
      handleClickMic,
      handleExit,
      handleClearInput,
      disableDoubleClickSelection,
      handleToggleTheme,
      clearKeyword,
      clearButton,
      handleClickInput,
      handleClickReload,
      isVipUser,
    }
  },
}
</script>

<style lang="stylus" scoped>
  .search-bar
    display flex
    align-items center
    justify-content space-between
    background rgba(0, 0, 0, 1)
    position fixed
    top 0
    left 0
    width 100vw
    z-index 6
    padding 36px 80px
    .notify-wrapper
      display flex
      align-items center
      flex 1
    input
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
      -webkit-touch-callout: none;
      -webkit-tap-highlight-color: transparent;
      // position relative
      // z-index 2
    @media screen and (max-width 1200px)
      padding 0px 60px 0px 48px
      height 140px
    &-right
      display flex
      align-items center
      .operate-position
        width 308px
        height 84px
        margin-right 40px
        background-image linear-gradient(101deg, rgba(249, 219, 184, 0.29) 0%, rgba(249, 219, 184, 0.29) 41%, rgba(218, 173, 119, 0.28) 100%)
        background url('https://qncweb.ktvsky.com/20230926/vadd/7ad8f2819d894e6990c045311626b55a.png') no-repeat  // https://qncweb.ktvsky.com/20230825/vadd/f348a2d9f739ca514dcf88bc6f87131b.png
        background-size 100% 100%
        background-position center
        @media screen and (max-width 1200px)
          width 166px
          height 64px
          margin-right 0
          background-image linear-gradient(101deg, rgba(249, 219, 184, 0.29) 0%, rgba(249, 219, 184, 0.29) 41%, rgba(218, 173, 119, 0.28) 100%)
          background url('https://qncweb.ktvsky.com/20230825/vadd/d503ffbc40dd05ba48d6ff53d928e573.png') no-repeat
          background-size 100% 100%
          background-position center
      .operate-position-search
        width 135px
        height 77px
        background url('https://qncweb.ktvsky.com/20231213/vadd/8ed4b42692fe01c465ecafb1a51e4c07.png') no-repeat
        background-size 100% 100%
        @media screen and (max-width 1200px)
          width 135px
          height 77px
          background url('https://qncweb.ktvsky.com/20231213/vadd/8ed4b42692fe01c465ecafb1a51e4c07.png') no-repeat
          background-size 100% 100%
      .operate-position-singer
        width 116px
        height 90px
        background url('https://qncweb.ktvsky.com/20231213/vadd/6620460cd5388a35ea813ff178bd1e54.png') no-repeat
        background-size 100% 100%
        @media screen and (max-width 1200px)
          width 116px
          height 90px
          background url('https://qncweb.ktvsky.com/20231213/vadd/6620460cd5388a35ea813ff178bd1e54.png') no-repeat
          background-size 100% 100%
      .already-order, .mic-main
        width 228px
        height 92px
        border-radius 4px
        border 2px solid rgba(255, 255, 255, 0.20)
        backdrop-filter blur(100px)
        margin-right 32px
        display flex
        align-items center
        justify-content center
        position relative
        svg
          width 40px
          height 40px
        span
          position absolute
          top 10px
          left 72%
          display flex
          justify-content center
          align-items center
          padding 0 10px
          height 32px
          background #E3AB5D
          border-radius 50%
          color #000
          font-size 20px
          font-weight 400
          z-index 1
        &-span
          width 54px !important
          border-radius 18px !important
        img
          width 40px
          height 40px
        p
          width auto
          height 92px
          line-height 92px
          opacity 0.8
          font-weight 400
          font-size 28px
          color #FFFFFF
          margin-left 12px
        @media screen and (max-width 1200px)
          margin 0 20px 0 20px
          width 178px
          height 72px
          p
            font-size 22px
          span
            width 28px
            height 28px
            font-size 18px
            top 7px
            right 10px
          img
            width 30px
            height 30px
      .mic
        position relative
        &-spots
          display flex
          justify-content center
          width 100%
          height 4px
          position absolute
          bottom 10px
          left 0
          p
            width 20px
            height 4px
            background: rgba(255, 255, 255, 0.20);
            margin 0 4px
            &.active
              background: #1AD773;
        &-info
          position absolute
          background #2B2F35
          padding 4px 24px
          left -222px
          top 0
          border-radius 10px
          box-shadow: 0px 4px 30px 0px #0000001A;
          .triangle
            content ""
            position absolute
            top 35px
            right -20px
            width: 20px;
            height: 10px;
            border-top: 10px solid transparent; /* 三角形的上边 */
            border-bottom: 10px solid transparent; /* 三角形的下边 */
            border-left: 10px solid rgba(255, 255, 255, 0.08); /* 三角形的右边 */
          li
            width 152px
            padding 20px 0
            color: rgba(255, 255, 255, 0.20);
            border-bottom 1px solid rgba(255, 255, 255, 0.08);
            display flex
            align-items center
            &:last-child
              border none
            span
              width: 12px;
              height: 12px;
              border-radius 50%
              background rgba(255, 255, 255, 0.20)
              margin-right 16px
            &.active
              color #1AD773
              span
                background #1AD773
    &-left
      display: flex
      align-items center
      flex 1
      margin-right 40px
    &-tool
      flex 1
      position relative
      width 520px
      height 92px
      border 2px solid rgba(255, 255, 255, 0.20)
      border-radius 4px
      display flex
      align-items center
      @media screen and (max-width 1200px)
        width 270px
        height 72px
      &-mask
        position absolute
        top 0
        right 0
        left 0
        bottom 0
        width 100%
        height 100%
      &-icon
        width 27px
        height 27px
        position absolute
        left 31px
        top 50%
        margin -13px 0 0
      form
        width 100%
        height 100%
      &-input
        display flex
        align-items center
        width 100% !important
        height 100% !important
        font-size 28px
        font-weight 500
        color rgba(255, 255, 255, .8)
        padding-left 76px
        padding-right 60px
        @media screen and (max-width 1200px)
          font-size 22px

  .search-input
    width 520px
    margin-left 0px
    margin-right 40px
    @media screen and (max-width 1200px)
      width 270px
      height 72px
      margin-right 20px
    .clear
      width 30px
      position absolute
      right 20px
      top 50%
      margin-top -15px
  .back
    width fit-content
    height 90px
    display flex
    align-items center
    justify-content center
    &-icon
      width 40px
      height 40px
      margin-right 60px
    @media screen and (max-width 1200px)
      height 72px
      img
        width 32px
        height 32px
        margin-right 48px
  .title
    color rgba(255, 255, 255, 1)
    font-size 32px
    font-weight 300
    width auto
    height 90px
    line-height 90px
    @media screen and (max-width 1200px)
      height 72px
      line-height 72px
      font-size 26px
  .search-btn
    display flex
    justify-content center
    align-items center
    width 200px
    height 92px
    border-radius 4px
    background #DBAE6A
    backdrop-filter blur(100px)
    color rgba(0, 0, 0, 0.8)
    font-size 28px
    @media screen and (max-width 1200px)
      width 160px
      height 72px
      font-size 22px
  .current-order-song
    width 300px
    height 80px
    padding 0 30px
    background #1E1F21
    border-radius 100px
    margin-right 40px
    display flex
    align-items center
    justify-content center
    overflow hidden
    position relative
    &-unactive
      font-size 24px
      color rgba(255, 255, 255, 0.20) !important
    &-shadow
      position absolute
      top 0
      right 30px
    @media screen and (max-width 1200px)
      margin 0 20px 0 20px
    &-scroll
      width 238px
      height 80px
      overflow hidden
      &-inner
        width auto
        height 80px
        background #1E1F21
        display flex
        align-items center
        font-weight 400
        font-size 24px
        white-space nowrap
        color rgba(255, 255, 255, 0.40)
        animation scroll 10s linear 0s infinite
        img
          width 26px
          height 26px
          margin-right 10px
  .mv-enter
    width 228px
    height 92px
    border-radius 4px
    border 2px solid rgba(255, 255, 255, 0.20)
    backdrop-filter blur(100px)
    color rgba(255, 255, 255, 0.8)
    justify-self flex-end
    font-size 28px
    display flex
    align-items center
    position relative
    &-icon
      width 40px
      height 40px
      margin 0 12px 0 32px
      position relative
      z-index 2
    @media screen and (max-width 1200px)
      width 178px
      height 72px
      font-size 22px
      &-icon
        width 32px
        height 32px
        margin 0 10px 0 24px
  .mv-enter-active
    width 100%
    height 100%
    border-radius 0px
    background url('../../assets/mv-enter-active.png') no-repeat
    background-position -16px center
    background-size auto 110px
    position absolute
    left 0
    top 0
  .order-tips
    width 228px
    height 99px
    padding-top 36px
    color #fff
    font-weight 400
    font-size 26px
    display flex
    justify-content center
    position absolute
    top 140px
    right 80px
    span
      position relative
      z-index 2
    svg
      position absolute
      top 0px
      left 0px
      width 100%
      height 100%
    // background url('../../assets/order-tips.png') no-repeat
    // background-size 100% 100%
    @media screen and (max-width 1200px)
      zoom calc(180 / 228)
@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-70%);
  }
}
.theme-themeLight
  .search-bar
    input
      color #1D1D1FCC
    .search-btn
      background #7A53E7
      color #FFFFFFCC
  .title
    color #1D1D1F
  .mic-main
    border: 2px solid #1D1D1F33
    p
      color #1D1D1F
  .mic-spots
    p
      background #1D1D1F33
      &.active
        background #00B71D
  .mic-info
    background #fff
    backdrop-filter none
    .triangle
      content ""
      position absolute
      top 35px
      right -20px
      width: 20px;
      height: 10px;
      border-top: 10px solid transparent; /* 三角形的上边 */
      border-bottom: 10px solid transparent; /* 三角形的下边 */
      border-left: 10px solid #FFFFFF; /* 三角形的右边 */
    li
      color #1D1D1F80
      border-bottom-color #1D1D1F1A
      span
        background #1D1D1F80
      &.active
        color #00B71D
        span
          background #00B71D
</style>

