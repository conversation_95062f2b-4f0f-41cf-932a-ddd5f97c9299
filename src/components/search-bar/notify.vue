<template>
  <div class="times-info" v-if="!isVip" @click="handleClickTimes">
    <!-- unlogin -->
    <template v-if="!isLogin">
      登录查看唱过的歌
    </template>
    <!-- new user -->
    <template v-else>
      <p v-if="isExpired">权益重磅升级，邀您回归！</p>
      <p v-else>
        每日更新，紧跟热点，<span class="light">点我</span>，海量歌曲免费唱！
      </p>
    </template>
  </div>
</template>

<script>
import useLoginValid from '@/composables/useLoginValid'
import useVip from '@/composables/useVip'
import { sendLog } from '@/directives/v-log/log'
import Toast from '@/utils/toast'
import { computed, watch } from 'vue'
import { useStore } from 'vuex'

export default {
  name: 'TimesInfo',
  setup() {
    const store = useStore()
    const { showVipQrcode } = useVip()
    const { isLogin, showLoginQrcode } = useLoginValid()

    const isVip = computed(() => !!store.state.vipInfo.end_time)
    const isExpired = computed(() => store.state.vipInfo.expire)

    const handleClickTimes = () => {
      sendLog({
        event_type: '10000~50000',
        event_name: 30256,
        event_data: {
          str1: '首页',
          str2: '右上角喇叭通知',
          str3: '点击',
          str4: 'click',
        },
      })
      if (!isLogin.value) {
        showLoginQrcode({
          log: '首页右上角小喇叭运营位-未登录',
        })

        sendLog({
          event_type: '10000~50000',
          event_name: 1021,
          event_data: {
            str1: '首页',
            str2: '顶部运营位',
            str3: '小喇叭运营位',
            str4: 'click',
            str5: 1,
            str9: '手机端',
            str10: '1800'
          }
        })
        return
      }else{
        sendLog({
          event_type: '10000~50000',
          event_name: 1021,
          event_data: {
            str1: '首页',
            str2: '顶部运营位',
            str3: '小喇叭运营位',
            str4: 'click',
            str5: 2,
            str6: isVip.value ? 2 : isExpired.value ? 4 : 1,
            str7: 1,
            str9: '车机端',
            str10: '1801'
          }
        })
        
      }
      if (!isVip.value) {
        showVipQrcode({
          log: '首页右上角小喇叭运营位-已登录',
        })
        return
        // if (isLogin.value) {
        //   showVipQrcode({
        //     isLogin: isLogin.value,
        //     fr: 1251801,
        //   })
        // } else {
        //   showVipQrcode({
        //     log: '首页右上角小喇叭运营位',
        //   })
        // }
      }
      Toast({
        message: '展示您的歌喉，尽情欢唱吧！',
        position: 'top',
      })
    }

    // watch(isLogin,(val)=>{
    //   if(val){
        
    //   }
    // },{
    //   deep: true
    // })

    return {
      isLogin,
      isVip,
      isExpired,
      handleClickTimes,
    }
  },
}
</script>

<style lang="stylus" scoped>
.times-info
  display flex
  align-items center
  background-color none
  height 90px
  background-image url('https://qncweb.ktvsky.com/20240710/other/b8108ad12e7dcc159db670aa63f8daa6.png')
  background-size 40px auto
  background-repeat no-repeat
  background-position 34px center
  border-radius 4px
  color rgba(255, 255, 255, 0.8)
  font-size 24px
  padding-left 84px
  padding-right 24px
  margin-left 32px
  background-color rgba(255, 255, 255, 0.1)
  img
    width 34px
    height 34px

  .light
    color #E5B875

  p
    color rgba(255, 255, 255, 0.8)

  .free-order
    position relative
    padding-right 20px
    margin-right 20px
    // &:after
    //   content ''
    //   position absolute
    //   top 50%
    //   margin-top -8px
    //   right 0
    //   width 2px
    //   height 20px
    //   background rgba(255, 255, 255, 0.2)
    &-zero
      color rgba(219, 174, 106, 1)
.theme-themeLight
  .times-info
    color rgba(29, 29, 31, 0.9)
    background-color rgba(255, 255, 255, 0.8)
    p
      color rgba(29, 29, 31, 0.9)
</style>
