<template>
  <div
    v-if="isShowFreeVipTips"
    class="free-vip-tips"
    @click="handleClickTips"
  >
    <template v-if="!isLogin">
      <span class="light">登录</span>即可享千元特权，好礼送不停！
    </template>
    <template v-else-if="isVip">
      尊敬的VIP用户，拉满状态开启您狂欢时刻！
    </template>
    <template v-else-if="isExpire">
      权益重磅升级，邀您回归！<span class="light">续费低至¥0.2/天</span>
    </template>
    <template v-else-if="freeVipNumber >= 3">
      每日更新曲库，紧跟实时热点，<span class="light">解锁</span>VIP，海量歌曲免费唱！
    </template>
    <template v-else>
      <p class="free-order">
        VIP歌曲免费点唱次数:
        <span class="free-order-zero">{{remainingFreeOrders}}/3</span>
      </p>
    </template>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useStore } from 'vuex'
import useVip from '@/composables/useVip'

const store = useStore()
const { isLogin, isVipUser, freeVipNumber } = useVip()

const isVip = computed(() => isVipUser.value)
const isExpire = computed(() => store.state.vipInfo.expire)
const remainingFreeOrders = computed(() => 3 - freeVipNumber.value)

// 显示免费VIP提示的条件
const isShowFreeVipTips = computed(() => {
  // 在首页显示，且不是搜索状态，且不是过期VIP用户显示快唱模式时
  return store.state.currentPage === 'home' && !isExpire.value
})

const handleClickTips = () => {
  // 点击提示时的处理逻辑，可以跳转到VIP页面或显示更多信息
  console.log('点击免费VIP提示')
}
</script>

<style lang="stylus" scoped>
.free-vip-tips
  padding 20px 32px
  background rgba(255, 255, 255, 0.05)
  border-radius 16px
  margin 20px 32px 0
  font-size 28px
  line-height 40px
  color rgba(255, 255, 255, 0.8)
  cursor pointer
  transition all 0.3s ease

  .light
    color #FFD850
    font-weight 600

  .free-order
    margin 0
    display flex
    align-items center
    justify-content space-between

    &-zero
      color #FFD850
      font-weight 600
      font-size 32px

  &:active
    background rgba(255, 255, 255, 0.1)

// 亮色主题适配
.themeLight .free-vip-tips
  background rgba(0, 0, 0, 0.05)
  color rgba(0, 0, 0, 0.8)

  &:active
    background rgba(0, 0, 0, 0.1)
</style>
