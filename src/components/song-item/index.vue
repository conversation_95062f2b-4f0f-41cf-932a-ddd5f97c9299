<template>
  <div class="song">
    <div v-if="renderType === 'list'" class="song-item" :class="className">
      <div class="left" :class="{ ordered: isOrdered }" @click="handleOrder">
          <div class="name" v-once>
            <span class="name-txt">{{ songItem.music_name }} </span>
          </div>
          <div class="desc" v-once>
            <template v-if="songItem.singer" v-once> 
              <span
                class="author"
                :class="[
                  singerCanClick && 'clickable',
                ]"
                style="text-overflow: unset;width: auto;max-width: unset;"
                @click="handleClickSinger"
              >
                {{ formattedSingerNamelist }}
              </span>
              <span class="divider">|</span>
            </template>
            <span class="album">{{ songItem.flag && songItem.flag.toString() }}</span>
            <img v-if="songItem.is_vip" class="song-block-vip" src="https://qncweb.ktvsky.com/20231206/vadd/56dc0bc6477bf2c7a6c4fcdc8360804e.png" alt="">
            <!-- <img v-if="false" class="song-block-score" src="https://qncweb.ktvsky.com/20240205/other/151412e04f9df9e6bbc1a781ddd6ab7d.png" /> -->
            <span 
              class="sing-cnt" 
              v-if="shouldDisplaySingCnt"
            >
              演唱过 <span>{{ songItem.sing_cnt > 99 ? '99+' : songItem.sing_cnt }}</span> 次
            </span>
          </div>
      </div>
      <div class="right" v-once>
        <span class="order-btn" @touchend="handleOrder">点歌</span>
      </div>
    </div>
    <div v-else
      :class="{
        'song-block': true,
        className: !!className,
        ordered: isOrdered,
      }" 
      @click="handleOrder"
      >
      <span class="name">{{ songItem.music_name }}</span>
      <div class="desc">
        <template v-if="songItem.singer">
          <span
            class="author"
            :class="[
              singerCanClick && 'clickable',
              songItem.sing_cnt && 'has-sing-cnt',
            ]"
            style="text-overflow: unset;width: auto;max-width: unset;"
            @click="handleClickSinger"
          >
            {{ formattedSingerName }}
          </span>
          <span class="divider">|</span>
        </template>
        <div class="center">
          <span class="album">{{ songItem.flag && songItem.flag.toString() }}</span>
          <img v-if="songItem.is_vip" class="song-block-vip" src="https://qncweb.ktvsky.com/20231206/vadd/56dc0bc6477bf2c7a6c4fcdc8360804e.png" alt="">
          <!-- <img v-if="false" class="song-block-score" src="https://qncweb.ktvsky.com/20240205/other/151412e04f9df9e6bbc1a781ddd6ab7d.png" /> -->
        </div>
        <span class="sing-cnt" v-if="songItem.sing_cnt">演唱过 <span>{{ songItem.sing_cnt > 99 ? '99+' : songItem.sing_cnt }}</span> 次</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, inject, toRefs, ref, defineEmits, defineProps } from 'vue'
import { useStore } from 'vuex'
import useSongItem from '@/composables/useSongItem'
// import useOrder from '@/composables/useOrder'
import { sendLog } from '@/directives/v-log/log'
import get from 'lodash/get'
import { useRouter, useRoute } from 'vue-router'
import split from 'lodash/split'
import Toast from '@/utils/toast'

const props = defineProps({
  className: {
    type: String,
    default: ''
  },
  songItem: Object,
  index: Number,
  startPosition: {
    type: Number,
    default: 0,
  },
  logFrom: {
    type: Object,
    default: () => {}
  },
  ponitActionLog: {
    type: Object,
    default: () => {}
  },
  singerEnable: {
    type: Boolean,
    default: true,
  },
  renderType: {
    default: 'list',
    type: String,
  },
  isStickButton: {
    default: false,
    type: Boolean,
  }
})

const emit = defineEmits(['singer-click', 'before-order-click'])

const router = useRouter()
const route = useRoute()
const { index, songItem, logFrom, ponitActionLog, singerEnable, renderType } = toRefs(props)
const store = useStore()
const orderedSongIdMap = inject('orderedSongIdMap')
const { orderSong } = useSongItem()
const isVip = computed(() => !!store.state.vipInfo.end_time)
const isExpired = computed(() => store.state.vipInfo.expire)
const isLogin= computed(() => !!store.state.userInfo.unionid)
const isOrdered = computed(() => {
  return orderedSongIdMap.value[songItem.value.songid]
})

const shouldDisplaySingCnt = computed(() => {
  if (renderType.value !== 'block') return songItem.value.sing_cnt > 0;

  const singerName = songItem.value.singer || '';
  const isVipSong = !!songItem.value.is_vip;
  const songFlag = songItem.value?.flag.toString() || '';
  
  if (singerName.length > 3 && isVipSong && songFlag.length > 5) {
    return false;
  }
  
  return songItem.value.sing_cnt > 0;
});

const mvIsHide = computed(() => store.state.mvIsHide);
const singerCanClick = computed(() => (mvIsHide.value && !route.query.singerid) || (!mvIsHide.value && singerEnable.value))

// const isLocalSong = ref(false)

const formattedSingerName = computed(() => {
  const { singer, is_vip, sing_cnt, flag } = songItem.value;
  if (singer.length > 2 && is_vip && sing_cnt && flag) {
    return singer.slice(0, 2) + '...';
  } else if (singer.length > 7 && sing_cnt && flag) {
    return sing_cnt > 9 ? singer.slice(0, 4) + '...' : singer.slice(0, 7) + '...';
  }
  return singer;
});

const formattedSingerNamelist = computed(() => {
  const { singer, sing_cnt, flag } = songItem.value;
  if (mvIsHide.value === false) {
    if (singer.length > 7 && sing_cnt && flag) {
      return singer.slice(0, 7) + '...';
    }
    return singer.length > 15 ? singer.slice(0, 15) + '...' : singer;
  } 
  
  if (singer.length > 22 && sing_cnt && flag) {
    return singer.slice(0, 22) + '...';
  }
  return singer.length > 35 ? singer.slice(0, 30) + '...' : singer;
});

const filterPALog = (type) => {
  if (get(ponitActionLog.value, 'event_type') !== '10000~50000') return {}
  const eventAction = ['进入常唱', '进入歌名点歌', '进入任意歌手', '歌曲列表']
  let res = {
    ...ponitActionLog.value,
  }
  if (eventAction.includes(get(res, 'event_data.str3'))) {
    if (type === 'order') {
      res.event_data.str5 = '点歌'
      res.event_data.str6 = 'click'
    } else if (type === 'stick') {
      res.event_data.str7 = '置顶点歌'
      res.event_data.str8 = 'click'
    }
  }
  return res
}

const handleOrder = () => {
  emit('before-order-click', {
    index: index.value,
    songItem: songItem.value,
  })

  if(logFrom.value.str1 == '1-guess'){
    sendLog({
      event_type: '10000~50000',
      event_name: 121,
      event_data: {
        str1: '首页',
        str2: '猜你会唱',
        str3: '加入已点',
        str4: 'click',
        str5: songItem.value.songid,
        str6: !songItem.value.is_vip ? 2 : 1
      },
    })

    if(isLogin.value){
      sendLog({
        event_type: '10000~50000',
        event_name: 1021,
        event_data: {
          str1: '首页',
          str2: '猜你会唱',
          str3: '点击 VIP 歌曲',
          str4: 'click',
          str5: 2,
          st6: isVip.value ? 2 : isExpired.value ? 4 : 1,
          str7: songItem.value.songid,
          str9: '手机端',
          str10: '1810'
        }
      })
    }else{
      sendLog({
        event_type: '10000~50000',
        event_name: 1021,
        event_data: {
          str1: '首页',
          str2: '猜你会唱',
          str3: '点击 VIP 歌曲',
          str4: 'click',
          str5: 1,
          str7: songItem.value.songid,
          str9: '手机端',
          str10: '1809'
        }
      })
    }
  }
  if(logFrom.value.str1 == '1-songTab'){
    sendLog({
      event_type: '10000~50000',
      event_name: 121,
      event_data: {
        str1: '首页',
        str2: '歌单',
        str3: '点歌',
        str4: 'click',
        str5: songItem.value.songid,
        str6: logFrom.value.song_list_id,
        str7: !songItem.value.is_vip ? 2 : 1
      },
    })
  }
  if(logFrom.value.str1 == '2-search'){
    sendLog({
      event_type: '10000~50000',
      event_name: 121,
      event_data: {
        str1: '搜索页',
        str2: '搜索结果页',
        str3: '加入已点',
        str4: 'click',
        str5: songItem.value.songid,
        str6: !songItem.value.is_vip ? 2 : 1
      },
    })
  }

  if(logFrom.value.str1 == '2-search-guess'){

    if(isLogin.value){
      sendLog({
        event_type: '10000~50000',
        event_name: 1021,
        event_data: {
          str1: '搜索',
          str2: '猜你会唱',
          str3: '点击 VIP 歌曲',
          str4: 'click',
          str5: 2,
          st6: isVip.value ? 2 : isExpired.value ? 4 : 1,
          str7: songItem.value.songid,
          str9: '手机端',
          str10: '1818'
        }
      })
    }else{
      sendLog({
        event_type: '10000~50000',
        event_name: 1021,
        event_data: {
          str1: '搜索',
          str2: '猜你会唱',
          str3: '点击 VIP 歌曲',
          str4: 'click',
          str5: 1,
          str7: songItem.value.songid,
          str9: '手机端',
          str10: '1817'
        }
      })
    }

    sendLog({
      event_type: '10000~50000',
      event_name: 121,
      event_data: {
        str1: '搜索页',
        str2: '猜你会唱',
        str3: '加入已点',
        str4: 'click',
        str5: songItem.value.songid,
        str6: !songItem.value.is_vip ? 2 : 1
      },
    })
  }

  if(logFrom.value.str1 == '2-songlist'){
    sendLog({
      event_type: '10000~50000',
      event_name: 121,
      event_data: {
        str1: '歌手列表页',
        str2: '歌手列表',
        str3: '歌手详情页点歌',
        str4: 'click',
        str5: songItem.value.songid,
        str6: songItem.value.singerid,
        str7: !songItem.value.is_vip ? 2 : 1
      },
    })
  }

  if(logFrom.value.str1 == '2-mine'){
    sendLog({
      event_type: '10000~50000',
      event_name: 121,
      event_data: {
        str1: '我的页',
        str2: '唱过的歌',
        str3: '加入已点',
        str4: 'click',
        str5: songItem.value.songid,
        str6: !songItem.value.is_vip ? 2 : 1
      },
    })
  }

  if(logFrom.value.str1 == '3-mv-fast'){
    sendLog({
      event_type: '10000~50000',
      event_name: 121,
      event_data: {
        str1: '欢唱页',
        str2: '快速点歌',
        str3: '点歌',
        str4: 'click',
        str5: songItem.value.songid,
        str6: songItem.value.singerid,
        str7: !songItem.value.is_vip ? 2 : 1
      },
    })

    if(isLogin.value){
      sendLog({
        event_type: '10000~50000',
        event_name: 1021,
        event_data: {
          str1: 'MV 页面',
          str2: '快速点歌-搜索-猜你会唱',
          str3: '点击 VIP 歌曲',
          str4: 'click',
          str5: 2,
          str7: songItem.value.songid,
          str9: '手机端',
          str10: '1824'
        }
      })
    }else{
      sendLog({
        event_type: '10000~50000',
        event_name: 1021,
        event_data: {
          str1: 'MV 页面',
          str2: '快速点歌-搜索-猜你会唱',
          str3: '点击 VIP 歌曲',
          str4: 'click',
          str5: 1,
          str7: songItem.value.songid,
          str9: '手机端',
          str10: '1823'
        }
      })
    }
  }

  if(logFrom.value.str1 == '3-mv-search'){
    sendLog({
      event_type: '10000~50000',
      event_name: 121,
      event_data: {
        str1: '欢唱页',
        str2: '搜索结果',
        str3: '点歌',
        str4: 'click',
        str5: songItem.value.songid
      }
    })
  }

  if(logFrom.value.str1 == '2-singing-already'){
    sendLog({
      event_type: '10000~50000',
      event_name: 121,
      event_data: {
        str1: '唱过的歌',
        str2: '点歌',
        str3: '加入已点',
        str4: 'click',
        str5: songItem.value.songid,
        str6: !songItem.value.is_vip ? 2 : 1
      },
    })
  }

  if(logFrom.value.str1 == '2-singing-nologin'){
    sendLog({
      event_type: '10000~50000',
      event_name: 121,
      event_data: {
        str1: '唱过的歌',
        str2: '猜你会唱',
        str3: '点歌',
        str4: 'click',
        str5: songItem.value.songid,
        str6: songItem.value.singerid,
        str7: !songItem.value.is_vip ? 2 : 1
      },
    })

    sendLog({
      event_type: '10000~50000',
      event_name: 6001,
      event_data: {
        str1: '首页',
        str2: '比亚迪车主尊贵车单',
        str3: '点歌',
        str4: 'click'
      }
    })

    sendLog({
      event_type: '10000~50000',
      event_name: 1021,
      event_data: {
        str1: '唱过的歌',
        str2: '长城车主尊享歌单',
        // str3: '点歌',
        str4: 'click',
        str5: 2
      }
    })
  }

  if(logFrom.value.str1 == '1-song-tab'){
    if(isLogin.value){
      sendLog({
        event_type: '10000~50000',
        event_name: 1021,
        event_data: {
          str1: '首页',
          str2: '歌单',
          str3: '点击 VIP 歌曲',
          str4: 'click',
          str5: 2,
          st6: isVip.value ? 2 : isExpired.value ? 4 : 1,
          str7: songItem.value.songid,
          str9: '手机端',
          str10: '1812'
        }
      })
    }else{
      sendLog({
        event_type: '10000~50000',
        event_name: 1021,
        event_data: {
          str1: '首页',
          str2: '歌单',
          str3: '点击 VIP 歌曲',
          str4: 'click',
          str5: 1,
          str7: songItem.value.songid,
          str9: '手机端',
          str10: '1811'
        }
      })
    }
    sendLog({
      event_type: '10000~50000',
      event_name: 121,
      event_data: {
        str1: '首页',
        str2: '歌单',
        str3: '点歌',
        str4: 'click',
        str5: songItem.value.songid,
        str6: logFrom.value.song_list,
        str7: !songItem.value.is_vip ? 2 : 1
      }
    })
  }

  if(logFrom.value.str1 == '2-search-order'){

    if(isLogin.value){
      sendLog({
        event_type: '10000~50000',
        event_name: 1021,
        event_data: {
          str1: '搜索',
          str2: '搜索结果-歌曲',
          str3: '点击 VIP 歌曲',
          str4: 'click',
          str5: 2,
          st6: isVip.value ? 2 : isExpired.value ? 4 : 1,
          str7: songItem.value.songid,
          str9: '手机端',
          str10: '1814'
        }
      })
    }else{
      sendLog({
        event_type: '10000~50000',
        event_name: 1021,
        event_data: {
          str1: '搜索',
          str2: '搜索结果-歌曲',
          str3: '点击 VIP 歌曲',
          str4: 'click',
          str5: 1,
          str7: songItem.value.songid,
          str9: '手机端',
          str10: '1813'
        }
      })
    }


    sendLog({
      event_type: '10000~50000',
      event_name: 121,
      event_data: {
        str1: '搜索页',
        str2: '搜索结果页',
        str3: '加入已点',
        str4: 'click',
        str5: songItem.value.songid,
        str6: !songItem.value.is_vip ? 2 : 1
      }
    })
  }

  if(logFrom.value.str1 == '2-songlist-singer'){
    console.log(logFrom.value,'567');

    if(isLogin.value){
      sendLog({
        event_type: '10000~50000',
        event_name: 1021,
        event_data: {
          str1: logFrom.value.path == "singer" ? '歌手' : '搜索',
          str2:  logFrom.value.path == "singer" ? '歌手详情页' : '搜索结果-歌星-歌星详情页',
          str3: '点击 VIP 歌曲',
          str4: 'click',
          str5: 2,
          st6: isVip.value ? 2 : isExpired.value ? 4 : 1,
          str7: songItem.value.songid,
          str9: '手机端',
          str10: logFrom.value.path == "singer" ? '1842' : '1816'
        }
      })
    }else{
      sendLog({
        event_type: '10000~50000',
        event_name: 1021,
        event_data: {
          str1: logFrom.value.path == "singer" ? '歌手' : '搜索',
          str2: logFrom.value.path == "singer" ?  '歌手详情页' :'搜索结果-歌星-歌星详情页',
          str3: '点击 VIP 歌曲',
          str4: 'click',
          str5: 1,
          str7: songItem.value.songid,
          str9: '手机端',
          str10:logFrom.value.path == "singer" ? '1841' : '1815'
        }
      })
    }

    sendLog({
      event_type: '10000~50000',
      event_name: 121,
      event_data: {
        str1: '歌手列表页',
        str2: '歌手列表',
        str3: '歌手详情页点歌',
        str4: 'click',
        str5: songItem.value.songid,
        str6: !songItem.value.is_vip ? 2 : 1
      }
    })
  }

  if(logFrom.value.str1 == '2-mine-order'){
    if(!isVip.value && songItem.value.is_vip){
      sendLog({
        event_type: '10000~50000',
        event_name: 1021,
        event_data: {
          str1: '我的',
          str2: '唱过的歌',
          str3: '点击 VIP 歌曲',
          str4: 'click',
          str5: 2,
          st6: isVip.value ? 2 : isExpired.value ? 4 : 1,
          str7: songItem.value.songid,
          str9: '手机端',
          str10: '1840'
        }
      })
    }
    sendLog({
      event_type: '10000~50000',
      event_name: 121,
      event_data: {
        str1: '我的页',
        str2: '唱过的歌',
        str3: '加入已点',
        str4: 'click',
        str5: songItem.value.songid,
        str6: !songItem.value.is_vip ? 2 : 1
      }
    })
  } 

  if(logFrom.value.str1 == '3-fast-singer'){
    if(isLogin.value){
      sendLog({
        event_type: '10000~50000',
        event_name: 1021,
        event_data: {
          str1: 'MV 页面',
          str2: '快速点歌-搜索结果-歌手',
          str3: '点击 VIP 歌曲',
          str4: 'click',
          str5: 2,
          st6: isVip.value ? 2 : isExpired.value ? 4 : 1,
          str7: songItem.value.songid,
          str9: '手机端',
          str10: '1828'
        }
      })
    }else{
      sendLog({
        event_type: '10000~50000',
        event_name: 1021,
        event_data: {
          str1: 'MV 页面',
          str2: '快速点歌-搜索结果-歌手',
          str3: '点击 VIP 歌曲',
          str4: 'click',
          str5: 1,
          str7: songItem.value.songid,
          str9: '手机端',
          str10: '1827'
        }
      })
    }
  }


  if(logFrom.value.str1 == '3-fast-order'){
    if(isLogin.value){
      sendLog({
        event_type: '10000~50000',
        event_name: 1021,
        event_data: {
          str1: 'MV 页面',
          str2: '快速点歌-搜索结果-歌曲',
          str3: '点击 VIP 歌曲',
          str4: 'click',
          str5: 2,
          st6: isVip.value ? 2 : isExpired.value ? 4 : 1,
          str7: songItem.value.songid,
          str9: '手机端',
          str10: '1826'
        }
      })
    }else{
      sendLog({
        event_type: '10000~50000',
        event_name: 1021,
        event_data: {
          str1: 'MV 页面',
          str2: '快速点歌-搜索结果-歌曲',
          str3: '点击 VIP 歌曲',
          str4: 'click',
          str5: 1,
          str7: songItem.value.songid,
          str9: '手机端',
          str10: '1825'
        }
      })
    }

    sendLog({
      event_type: '10000~50000',
      event_name: 121,
      event_data: {
        str1: '欢唱页',
        str2: '搜索结果',
        str3: '点歌',
        str4: 'click',
        str5: songItem.value.songid,
        // str6: !songItem.value.is_vip ? 2 : 1
      }
    })
  }

  if(logFrom.value.str1 == '2-singing-order'){
    sendLog({
      event_type: '10000~50000',
      event_name: 121,
      event_data: {
        str1: '唱过的歌',
        str2: '点歌',
        str3: '加入已点',
        str4: 'click',
        str5: songItem.value.songid,
        str6: !songItem.value.is_vip ? 2 : 1
      }
    })

    sendLog({
      event_type: '10000~50000',
      event_name: 1021,
      event_data: {
        str1: '唱过的歌',
        str2: '唱过的歌',
        str3: '点击 VIP 歌曲',
        str4: 'click',
        str5: 2,
        str6: isVip.value ? 2 : isExpired.value ? 4 : 1,
        str7: songItem.value.songid,
        str9: '手机端',
        str10: '1808'
      }
    })
  }

  const filterPALogData = filterPALog('order')
  store.commit('UPDATE_CONTROL_FROM_TYPE', 1)
  if (Object.keys(orderedSongIdMap.value).length === 0) {
    orderSong(songItem.value, {
      from: logFrom.value,
      ponitActionLog: filterPALogData,
    })
    return
  }
  orderSong(songItem.value, {
    immediate: false,
    from: logFrom.value,
    ponitActionLog: filterPALogData,
  })
}

const handleClickSinger = (e) => {
  if (!singerCanClick.value) { return; }
  e.stopPropagation();
  sendLog({
    event_type: '10000~50000',
    event_name: 10108,
    event_data: {
      str1: '任意点歌页',
      str2: '歌曲列表',
      str3: '点击任意歌手',
      str4: 'click',
    },
  })

  sendLog({
    event_type: '10000~50000',
    event_name: 6006,
    event_data: {
      str1: '歌手详情页',
      str2: '歌手详情页',
      str3: '进入歌手详情页',
      str4: 'show',
      str5: songItem.value.singerid,
      str6: 3 
    }
  })
  
  if (mvIsHide.value) {
    router.push({
      name: 'songList',
      query: {
        name: split(songItem.value.singer, ',')[0],
        image: songItem.value.singer_head,
        singerid: songItem.value.singerid,
      },
    })
  } else {
    emit('singer-click', {
      singer: split(songItem.value.singer, ',')[0],
      singerhead: songItem.value.singer_head,
      singerid: songItem.value.singerid,
    })
    sendLog({
      event_type: '10000~50000',
      event_name: 10109,
      event_data: {
        str1: 'MV点歌页',
        str2: '歌曲列表',
        str3: '点击任意歌手',
        str4: 'click',
      },
    })
  }
}
</script>

<style lang="stylus" scoped>
.song
  height fit-content
  &-item
    display flex
    align-items center
    justify-content space-between
    height 141px
    color #ffffff
    border-bottom 1px solid rgba(255, 255, 255, 0.18)
    // border-radius 10px
    color rgba(255,255,255,0.7)
    .album
      white-space nowrap
    .left
      flex 1
      display flex
      flex-direction column
      overflow hidden
    .name
      font-size 32px
      color rgba(255,255,255,0.7)
      margin-bottom 15px
      display flex
      align-items center
      &-txt
        white-space nowrap
        overflow hidden
        text-overflow ellipsis
        max-width 90%
        @media screen and (max-width 1200px)
          max-width 690px
          font-size 26px
    .desc
      display flex
      align-items center
      height 33px
      font-size 24px
      color rgba(255,255,255,0.4)
      @media screen and (max-width 1200px)
        font-size 20px
      .clickable
        position relative
        padding-right 26px
        &::after
          content ""
          position absolute
          right 0
          top 50%
          margin-top -11px
          width 22px
          height 22px
          background url('https://qncweb.ktvsky.com/20231212/vadd/70dbe52816b882ae1f6871b3a509f375.png') no-repeat
          background-size 100% 100%
      .divider
        margin -4px 16px 0 16px
      .sing-cnt
        margin-left 5px
        span
          color #DBAE6A
      .author
        max-width 60%
        overflow hidden
        white-space nowrap
        text-overflow ellipsis
        @media screen and (max-width 1200px)
          max-width 160px
    .ordered
      .name
        color rgba(219, 174, 106, 1)
      .desc
        color rgba(219, 174, 106, 1)
      .clickable::after
        content ""
        background-image url('https://qncweb.ktvsky.com/20231212/vadd/4a5a96b4a7e0b0ae7f364679f5f69417.png')
    .right
      margin-right 20px
      img
        width 40px
        height 40px
      span
        width 200px
        height 80px
        font-size 28px
        color rgba(255, 255, 255, .6)
        text-align center
        display flex
        align-items center
        justify-content center
        border-radius 100px
        border 2px solid rgba(255, 255, 255, 0.10)
        background rgba(255, 255, 255, 0.08)
        @media screen and (max-width 1200px)
          width 160px
          height 64px
          font-size 22px
        &:active
          color rgba(219, 174, 106, 0.8)
          border 2px solid rgba(219, 174, 106, 0.80)
          background rgba(219, 174, 106, 0.10)
    @media screen and (max-width 1200px)
      .name
        &-txt
          font-size: 26px;
      .desc
        font-size: 20px;
  &-block
    width 560px
    height 160px
    display flex
    flex-direction column
    justify-content center
    padding 0 10px 0 20px
    margin-bottom 40px
    background rgba(255, 255, 255, 0.10)
    border-radius 10px
    @media screen and (max-width: 1200px)
      padding 0 20px
    .name
      // height 38px
      // line-height 38px
      font-size 32px
      font-weight 500
      margin-bottom 10px
      color rgba(255, 255, 255, 1)
      overflow hidden
      white-space nowrap
      text-overflow ellipsis
      max-width 500px
      @media screen and (max-width: 1200px)
        max-width 690px
        margin-bottom 0
    .desc
      display flex
      align-items center
      flex-wrap nowrap
      height 28px
      font-size 24px
      color rgba(153, 153, 153, 1)
      .author
        max-width 280px
        overflow hidden
        white-space nowrap
        text-overflow ellipsis
        @media screen and (max-width 1200px)
          max-width 160px
        &.has-sing-cnt
          max-width 115px
      span
        height 28px
        line-height 28px
      .clickable
        position relative
        padding-right 26px
        &::after
          content ""
          position absolute
          right 0
          top 50%
          margin-top -11px
          width 22px
          height 22px
          background url('https://qncweb.ktvsky.com/20231212/vadd/70dbe52816b882ae1f6871b3a509f375.png') no-repeat
          background-size 100% 100%
      .divider
        margin -4px 16px 0 16px
        color rgba(255, 255, 255, 1)
        opacity 0.4
      .center
        display flex
        align-items center
        margin-right 16px
      .sing-cnt
        white-space nowrap
        span
          color #DBAE6A
    &-vip, &-score
      width 50px
      height 26px
      margin-left 5px
      @media screen and (max-width 1200px)
        width 42.3px
        height auto
        margin-left 8px
  .ordered
    .name
      color rgba(219, 174, 106, 1)
    .desc
      color rgba(219, 174, 106, 1)
    .divider
      color rgba(219, 174, 106, 1)
      opacity 1
    .clickable::after
      content ""
      background-image url('https://qncweb.ktvsky.com/20231212/vadd/4a5a96b4a7e0b0ae7f364679f5f69417.png')
  @media screen and (max-width 1200px)
    .divider
      margin-left 8px!important
      margin-right 8px!important
.sheet-list-song-item
  .desc
    ::v-deep .clickable
      background-position 0px right
      &::after
        content ""
        position absolute
        margin-top -13px
</style>
