<template>
  <div class="already-song-item">
    <div class="info" :class="{ ordered: isOrdered }" @click="handleOrder">
      <p>
        <span class="name">{{ songItem.music_name }}</span>
      </p>
      <p>
        <DownloadStatus
          :state="songItem.downloadState"
          :progress="songItem.downloadProgress"
        ></DownloadStatus>
        <template v-if="songItem.singer">
          <span
            class="singer"
            :class="singerCanClick && 'clickable'"
            @click="handleClickSinger"
          >{{ singerTxt }}</span>
          <span class="divide"></span>
        </template>
        <span class="flag">{{ songItem.flag && songItem.flag.toString() }}</span>
        <img v-if="songItem.is_vip" class="song-block-vip" src="https://qncweb.ktvsky.com/20231206/vadd/56dc0bc6477bf2c7a6c4fcdc8360804e.png" alt="">
      </p>
    </div>
    <div class="control">
      <div @click="handleStickSongToTop">
        <svg v-show="themeClass == 'themeLight'" width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g opacity="0.9" clip-path="url(#clip0_7079_43181)">
          <path d="M31.7002 23.0293L20.0002 11.3293L8.30019 23.0293" stroke="#1D1D1F" stroke-opacity="0.9" style="stroke:#1D1D1F;stroke:color(display-p3 0.1137 0.1137 0.1216);stroke-opacity:0.9;" stroke-width="2" stroke-linecap="square"/>
          <path d="M21 12L21 36L19 36L19 12L21 12Z" fill="#1D1D1F" fill-opacity="0.9" style="fill:#1D1D1F;fill:color(display-p3 0.1137 0.1137 0.1216);fill-opacity:0.9;"/>
          <rect x="7" y="5" width="26" height="2" fill="#1D1D1F" fill-opacity="0.9" style="fill:#1D1D1F;fill:color(display-p3 0.1137 0.1137 0.1216);fill-opacity:0.9;"/>
          </g>
          <defs>
          <clipPath id="clip0_7079_43181">
          <rect width="40" height="40" fill="white" style="fill:white;fill-opacity:1;"/>
          </clipPath>
          </defs>
        </svg>

        <svg v-show="themeClass == 'themeDark'" width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g opacity="0.9" clip-path="url(#clip0_7078_23556)">
          <path d="M31.6992 23.0293L19.9992 11.3293L8.29922 23.0293" stroke="white" style="stroke:white;stroke-opacity:1;" stroke-width="2" stroke-linecap="square"/>
          <path d="M21 12L21 36L19 36L19 12L21 12Z" fill="white" style="fill:white;fill-opacity:1;"/>
          <rect x="7" y="5" width="26" height="2" fill="white" style="fill:white;fill-opacity:1;"/>
          </g>
          <defs>
          <clipPath id="clip0_7078_23556">
          <rect width="40" height="40" fill="white" style="fill:white;fill-opacity:1;"/>
          </clipPath>
          </defs>
        </svg>
      </div>
      <div @click="handleDelete">
        <svg v-show="themeClass == 'themeLight'" width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g opacity="0.9">
          <path d="M16 5H24V7H16V5Z" fill="#1D1D1F" fill-opacity="0.9" style="fill:#1D1D1F;fill:color(display-p3 0.1137 0.1137 0.1216);fill-opacity:0.9;"/>
          <path d="M5 9H35V11H5V9Z" fill="#1D1D1F" fill-opacity="0.9" style="fill:#1D1D1F;fill:color(display-p3 0.1137 0.1137 0.1216);fill-opacity:0.9;"/>
          <path d="M12.4755 35L9.14218 10H30.8578L27.5245 35H12.4755Z" stroke="#1D1D1F" stroke-opacity="0.9" style="stroke:#1D1D1F;stroke:color(display-p3 0.1137 0.1137 0.1216);stroke-opacity:0.9;" stroke-width="2"/>
          </g>
        </svg>
        
        <svg v-show="themeClass == 'themeDark'" width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g opacity="0.9">
          <path d="M16 5H24V7H16V5Z" fill="white" style="fill:white;fill-opacity:1;"/>
          <path d="M5 9H35V11H5V9Z" fill="white" style="fill:white;fill-opacity:1;"/>
          <path d="M12.4755 35L9.14218 10H30.8578L27.5245 35H12.4755Z" stroke="white" style="stroke:white;stroke-opacity:1;" stroke-width="2"/>
          </g>
        </svg>
      </div>
    </div>
  </div>
</template>

<script setup>
import { toRefs, computed, defineEmits, defineProps } from 'vue'
import { useStore } from 'vuex'
import { sendLog } from '@/directives/v-log/log'
import split from 'lodash/split'
import { getSongInfo } from '@/service/singer'

const props = defineProps({
  index: Number,
  songItem: {
    type: Object,
    default() {
      return {
        acc: '1',
        org: '2',
        flag: [],
        m3u8: {
          480: '',
          720: '',
          1080: '',
        },
        music_name: '',
        played: 0,
        singer: '',
        songid: 0,
      }
    }
  },
  isLandscape: {
    type: Boolean,
    default: false,
  }
})

const emit = defineEmits(['delete', 'stick-top', 'order', 'singer-click'])

const { songItem, index, isLandscape } = toRefs(props)
const store = useStore()
const orderedList = computed(() => store.state.orderedList)
const isOrdered = computed(() => orderedList.value.some(item => item.songid === songItem.value.songid))
const singerCanClick = computed(() => (!store.state.mvIsHide && isLandscape.value) || store.state.mvIsHide)
const singerTxt = computed(() => {
  const singer = songItem.value.singer.replace(/,$/, '');
  return singer.length > 10 ? `${singer.substring(0, 10)}...` : singer;
})

// const imgs = {
//   themeDark: {
//     top: require('@/assets/zhiding-dark.png'),
//     delete: require('@/assets/shanchu-dark.png'),
//   },
//   themeLight: {
//     top: require('@/assets/zhiding-light.png'),
//     delete: require('@/assets/shanchu-light.png'),
//   },
//   themeSystem: {
//     top: require('@/assets/zhiding-dark.png'),
//     delete: require('@/assets/shanchu-dark.png'),
//   },
// }

const themeClass = computed(() => store.state.themeClass)

const handleDelete = () => {
  emit('delete', songItem.value, index.value)
}

const handleStickSongToTop = () => {
  emit('stick-top', songItem.value, index.value)
}

const handleOrder = () => {
  emit('order', songItem.value, index.value)
}

const handleClickSinger = async(e) => {
  if (!singerCanClick.value) return
  e.stopPropagation();
  sendLog({
    event_type: '10000~50000',
    event_name: 10109,
    event_data: {
      str1: '任意点歌页',
      str2: '歌曲列表',
      str3: '点击任意歌手',
      str4: 'click',
    },
  })

  sendLog({
    event_type: '10000~50000',
    event_name: 6006,
    event_data: {
      str1: '歌手详情页',
      str2: '歌手详情页',
      str3: '进入歌手详情页',
      str4: 'show',
      str5: songItem.value.singerid,
      str6: 3 
    }
  })
  if (!songItem.value.singerid) {
    const { song_info = {} } = await getSongInfo(songItem.value.songid)
    emit('singer-click', {
      singer: split(songItem.value.singer, ',')[0],
      singerhead: song_info.singer_head,
      singerid: song_info.singerid,
    })
    return
  }
  emit('singer-click', {
    singer: split(songItem.value.singer, ',')[0],
    singerhead: songItem.value.singer_head,
    singerid: songItem.value.singerid,
  })
}
</script>

<style lang="stylus" scoped>
.already-song-item
  padding 30px 0
  width 100%
  height 137px
  border-bottom 1px solid rgba(255, 255, 255, 0.08)
  display flex
  justify-content space-between
  @media screen and (max-width 1200px)
    height 130px
    padding 30px 20px
  &.playing
    .info
      color rgba(219, 174, 106, 1)
      .divide
        background rgba(219, 174, 106, 1)
      .name
        color rgba(219, 174, 106, 1)
  .info
    display flex
    flex-direction column
    color rgba(255, 255, 255, .4)
    font-size 28px
    flex 1
    .singer
      max-width: 60%
    .name
      margin 0
      color rgba(255, 255, 255, .8)
      white-space nowrap
      overflow hidden
      text-overflow ellipsis
      max-width 690px
      @media screen and (max-width 1200px)
        max-width 620px
    .vip
      display flex
      justify-content center
      align-items center
      width 64px
      height 36px
      font-size 22px
      border 3px solid #F0D290
      color #F0D290
      border-radius 8px
      margin-left 16px
    .divide
      width 2px
      height 30px
      margin 0 16px
      background rgba(255, 255, 255, .2)
    p
      &:nth-child(1)
        height 38px
        line-height 38px
        display flex
        align-items center
        font-size 32px
        margin-bottom 10px
        img
          width 32px
          height 32px
          margin-left 16px
      &:nth-child(2)
        height 28px
        line-height 38px
        display flex
        align-items center
    .clickable
      position relative
      padding-right 26px
      &::after
        content ""
        position absolute
        right 0
        top 50%
        margin-top -10px
        width 22px
        height 22px
        background url('https://qncweb.ktvsky.com/20231212/vadd/70dbe52816b882ae1f6871b3a509f375.png') no-repeat
        background-size 100% 100%
    .song-block-vip, .song-block-score
      width 50px
      height 26px
      margin-left 13px
    @media screen and (max-width 1200px)
      p
        &:nth-child(1)
          font-size 26px
        &:nth-child(2)
          font-size 20px
  .ordered
    .name, .singer, .divide, .flag
      color rgba(219, 174, 106, 1) !important
    .divide
      background rgba(219, 174, 106, 1) !important
    .clickable::after
      content ""
      background-image url('https://qncweb.ktvsky.com/20231212/vadd/4a5a96b4a7e0b0ae7f364679f5f69417.png')
  .control
    display flex
    align-items center
    div
      width 40px
      height 40px
      svg
        width 100%
        height 100%
      &:last-child
        margin-left 72px

.theme-themeLight
  .already-song-item
    border-color rgba(29,29,31,0.1)
</style>