<template>
  <div
    v-if="orderedList.length"
    class="song-list"
    :class="[from]"
  >
    <RecycleScroller
      :items="orderedList"
      :item-size="tableItemSize"
      :buffer="500"
      key-field="_i"
      v-slot="{ item }"
    >
      <SongItem
        class="order-song-item"
        :index="item._index"
        :key="item._i"
        :songItem="item"
        :isLandscape="isLandscape"
        @order="handleOrder"
        @stickTop="handleStickTop"
        @delete="handleDelete"
        @singer-click="handleSingerClick"
      ></SongItem>
    </RecycleScroller>
  </div>
  <div class="empty" v-else>
    <svg v-show="themeClass == 'themeLight'" width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g opacity="0.4" clip-path="url(#clip0_346_155436)">
      <circle cx="62.1964" cy="33.4815" r="18.5557" stroke="#1D1D1F" stroke-width="4"/>
      <path d="M45.0664 38.9043L21.6423 68.7885L28.1023 75.2485L57.9865 51.8244" stroke="#1D1D1F" stroke-width="4"/>
      <path d="M18.8096 77.4078L20.1455 78.8963L20.1455 78.8963L18.8096 77.4078ZM9.2533 68.1959L10.7898 69.4763L9.2533 68.1959ZM15.6039 60.5749L17.1404 61.8552L17.1404 61.8552L15.6039 60.5749ZM12.9925 54.9995L12.9925 52.9995L12.9925 52.9995L12.9925 54.9995ZM12.75 52.9995C11.6454 52.9995 10.75 53.8949 10.75 54.9995C10.75 56.1041 11.6454 56.9995 12.75 56.9995L12.75 52.9995ZM9.79607 77.293L11.1694 75.8391L11.1694 75.8391L9.79607 77.293ZM15.2047 55.8179L13.9031 57.3364L15.2047 55.8179ZM24.6137 69.5115L17.4738 75.9194L20.1455 78.8963L27.2854 72.4884L24.6137 69.5115ZM10.7898 69.4763L17.1404 61.8552L14.0675 59.2945L7.71683 66.9156L10.7898 69.4763ZM12.9925 52.9995L12.75 52.9995L12.75 56.9995L12.9925 56.9995L12.9925 52.9995ZM11.1694 75.8391C9.38241 74.1512 9.21616 71.3647 10.7898 69.4763L7.71683 66.9156C4.79078 70.427 5.09992 75.6084 8.42274 78.747L11.1694 75.8391ZM17.4738 75.9194C15.6715 77.5369 12.9299 77.502 11.1694 75.8391L8.42274 78.747C11.6964 81.8391 16.7942 81.904 20.1455 78.8963L17.4738 75.9194ZM16.5063 54.2993C15.5277 53.4606 14.2814 52.9995 12.9925 52.9995L12.9925 56.9995C13.3265 56.9995 13.6495 57.119 13.9031 57.3364L16.5063 54.2993ZM17.1404 61.8552C19.0306 59.5869 18.7481 56.2209 16.5063 54.2993L13.9031 57.3364C14.4841 57.8344 14.5573 58.7067 14.0675 59.2945L17.1404 61.8552Z" fill="#1D1D1F"/>
      </g>
      <defs>
      <clipPath id="clip0_346_155436">
      <rect width="90" height="90" fill="white"/>
      </clipPath>
      </defs>
    </svg>
    <svg v-show="themeClass == 'themeDark'" width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g opacity="0.4" clip-path="url(#clip0_346_155436)">
      <circle cx="62.1964" cy="33.4815" r="18.5557" stroke="rgba(255, 255, 255, 0.80)" stroke-width="4"/>
      <path d="M45.0664 38.9043L21.6423 68.7885L28.1023 75.2485L57.9865 51.8244" stroke="rgba(255, 255, 255, 0.80)" stroke-width="4"/>
      <path d="M18.8096 77.4078L20.1455 78.8963L20.1455 78.8963L18.8096 77.4078ZM9.2533 68.1959L10.7898 69.4763L9.2533 68.1959ZM15.6039 60.5749L17.1404 61.8552L17.1404 61.8552L15.6039 60.5749ZM12.9925 54.9995L12.9925 52.9995L12.9925 52.9995L12.9925 54.9995ZM12.75 52.9995C11.6454 52.9995 10.75 53.8949 10.75 54.9995C10.75 56.1041 11.6454 56.9995 12.75 56.9995L12.75 52.9995ZM9.79607 77.293L11.1694 75.8391L11.1694 75.8391L9.79607 77.293ZM15.2047 55.8179L13.9031 57.3364L15.2047 55.8179ZM24.6137 69.5115L17.4738 75.9194L20.1455 78.8963L27.2854 72.4884L24.6137 69.5115ZM10.7898 69.4763L17.1404 61.8552L14.0675 59.2945L7.71683 66.9156L10.7898 69.4763ZM12.9925 52.9995L12.75 52.9995L12.75 56.9995L12.9925 56.9995L12.9925 52.9995ZM11.1694 75.8391C9.38241 74.1512 9.21616 71.3647 10.7898 69.4763L7.71683 66.9156C4.79078 70.427 5.09992 75.6084 8.42274 78.747L11.1694 75.8391ZM17.4738 75.9194C15.6715 77.5369 12.9299 77.502 11.1694 75.8391L8.42274 78.747C11.6964 81.8391 16.7942 81.904 20.1455 78.8963L17.4738 75.9194ZM16.5063 54.2993C15.5277 53.4606 14.2814 52.9995 12.9925 52.9995L12.9925 56.9995C13.3265 56.9995 13.6495 57.119 13.9031 57.3364L16.5063 54.2993ZM17.1404 61.8552C19.0306 59.5869 18.7481 56.2209 16.5063 54.2993L13.9031 57.3364C14.4841 57.8344 14.5573 58.7067 14.0675 59.2945L17.1404 61.8552Z" fill="rgba(255, 255, 255, 0.80)"/>
      </g>
      <defs>
      <clipPath id="clip0_346_155436">
      <rect width="90" height="90" fill="white"/>
      </clipPath>
      </defs>
    </svg>
    <p>暂无歌曲，快去点歌吧～</p>
  </div>
</template>

<script setup>
import { getCurrentInstance, onMounted, ref, defineEmits, defineProps, computed } from 'vue'
import SongItem from './item.vue'
import { useStore } from 'vuex'
import useOrder from '@/composables/useOrder'
import { sendLog } from '@/directives/v-log/log'
import get from 'lodash/get'
import { checkLandscapeOrPortrait } from '@/utils/device'
import eventBus from '@/utils/event-bus';
import { TSVehicleInstance } from '@/packages/TSJsbridge';
import store2 from 'store2'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'
import useDownload from '@/composables/useDownload';
import useVip from '@/composables/useVip';
const { getIsLocalSong } = useDownload();
const { showVipQrcode, isVipUser } = useVip()
import { RecycleScroller } from 'vue-virtual-scroller'
import { debounce } from 'lodash'

const props = defineProps({
  from: {
    type: String,
    default: '',
  }
})
const emit = defineEmits(['singer-click'])
const store = useStore()
let isLandscape = ref(false)

const { orderSong, stickSongToTop, deleteSong, orderedList } = useOrder()
const instance = getCurrentInstance()
const mediaVolume = computed(() => store.state.mediaVolume)
const isLogin = computed(() => !!store.state.userInfo.unionid)
const themeClass = computed(() => store.state.themeClass)
const mvIsHide = computed(() => store.state.mvIsHide)
const isVip = computed(() => !!store.state.vipInfo.end_time)
const isExpired = computed(() => store.state.vipInfo.expire)

const handleOrder = debounce(async (songItem, index) => {
  const { from } =  props;
  const { parent } = instance;
  
  if(!isLogin.value){
    if(from == 'mvOrder'){
      sendLog({
        event_type: '10000~50000',
        event_name: 1021,
        event_data: {
          str1: 'MV 页面',
          str2: '快速点歌-已点',
          str3: '点击 VIP 歌曲',
          str4: 'click',
          str5: 1,
          str7: songItem?.songid,
          str9: '手机端',
          str10: '1819'
        }
      })
    }else{
      sendLog({
        event_type: '10000~50000',
        event_name: 1021,
        event_data: {
          str1: '全局',
          str2: '已点',
          str3: '点击 VIP 歌曲',
          str4: 'click',
          str5: 1,
          str7: songItem?.songid,
          str9: '手机端',
          str10: '1802'
        }
      })
    }
    
  }else{
    if(from == 'mvOrder'){
      sendLog({
        event_type: '10000~50000',
        event_name: 1021,
        event_data: {
          str1: 'MV 页面',
          str2: '快速点歌-已点',
          str3: '点击 VIP 歌曲',
          str4: 'click',
          str5: 2,
          str6: isVip.value ? 2 : isExpired.value ? 4 : 1, 
          str7: songItem?.songid,
          str9: '手机端',
          str10: '1820'
        }
      })
    }else{
      sendLog({
        event_type: '10000~50000',
        event_name: 1021,
        event_data: {
          str1: '全局',
          str2: '已点',
          str3: '点击 VIP 歌曲',
          str4: 'click',
          str5: 2,
          str6: isVip.value ? 2 : isExpired.value ? 4 : 1, 
          str7: songItem?.songid,
          str9: '手机端',
          str10: '1803'
        }
      })
    }

    
  }
  

  sendLog({
    event_type: '10000~50000',
    event_name: 6012,
    event_data: {
      str1: '通用',
      str2: '已点/已唱弹窗',
      str3: '已点-点击歌曲，进入MV',
      str4: 'click',
      str5: songItem?.songid,
      str6: songItem?.is_vip == 1 ? 1 : 2
    }
  })
  
  // 提前拦截，不做后续处理
  if (!isVipUser.value) {
    const isLocal = await getIsLocalSong(songItem)
    if (songItem.is_vip && !isLocal) {
        if(!isLogin.value){
          if(from == 'mvOrder'){
              showVipQrcode({
              log: 'mv-已点-未登录',
            })
            return
          }
          showVipQrcode({
            log: '全局-已点-未登录',
          })
      }else{
        if(from == 'mvOrder'){
            showVipQrcode({
            log: 'mv-已点-已登录',
          })
          return
        }
        
        showVipQrcode({
          log: '全局-已点-已登录',
        })
      }
      // showVipQrcode()
      return
    }
  }
  // 进入mv使用记忆的多媒体值
  const storageMediaVolume = mediaVolume.value?mediaVolume.value : store2.get('storageMediaVolume')
  if (typeof storageMediaVolume === 'number' && TSVehicleInstance) {
    store.commit('UPDATE_MV_VIDEO_MEDIA', storageMediaVolume);
    TSVehicleInstance.setMediaVolume(storageMediaVolume);
  }

  
  
  if (from === 'mvOrder' && index === 0) {
    return;
  }

  if (from !== 'mvOrder') {
    eventBus.emit('close-order-song-control-popup');
    store.commit('UPDATE_IS_SING_STATUS', true);
    store.dispatch('searchTips/updateIsShowSingTips', false); // 关闭tips弹窗
  }
  
  if (index !== 0) {
    await store.commit('RESET_CURR_SONG_LRC')
  }

  await store.commit('UPDATE_CURR_IYRIC_INDEX', -1)

  orderSong(songItem, index);

  const { exposed } = parent;
  if (get(exposed, 'close', '') && typeof exposed.close === 'function') {
    exposed.close();
  }
}, 500, { leading: true, trailing: false });

const handleStickTop = (songItem,index) => {
  stickSongToTop(index)
  sendLog({
    event_type: '10000~50000',
    event_name: 10059,
    event_data: {
      str1: '已点',
      str2: '已点',
      str3: '置顶歌曲',
      str4: 'click',
    },
  })

  sendLog({
      event_type: '10000~50000',
      event_name: 6012,
      event_data: {
        str1: '通用',
        str2: '已点/已唱弹窗',
        str3: '已点-置顶歌曲',
        str4: 'click',
        str5: songItem.songid,
        str6: songItem.is_vip == 1 ? 1 : 2
      },
    })
  

  if(mvIsHide.value){
    sendLog({
      event_type: '10000~50000',
      event_name: 121,
      event_data: {
        str1: '通用',
        str2: '已点/已唱弹窗——已点',
        str3: '置顶点歌',
        str4: 'click',
        str5: songItem.songid || '',
        str6: !isVip.value ? 2 : 1
      }
    })

    sendLog({
      event_type: '10000~50000',
      event_name: 6002,
      event_data: {
        str1: '通用',
        str2: '已点/已唱弹窗',
        str3: '已点-置顶点歌',
        str4: 'click',
        str5: songItem.songid || '',
        str6: !isVip.value ? 2 : 1
      },
    })
  }else{
    sendLog({
      event_type: '10000~50000',
      event_name: 121,
      event_data: {
        str1: '欢唱页',
        str2: '已点',
        str3: '置顶歌曲',
        str4: 'click',
        str5: songItem.songid || ''
      }
    })
  }
}

const handleDelete = (songItem,index) => {
  deleteSong(index)
  sendLog({
    event_type: '10000~50000',
    event_name: 10060,
    event_data: {
      str1: '已点',
      str2: '已点',
      str3: '删除歌曲',
      str4: 'click',
    },
  })

  if(!mvIsHide.value){
    sendLog({
      event_type: '10000~50000',
      event_name: 6007,
      event_data: {
        str1: '欢唱页',
        str2: '已点',
        str3: '已点-删除歌曲',
        str4: 'click',
      },
    })
  }else{
    sendLog({
      event_type: '10000~50000',
      event_name: 6002,
      event_data: {
        str1: '通用',
        str2: '已点/已唱弹窗',
        str3: '已点-删除歌曲',
        str4: 'click',
        str5: songItem.songid || '',
        str6: !isVip.value ? 2 : 1
      },
    })

    sendLog({
      event_type: '10000~50000',
      event_name: 6012,
      event_data: {
        str1: '通用',
        str2: '已点/已唱弹窗',
        str3: '已点-删除歌曲',
        str4: 'click',
        str5: songItem.songid,
        str6: songItem.is_vip == 1 ? 1 : 2
      },
    })
  }
}

const handleSingerClick = (v) => {
  emit('singer-click', v)
}

onMounted(async () => {
  isLandscape.value = checkLandscapeOrPortrait() === 'landscape'
})

//数据行实际高度
const tableItemSize = ref((document.documentElement.clientWidth || document.body.clientWidth) * 137 / 1920);
</script>

<style lang="stylus" scoped>
.song-list
  height 650px
  padding 0 48px
  overflow hidden
  .vue-recycle-scroller
    height 100%
    .vue-recycle-scroller__item-wrapper
      &::-webkit-scrollbar
        display none
  @media screen and (max-width: 1950px) and (min-width: 1700px) and (max-height: 720px) and (min-height: 650px)
    height 83% !important
  .loadmore
    &::-webkit-scrollbar
      display none
.empty
  display flex
  flex-direction column
  justify-content center
  align-items center
  font-size 28px
  color rgba(255, 255, 255, 0.40)
  text-align center
  padding-top 184px
  @media screen and (max-width: 1200px)
    padding-top 238px
  svg
    width 80px
    height 80px
    margin-bottom 40px
  p
    height 32px
    line-height 32px
.theme-themeLight
  .empty
    color rgba(29, 29, 31, 0.4)
</style>
