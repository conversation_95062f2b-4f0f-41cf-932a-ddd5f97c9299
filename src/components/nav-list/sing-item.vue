<template>
  <div class="nav-item-sing"
    @touchstart="handleTouchStart"
    @touchend="handleGoSinging"
  >
    <div class="nav-item-sing-content">
      <!-- 本车常唱 -->
      <p class="nav-item-sing-content-title">唱过的歌</p>
      <div v-if="isLogin">
        <div class="nav-item-sing-content-name"  v-if="song.sing_cnt">
          <span class="name">{{ songname ? songname : '无歌曲' }}</span>&nbsp;•&nbsp;
          <div>演唱过 
            <!-- class="cnt" -->
            <span>{{ song.sing_cnt > 99 ? '99+' : song.sing_cnt }}</span>
             次
          </div>
        </div>
        <div class="nav-item-sing-content-name"  v-else>
          <span class="name"></span>
        </div>
      </div>
      <div v-else class="nav-item-sing-content-name">长城车主专享歌单</div>
    </div>
  </div>
</template>

<script setup>
import useLoginValid from '@/composables/useLoginValid'
import { sendLog } from '@/directives/v-log/log'
import { defineEmits, defineExpose, defineProps, toRefs } from 'vue'
import { useRouter } from 'vue-router'

const props = defineProps({
  singer: {
    type: String,
    default: ''
  },
  songname: {
    type: String,
    default: ''
  },
  isLogin: {
    type: Boolean,
    default: false
  },
  song: {
    type: Object,
    default: () => {}
  },
})
const { isLogin } = toRefs(props)
const { showLoginQrcode } = useLoginValid()
const router = useRouter()
const handleTouchStart = (event) => {
  // 判断是否有两个手指按下
  if (event.touches.length === 2) {
    this.handleTwoFingerTap(event);
  }
}
const handleTwoFingerTap = (event) => {
  // 在这里处理双指点击逻辑
  console.log("双指点击触发", event);
  event.preventDefault(); // 可选：阻止默认行为
}

const handleGoSinging = () => {
  router.push({
    name: 'singing'
  })
  // if (isLogin.value) {
  //   router.push({
  //     name: 'singing'
  //   })
  // } else {
  //   showLoginQrcode({
  //     log: '常唱列表'
  //   })
  // }
  
  // if (isLogin.value) {
  //   router.push({
  //     name: 'singing'
  //   })
  // } else {
  //   showLoginQrcode({
  //     log: '常唱列表-未登录'
  //   })
  //   sendLog({
  //     event_type: '10000~50000',
  //     event_name: 1021,
  //     event_data: {
  //       str1: '首页',
  //       str2: '唱过的歌',
  //       str3: '点击 VIP 歌曲',
  //       str4: 'click',
  //       str5: 1,
  //       str9: '手机端',
  //       str10: '1806'
  //     }
  //   })
  // }
  sendLog({
    event_type: '10000~50000',
    event_name: 10007,
    event_data: {
      str1: '首页',
      str2: '常唱',
      str3: '进入常唱',
      str4: 'click',
    },
  })
  sendLog({
    event_type: '10000~50000',
    event_name: 6001,
    event_data: {
      str1: '首页',
      str2: '唱过的歌',
      str3: '唱过的歌',
      str4: 'click',
      str5: !isLogin.value ? 2 : 1
    },
  })
}

defineEmits(['goSinging'])

defineExpose({
  handleGoSinging,
  handleTouchStart,
  handleTwoFingerTap,
})
</script>

<style lang="stylus" scoped>
.nav-item-sing
  width 560px
  height 320px
  position relative
  background url('https://qncweb.ktvsky.com/20250319/vadd/b416f9283495b2851a3068c5a68f55cb.png') no-repeat
  // background url('https://qncweb.ktvsky.com/20240322/vadd/8525bc095458717e0b3eecf3061773f1.png') no-repeat
  background-size 100% auto!important
  background-position center bottom!important
  &-content
    &-title
      width 200px
      height 49px
      line-height 49px
      margin 195px 0 8px 42px
      font-size 42px
      color rgba(108, 64, 12, 1)
      font-weight 400
    &-name
      width auto
      height 28px
      line-height 28px
      margin-left 42px
      font-weight 400
      font-size 24px
      color rgba(108, 64, 12, 1)
      display flex
      align-items center
      .name
        display block
        max-width 330px
        white-space nowrap
        overflow hidden
        text-overflow ellipsis
      .cnt
        color #F0D290
  // @media screen and (max-width 1200px) and (min-height 1000px)
  //   width 344px
  //   height 230px
  //   border-radius 10px
  //   &-content-title
  //     font-size 30px
  //     height 35px
  //     line-height 35px
  //     margin 144px 0 6px 24px
  //   &-content-name
  //     font-size 18px
  //     height 21px
  //     line-height 21px
  //     margin-left 24px
  //     .name
  //       max-width 200px
</style>
