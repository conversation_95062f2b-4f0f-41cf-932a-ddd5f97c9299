<template>
  <div class="nav-item-sing" @click="handleGoSinging">
    <div class="nav-item-sing-content">
      <!-- 本车常唱 -->
      <p class="nav-item-sing-content-title">唱过的歌</p>
      <div class="nav-item-sing-content-name"  v-if="song.sing_cnt">
        <span class="name">{{ songname ? songname : '无歌曲' }}</span>&nbsp;•&nbsp;
        <div>演唱过 <span class="cnt">{{ song.sing_cnt }}</span> 次</div>
      </div>
      <div class="nav-item-sing-content-name"  v-else>
        <span class="name">{{ songname ? songname : '无歌曲' }}</span>&nbsp;-&nbsp;
        <div>{{ singer ? singer : '快去点歌吧' }}</div>
      </div>
    </div>
    <!-- <img class="nav-item-sing-order" src="https://qncweb.ktvsky.com/20230706/vadd/9a7a9fbd51868d5d51779c41a5a854b0.png" @click.stop="handleOrder"> -->
  </div>
</template>

<script>
import { useRouter } from 'vue-router'
import useLoginValid from '@/composables/useLoginValid'
import { sendLog } from '@/directives/v-log/log'

export default {
  name: 'NavSingItem',
  props: {
    singer: {
      type: String,
      default: ''
    },
    songname: {
      type: String,
      default: ''
    },
    song: {
      type: Object,
      default: () => {}
    },
  },
  setup() {
    const router = useRouter()
    const { showLoginQrcode, isLogin } = useLoginValid()

    const handleGoSinging = () => {
      if (!isLogin.value) {
        sendLog({
          event_type: '10000~50000',
          event_name: 30182,
          event_data: {
            str1: '首页',
            str2: '唱过的歌',
            str3: '未登录二维码展示',
            str4: 'show',
          },
        })
        showLoginQrcode({
          log: '未登录-唱过的歌',
          from: 'singing'
        })
        return
      }
      router.push({
        name: 'singing'
      })
      sendLog({
        event_type: '10000~50000',
        event_name: 10007,
        event_data: {
          str1: '首页',
          str2: '常唱',
          str3: '进入常唱',
          str4: 'click',
        },
      })
    }

    // const isCanOrder = () => {
    //   console.log(song.value)
    //   if (!song.value || !Object.keys(song.value).length || !song.value.songid) {
    //     // 无歌曲
    //     Toast('本车常唱暂无歌曲，先点歌吧')
    //     return false
    //   }
      
    //   return validSong(song.value)
    // }

    // const handleOrder = () => {
    //   if (!isLogin.value) {
    //     showVipQrcode()
    //     return
    //   }
    //   // 歌曲数据校验
    //   if (isCanOrder()) {
    //     addSong(song.value, {
    //       from: {
    //         song_list_source: 5,
    //       }
    //     })
    //     orderSong(song.value, orderedListNumber.value - 1)
    //   }
    // }

    return {
      handleGoSinging,
      // handleOrder,
    }
  }
}
</script>

<style lang="stylus" scoped>
.nav-item-sing
  width 560px
  height 320px
  position relative
  // background url('https://qncweb.ktvsky.com/20231206/vadd/3e69d367f9e933fedb5d8ac2998eecd9.png') no-repeat
  background url('https://qncweb.ktvsky.com/20240322/vadd/8525bc095458717e0b3eecf3061773f1.png') no-repeat
  background-size 100% 100%
  &-content
    &-title
      width 200px
      height 49px
      line-height 49px
      margin 195px 0 8px 42px
      font-size 42px
      color #FFFFFF
      font-weight 400
    &-name
      width auto
      height 28px
      line-height 28px
      margin-left 42px
      font-weight 400
      font-size 24px
      color #FFFFFF
      display flex
      align-items center
      .name
        display block
        max-width 330px
        white-space nowrap
        overflow hidden
        text-overflow ellipsis
      .cnt
        color #F0D290
  @media screen and (max-width 1200px) and (min-height 1000px)
    width 344px
    height 230px
    border-radius 10px
    background-size 100% auto
    background-position bottom center
    &-content-title
      font-size 30px
      height 35px
      line-height 35px
      margin 144px 0 6px 24px
    &-content-name
      font-size 18px
      height 21px
      line-height 21px
      margin-left 24px
      .name
        max-width 200px
  // &-order
  //   width 68px
  //   height 68px
  //   position absolute
  //   top 66px
  //   right 30px
</style>
