<template>
  <div class="nav-list">
    <User />
    
    <div
      class="nav-list-item"
      v-for="(nav, index) in navList"
      :key="index"
      :style="{background: `url(${nav.image}) no-repeat`, backgroundSize: '100% 100%'}"
      @touchstart.prevent="handleTouchStart"
      @touchend.prevent="handleClickNav(nav)"
    >
      <p class="zh">{{ nav.text }}</p>
      <p class="en">{{ nav.en }}</p>
    </div>

    <NavSingItem 
      :isLogin="isLogin"
      :singer="firstSong.singer"
      :songname="firstSong.music_name"
      :song="firstSong"
    />
  </div>
</template>

<script setup>
import useLoginValid from '@/composables/useLoginValid'
import useQRCode from '@/composables/useQRCode'
import { sendLog } from '@/directives/v-log/log'
import { getCarplayInfo } from '@/service/carplay-info'
import { withTimeoutHandling } from '@/utils/promiseUtils'
import Toast from '@/utils/toast'
import { computed, defineEmits, defineProps, onMounted, ref, watch } from 'vue'
import { useStore } from 'vuex'
import NavSingItem from './sing-item.vue'
import User from './user.vue'

const props = defineProps({
  firstSong: {
    type: Object,
    default: () => ({
      singer: '',
      music_name: '',
    }),
  },
})

const emit = defineEmits(['click-nav'])

const navList = [
  {
    text: '歌手',
    en: '薛之谦、王心凌、林俊杰...',
    image: 'https://qncweb.ktvsky.com/20250319/vadd/430f06212a0878fa4b314a9505fa63d8.png', // 'https://qncweb.ktvsky.com/20240322/vadd/39be4b89d6fb7865b67d9096cc0f263a.png',
    pathName: 'singer',
    isSupport: true,
    type: 'page',
  },
]

const store = useStore()
const { isLogin } = useLoginValid()
const net_status = computed(() => store.state.base.net_status)
const { getQRCodeURL } = useQRCode()
const carplayInfo = computed(() => store.state.carplayInfo)

let qrCodeURL = ref('')
let isRequest = ref(false)

const getLoginQrcode = async (payload) => {
  try {
    if (isRequest.value || !net_status.value && payload !== 'reload') return;

    isRequest.value = true
    
    let pay_qr
    if (payload === 'reload') {
      pay_qr = (await getCarplayInfo(true)).data.pay_qr
    } else {
      pay_qr = carplayInfo.value.pay_qr || (await withTimeoutHandling(getCarplayInfo())).data.pay_qr
    }
    if (pay_qr) {
      const qrCodeData = await withTimeoutHandling(getQRCodeURL(`${pay_qr}&fr=mine`))
      if (qrCodeData) {
        qrCodeURL.value = qrCodeData
      }
    }
  } catch (error) {
    console.log('getLoginQrcode error', error)
  } finally {
    isRequest.value = false
  }
}

const handleTouchStart = (event) => {
  // 判断是否有两个手指按下
  if (event.touches.length === 2) {
    handleTwoFingerTap(event);
  }
}
const handleTwoFingerTap = (event) => {
  // 在这里处理双指点击逻辑
  console.log("双指点击触发", event);
  event.preventDefault(); // 可选：阻止默认行为
}

const handleClickNav = (nav) => {
  setTimeout(() => {
    if (nav.isSupport) {
      emit('click-nav', nav)
      if (nav.pathName === 'singer') {
        sendLog({
          event_type: '10000~50000',
          event_name: 10009,
          event_data: {
            str1: '首页',
            str2: '歌星',
            str3: '进入歌星页',
            str4: 'click',
          },
        })

        sendLog({
          event_type: '10000~50000',
          event_name: 6001,
          event_data: {
            str1: '首页',
            str2: '歌手',
            str3: '歌手',
            str4: 'click',
          }
        })

        sendLog({
          event_type: '10000~50000',
          event_name: 6005,
          event_data: {
            str1: '歌手列表页',
            str2: '歌手分类',
            str3: '进入歌手分类页',
            str4: 'show',
          }
        })
      }
      return
    }
    Toast('此功能暂未完成移植')
  }, 50)
}

watch(isLogin, (val) => {
  if (!val) {
    getLoginQrcode()
  }
}, { immediate: true })

watch(net_status, (val) => {
  if (val) {
    getLoginQrcode()
  }
}, {
  immediate: true,
  deep: true
})

onMounted(() => {
  if (!isLogin.value) {
    getLoginQrcode()
    console.log(net_status.value, '1108')
    sendLog({
      event_type: '10000~50000',
      event_name: 6001,
      event_data: {
        str1: '首页',
        str2: '登录卡片',
        str3: '展示',
        str4: 'show',
      }
    })
  }
})
</script>

<style lang="stylus" scoped>
.nav-list
  display flex
  justify-content space-between
  color #ffffff
  margin-bottom 48px
  @media screen and (max-width 1200px)
    flex-flow wrap
  &-item
    display flex
    flex-direction column
    align-items left
    width 560px
    height 320px
    padding 195px 0 0 42px
    background-size 100% auto!important
    background-position center bottom!important
    img
      width 420px
      height 120px
      margin-left 28px
    .zh
      font-size 42px
      color #ffffff
    .en
      font-size 24px
      opacity 0.8
</style>
