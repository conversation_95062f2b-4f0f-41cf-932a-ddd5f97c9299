<template>
  <div>
    <div
      v-show="isLogin"
      class="nav-list-user"
      :class="[isVip && 'active-vip']"
      @touchstart="handleTouchStart"
      @touchend="handleGoMine"

    >
      <div class="nav-list-user-top-img">
        <img v-if="userInfo.avatar" :src="userInfo.avatar" alt="我的" />
        <img
          v-else
          src="https://qncweb.ktvsky.com/20230424/vadd/608c9e8b386f6493599d2cc79ba361d4.png"
          alt="我的"
        />
      </div>
      <div class="nav-list-user-name" :class="{'nav-list-user-name-vip':isVip}">
        <p>{{ isLogin ? userInfo.username : '未登录' }}</p>
        <p aria-label="vip-period">
          {{
            isVip
              ? `会员有效期至：${end_time}`
              : isExpired
              ? '您的会员已过期'
              : '未开通 VIP'
          }}
        </p>
      </div>
      <div
        class="nav-list-user-vip-tips"
        @click.stop="handleOpenVip"
        v-if="!isVip"
      >
        <div class="tips-left" v-if="isVip">
          <p>倾心回馈，感恩一路有你</p>
          <p>续费低至<span>0.2元/天</span></p>
        </div>
        <template v-else>
          <div class="tips-left" v-if="isExpired">
            <p>会员权益全新升级！</p>
            <p>6大权益低至 <span>0.2元/天</span></p>
          </div>
          <div class="tips-left" v-else>
            <p>您有一张VIP卡待领取</p>
            <p><span>6</span> 项会员权益，尽享奢华体验</p>
          </div>
        </template>
        <div class="tips-right">
          <span>立即查看</span>
          <img
            src="https://qncweb.ktvsky.com/20241122/vadd/a9685d99927b806734be96910e748100.png"
            alt=""
          />
        </div>
      </div>
      <div v-if="isLogin" class="nav-list-user-vip">
        <img
          src="https://qncweb.ktvsky.com/20231206/vadd/56dc0bc6477bf2c7a6c4fcdc8360804e.png"
        />
      </div>
    </div>
    <div v-show="!isLogin" class="nav-list-unlogin">
      <!-- <div class="nav-list-unlogin-left">
        <p>微信扫码登录</p>
        <p>解锁点歌特权</p>
      </div> -->
      <div class="nav-list-unlogin-right">
        <img v-if="net_status" :src="qrCodeURL">
        <div v-else class="net-error" @click="getLoginQrcode('reload')">
          <svg :class="isRequest && 'active'" width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g opacity="0.4" clip-path="url(#clip0_1270_101510)">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M50.9798 10.5088C49.0366 10.1743 47.0387 10 45 10C25.67 10 10 25.67 10 45C10 56.1164 15.1825 66.0224 23.2632 72.4337L27.1902 70.3766C19.2129 64.7677 14 55.4926 14 45C14 27.8792 27.8792 14 45 14C45.4133 14 45.8247 14.0081 46.2341 14.0241L50.9798 10.5088ZM62.6381 19.5035C70.7122 25.0996 76 34.4323 76 45C76 62.1208 62.1208 76 45 76C44.4595 76 43.9222 75.9862 43.3885 75.9588L38.697 79.434C40.7416 79.8058 42.8481 80 45 80C64.33 80 80 64.33 80 45C80 33.8105 74.7491 23.8474 66.577 17.4403L62.6381 19.5035Z" fill="black" style="fill:black;fill-opacity:1;"/>
            <path d="M45.0424 80L48.3127 72.3843C48.3712 72.2479 48.4006 72.1796 48.4134 72.1425C48.7162 71.2627 47.8202 70.4473 46.9728 70.8315C46.9371 70.8477 46.8719 70.8833 46.7416 70.9545L37.0398 76.2512C36.3988 76.6012 36 77.2732 36 78.0035C36 78.5837 36.5272 78.9725 37.0924 79.103C39.6336 79.6899 42.2806 80 45 80C45.0141 80 45.0283 80 45.0424 80Z" fill="black" style="fill:black;fill-opacity:1;"/>
            <path d="M44.5523 10.0028L41.2832 17.6158C41.2246 17.7522 41.1953 17.8204 41.1825 17.8575C40.8796 18.7373 41.7757 19.5527 42.623 19.1685C42.6588 19.1523 42.7239 19.1167 42.8543 19.0455L52.5561 13.7488C53.1971 13.3988 53.5959 12.7268 53.5959 11.9965C53.5959 11.3276 52.9615 10.9029 52.3073 10.7639C49.9505 10.2634 47.506 10 45 10C44.8505 10 44.7013 10.0009 44.5523 10.0028Z" fill="black" style="fill:black;fill-opacity:1;"/>
            </g>
            <defs>
            <clipPath id="clip0_1270_101510">
            <rect width="90" height="90" fill="white" style="fill:white;fill-opacity:1;"/>
            </clipPath>
            </defs>
          </svg>
          <p>网络异常</p>
          <p>点击刷新二维码</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import useLoginValid from '@/composables/useLoginValid'
import useQRCode from '@/composables/useQRCode'
import useVip from '@/composables/useVip'
import { sendLog } from '@/directives/v-log/log'
import { getCarplayInfo } from '@/service/carplay-info'
import eventBus from '@/utils/event-bus'
import { computed, onMounted, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'

const router = useRouter()
const store = useStore()
const { showLoginQrcode, isLogin } = useLoginValid()
const { showVipQrcode } = useVip()
const { getQRCodeURL } = useQRCode()

const net_status = computed(() => store.state.base.net_status)
const userInfo = computed(() => store.state.userInfo)
const vipInfo = computed(() => store.state.vipInfo)
const isVip = computed(() => !!vipInfo.value.end_time)
const carplayInfo = computed(() => store.state.carplayInfo)
const end_time = computed(() => isVip.value ? vipInfo.value.end_time.split(' ')[0] : '')
const isExpired = computed(() => vipInfo.value.expire)

const qrCodeURL = ref('https://qncweb.ktvsky.com/20211130/vadd/82b806b8bf560e49df69162835879c65.png')
const isRequest = ref(false)

const getLoginQrcode = async (payload) => {
  try {
    if (isRequest.value || !net_status.value && payload !== 'reload') return;

    isRequest.value = true
    
    let pay_qr
    if (payload === 'reload') {
      pay_qr = (await getCarplayInfo(true)).data.pay_qr
    } else {
      pay_qr = carplayInfo.value.pay_qr || (await getCarplayInfo()).data.pay_qr
    }
    if (pay_qr) {
      const qrCodeData = await getQRCodeURL(`${pay_qr}&fr=mine&log=1843`)
      if (qrCodeData) {
        qrCodeURL.value = qrCodeData

        sendLog({
          event_type: '10000~50000',
          event_name: 1021,
          event_data: {
            str1: '首页',
            str2: '我的',
            str3: '扫码登录',
            str4: 'show',
            str5: 1,
            str9: '手机端',
            str10: '1843'
          }
        })
      }
    }
  } catch (error) {
    console.log('getLoginQrcode error', error)
  } finally {
    isRequest.value = false
  }
}
const handleTouchStart = (event) => {
  // 判断是否有两个手指按下
  if (event.touches.length === 2) {
    handleTwoFingerTap(event);
  }
}
const handleTwoFingerTap = (event) => {
  // 在这里处理双指点击逻辑
  console.log("双指点击触发", event);
  event.preventDefault(); // 可选：阻止默认行为
}

const handleGoMine = () => {
  sendLog({
    event_type: 'click',
    event_name: 210,
  })
  if (!isLogin.value) {
    showLoginQrcode({
      log: '我的入口-头像',
    })
    return
  }
  router.push({
    name: 'mine',
  })
  sendLog({
    event_type: '10000~50000',
    event_name: 10010,
    event_data: {
      str1: '首页',
      str2: '我的入口',
      str3: '进入我的',
      str4: 'click',
    },
  })
  
  sendLog({
    event_type: '10000~50000',
    event_name: 6001,
    event_data: {
      str1: '首页',
      str2: '我的',
      str3: '我的',
      str4: 'click',
    },
  })
  eventBus.emit('handle-home-user-enter')
}

const handleOpenVip = () => {
  sendLog({
    event_type: 'click',
    event_name: 201,
    event_data: {
      type: 4,
    },
  })
  showVipQrcode({
    log: '我的入口-头像',
  })
  sendLog({
    event_type: '10000~50000',
    event_name: 10011,
    event_data: {
      str1: '首页',
      str2: '我的开通会员',
      str3: '点击开通会员',
      str4: 'click',
    },
  })

  sendLog({
      event_type: '10000~50000',
      event_name: 1021,
      event_data: {
        str1: '首页',
        str2: '用户卡片运营',
        str3: '触发 VIP 弹窗',
        str4: 'click',
        str5: 2,
        str6: isVip.value ? 2 : isExpired.value ? 4 : 1,
        str9: '车机端',
        str10: '1835'
      }
    })
}
watch(isLogin, (val) => {
  if (!val) {
    getLoginQrcode()
  }
}, { immediate: true })

onMounted(() => {
  if (!isLogin.value) {
    getLoginQrcode()
  }
})
</script>

<style lang="stylus" scoped>
.nav-list-user-name-vip
  margin-top: 30px
.nav-list
  &-user
    width 560px
    height 320px
    display flex
    flex-direction column
    align-items center
    position relative
    background url('../../assets/logined-new.png') no-repeat center bottom
    background-size 100% 100%
    &-top-img
      width 110px
      height 110px
      border-radius 100%
      overflow hidden
      margin 12px 0 8px
      img
        width 100%
        height 100%
    &-vip
      width 50px
      height 26px
      position absolute
      top 20px
      right 20px
      filter grayscale(100%)
      img
        width 100%
        height 100%
    &-name
      p
        width 439px
        text-align center
        &:nth-child(1)
          font-size 28px
          font-weight 400
          color #fff
          letter-spacing 0
          height 33px
          line-height 33px
          max-width 439px
          white-space nowrap
          overflow hidden
          text-overflow ellipsis
        &:nth-child(2)
          font-size 22px
          font-weight 400
          color #fff
          opacity 0.8
          margin-top 8px
          height 26px
          line-height 26px
    &-vip-tips
      width 510px
      height 92px
      margin 0 auto
      background linear-gradient(92.85deg, #F6E4D0 1.33%, #DDB793 100.32%);
      border-radius 20px
      display flex
      align-items center
      justify-content center
      font-size 20px
      font-weight 400
      color rgba(88, 45, 4, 1)
      letter-spacing 0
      padding 0 24px
      position absolute
      bottom 13px
      left 50%
      transform translateX(-50%)

      .tips-left
        flex 1
        display flex
        flex-direction column
        align-items center
        justify-content center
        p
          width 100%
        p:nth-child(1)
          font-size 24px
          font-weight 700
        p:nth-child(2)
          span
            color rgba(216, 0, 0, 1)
            font-weight 700
      .tips-right
        width auto
        height auto
        padding 12px 16px
        border 1px solid rgba(88, 45, 4, 1)
        border-radius 30px
        display flex
        align-items center
        justify-content center
        font-weight 400
        color #000
        span
          width 80px
          // 不换行
          white-space nowrap
          overflow hidden
          text-overflow ellipsis
        img
          width 20px
          height 20px

    &-button
      width 180px
      height 60px
      margin-top 33px
      background linear-gradient(90deg, #FFDAB3 -39.21%, #C69156 137.77%)
      border-radius 40px
      font-size 24px
      font-weight 400
      color #000
      letter-spacing 0
      display flex
      align-items center
      justify-content center
  .active-vip
    .nav-list-user-vip
      filter grayscale(0%)
    .nav-list-user-name
      p
        &:nth-child(1)
          height 38px
          line-height 38px
          font-size 32px
          color #FFD596
        &:nth-child(2)
          height 28px
          line-height 28px
          font-size 24px
          margin-top 16px
          opacity 0.8
  &-unlogin
    width 560px
    height 320px
    background #2E3033
    border-radius 10px
    padding 195px 0 0 42px
    background url('https://qncweb.ktvsky.com/20250319/vadd/cb22329e541ef123d4deade4c3589d01.png') no-repeat
    // background url(https://qncweb.ktvsky.com/20240415/other/bec3639d74b890d8744dd756675c7858.png) no-repeat
    background-size 100% auto!important
    background-position center bottom!important
    position relative
    &-left
      p
        &:nth-child(1)
          width auto
          height 49px
          line-height 49px
          font-size 34px
          font-weight 400
          margin-bottom 8px
        &:nth-child(2)
          height 28px
          line-height 28px
          font-size 24px
          color rgba(255, 255, 255, 0.80)
          letter-spacing 2px
    &-right
      width 150px
      height 150px
      background #fff
      display flex
      justify-content center
      align-items center
      border-radius 6px
      position absolute
      // top 63px
      // right 71px
      top 68px
      right 45px
      img
        width 150px
        height 150px
        padding 0px
        max-width unset
.net-error
  zoom 0.7
</style>
