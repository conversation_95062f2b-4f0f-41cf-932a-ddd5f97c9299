<template>
    <SecContainer
      :title="title"
      class="sec-gusse-sing"
    >
      <template #header>
        <div v-if="!showAllData" class="sec-gusse-sing-change" @click.stop="handlechangePagenation">
          <span>换一批</span>
          <svg v-show="themeClass === 'themeLight'" width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M30.5 6.99998V13C30.5 13.3978 30.342 13.7793 30.0607 14.0606C29.7794 14.3419 29.3978 14.5 29 14.5H23C22.6022 14.5 22.2207 14.3419 21.9394 14.0606C21.6581 13.7793 21.5 13.3978 21.5 13C21.5 12.6022 21.6581 12.2206 21.9394 11.9393C22.2207 11.658 22.6022 11.5 23 11.5H25.1375L22.7625 9.32748C22.7463 9.31248 22.73 9.29748 22.715 9.28123C21.6138 8.18068 20.259 7.36764 18.7697 6.91365C17.2805 6.45967 15.7025 6.37865 14.1746 6.67773C12.6467 6.97681 11.2157 7.64682 10.0076 8.6288C8.79944 9.61079 7.85115 10.8747 7.24616 12.3092C6.64117 13.7437 6.39801 15.305 6.53809 16.8555C6.67817 18.4061 7.19719 19.8985 8.04949 21.2014C8.90178 22.5043 10.0612 23.5777 11.4258 24.3273C12.7904 25.0769 14.3183 25.4796 15.875 25.5H16C18.4249 25.5058 20.7591 24.5784 22.5188 22.91C22.808 22.6366 23.194 22.4894 23.5918 22.5007C23.9896 22.5119 24.3667 22.6807 24.64 22.97C24.9134 23.2592 25.0606 23.6452 25.0493 24.043C25.0381 24.4408 24.8693 24.8179 24.58 25.0912C22.2636 27.2867 19.1916 28.5073 16 28.5H15.8288C13.7826 28.4711 11.7748 27.9404 9.98163 26.9543C8.18848 25.9682 6.66486 24.557 5.54454 22.8445C4.42423 21.132 3.74148 19.1706 3.55626 17.1326C3.37103 15.0946 3.68899 13.0423 4.48222 11.1559C5.27545 9.26953 6.51969 7.60673 8.10567 6.31352C9.69166 5.02032 11.5709 4.13628 13.5784 3.73902C15.5858 3.34176 17.6601 3.44343 19.6191 4.03511C21.5781 4.62679 23.3619 5.69037 24.8138 7.13248L27.5 9.58998V6.99998C27.5 6.60216 27.6581 6.22062 27.9394 5.93932C28.2207 5.65802 28.6022 5.49998 29 5.49998C29.3978 5.49998 29.7794 5.65802 30.0607 5.93932C30.342 6.22062 30.5 6.60216 30.5 6.99998Z" fill="#1D1D1F" fill-opacity="0.9" style="fill:#1D1D1F;fill:color(display-p3 0.1137 0.1137 0.1216);fill-opacity:0.9;"/>
          </svg>
          <svg v-show="themeClass === 'themeDark'" width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M30.5 6.99998V13C30.5 13.3978 30.342 13.7793 30.0607 14.0606C29.7794 14.3419 29.3978 14.5 29 14.5H23C22.6022 14.5 22.2207 14.3419 21.9394 14.0606C21.6581 13.7793 21.5 13.3978 21.5 13C21.5 12.6022 21.6581 12.2206 21.9394 11.9393C22.2207 11.658 22.6022 11.5 23 11.5H25.1375L22.7625 9.32748C22.7463 9.31248 22.73 9.29748 22.715 9.28123C21.6138 8.18068 20.259 7.36764 18.7697 6.91365C17.2805 6.45967 15.7025 6.37865 14.1746 6.67773C12.6467 6.97681 11.2157 7.64682 10.0076 8.6288C8.79944 9.61079 7.85115 10.8747 7.24616 12.3092C6.64117 13.7437 6.39801 15.305 6.53809 16.8555C6.67817 18.4061 7.19719 19.8985 8.04949 21.2014C8.90178 22.5043 10.0612 23.5777 11.4258 24.3273C12.7904 25.0769 14.3183 25.4796 15.875 25.5H16C18.4249 25.5058 20.7591 24.5784 22.5188 22.91C22.808 22.6366 23.194 22.4894 23.5918 22.5007C23.9896 22.5119 24.3667 22.6807 24.64 22.97C24.9134 23.2592 25.0606 23.6452 25.0493 24.043C25.0381 24.4408 24.8693 24.8179 24.58 25.0912C22.2636 27.2867 19.1916 28.5073 16 28.5H15.8288C13.7826 28.4711 11.7748 27.9404 9.98163 26.9543C8.18848 25.9682 6.66486 24.557 5.54454 22.8445C4.42423 21.132 3.74148 19.1706 3.55626 17.1326C3.37103 15.0946 3.68899 13.0423 4.48222 11.1559C5.27545 9.26953 6.51969 7.60673 8.10567 6.31352C9.69166 5.02032 11.5709 4.13628 13.5784 3.73902C15.5858 3.34176 17.6601 3.44343 19.6191 4.03511C21.5781 4.62679 23.3619 5.69037 24.8138 7.13248L27.5 9.58998V6.99998C27.5 6.60216 27.6581 6.22062 27.9394 5.93932C28.2207 5.65802 28.6022 5.49998 29 5.49998C29.3978 5.49998 29.7794 5.65802 30.0607 5.93932C30.342 6.22062 30.5 6.60216 30.5 6.99998Z" fill="#fff" fill-opacity="0.6" style="fill:#fff;"/>
          </svg>
        </div>
      </template>
      <div class="sec-gusse-sing-list">
        <RecycleScroller
          v-if="isFastList"
          class="sec-gusse-sing-list"
          :items="guessSongShowList"
          :item-size="tableItemSize"
          key-field="i"
          v-slot="{ item: songItem }"
          :buffer="2000"
        >
          <SongItem
              className="sec-gusse-sing-list-item"
              :songItem="songItem"
              :key="songItem.i"
              :index="songItem.i"
              :log-from="{
                song_list_source: song_list_source ? song_list_source : (isGuessSongList && songItem.sing_cnt) ? 5 : 9,
                str1: pageRoute
              }"
              :ponitActionLog="ponitActionLog"
              @singer-click="handleClickSinger"
          />
        </RecycleScroller>
        <SongItem
          v-else
          :renderType="renderType"
          className="sec-gusse-sing-list-item"
          v-for="(songItem, ind) in guessSongShowList"
          :key="ind"
          :index="ind + 1"
          :songItem="songItem"
          :log-from="{
            song_list_source: pageRoute =='2-search-guess' && isLogin ? 18 : pageRoute =='2-search-guess' && !isLogin ? 19 : song_list_source ? song_list_source : (isGuessSongList && songItem.sing_cnt) ? 14 : 9,
            str1: pageRoute
          }"
          :ponitActionLog="ponitActionLog"
          @singer-click="handleClickSinger"
          @before-order-click="handleBeforeOrderClick"
        />
      </div>
    </SecContainer>
</template>

<script>
import SecContainer from '@/components/section-container/index.vue'
import SongItem from "@/components/song-item/index.vue"
import { sendLog } from '@/directives/v-log/log'
import { computed, nextTick, onMounted, ref, toRefs, watch, onBeforeMount } from 'vue'
import { useRoute } from 'vue-router'
import { RecycleScroller } from 'vue-virtual-scroller'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'
import { useStore } from 'vuex'
import eventBus from '@/utils/event-bus'
import { LocalSong } from './rsong'

export default {
  name: 'GuessSongList',
  components: {
    SongItem,
    SecContainer,
    RecycleScroller
  },
  props: {
    title: {
      type: String,
      default: '猜你会唱'
    },
    //是否快速虚拟列表
    isFastList: Boolean,
    // 渲染形态
    renderType: {
      default: 'list', // list:列表形态 block：块状形态
      type: String,
    },
    // // 是否显示置顶按钮
    // isStickButton: {
    //   default: true,
    //   type: Boolean
    // },
    pageSize: {
      default: 6,
      type: Number
    },
    showAllData: {
      default: false,
      type: Boolean
    },
    pageRoute: {
      default: '1', // 1：首页 2：二级页面 3：mv页
      type: String,
    },
    ponitActionLog: {
      default: () => { // 点歌位置上报 - 10000~50000
        return {
            event_type: '10000~50000',
            event_name: 10017,
            event_data: {
              str1: '首页',
              str2: '猜你会唱',
              str3: '歌曲列表',
              str4: 'click',
            },
          }
      },
      type: Object,
    },
    song_list_source: {
      default: 0,
      type: Number,
    },
  },
  emits: ['singer-click'],
  setup(props, { emit }) {
    const store = useStore()
    const isLogin= computed(() => !!store.state.userInfo.unionid)
    const oftenSingList = computed(() => store.state.oftenSing.oftenSingList)
    const isGuessSongList = computed(() => isLogin.value && oftenSingList.value.length)
    const top50List = computed(() => store.state.oftenSing.top50)
    const { pageSize, showAllData } = toRefs(props)
    let page = 1
    let guessSongShowList = ref([])
    const route = useRoute()
    let rsongList = LocalSong()
    // const imgs = {
    //   themeDark: {
    //     change: 'https://qncweb.ktvsky.com/20231206/vadd/c48ff9a1b6f6d161eebea8d8acf64751.png',
    //   },
    //   themeLight: {
    //     change: 'https://qncweb.ktvsky.com/20240221/vadd/02a20587146edaf78d83a570dd33e9e0.png',
    //   },
    //   themeSystem: {
    //     change: 'https://qncweb.ktvsky.com/20231206/vadd/c48ff9a1b6f6d161eebea8d8acf64751.png',
    //   },
    // }

    const themeClass = computed(() => store.state.themeClass)

    // 处理歌曲数据，添加随机标签和处理歌手头像
    const getProcessedSongItem = (songItem, index) => {
      const processedItem = { ...songItem }
      
      return processedItem
    }

    // 处理歌曲列表数据，对每一页的第5和第6个数据替换为rsong数据源里的数据
    const processSongList = (songList) => {
      if (!songList || !songList.length) return songList
      
      const result = [...songList]
      let rsongIndex = 0
      const pageCount = Math.ceil(result.length / pageSize.value)
      
      // 遍历每一页
      for (let page = 0; page < pageCount; page++) {
        // 计算当前页的第5和第6个位置
        const fifthIndex = page * pageSize.value + 4
        const sixthIndex = page * pageSize.value + 5
        
        // 替换第5个位置的数据（如果存在）
        if (fifthIndex < result.length) {
          // if (rsongIndex >= rsongList.length) rsongIndex = 0
          if (rsongIndex < 20) result[fifthIndex] = getProcessedSongItem(rsongList[rsongIndex++], fifthIndex)
        }
        
        // 替换第6个位置的数据（如果存在）
        if (sixthIndex < result.length) {
          // if (rsongIndex >= rsongList.length) rsongIndex = 0
          if (rsongIndex < 20) result[sixthIndex] = getProcessedSongItem(rsongList[rsongIndex++], sixthIndex)
        }
      }
      
      return result
    }

    const guessSongShowListUpdate = async () => {
      await nextTick()
      let guessSongList = isGuessSongList.value ? oftenSingList.value.concat(top50List.value) : top50List.value
      guessSongList = processSongList(guessSongList)
      
      if (showAllData.value) {
        //增加序号，解决重复数据不显示问题
        guessSongList.map((item, i)=> {item.i = i;})
        guessSongShowList.value = guessSongList
        return
      }
      guessSongShowList.value = []
      // page已至最后页时重置为首页
      if (guessSongList.length <= (page - 1) * pageSize.value) page = 1
      for (let i in guessSongList) {
        if (i < page * pageSize.value && i >= (page - 1) * pageSize.value) {
          guessSongShowList.value.push(guessSongList[i])
        }
      }
      if (guessSongShowList.value.length < pageSize.value) {
        guessSongShowList.value = guessSongShowList.value.concat(guessSongList).filter((v, i) => i < pageSize.value)
      }
    }

    const handlechangePagenation = () => {
      if (!oftenSingList.value.length && !top50List.value.length) return
      ++page
      guessSongShowListUpdate()
      sendLog({
        event_type: '10000~50000',
        event_name: 10018,
        event_data: {
          str1: '首页',
          str2: '猜你会唱列表',
          str3: '换一换',
          str4: 'click',
        },
      })

      if(route.name == 'home'){
        sendLog({
          event_type: '10000~50000',
          event_name: 6001,
          event_data: {
            str1: '首页',
            str2: '猜你会唱',
            str3: '换一换',
            str4: 'click',
          },
        })
      }

      if(route.name == 'search'){
        sendLog({
          event_type: '10000~50000',
          event_name: 6003,
          event_data: {
            str1: '搜索页',
            str2: '猜你会唱',
            str3: '换一换',
            str4: 'click',
          },
        })
      }

      // if (route.name === 'home') {
      if (page < 11) {
        sendLog({
          event_type: '30000～35000',
          event_name: 6001,
          event_data: {
            str1: route.name == 'home' ? '首页' : '搜索页',
            str2: '猜你会唱（热门歌曲）',
            str3: '粤语歌推荐位',
            str4: 'show',
            str5: page,
          },
        })
      }

    }

    const resetPage = () => {
      page = 1
      guessSongShowListUpdate()
    }

    const handleClickSinger = (data) => {
      emit('singer-click', data)
    }

    // watch(isLogin, () => {
    //   resetPage()
    // })

    const recommendIndex = [
      5, 
      6, 
      11, 
      12, 
      17, 
      18, 
      23, 
      24, 
      29, 
      30, 
      35, 
      36, 
      41, 
      42, 
      47, 
      48, 
      53, 
      54, 
      59, 
      60
    ]
    const handleBeforeOrderClick = ({ index }) => {
      console.log('handleBeforeOrderClick:', index, page < 11, recommendIndex.includes(index))
      // route.name === 'home' && 
      if (page < 11 && recommendIndex.includes(index)) {
        sendLog({
          event_type: '10000~50000',
          event_name: 6001,
          event_data: {
            str1: route.name == 'home' ? '首页' : '搜索页',
            str2: '猜你会唱（热门歌曲）',
            str3: '粤语歌推荐位',
            str4: 'click',
            str5: page,
          },
        })
      }
    }

    watch([oftenSingList, top50List, isLogin], () => {
      resetPage()
    })

    // watch(oftenSingList, () => {
    //   resetPage()
    // })

    // watch(top50List, () => {
    //   resetPage()
    // })

    onMounted(() => {
      // eventBus.on('top50ListUpdated', resetPage);
      resetPage()
      
      //  && route.name === 'home'
      if (page == 1) {
        sendLog({
          event_type: '30000～35000',
          event_name: 6001,
          event_data: {
            str1: route.name == 'home' ? '首页' : '搜索页',
            str2: '猜你会唱（热门歌曲）',
            str3: '粤语歌推荐位',
            str4: 'show',
            str5: page,
          },
        })
      }

    })

    // onBeforeUnmount(() => {
    //   eventBus.off('top50ListUpdated', resetPage);
    // })

    //计算表格数据行实际高度
    const tableItemSize = ref((document.documentElement.clientWidth || document.body.clientWidth) * 137 / 1920);

    onBeforeMount(() => {
      // 初始化rsong数据源,并打乱数组顺序
      rsongList = LocalSong().sort(() => Math.random() - 0.5)
      // console.log('rsongList:', rsongList)
    })

    return {
      // imgs,
      themeClass,
      isLogin,
      isGuessSongList,
      guessSongShowList,
      handlechangePagenation,
      handleClickSinger,
      tableItemSize,
      handleBeforeOrderClick,
    }
  },
}
</script>

<style lang="stylus" scoped>
.sec-gusse
  &-sing
    margin-bottom 40px
    ::-webkit-scrollbar
      display none
    &-list
      will-change: transform // 启用硬件加速
      min-height 400px
      display grid
      grid-template-columns repeat(3, 560px)
      justify-content space-between
      position relative
      ::v-deep .song-item
        height 137px
      @media screen and (max-width 1200px)
        grid-template-columns repeat(3, 344px)
        min-height 304px
        overflow hidden
        ::v-deep .song-block
          width 344px
          height 140px
          margin-bottom 24px
          .name
            font-size 26px
          .desc
            font-size 20px
    &-change
      width 122px
      height 40px
      display flex
      align-items center
      justify-content center
      span
        width auto
        height 40px
        margin-right 5px
        color rgba(255, 255, 255, 0.6)
        font-size 28px
      svg
        width 32px
        height 32px
    @media screen and (max-width 1200px)
      ::v-deep .section-container-header
        padding-bottom 32px
        .section-container-header-title
          font-size 26px
      &-change
        span
          font-size 22px
</style>
