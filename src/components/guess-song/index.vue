<template>
  <div class="guess-song">
    <p class="guess-song-title">猜你想唱</p>
    <div class="guess-song-list">
      <div class="guess-song-list-item" @click="handleOrder(item)" v-for="(item, index) in guessSongList" :key="index">
        <div class="guess-song-list-item-left">
          <img :src="item.singer_head" v-img-fallback="imgFallback" alt="">
        </div>
        <div class="guess-song-list-item-right" :class="{ 'guess-song-list-item-ordered': orderedSongIdMap[item.songid] }">
          <p>{{ item.music_name }}</p>
          <p>{{ item.singer}}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed, onMounted, ref, inject } from 'vue'
import { useStore } from 'vuex'
import useSongItem from '@/composables/useSongItem'
import { getSingsingList } from '@/service/singing'

export default {
  name: 'GuessSong',
  setup(props) {
    const store = useStore()
    const imgFallback = {
      loading: 'https://qncweb.ktvsky.com/20210926/vadd/8a6ac4b746c04f4701481e4da1e146b3.png',
      error: 'https://qncweb.ktvsky.com/20210926/vadd/8a6ac4b746c04f4701481e4da1e146b3.png'
    }

    const { orderSong } = useSongItem()
    const guessSongList = ref([])
    const unionid = computed(() => store.state.userInfo.unionid)
    const orderedSongIdMap = computed(() => store.state.orderedSongIdMap)

    onMounted(async () => {
      const res = await getSingsingList({
        unionid: unionid.value,
      })
      guessSongList.value = res.filter((v, i) => i < 10)
    })

    const handleOrder = (songItem) => {
      store.commit('UPDATE_CONTROL_FROM_TYPE', 1)
      if (Object.keys(orderedSongIdMap.value).length === 0) {
        orderSong(songItem, {
          from: {
            song_list_source: 2
          },
          ponitActionLog: {
            event_type: '10000~50000',
            event_name: 10022,
            event_data: {
              str1: '搜索页',
              str2: '猜你会唱',
              str3: '点歌',
              str4: 'click',
            },
          }
        })
        return
      }
      orderSong(songItem, {
        immediate: false,
        from: {
          song_list_source: 2
        },
        ponitActionLog: {
          event_type: '10000~50000',
          event_name: 10022,
          event_data: {
            str1: '搜索页',
            str2: '猜你会唱',
            str3: '点歌',
            str4: 'click',
          },
        }
      })
    }

    return {
      imgFallback,
      guessSongList,
      orderedSongIdMap,
      handleOrder,
    }
  }
}
</script>

<style lang="stylus" scoped>
.guess-song
  width 100%
  height auto
  margin-top 40px
  &-title
    color rgba(255, 255, 255, 0.5)
    font-size 30px
    font-weight 400
    height 35px
    line-height 35px
  &-list
    display flex
    flex-wrap wrap
    width 100%
    height auto
    max-height 216px
    margin-top 40px
    overflow hidden
    @media screen and (max-width 1200px)
      max-height 464px
    &-item
      height 92px
      // padding 10px 0 10px 10px
      border-radius 163px
      background #1E1F21
      margin-right 32px
      margin-bottom 32px
      display flex
      align-items center
      &-left
        width 72px
        height 72px
        border-radius 72px
        margin-left 10px
        margin-right 20px
        overflow hidden
        img
          width 100%
          height 100%
      &-right
        min-width 66px
        max-width 259px
        margin-right 50px
        p:nth-child(1)
          color #FFF
          font-size 26px
          font-weight 400
          height 30px
          line-height 30px
          max-width 259px
          white-space nowrap
          overflow hidden
          text-overflow ellipsis
        p:nth-child(2)
          color rgba(255, 255, 255, 1)
          opacity 0.4
          font-size 22px
          font-weight 400
          height 25px
          line-height 25px
          margin-top 10px
      &-ordered
        p:nth-child(1)
          color #9497FF
        p:nth-child(2)
          color #9497FF
</style>