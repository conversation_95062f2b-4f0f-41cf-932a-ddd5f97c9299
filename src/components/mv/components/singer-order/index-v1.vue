<template>
  <div class="singer-order-side">
    <div class="singer-order-side-main">
      <LoadMore
        ref="loadMoreRef"
        @load-more="fetchSingerList"
        safeAreaHeight="9.6991vw"
      >
        <template v-if="oftenSingSingerList.length">
          <SecContainer
            title="常唱歌手"
            class="often-sing-singer"
          >
            <div class="often-sing-singer-list">
              <SingerItem
                className="often-sing-singer-list-item"
                v-for="(item, index) in oftenSingSingerList"
                :singer="item"
                :key="index"
                @click="handleClickSinger(item, '常唱歌手')"
              />
            </div>
          </SecContainer>
          <div class="diver-line"></div>
        </template>
        <!-- <div ref="watcherRef"></div> -->
        <div
          ref="singerTabRefs"
          class="singer-tabs sticky"
          :class="[
            oftenSingSingerList.length ? 'has-often-sing-singer' : ''
          ]"
        >
          <div
            class="tab"
            v-for="item in tabList"
            :key="item"
            :class="{'active': curTab == item}"
            @click="handleChangeTab(item)"
          >
            {{ item }}
          </div>
        </div>
        <div class="singer-list">
          <SingerItem
            v-for="item in singerUnionList"
            :singer="item"
            :key="item.singerid"
            @click="handleClickSinger(item, '列表歌手')"
          />
        </div>
      </LoadMore>
      <p class="hint" v-if="isEmpty && singerUnionList.length > 20">已加载全部</p>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, onBeforeUnmount, ref, watch, nextTick, defineProps, defineEmits } from 'vue'
import { useStore } from 'vuex'
import axios from 'axios'
import SecContainer from '@/components/section-container/index.vue'
import SingerItem from '@/components/singer-item/index.vue'
import LoadMore from '@/components/load-more/index.vue'
import { getSingsingerList } from '@/service/singing'
import { getSingerClassList, getSingerList } from '@/service/singer'
import { sendLog } from '@/directives/v-log/log'

const props = defineProps({
  currPageTab: String
})
const emit = defineEmits(['singer-click'])

const store = useStore()
const unionid = computed(() => store.state.userInfo.unionid)
const isLogin = computed(() => !!unionid.value)

// Refs
const loadMoreRef = ref(null)
const singerTabRefs = ref(null)
const watcherRef = ref(null)
const oftenSingSingerList = ref([])
const tabList = ref([])
const singerList = ref([])
const curTab = ref('')
const isEmpty = ref(false)
const isLoaded = ref(false)
const tempSingerList = ref([])
const isSticky = ref(false)
const cancelSource = ref(null)

// 分页参数
let p = 1
let version = { current: '', latest: '' }
let isRequest = false
let observer = null

// 防抖函数初始化
onMounted(() => {
  initOftenSingSinger()
  fetchSingerClassList()
})

// 组件卸载清理
onBeforeUnmount(() => {
  if (cancelSource.value) {
    cancelSource.value.cancel('组件卸载取消请求')
  }
  if (observer) {
    observer.disconnect()
  }
})

const handleChangeTab = tab => {
  curTab.value = tab
  if (loadMoreRef.value) {
    loadMoreRef.value.root.scrollTop = 0
  }

  if (cancelSource.value) {
    cancelSource.value.cancel('切换分类取消请求')
    isRequest = false
  }
  
  p = 1
  tempSingerList.value = []
  singerList.value = []
  isEmpty.value = false
  console.log('handleChangeTab', tab)
  fetchSingerList(tab)
}

const initOftenSingSinger = async () => {
  if (unionid.value) {
    try {
      oftenSingSingerList.value = await getSingsingerList({
        unionid: unionid.value
      })
      isLoaded.value = true
    } catch (error) {
      console.error('获取常唱歌手列表失败:', error)
      isLoaded.value = true
    } finally {
      await nextTick()
      observer = new IntersectionObserver(handleIntersection);
      if (watcherRef.value) {
        observer.observe(watcherRef.value);
      }
    }
  } else {
    await nextTick()
    observer = new IntersectionObserver(handleIntersection);
    if (watcherRef.value) {
      observer.observe(watcherRef.value);
    }
  }
}

const fetchSingerClassList = async () => {
  tabList.value = await getSingerClassList()
  handleChangeTab(tabList.value[0])
}

const fetchSingerList = async () => {
  console.log('fetchSingerList')
  if (isRequest) return
  isRequest = true

  console.log('fetchSingerList', isRequest)
  try {
    // 创建新的取消令牌
    const newSource = axios.CancelToken.source()
    cancelSource.value = newSource
    
    // 显示加载指示器
    isLoaded.value = false;

    let bussinessResponseData = await getSingerList({
      p,
      k: curTab.value,
      version: version.latest,
    }, {
      cancelToken: newSource.token,
    })

    if (bussinessResponseData.data?.length !== 0) {
      if (p === 1 && bussinessResponseData.version) {
        version = bussinessResponseData.version
        tempSingerList.value = []
      }
      tempSingerList.value = p === 1 ? bussinessResponseData.data : tempSingerList.value.concat(bussinessResponseData.data);
      p++
    }
    // 更新 singerList
    singerList.value = tempSingerList.value;

    // 隐藏加载指示器
    isLoaded.value = true;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('请求已取消:', error.message)
    } else if (!axios.isCancel(error)) {
      console.error('请求失败:', error)
    }
  } finally {
    isRequest = false
    cancelSource.value = null
  }
}

const handleClickSinger = ({singername, singerheader, singerid}, type) => {
  sendLog({
    event_type: '10000~50000',
    event_name: type === '常唱歌手' ? 10101 : 10102,
    event_data: {
      str1: '快速点歌',
      str2: '歌手点歌',
      str3: `点击${type}`,
      str4: 'click',
    },
  })
  emit('singer-click', {
    singer: singername,
    singerhead: singerheader,
    singerid,
  })

  if(type == '常唱歌手') {
    sendLog({
      event_type: '10000~50000',
      event_name: 6006,
      event_data: {
        str1: '歌手详情页',
        str2: '歌手详情页',
        str3: '进入歌手详情页',
        str4: 'show',
        str5: singerid,
        str6: 4
      },
    })
  }else{
    sendLog({
      event_type: '10000~50000',
      event_name: 6006,
      event_data: {
        str1: '歌手详情页',
        str2: '歌手详情页',
        str3: '进入歌手详情页',
        str4: 'show',
        str5: singerid,
        str6: 5
      },
    })
  }
}

// 歌手列表去重
const singerUnionList = computed(() => {
  const idsMap = new Map();
  singerList.value && singerList.value.forEach((singer) => {
    if (singer && !idsMap.has(singer.singerid)) {
      idsMap.set(singer.singerid, singer);
    }
  });
  return Array.from(idsMap.values());
});

const handleIntersection = (entries) => {
  if (props.currPageTab !== 'singer') return

  const entry = entries[0]
  if (entry.isIntersecting) {
    isSticky.value = false;
  } else {
    isSticky.value = true;
    console.log('1203')
  }
};

// Watchers
watch(isLogin, val => {
  if (val) initOftenSingSinger()
})
</script>

<style lang="stylus" scoped>
.singer-order-side
  width 800px
  height calc(100% - 164px)
  @media screen and (max-width 1200px)
    width 100%
    height calc(100% - 120px)
  ::-webkit-scrollbar
    display none
  &-main
    width 100%
    height 100%
    padding-left 48px
    padding-right 48px
    overflow hidden
    display flex
    flex-direction column
    .loadmore
      height 100% !important
    @media screen and (max-width 1200px)
      padding-left 60px
      padding-right 60px
    .singer-tabs
      width 100%
      height 80px
      margin-bottom 40px
      display flex
      overflow-x scroll
      grid-column span 4
      background: none
      &.sticky
        background #1e1f21
        position sticky
        top 0px
      &.has-often-sing-singer
        top -2px
      .tab
        width fit-content
        height 100%
        min-width 160px
        padding 21.5px 24.5px
        border-radius 4px
        font-size 28px
        color rgba(255, 255, 255, 0.60)
        float left
        margin 0 20px 0 0
        background rgba(255, 255, 255, 0.08)
        flex-shrink 0
        text-align center
        border none
      .active
        background #DBAE6A
        color rgba(0, 0, 0, 0.80)
      @media screen and (max-width 1200px)
        height 64px
        margin-bottom 50px
        .tab
          padding 19px 15px
          font-size 22px
    .singer-list
      flex 1
      text-align center
      display flex
      flex-wrap wrap
      box-sizing border-box
      padding 0 !important
      display grid
      grid-template-columns repeat(4, 140px)
      justify-content space-between
      height fit-content
      ::v-deep .singer-item
        width 140px
        margin-bottom 40px
        font-size 24px
        margin-right 0
        .singer-item-cover
          width 140px
          height 140px
          margin-bottom 20px
        p
          width 140px
      @media screen and (max-width 1200px)
        grid-template-columns repeat(4, 200px)
        ::v-deep .singer-item
          width 200px
          margin-bottom 60px
          font-size 22px
          .singer-item-cover
            width 200px
            height 200px
          p
            width 200px
    .diver-line
      width 100%
      height 2px
      margin 0 0 48px
      background rgba(255, 255, 255, 0.10)
      grid-column span 4
    .no-data
      font-size 28px
      color rgba(255, 255, 255, 0.5)
      text-align center
      width 100%
      height 32px
      clear both
    .hint
      text-align center
      color #555555
    .often-sing-singer
      width 100%
      height 274px
      grid-column span 4
      ::v-deep .section-container-header
        padding-bottom 30px
      ::v-deep .section-container-header-title
        color rgba(255, 255, 255, 0.40) !important
        font-size 28px !important
        font-weight 400
      &-list
        height 202px
        display flex
        width 100%
        overflow-x auto
        overflow-y hidden
        &-item
          width 110px
          height 154px
          margin-right 32px
          flex-shrink 0
          color rgba(255, 255, 255, 0.50)
          font-size 24px
          text-align center
          ::v-deep img
            width 110px
            height 110px
            border-radius 50%
            margin-bottom 16px
          ::v-deep p
            height calc(28px * 2)
            line-height 28px
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
          ::v-deep .wrapper
            width auto
            height 110px
            margin-bottom 20px
            background url('../../../../assets/singer-dark.png') no-repeat
            background-size 100%
            border-radius 50%
            .often-sing-singer-list-item-cover
              height 110px
              background-position center
              background-size 100% 100%
              border-radius 50%
    .empty
      display flex
      flex-direction column
      justify-content center
      align-items center
      margin-top 30px
      font-size 28px
      color rgba(255, 255, 255, 0.40)
      text-align center
      img
        width 80px
        height 80px
        margin-bottom 40px
      p
        height 32px
        line-height 32px
      @media screen and (max-width 1200px) and (min-height 1200px)
        margin-top 14vh
.theme-themeLight
  .singer-order-side-main
    ::v-deep .section-container-header-title
      color rgba(29, 29, 31, 0.5)!important
    ::v-deep .often-sing-singer-list
      p
        color rgba(29, 29, 31, 0.7)!important
    .diver-line
      background rgba(29, 29, 31, 0.1)
    .singer-tabs
      background: #f0f2f7
      .tab
        background #D2D5DD
        color rgba(29, 29, 31, 0.5)
      .active
        background rgba(122, 83, 231, 1)
        color rgba(255, 255, 255, 0.8)
  .often-sing-singer-list-item ::v-deep .wrapper
    background-image url('../../../../assets/singer-light.png')
</style>
