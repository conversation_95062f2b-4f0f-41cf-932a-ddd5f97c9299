<template>
  <div class="singer-order-side">
    <div class="singer-order-side-main">
      <LoadMore
        class="singer-list"
        ref="loadMoreRef"
        @load-more="fetchSingerList"
        safeAreaHeight="9.6991vw"
      >
        <template v-if="oftenSingSingerList.length">
          <SecContainer
            title="常唱歌手"
            class="often-sing-singer"
          >
            <div class="often-sing-singer-list">
              <SingerItem
                className="often-sing-singer-list-item"
                v-for="(item, index) in oftenSingSingerList"
                :singer="item"
                :key="index"
                @click="handleClickSinger(item, '常唱歌手')"
              />
            </div>
          </SecContainer>
          <div class="diver-line"></div>
        </template>
        <div ref="watcherRef"></div>
        <div
          ref="singerTabRefs"
          class="singer-tabs"
          :class="[
            (isSticky || (!oftenSingSingerList.length && isLoaded)) ? 'sticky' : '',
            oftenSingSingerList.length ? 'has-often-sing-singer' : ''
          ]"
        >
          <div
            class="tab"
            v-for="item in tabList"
            :key="item"
            :class="{'active': curTab == item}"
            @click="handleChangeTab(item)"
          >
            {{ item }}
          </div>
        </div>
        <SingerItem
          v-for="item in singerUnionList"
          :singer="item"
          :key="item.singerid"
          @click="handleClickSinger(item, '列表歌手')"
        />
      </LoadMore>
      <p class="hint" v-if="isEmpty && singerUnionList.length > 20">已加载全部</p>
    </div>
  </div>
</template>

<script>
import { computed, onMounted, onBeforeUnmount, ref, watch, nextTick, toRefs } from 'vue'
import { useStore } from 'vuex'
import SecContainer from '@/components/section-container/index.vue'
import SingerItem from '@/components/singer-item/index.vue'
import { getSingsingerList } from '@/service/singing'
import { getSingerClassList, getSingerList } from '@/service/singer'
import { sendLog } from '@/directives/v-log/log'

export default {
  name: 'SingerOrder',
  props: {
    currPageTab: String
  },
  components: {
    SecContainer,
    SingerItem,
  },
  setup(props, { emit }) {
    const store = useStore()
    const unionid = computed(() => store.state.userInfo.unionid)
    const isLogin = computed(() => !!unionid.value)

    const { currPageTab } = toRefs(props)

    let loadMoreRef = ref(null)
    let singerTabRefs = ref(null)
    let watcherRef = ref(null)
    let oftenSingSingerList = ref([])
    let tabList = ref([])
    let singerList = ref([])
    let curTab = ref('')
    let p = 1
    let version = {
      current: '',
      latest: ''
    }
    let isEmpty = ref(false)
    let isRequest = false
    let isLoaded = ref(false)
    let tempSingerList = ref([])

    // let singerHeaderIsfixed = ref(false)

    const isSticky = ref(false);
    let observer = null;

    const handleClickSinger = ({singername, singerheader, singerid}, type) => {
      sendLog({
        event_type: '10000~50000',
        event_name: type === '常唱歌手' ? 10101 : 10102,
        event_data: {
          str1: '快速点歌',
          str2: '歌手点歌',
          str3: `点击${type}`,
          str4: 'click',
        },
      })
      emit('singer-click', {
        singer: singername,
        singerhead: singerheader,
        singerid,
      })

      if(type == '常唱歌手') {
        sendLog({
          event_type: '10000~50000',
          event_name: 6006,
          event_data: {
            str1: '歌手详情页',
            str2: '歌手详情页',
            str3: '进入歌手详情页',
            str4: 'show',
            str5: singerid,
            str6: 4
          },
        })
      }else{
        sendLog({
          event_type: '10000~50000',
          event_name: 6006,
          event_data: {
            str1: '歌手详情页',
            str2: '歌手详情页',
            str3: '进入歌手详情页',
            str4: 'show',
            str5: singerid,
            str6: 5
          },
        })
      }
    }

    const initOftenSingSinger = async () => {
      if (unionid.value) {
        try {
          oftenSingSingerList.value = await getSingsingerList({
            unionid: unionid.value
          })
          isLoaded.value = true
        } catch (error) {
          console.error('获取常唱歌手列表失败:', error)
          isLoaded.value = true
        } finally {
          await nextTick()
          observer = new IntersectionObserver(handleIntersection);
          if (watcherRef.value) {
            observer.observe(watcherRef.value);
          }
        }
      } else {
        await nextTick()
        observer = new IntersectionObserver(handleIntersection);
        if (watcherRef.value) {
          observer.observe(watcherRef.value);
        }
      }
    }

    // 歌手列表去重
    const singerUnionList = computed(() => {
      const idsMap = new Map();
      singerList.value && singerList.value.forEach((singer) => {
        if (singer && !idsMap.has(singer.singerid)) {
          idsMap.set(singer.singerid, singer);
        }
      });
      return Array.from(idsMap.values());
    });

    const fetchSingerClassList = async () => {
      tabList.value = await getSingerClassList()
      handleChangeTab(tabList.value[0])
    }

    const fetchSingerList = async (callBack) => {
      if (isRequest) {
        return
      }
      isRequest = true

      // 显示加载指示器
      isLoaded.value = false;

      let bussinessResponseData = await getSingerList({
        p,
        k: curTab.value,
        version: version.latest
      })

      if (bussinessResponseData.data?.length !== 0) {
        if (p === 1 && bussinessResponseData.version) {
          version = bussinessResponseData.version
          tempSingerList.value = []
        }
        tempSingerList.value = p === 1 ? bussinessResponseData.data : tempSingerList.value.concat(bussinessResponseData.data);
        p++
      }
      // 更新 singerList
      singerList.value = tempSingerList.value;

      // 隐藏加载指示器
      isLoaded.value = true;
      isRequest = false;
      if (callBack) callBack()
    }

    const handleChangeTab = (tab) => {
      curTab.value = tab
      // 当歌手类别方式变化后  初始化 距顶部距离0
      if (loadMoreRef.value) {
        loadMoreRef.value.root.scrollTop = 0;
      }
    }

    const handleIntersection = (entries) => {
      if (currPageTab.value !== 'singer') return

      const entry = entries[0]
      if (entry.isIntersecting) {
        isSticky.value = false;
      } else {
        isSticky.value = true;
        console.log('1203')
      }
    };

    onMounted(async () => {
      initOftenSingSinger()
      fetchSingerClassList()
    })

    onBeforeUnmount(() => {
      if (observer) {
        observer.disconnect();
      }
    })

    watch(isLogin, (val) => {
      if (val) initOftenSingSinger()
    })

    watch(curTab, (tab) => {
      if (tab) {
        p = 1
        setTimeout(() => {
          fetchSingerList()
        }, 100)
      }
    })

    return {
      loadMoreRef,
      singerTabRefs,
      oftenSingSingerList,
      singerUnionList,
      tabList,
      curTab,
      isEmpty,
      fetchSingerList,
      handleClickSinger,
      handleChangeTab,
      isSticky,
      watcherRef,
      isLoaded,
    }
  }
}
</script>

<style lang="stylus" scoped>
.singer-order-side
  width 800px
  height calc(100% - 164px)
  @media screen and (max-width 1200px)
    width 100%
    height calc(100% - 120px)
  ::-webkit-scrollbar
    display none
  &-main
    width 100%
    height 100%
    padding-left 48px
    padding-right 48px
    overflow hidden
    display flex
    flex-direction column
    // ::-webkit-scrollbar-track
    //   background none
    @media screen and (max-width 1200px)
      padding-left 60px
      padding-right 60px
    // .singer-tabs-vis
    //   width 100%
    //   height 80px
    // .singer-tabs-fixed
    //   position absolute
    //   top 8.10417vw
    //   left 48px
    //   padding-right 50px
    //   background #1e1f21
    //   width 700px
    //   @media screen and (max-width 1200px)
    //     top 11.10417vw
    .singer-tabs
      width 100%
      height 80px
      margin-bottom 40px
      display flex
      overflow-x scroll
      grid-column span 4
      background: none
      &.sticky
        background #1e1f21
        position sticky
        top 0px
      &.has-often-sing-singer
        top -2px
      .tab
        width fit-content
        height 100%
        min-width 160px
        padding 21.5px 24.5px
        border-radius 4px
        font-size 28px
        color rgba(255, 255, 255, 0.60)
        float left
        margin 0 20px 0 0
        background rgba(255, 255, 255, 0.08)
        flex-shrink 0
        text-align center
        border none
      .active
        background #DBAE6A
        color rgba(0, 0, 0, 0.80)
      @media screen and (max-width 1200px)
        height 64px
        margin-bottom 50px
        .tab
          padding 19px 15px
          font-size 22px
    .singer-list
      flex 1
      text-align center
      display flex
      flex-wrap wrap
      box-sizing border-box
      padding 0 !important
      display grid
      grid-template-columns repeat(4, 140px)
      justify-content space-between
      height 230px
      ::v-deep .singer-item
        width 140px
        margin-bottom 40px
        font-size 24px
        margin-right 0
        .singer-item-cover
          width 140px
          height 140px
          margin-bottom 20px
        p
          width 140px
      @media screen and (max-width 1200px)
        grid-template-columns repeat(4, 200px)
        ::v-deep .singer-item
          width 200px
          margin-bottom 60px
          font-size 22px
          .singer-item-cover
            width 200px
            height 200px
          p
            width 200px
    .diver-line
      width 100%
      height 2px
      margin 0 0 48px
      background rgba(255, 255, 255, 0.10)
      grid-column span 4
    .no-data
      font-size 28px
      color rgba(255, 255, 255, 0.5)
      text-align center
      width 100%
      height 32px
      clear both
    .hint
      text-align center
      color #555555
    .often-sing-singer
      width 100%
      height 274px
      grid-column span 4
      ::v-deep .section-container-header
        padding-bottom 30px
      ::v-deep .section-container-header-title
        color rgba(255, 255, 255, 0.40) !important
        font-size 28px !important
        font-weight 400
      &-list
        height 202px
        display flex
        width 100%
        overflow-x auto
        overflow-y hidden
        &-item
          width 110px
          height 154px
          margin-right 32px
          flex-shrink 0
          color rgba(255, 255, 255, 0.50)
          font-size 24px
          text-align center
          ::v-deep img
            width 110px
            height 110px
            border-radius 50%
            margin-bottom 16px
          ::v-deep p
            height calc(28px * 2)
            line-height 28px
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
          ::v-deep .wrapper
            width auto
            height 110px
            margin-bottom 20px
            background url('../../../../assets/singer-dark.png') no-repeat
            background-size 100%
            border-radius 50%
            .often-sing-singer-list-item-cover
              height 110px
              background-position center
              background-size 100% 100%
              border-radius 50%
    .empty
      display flex
      flex-direction column
      justify-content center
      align-items center
      margin-top 30px
      font-size 28px
      color rgba(255, 255, 255, 0.40)
      text-align center
      img
        width 80px
        height 80px
        margin-bottom 40px
      p
        height 32px
        line-height 32px
      @media screen and (max-width 1200px) and (min-height 1200px)
        margin-top 14vh
.theme-themeLight
  .singer-order-side-main
    ::v-deep .section-container-header-title
      color rgba(29, 29, 31, 0.5)!important
    ::v-deep .often-sing-singer-list
      p
        color rgba(29, 29, 31, 0.7)!important
    .diver-line
      background rgba(29, 29, 31, 0.1)
    .singer-tabs
      background: #f0f2f7
      .tab
        background #D2D5DD
        color rgba(29, 29, 31, 0.5)
      .active
        background rgba(122, 83, 231, 1)
        color rgba(255, 255, 255, 0.8)
  .often-sing-singer-list-item ::v-deep .wrapper
    background-image url('../../../../assets/singer-light.png')
</style>
