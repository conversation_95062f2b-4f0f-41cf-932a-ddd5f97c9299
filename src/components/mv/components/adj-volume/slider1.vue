<template>
  <div class="volume-item" :class="{ 'disabled': disabled }">
    <div class="v-slider">
      <p class="name">
        <slot>名称</slot>
      </p>
      <div
        :style="{ opacity: disabled ? 0.3 : 1 }"
        class="slider-container"
        @mousedown="startDrag"
        @touchstart="startDrag"
        @click="onSliderClick"
        ref="slider"
      >
        <div class="slider-bar" :style="{ width: `${sliderWidth}%` }"></div>
        <div class="slider-button" :style="{ left: `${sliderPosition}%` }">
          <svg width="15" height="16" viewBox="0 0 15 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect width="1" height="16" fill="white" fill-opacity="0.4" />
            <rect x="7" width="1" height="16" fill="white" fill-opacity="0.4" />
            <rect x="14" width="1" height="16" fill="white" fill-opacity="0.4" />
          </svg>
        </div>
      </div>
      <div :style="{ opacity: disabled ? 0.3 : 1 }" class="v-slider-ruler">
        <div
          v-for="(item, index) in rulers"
          :key="item"
          :class="{ 'disabled': disabled && index === 0 }"
          @click="onRulerClick(index)"
        >
          {{ item }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed, defineProps, defineEmits } from 'vue';

const props = defineProps({
  defaultValue: Number,
  disabled: Boolean,
  resetValue: Number,
  step: { type: Number, default: 5 },
  max: { type: Number, default: 100 },
  min: { type: Number, default: 0 },
});

const emit = defineEmits(['change', 'update']);

const value = ref(50);
const rulers = [0, 25, 50, 75, 100];

const sliderWidth = computed(() => {
  const roundedValue = Math.round(value.value);
  return roundedValue >= 73 && roundedValue <= 75 ? 74 : value.value;
});

const sliderPosition = computed(() => {
  const roundedValue = Math.round(value.value);
  return Math.min(98, Math.max(2, roundedValue >= 73 && roundedValue <= 75 ? 74 : roundedValue));
});

const updateValue = (clientX, rect) => {
  if (props.disabled) return;

  let newValue = ((clientX - rect.left) / rect.width) * 100;
  newValue = Math.max(0, Math.min(newValue, 100));
  const actualValue = Math.round((newValue / 100) * props.max);
  value.value = (actualValue / props.max) * 100;
  emit('update', actualValue);
};

const startDrag = (event) => {
  if (props.disabled) return;

  const slider = event.currentTarget;
  const rect = slider.getBoundingClientRect();

  const onMouseMove = (e) => updateValue(e.touches ? e.touches[0].clientX : e.clientX, rect);
  const onMouseUp = () => {
    document.removeEventListener('mousemove', onMouseMove);
    document.removeEventListener('mouseup', onMouseUp);
    document.removeEventListener('touchmove', onMouseMove);
    document.removeEventListener('touchend', onMouseUp);
  };

  document.addEventListener('mousemove', onMouseMove);
  document.addEventListener('mouseup', onMouseUp);
  document.addEventListener('touchmove', onMouseMove);
  document.addEventListener('touchend', onMouseUp);
};

const onRulerClick = (index) => {
  if (props.disabled) return;

  const newValue = (index / (rulers.length - 1)) * 100;
  const actualValue = Math.round((newValue / 100) * props.max);
  value.value = (actualValue / props.max) * 100;
  emit('update', actualValue);
};

const onSliderClick = (event) => {
  if (props.disabled) return;

  const slider = event.currentTarget;
  const rect = slider.getBoundingClientRect();
  updateValue(event.clientX, rect);
};

onMounted(() => {
  if (typeof props.defaultValue === 'number') {
    value.value = (props.defaultValue / props.max) * 100;
  }
  if (typeof props.resetValue === 'number' && props.resetValue > 5) {
    value.value = (props.resetValue / props.max) * 100;
  }
});

watch(
  () => props.resetValue,
  (val) => {
    if (typeof val === 'number') {
      value.value = (val / props.max) * 100;
    }
  },
  { immediate: true }
);
</script>

<style lang="stylus" scoped>
.volume-item
  display flex
  align-items center
  justify-content center
  height 165px
  color rgba(255, 255, 255, 0.6)
  &:nth-child(3)
    margin 10px 0
  .v-slider
    width 800px
    height 112px
    display flex
    position relative
    .slider-container
      position relative
      width 586px
      height 14px
      background-color rgba(0, 0, 0, 1)
      border-radius 38px
      cursor pointer
      margin-top 20px
    .slider-bar
      position absolute
      top 50%
      transform translateY(-50%)
      left 6px
      right 6px
      height 2px
      background-color rgba(219, 174, 106, 1)
    .slider-button
      display flex
      align-items center
      justify-content center
      position absolute
      top 0px
      width 83px
      height 50px
      background-color rgba(54, 54, 54, 1)
      border-radius 38px
      transform translateX(-50%) translateY(-45%)
      cursor pointer
      z-index 2
      svg
        width 15px
        height 16px
    .v-slider-ruler
      width 606px
      display flex
      justify-content space-between
      position absolute
      left 154px
      top 60px
      bottom unset
      color rgba(255,255,255,0.2)
      font-size 24px
      div
        width 42px
        text-align center
        position relative
        &::after
          content ""
          position absolute
          top -10px
          left 50%
          width 1px
          height 12px
          background rgba(255,255,255,0.2)
          z-index 1
      .disabled
        &::after
          content ""
          display none
.theme-themeLight
  .volume-item
    .slider-container
      background rgba(29, 29, 31, 0.08)
    .slider-bar
      background rgba(122, 83, 231, 1)
    .slider-button
      background-color rgba(122, 83, 231, 1)
      color rgba(0, 0, 0, 1)
    .v-slider-ruler
      color rgba(29, 29, 31, 0.6)
      div
        &::after
          background rgba(0, 0, 0, 0.2)
</style>
