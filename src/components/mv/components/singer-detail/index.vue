<template>
  <div class="singer-detail-side">
    <div class="singer-detail-side-back" @click="$emit('back')">
      <svg v-show="themeClass === 'themeLight'" class="back-icon" width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M19.6759 3.70701L20.383 2.99991L18.9688 1.58569L18.2617 2.2928L1.29109 19.2634L0.583984 19.9705L1.29109 20.6776L18.2617 37.6481L18.9688 38.3552L20.383 36.941L19.6759 36.2339L4.44185 20.9999H37V18.9999H4.38297L19.6759 3.70701Z" fill="#1D1D1F" style="fill:#1D1D1F;fill:color(display-p3 0.1137 0.1137 0.1216);fill-opacity:1;"/>
      </svg>

      <svg v-show="themeClass === 'themeDark'" class="back-icon" width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M19.6759 3.70714L20.383 3.00003L18.9688 1.58582L18.2617 2.29292L1.29109 19.2635L0.583984 19.9706L1.29109 20.6777L18.2617 37.6483L18.9688 38.3554L20.383 36.9412L19.6759 36.234L4.44185 21H37V19H4.38297L19.6759 3.70714Z" fill="white" style="fill:white;fill-opacity:1;"/>
      </svg>
      <p>{{ singerData.name }}</p>
    </div>
    <div class="close-side" @click="$emit('close')">
      <svg v-show="themeClass === 'themeLight'" width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g opacity="0.4">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M3.74219 26.3708L5.62781 28.2564L15.9987 17.8855L26.3698 28.2566L28.2554 26.371L17.8843 15.9999L28.2552 5.62903L26.3696 3.74341L15.9987 14.1143L5.628 3.7436L3.74238 5.62922L14.1131 15.9999L3.74219 26.3708Z" fill="#1D1D1F" style="fill:#1D1D1F;fill:color(display-p3 0.1137 0.1137 0.1216);fill-opacity:1;"/>
        </g>
      </svg>
      <svg v-show="themeClass === 'themeDark'" width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g opacity="0.4">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M3.74219 26.3708L5.62781 28.2564L15.9987 17.8855L26.3698 28.2566L28.2554 26.371L17.8843 15.9999L28.2552 5.62903L26.3696 3.74341L15.9987 14.1143L5.628 3.7436L3.74238 5.62922L14.1131 15.9999L3.74219 26.3708Z" fill="white" style="fill:white;fill-opacity:1;"/>
        </g>
      </svg>
    </div>
    <div class="singer-detail-side-main">
      <div v-if="singerHeaderIsfixed" class="avatar-default-wrapper fixed-header">
        <div :style="{ backgroundImage: `url(${singerData.image})` }"></div>
      </div>
      <div class="list">
        <LoadMore
          class="song-list"
          ref="loadMoreRef"
          v-if="dataList.length"
          @load-more="fetchData"
          :safeAreaHeight="browserType === 'B07' ? '0vw' : '11.6991vw'"
        >
          <div class="avatar-default-wrapper header">
            <div :style="{ backgroundImage: `url(${singerData.image})` }"></div>
          </div>
          <SongItem
            v-for="(songItem, index) in dataList"
            :key="index"
            :songItem="songItem"
            :log-from="{
              str1:'3-fast-singer',
              song_list_source: isLogin ? 27 : 26,
            }"
            :singerEnable="false"
          />
        </LoadMore>
        <div v-else-if="!isRequest" class="empty">
          暂无歌曲
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { onMounted, onBeforeUnmount, toRefs, ref, computed } from 'vue'
import SongItem from '@/components/song-item/index.vue'
import { searchSinger } from '@/service/search'
import { useStore } from 'vuex'
import {useShareBrowserSize} from "@/composables/sharedComposable";

export default {
  name: 'SingerDetail',
  props: {
    singerData: {
      default: () => {
        return {
          singerid: '',
          name: '',
          image: '',
        }
      },
      type: Object,
    }
  },
  components: {
    SongItem,
  },
  setup(props) {
    const store = useStore()
    const { singerData } = toRefs(props)

    const imgs = {
      themeDark: {
        back: require('@/assets/back-dark.png'),
        imgFallback: {
          loading: require('@/assets/singer-dark.png'),
          error: require('@/assets/singer-dark.png')
        }
      },
      themeLight: {
        back: require('@/assets/back-light.png'),
        imgFallback: {
          loading: require('@/assets/singer-light.png'),
          error: require('@/assets/singer-light.png'),
        }
      },
      themeSystem: {
        back: require('@/assets/back-dark.png'),
        imgFallback: {
          loading: require('@/assets/singer-dark.png'),
          error: require('@/assets/singer-dark.png')
        }
      },
    }
    const isLogin = computed(() => !!store.state.userInfo.unionid)
    const themeClass = computed(() => store.state.themeClass)
    const { browserType } = useShareBrowserSize()
    let p = 1
    let version = ref({
      current: '',
      latest: ''
    })
    let isRequest = ref(false)
    let dataList = ref([])
    let loadMoreRef = ref(null)
    let singerHeaderIsfixed = ref(false)

    const requestBussinessData = async () => {
      let responseData = []
      const { singerid } = singerData.value
      if (singerid) {
        const { data } = await searchSinger(singerid, p);
        responseData = {
          data: data.song
        }
      }
      return responseData
    }

    const fetchData = async () => {
      if (isRequest.value) {
        return
      }
      isRequest.value = true

      const res = await requestBussinessData()
      if (res.data.length !== 0) {
        if (p === 1 && res.version) {
          version.value = res.version
        }
        dataList.value = dataList.value.concat(res.data)
        p++
      }
      isRequest.value = false
    }

    const handleScroll = (e) => {
      const scrollTop = e.target.scrollTop
      singerHeaderIsfixed.value = scrollTop > 210
    }

    onMounted(() => {
      // 请求数据
      fetchData()
      setTimeout(() => {
        if (loadMoreRef.value) loadMoreRef.value.root.addEventListener('scroll', handleScroll, false)
      }, 800)
    })

    onBeforeUnmount(() => {
      if (loadMoreRef.value) loadMoreRef.value.root.removeEventListener('scroll', handleScroll)
    })

    return {
      dataList,
      singerHeaderIsfixed,
      fetchData,
      loadMoreRef,
      isRequest,
      imgs,
      themeClass,
      browserType,
      isLogin
    }
  }
}
</script>

<style lang="stylus" scoped>
.singer-detail-side
  width 100%
  height 100vh
  padding 0 48px
  position relative
  @media screen and (max-width 1200px)
    padding 0 60px
  ::-webkit-scrollbar
    display none
  &-title
    color rgba(255, 255, 255, 0.8)
    font-size 40px
    margin 64px 0 48px
    width 500px
    height 48px
    line-height 48px
    font-weight 600
  &-back
    color rgba(255, 255, 255, 0.8)
    font-size 32px
    font-weight 400
    display flex
    align-items center
    margin 63px 0 63px
    width 320px
    height 38px
    line-height 38px
    svg
      width 32px
      height 32px
      margin-right 30px
    p
      flex 1
      text-overflow ellipsis
      white-space nowrap
      overflow hidden
    @media screen and (max-width 1200px)
      font-size 26px
      height 30px
      line-height 30px
    img
      width 36px
      height 36px
      margin-right 30px
    @media screen and (max-width 1200px)
      font-size 26px
      height 30px
      line-height 30px
      margin 48px 0 20px
      img
        width 26px
        height 26px
        margin-right 38px
  .close-side
    position absolute
    top 61px
    right 48px
    width 32px
    height 32px
    img
      width 100%
      height 100%
    @media screen and (max-width 1200px)
      top 48px
  &-main
    width 100%
    height 88vh
    display flex
    flex-direction column
    align-items center
    overflow-y scroll
    @keyframes fadeIn {
      from {
        opacity 0
      }
      to {
        opacity 1
      }
    }
    @media screen and (max-width: 1950px) and (min-width: 1700px) and (max-height: 720px) and (min-height: 650px)
      height 100vh
    .visi-hidden
      visibility hidden
    .fixed-header
      position absolute
      top 52px
      left 50%
      margin-left -30px
      width 60px
      height 60px
      border-radius 50%
      animation fadeIn 1s ease-in-out
      @media screen and (max-width 1200px)
        top 28px
    .empty
      text-align center
      color rgba(255, 255, 255, 0.4)
    .list
      width 100%
      margin-right 0!important
      ::-webkit-scrollbar
        display none
      // ::-webkit-scrollbar-track
      //   background #1E1F21
      .song-list
        width 100% !important
        padding 0 !important
        .header
          width 200px
          height 200px
          border-radius 50%
          margin 0 auto
          margin-bottom 48px
          padding 0 !important
        ::v-deep .song-item
          height 141px
          margin-top 0
          .right
            margin-right 0
        @media screen and (max-width 1200px)
          ::v-deep .song-item
            padding 0
            .name
              font-size 26px
            .desc
              font-size 20px
            .order-btn
              width 160px
              height 64px
              font-size 22px
.theme-themeLight
  .singer-detail-side-back
    color #1D1D1FE5
  .empty
    color #1D1D1FE5
</style>