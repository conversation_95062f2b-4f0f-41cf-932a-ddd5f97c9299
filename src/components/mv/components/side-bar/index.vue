<template>
  <transition ref="root" name="slide-transition">
    <div class="mv-side" :style="posStyle" :data-pos="pos">
      <slot name="default"></slot>
    </div>
  </transition>
</template>

<script>
import { computed, toRefs, watch, onMounted, nextTick, ref } from 'vue'
import { useShareBrowserSize } from '@/composables/sharedComposable'

export default {
  name: 'MvSideBar',
  props: {
    pos: {
      type: Number,
      default: 0, // 0 不显示 1 右侧显示 2 左侧显示
    }
  },
  setup(props) {
    const { browserType } = useShareBrowserSize()
    const { pos } = toRefs(props)
    let posMaps = {
      0: { 'width': 0 },
      1: { 'width': '43vw', 'right': 0 },
      2: { 'width': '43vw', 'left': 0 },
      3: { 'height': '56vh' }
    }
    const posStyle = ref({ 'width': 0 })

    onMounted(() => {
      console.log('side-bar', onMounted)
      nextTick(() => {
        if (browserType.value !== 'landscape') {
          posMaps = {
            ...posMaps,
            0: { 'width': '100vw', 'height': 0 }
          }
          posStyle.value = posMaps[pos.value]
        }

        if (browserType.value === 'B07') {
          posMaps = {
            ...posMaps,
            1: { 'width': '30vw', 'right': 0 },
            2: { 'width': '30vw', 'left': 0 },
          }
          posStyle.value = posMaps[pos.value]
        }
      })
    })

    watch(browserType, val => {
      posMaps = {
        ...posMaps,
        0: val !== 'landscape' ? { 'width': '100vw', 'height': 0 } : { 'width': 0, 'height': '100vh' }
      }
      posStyle.value = posMaps[pos.value]
    })

    // 监听侧边栏的展示位置并标记 使其能在关闭时动画效果位置正常
    watch(pos, val => {
      if (val !== 0) {
        posMaps = {
          ...posMaps,
          0: browserType.value === 'landscape' ? { 
            'width': 0,
            [val === 1 ? 'right' : 'left']: 0
          } : {
            'height': 0,
          },
        }
      }
      posStyle.value = posMaps[pos.value]
    })

    return {
      posStyle,
    }
  }
}
</script>

<style lang="stylus" scoped>
.mv-side
  // width 800px
  height 100vh
  background rgba(30, 31, 33, 1)
  display flex
  flex-direction column
  align-items center
  overflow hidden
  position absolute
  top 0
  z-index 12
  ::v-deep .order-song-item, ::v-deep .already-song-item
    .info
      max-width 75%
  @media screen and (max-width 1200px) and (min-height 1200px)
    width 100vw
    top auto
    bottom 0
    left 0
  @media screen and (max-width: 1950px) and (min-width: 1700px) and (max-height: 720px) and (min-height: 650px)
    ::v-deep
      & > div:not(.adj-volume)
        zoom 0.7
      .adj-volume

        &-title
          zoom 0.7

        &-main
          transform: scale(0.7);
          transform-origin: 100px 0px; // 确保缩放从左上角开始

.theme-themeLight
  .mv-side
    background: #f0f2f7
</style>