<template>
  <div class="fast-order-page" v-show="pageViewName === 'searchTabPage'">
    <div class="close-side" @click="$emit('close')">
      <svg v-show="themeClass === 'themeLight'" width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g opacity="0.4">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M3.74219 26.3708L5.62781 28.2564L15.9987 17.8855L26.3698 28.2566L28.2554 26.371L17.8843 15.9999L28.2552 5.62903L26.3696 3.74341L15.9987 14.1143L5.628 3.7436L3.74238 5.62922L14.1131 15.9999L3.74219 26.3708Z" fill="#1D1D1F" style="fill:#1D1D1F;fill:color(display-p3 0.1137 0.1137 0.1216);fill-opacity:1;"/>
        </g>
      </svg>
      <svg v-show="themeClass === 'themeDark'" width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g opacity="0.4">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M3.74219 26.3708L5.62781 28.2564L15.9987 17.8855L26.3698 28.2566L28.2554 26.371L17.8843 15.9999L28.2552 5.62903L26.3696 3.74341L15.9987 14.1143L5.628 3.7436L3.74238 5.62922L14.1131 15.9999L3.74219 26.3708Z" fill="white" style="fill:white;fill-opacity:1;"/>
        </g>
      </svg>
    </div>
    
    <div class="fast-order-page-back" v-if="isSearch" @click="handleBackFastSearch">
      <svg v-show="themeClass === 'themeLight'" class="back-icon" width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M19.6759 3.70701L20.383 2.99991L18.9688 1.58569L18.2617 2.2928L1.29109 19.2634L0.583984 19.9705L1.29109 20.6776L18.2617 37.6481L18.9688 38.3552L20.383 36.941L19.6759 36.2339L4.44185 20.9999H37V18.9999H4.38297L19.6759 3.70701Z" fill="#1D1D1F" style="fill:#1D1D1F;fill:color(display-p3 0.1137 0.1137 0.1216);fill-opacity:1;"/>
      </svg>

      <svg v-show="themeClass === 'themeDark'" class="back-icon" width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M19.6759 3.70714L20.383 3.00003L18.9688 1.58582L18.2617 2.29292L1.29109 19.2635L0.583984 19.9706L1.29109 20.6777L18.2617 37.6483L18.9688 38.3554L20.383 36.9412L19.6759 36.234L4.44185 21H37V19H4.38297L19.6759 3.70714Z" fill="white" style="fill:white;fill-opacity:1;"/>
      </svg>
      搜索
    </div>
    
    <div class="tab" v-else>
      <div class="tab-item" 
           :class="{ 'active': currPageTab === 'search' }" 
           @click="handleSwitchPageTab('search')">
        快速点歌
      </div>
      <div class="tab-item" 
           :class="{ 'active': currPageTab === 'singer' }" 
           @click="handleSwitchPageTab('singer')">
        歌手点歌
      </div>
    </div>
    
    <div class="fast-order-page-main" v-show="currPageTab === 'search'">
      <SearchBar
        ref="searchInputRef"
        :isSearch="isSearch"
        @go-search="handleGoSearch"
        @search="handleSearch"
      />
      
      <div class="song-list" v-if="keyword">
        <div class="tab">
          <div class="tab-item tab-right" 
               :class="{ 'active': curSearchTab === 'song' }" 
               @click="handleSwitchSearchTab('song')">
            歌曲
          </div>
          <div class="tab-item" 
               :class="{ 'active': curSearchTab === 'singer' }" 
               @click="handleSwitchSearchTab('singer')">
            歌手
          </div>
        </div>

        <div v-show="isShowEmpty" class="empty">
          <svg v-show="themeClass == 'themeLight'" width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g opacity="0.4" clip-path="url(#clip0_346_155436)">
            <circle cx="62.1964" cy="33.4815" r="18.5557" stroke="#1D1D1F" stroke-width="4"/>
            <path d="M45.0664 38.9043L21.6423 68.7885L28.1023 75.2485L57.9865 51.8244" stroke="#1D1D1F" stroke-width="4"/>
            <path d="M18.8096 77.4078L20.1455 78.8963L20.1455 78.8963L18.8096 77.4078ZM9.2533 68.1959L10.7898 69.4763L9.2533 68.1959ZM15.6039 60.5749L17.1404 61.8552L17.1404 61.8552L15.6039 60.5749ZM12.9925 54.9995L12.9925 52.9995L12.9925 52.9995L12.9925 54.9995ZM12.75 52.9995C11.6454 52.9995 10.75 53.8949 10.75 54.9995C10.75 56.1041 11.6454 56.9995 12.75 56.9995L12.75 52.9995ZM9.79607 77.293L11.1694 75.8391L11.1694 75.8391L9.79607 77.293ZM15.2047 55.8179L13.9031 57.3364L15.2047 55.8179ZM24.6137 69.5115L17.4738 75.9194L20.1455 78.8963L27.2854 72.4884L24.6137 69.5115ZM10.7898 69.4763L17.1404 61.8552L14.0675 59.2945L7.71683 66.9156L10.7898 69.4763ZM12.9925 52.9995L12.75 52.9995L12.75 56.9995L12.9925 56.9995L12.9925 52.9995ZM11.1694 75.8391C9.38241 74.1512 9.21616 71.3647 10.7898 69.4763L7.71683 66.9156C4.79078 70.427 5.09992 75.6084 8.42274 78.747L11.1694 75.8391ZM17.4738 75.9194C15.6715 77.5369 12.9299 77.502 11.1694 75.8391L8.42274 78.747C11.6964 81.8391 16.7942 81.904 20.1455 78.8963L17.4738 75.9194ZM16.5063 54.2993C15.5277 53.4606 14.2814 52.9995 12.9925 52.9995L12.9925 56.9995C13.3265 56.9995 13.6495 57.119 13.9031 57.3364L16.5063 54.2993ZM17.1404 61.8552C19.0306 59.5869 18.7481 56.2209 16.5063 54.2993L13.9031 57.3364C14.4841 57.8344 14.5573 58.7067 14.0675 59.2945L17.1404 61.8552Z" fill="#1D1D1F"/>
            </g>
            <defs>
            <clipPath id="clip0_346_155436">
            <rect width="90" height="90" fill="white"/>
            </clipPath>
            </defs>
          </svg>
          <svg v-show="themeClass == 'themeDark'" width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g opacity="0.4" clip-path="url(#clip0_346_155436)">
            <circle cx="62.1964" cy="33.4815" r="18.5557" stroke="rgba(255, 255, 255, 0.80)" stroke-width="4"/>
            <path d="M45.0664 38.9043L21.6423 68.7885L28.1023 75.2485L57.9865 51.8244" stroke="rgba(255, 255, 255, 0.80)" stroke-width="4"/>
            <path d="M18.8096 77.4078L20.1455 78.8963L20.1455 78.8963L18.8096 77.4078ZM9.2533 68.1959L10.7898 69.4763L9.2533 68.1959ZM15.6039 60.5749L17.1404 61.8552L17.1404 61.8552L15.6039 60.5749ZM12.9925 54.9995L12.9925 52.9995L12.9925 52.9995L12.9925 54.9995ZM12.75 52.9995C11.6454 52.9995 10.75 53.8949 10.75 54.9995C10.75 56.1041 11.6454 56.9995 12.75 56.9995L12.75 52.9995ZM9.79607 77.293L11.1694 75.8391L11.1694 75.8391L9.79607 77.293ZM15.2047 55.8179L13.9031 57.3364L15.2047 55.8179ZM24.6137 69.5115L17.4738 75.9194L20.1455 78.8963L27.2854 72.4884L24.6137 69.5115ZM10.7898 69.4763L17.1404 61.8552L14.0675 59.2945L7.71683 66.9156L10.7898 69.4763ZM12.9925 52.9995L12.75 52.9995L12.75 56.9995L12.9925 56.9995L12.9925 52.9995ZM11.1694 75.8391C9.38241 74.1512 9.21616 71.3647 10.7898 69.4763L7.71683 66.9156C4.79078 70.427 5.09992 75.6084 8.42274 78.747L11.1694 75.8391ZM17.4738 75.9194C15.6715 77.5369 12.9299 77.502 11.1694 75.8391L8.42274 78.747C11.6964 81.8391 16.7942 81.904 20.1455 78.8963L17.4738 75.9194ZM16.5063 54.2993C15.5277 53.4606 14.2814 52.9995 12.9925 52.9995L12.9925 56.9995C13.3265 56.9995 13.6495 57.119 13.9031 57.3364L16.5063 54.2993ZM17.1404 61.8552C19.0306 59.5869 18.7481 56.2209 16.5063 54.2993L13.9031 57.3364C14.4841 57.8344 14.5573 58.7067 14.0675 59.2945L17.1404 61.8552Z" fill="rgba(255, 255, 255, 0.80)"/>
            </g>
            <defs>
            <clipPath id="clip0_346_155436">
            <rect width="90" height="90" fill="white"/>
            </clipPath>
            </defs>
          </svg>
          <p>抱歉，暂无“{{ keyword }}”的结果</p>
        </div>
        
        <div v-if="curSearchTab === 'song'" class="song-list">
          <LoadMore
            v-if="resultData.song.length"
            @load-more="getSearchResult"
            class="search-result"
          >
            <SongItem
              v-for="(songItem, index) in resultData.song"
              :key="index"
              :songItem="songItem"
              :log-from="{ song_list_source: isLogin ? 25 : 24, str1:'3-fast-order' }"
              @singer-click="handleClickSinger"
            />
          </LoadMore>
          <!-- <div class="empty" v-if="resultData.song.length <= 0 && !isRequest && !isInit">
            <svg v-show="themeClass == 'themeLight'" width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g opacity="0.4" clip-path="url(#clip0_346_155436)">
              <circle cx="62.1964" cy="33.4815" r="18.5557" stroke="#1D1D1F" stroke-width="4"/>
              <path d="M45.0664 38.9043L21.6423 68.7885L28.1023 75.2485L57.9865 51.8244" stroke="#1D1D1F" stroke-width="4"/>
              <path d="M18.8096 77.4078L20.1455 78.8963L20.1455 78.8963L18.8096 77.4078ZM9.2533 68.1959L10.7898 69.4763L9.2533 68.1959ZM15.6039 60.5749L17.1404 61.8552L17.1404 61.8552L15.6039 60.5749ZM12.9925 54.9995L12.9925 52.9995L12.9925 52.9995L12.9925 54.9995ZM12.75 52.9995C11.6454 52.9995 10.75 53.8949 10.75 54.9995C10.75 56.1041 11.6454 56.9995 12.75 56.9995L12.75 52.9995ZM9.79607 77.293L11.1694 75.8391L11.1694 75.8391L9.79607 77.293ZM15.2047 55.8179L13.9031 57.3364L15.2047 55.8179ZM24.6137 69.5115L17.4738 75.9194L20.1455 78.8963L27.2854 72.4884L24.6137 69.5115ZM10.7898 69.4763L17.1404 61.8552L14.0675 59.2945L7.71683 66.9156L10.7898 69.4763ZM12.9925 52.9995L12.75 52.9995L12.75 56.9995L12.9925 56.9995L12.9925 52.9995ZM11.1694 75.8391C9.38241 74.1512 9.21616 71.3647 10.7898 69.4763L7.71683 66.9156C4.79078 70.427 5.09992 75.6084 8.42274 78.747L11.1694 75.8391ZM17.4738 75.9194C15.6715 77.5369 12.9299 77.502 11.1694 75.8391L8.42274 78.747C11.6964 81.8391 16.7942 81.904 20.1455 78.8963L17.4738 75.9194ZM16.5063 54.2993C15.5277 53.4606 14.2814 52.9995 12.9925 52.9995L12.9925 56.9995C13.3265 56.9995 13.6495 57.119 13.9031 57.3364L16.5063 54.2993ZM17.1404 61.8552C19.0306 59.5869 18.7481 56.2209 16.5063 54.2993L13.9031 57.3364C14.4841 57.8344 14.5573 58.7067 14.0675 59.2945L17.1404 61.8552Z" fill="#1D1D1F"/>
              </g>
              <defs>
              <clipPath id="clip0_346_155436">
              <rect width="90" height="90" fill="white"/>
              </clipPath>
              </defs>
            </svg>
            <svg v-show="themeClass == 'themeDark'" width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g opacity="0.4" clip-path="url(#clip0_346_155436)">
              <circle cx="62.1964" cy="33.4815" r="18.5557" stroke="rgba(255, 255, 255, 0.80)" stroke-width="4"/>
              <path d="M45.0664 38.9043L21.6423 68.7885L28.1023 75.2485L57.9865 51.8244" stroke="rgba(255, 255, 255, 0.80)" stroke-width="4"/>
              <path d="M18.8096 77.4078L20.1455 78.8963L20.1455 78.8963L18.8096 77.4078ZM9.2533 68.1959L10.7898 69.4763L9.2533 68.1959ZM15.6039 60.5749L17.1404 61.8552L17.1404 61.8552L15.6039 60.5749ZM12.9925 54.9995L12.9925 52.9995L12.9925 52.9995L12.9925 54.9995ZM12.75 52.9995C11.6454 52.9995 10.75 53.8949 10.75 54.9995C10.75 56.1041 11.6454 56.9995 12.75 56.9995L12.75 52.9995ZM9.79607 77.293L11.1694 75.8391L11.1694 75.8391L9.79607 77.293ZM15.2047 55.8179L13.9031 57.3364L15.2047 55.8179ZM24.6137 69.5115L17.4738 75.9194L20.1455 78.8963L27.2854 72.4884L24.6137 69.5115ZM10.7898 69.4763L17.1404 61.8552L14.0675 59.2945L7.71683 66.9156L10.7898 69.4763ZM12.9925 52.9995L12.75 52.9995L12.75 56.9995L12.9925 56.9995L12.9925 52.9995ZM11.1694 75.8391C9.38241 74.1512 9.21616 71.3647 10.7898 69.4763L7.71683 66.9156C4.79078 70.427 5.09992 75.6084 8.42274 78.747L11.1694 75.8391ZM17.4738 75.9194C15.6715 77.5369 12.9299 77.502 11.1694 75.8391L8.42274 78.747C11.6964 81.8391 16.7942 81.904 20.1455 78.8963L17.4738 75.9194ZM16.5063 54.2993C15.5277 53.4606 14.2814 52.9995 12.9925 52.9995L12.9925 56.9995C13.3265 56.9995 13.6495 57.119 13.9031 57.3364L16.5063 54.2993ZM17.1404 61.8552C19.0306 59.5869 18.7481 56.2209 16.5063 54.2993L13.9031 57.3364C14.4841 57.8344 14.5573 58.7067 14.0675 59.2945L17.1404 61.8552Z" fill="rgba(255, 255, 255, 0.80)"/>
              </g>
              <defs>
              <clipPath id="clip0_346_155436">
              <rect width="90" height="90" fill="white"/>
              </clipPath>
              </defs>
            </svg>
            <p>抱歉，暂无“{{ keyword }}”的结果</p>
          </div> -->
          <p class="hint" 
             v-if="isEmpty && resultData.song.length">
            已加载全部
          </p>
        </div>
        
        <div v-else class="song-list">
          <LoadMore
            class="singer-list-padding search-result"
            ref="loadMoreRef"
            v-if="resultData.singer.length"
            @load-more="getSearchResult"
          >
            <div class="singer-list">
              <SingerItem
                  v-for="(item, index) in resultData.singer"
                  :key="index"
                  :singer="item"
                  @click="handleClickSinger(item, 'search')"
              />
            </div>
          </LoadMore>
          <!-- <div class="empty" 
               v-if="resultData.singer.length <= 0 && !isRequest && !isInit">
               <svg v-show="themeClass == 'themeLight'" width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g opacity="0.4" clip-path="url(#clip0_346_155436)">
              <circle cx="62.1964" cy="33.4815" r="18.5557" stroke="#1D1D1F" stroke-width="4"/>
              <path d="M45.0664 38.9043L21.6423 68.7885L28.1023 75.2485L57.9865 51.8244" stroke="#1D1D1F" stroke-width="4"/>
              <path d="M18.8096 77.4078L20.1455 78.8963L20.1455 78.8963L18.8096 77.4078ZM9.2533 68.1959L10.7898 69.4763L9.2533 68.1959ZM15.6039 60.5749L17.1404 61.8552L17.1404 61.8552L15.6039 60.5749ZM12.9925 54.9995L12.9925 52.9995L12.9925 52.9995L12.9925 54.9995ZM12.75 52.9995C11.6454 52.9995 10.75 53.8949 10.75 54.9995C10.75 56.1041 11.6454 56.9995 12.75 56.9995L12.75 52.9995ZM9.79607 77.293L11.1694 75.8391L11.1694 75.8391L9.79607 77.293ZM15.2047 55.8179L13.9031 57.3364L15.2047 55.8179ZM24.6137 69.5115L17.4738 75.9194L20.1455 78.8963L27.2854 72.4884L24.6137 69.5115ZM10.7898 69.4763L17.1404 61.8552L14.0675 59.2945L7.71683 66.9156L10.7898 69.4763ZM12.9925 52.9995L12.75 52.9995L12.75 56.9995L12.9925 56.9995L12.9925 52.9995ZM11.1694 75.8391C9.38241 74.1512 9.21616 71.3647 10.7898 69.4763L7.71683 66.9156C4.79078 70.427 5.09992 75.6084 8.42274 78.747L11.1694 75.8391ZM17.4738 75.9194C15.6715 77.5369 12.9299 77.502 11.1694 75.8391L8.42274 78.747C11.6964 81.8391 16.7942 81.904 20.1455 78.8963L17.4738 75.9194ZM16.5063 54.2993C15.5277 53.4606 14.2814 52.9995 12.9925 52.9995L12.9925 56.9995C13.3265 56.9995 13.6495 57.119 13.9031 57.3364L16.5063 54.2993ZM17.1404 61.8552C19.0306 59.5869 18.7481 56.2209 16.5063 54.2993L13.9031 57.3364C14.4841 57.8344 14.5573 58.7067 14.0675 59.2945L17.1404 61.8552Z" fill="#1D1D1F"/>
              </g>
              <defs>
              <clipPath id="clip0_346_155436">
              <rect width="90" height="90" fill="white"/>
              </clipPath>
              </defs>
            </svg>
            <svg v-show="themeClass == 'themeDark'" width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g opacity="0.4" clip-path="url(#clip0_346_155436)">
              <circle cx="62.1964" cy="33.4815" r="18.5557" stroke="rgba(255, 255, 255, 0.80)" stroke-width="4"/>
              <path d="M45.0664 38.9043L21.6423 68.7885L28.1023 75.2485L57.9865 51.8244" stroke="rgba(255, 255, 255, 0.80)" stroke-width="4"/>
              <path d="M18.8096 77.4078L20.1455 78.8963L20.1455 78.8963L18.8096 77.4078ZM9.2533 68.1959L10.7898 69.4763L9.2533 68.1959ZM15.6039 60.5749L17.1404 61.8552L17.1404 61.8552L15.6039 60.5749ZM12.9925 54.9995L12.9925 52.9995L12.9925 52.9995L12.9925 54.9995ZM12.75 52.9995C11.6454 52.9995 10.75 53.8949 10.75 54.9995C10.75 56.1041 11.6454 56.9995 12.75 56.9995L12.75 52.9995ZM9.79607 77.293L11.1694 75.8391L11.1694 75.8391L9.79607 77.293ZM15.2047 55.8179L13.9031 57.3364L15.2047 55.8179ZM24.6137 69.5115L17.4738 75.9194L20.1455 78.8963L27.2854 72.4884L24.6137 69.5115ZM10.7898 69.4763L17.1404 61.8552L14.0675 59.2945L7.71683 66.9156L10.7898 69.4763ZM12.9925 52.9995L12.75 52.9995L12.75 56.9995L12.9925 56.9995L12.9925 52.9995ZM11.1694 75.8391C9.38241 74.1512 9.21616 71.3647 10.7898 69.4763L7.71683 66.9156C4.79078 70.427 5.09992 75.6084 8.42274 78.747L11.1694 75.8391ZM17.4738 75.9194C15.6715 77.5369 12.9299 77.502 11.1694 75.8391L8.42274 78.747C11.6964 81.8391 16.7942 81.904 20.1455 78.8963L17.4738 75.9194ZM16.5063 54.2993C15.5277 53.4606 14.2814 52.9995 12.9925 52.9995L12.9925 56.9995C13.3265 56.9995 13.6495 57.119 13.9031 57.3364L16.5063 54.2993ZM17.1404 61.8552C19.0306 59.5869 18.7481 56.2209 16.5063 54.2993L13.9031 57.3364C14.4841 57.8344 14.5573 58.7067 14.0675 59.2945L17.1404 61.8552Z" fill="rgba(255, 255, 255, 0.80)"/>
              </g>
              <defs>
              <clipPath id="clip0_346_155436">
              <rect width="90" height="90" fill="white"/>
              </clipPath>
              </defs>
            </svg>
            <p>抱歉，暂无“{{ keyword }}”的结果</p>
          </div> -->
          <p class="hint" 
             v-if="isEmpty && resultData.singer.length > 20">
            已加载全部
          </p>
        </div>
      </div>
      
      <GuessSongList
        :isFastList="true"
        class="guess-song-list"
        showAllData
        v-if="!isSearch"
        pageRoute="3-mv-fast"
        :song_list_source="isLogin ? 35 : 34"
        :ponitActionLog="{ }"
        @singer-click="handleClickSinger"
      />
      
      <SearchHistory
        v-if="isSearch && !keyword"
        @clickWord="handleChangeSearchInput"
        @delete-word="handleDeleteSearchWord"
      />
    </div>
    
    <SingerOrder 
      v-show="currPageTab === 'singer'"
      :currPageTab="currPageTab" 
      @singer-click="handleClickSinger"
    />
  </div>
  
  <SingerDetail 
    v-if="pageViewName === 'singerDetail'" 
    :singerData="singerData" 
    @close="handleClose" 
    @back="handlechangePageViewName" 
  />
</template>

<script setup>
import { ref, watch, computed, defineEmits } from 'vue'
import { useStore } from 'vuex'
import SearchBar from './search.vue'
import SongItem from '@/components/song-item/index.vue'
import SingerItem from '@/components/singer-item/index.vue'
import GuessSongList from '@/components/guess-song/songlist.vue'
import SearchHistory from '@/components/search-history/index.vue'
import SingerOrder from './../singer-order/index-v1.vue'
import SingerDetail from './../singer-detail/index.vue'
import { search } from '@/service/search'
import { sendLog } from '@/directives/v-log/log'
import { setSearchCache } from '@/utils/historyCache'
import get from 'lodash/get'

const store = useStore()
const isLogin = computed(() => !!store.state.userInfo.unionid)
const searchCacheList = computed(() => store.state.search.searchCache)
const isShowEmpty = computed(() => {
  return !isRequest.value && !isInit.value && (
    curSearchTab.value === 'song' && !resultData.value.song.length ||
    curSearchTab.value === 'singer' && !resultData.value.singer.length
  )
})
const mvIsHide = computed(() => store.state.mvIsHide)
const emit = defineEmits(['click-retry'])

let pageViewName = ref('searchTabPage')
let singerData = ref({
  singerid: '',
  name: '',
  image: '',
}) // 侧边栏 - 歌手详情 -歌手数据

let currPageTab = ref('search')

let searchInputRef = ref(null)
let isSearch = ref(false)
let keyword = ref('')
let curSearchTab = ref('song')
let isEmpty = ref(false)
let resultData = ref({
  singer: [],
  song: []
})
let pagination = {
  singer: 1,
  song: 1
}
let page = 1
let isRequest = ref(false)
let isInit = ref(true)

const themeClass = computed(() => store.state.themeClass)

const handleGoSearch = () => {
  isSearch.value = true
  sendLog({
    event_type: '10000~50000',
    event_name: 10080,
    event_data: {
      str1: '快速点歌',
      str2: '搜索',
      str3: '点击搜索栏',
      str4: 'click',
    },
  })

  sendLog({
    event_type: '10000~50000',
    event_name: 6007,
    event_data: {
        str1: '欢唱页',
        str2: '快速点歌',
        str3: '搜索栏',
        str4: 'click',
    },
  })
}

const handleBackFastSearch = () => {
  isSearch.value = false
  curSearchTab.value = 'song'
  keyword.value = ''
  resultData.value.singer = []
  resultData.value.song = []
  if (searchInputRef.value) searchInputRef.value.changeInput('', true)
}

const handleSearch = async (k) => {
  keyword.value = k
}

const handleChangeSearchInput = (v) => {
  if (searchInputRef.value) searchInputRef.value.changeInput(v)
  sendLog({
    event_type: '10000~50000',
    event_name: 10084,
    event_data: {
      str1: '搜索',
      str2: '搜索历史',
      str3: '点击歌曲',
      str4: 'click',
    },
  })
}

const handleDeleteSearchWord = (v) => {
  sendLog({
      event_type: '10000~50000',
      event_name: 6002, 
      event_data: {
          str1: '搜索页',
          str2: '搜索历史',
          str3: v !== -1 ? '删除任意歌曲/内容' : '全部删除',
          str4: 'click',
      },
  })

  sendLog({
    event_type: '10000~50000',
    event_name: v !== -1 ? 10085 : 10086, // 10085 单首删除 10086 全部删除
    event_data: {
      str1: '搜索',
      str2: '搜索历史',
      str3: v !== -1 ? '单首删除' : '全部删除',
      str4: 'click',
    },
  })

  if(!mvIsHide.value){
    sendLog({
      event_type: '10000~50000',
      event_name: 6007, // 10085 单首删除 10086 全部删除
      event_data: {
          str1: '欢唱页',
          str2: '快速点歌-搜索历史',
          str3: v !== -1 ? '删除任意内容' : '全部删除',
          str4: 'click'
      }
    })
  }else{
    sendLog({
      event_type: '10000~50000',
      event_name: 6003, // 10085 单首删除 10086 全部删除
      event_data: {
          str1: '搜索页',
          str2: '搜索历史',
          str3: v !== -1 ? '删除任意内容' : '全部删除',
          str4: 'click',
      }
    })
  }
}

// const getSearchReportStatus = (res) => {
//   if (res.errcode !== 200) {
//     return 12
//   }
//   return (res.singer.length || res.song.length)
//     ? 10
//     : 11
// }

// // 搜索词条时 如果第一页中有一首歌名与搜索内容一致 则符合一键演唱功能
// const setSearchSongList = (list) => {
//   if (!!keyword.value && !resultData.value.song.length) {
//     list.some(v => {
//       const music_name = get(v, 'music_name', '').split('(HD)')[0]
//       if (music_name === keyword.value) {
//         store.dispatch('search/addSearchSong', {
//           ...v,
//           searchname: keyword.value
//         })
//         return true
//       }
//       return false
//     })
//   }
// }

// 存储搜索历史
const setSearchCacheList = (k) => {
  const keyword = k.trim()
  let newSearchCacheList = [keyword, ...searchCacheList.value.filter(item => item !== keyword)]
  newSearchCacheList = newSearchCacheList.slice(0, 10)
  store.dispatch('search/updateSearchCache', newSearchCacheList)
  setSearchCache(newSearchCacheList)
}

// // 优先展示搜索的vip歌曲
// const toSongvipSort = (arr) => {
//   let arr1 = [], arr2 = []
//   for(let i in arr) {
//     if (arr[i].is_vip) {
//       arr1.push(arr[i])
//     } else {
//       arr2.push(arr[i])
//     }
//   }
//   return arr1.concat(arr2)
// }

// const searchHandler = {
//   singer: async () => {
//     const res = await search(keyword.value, pagination.singer)
//     // 搜索上报 10:搜索到结果，11:未搜索到结果，12:接口异常
//     sendLog({
//       event_type: 'click',
//       event_name: 122,
//       event_data: {
//         key_words: keyword.value,
//         status: getSearchReportStatus(res)
//       }
//     })
//     if (res.singer.length) {
//       resultData.value.singer = resultData.value.singer.concat(res.singer)
//       pagination.singer ++
//       console.log('searchHandler', res)
//     }
//     isRequest.value = false
//     isInit.value = false
//     sendLog({
//       event_type: '10000~50000',
//       event_name: 10081,
//       event_data: {
//         str1: '快速点歌',
//         str2: '搜索',
//         str3: '点击搜索',
//         str4: 'click',
//       },
//     })
//   },
//   song: async () => {
//     const res = await search(keyword.value, pagination.song, 'song')
//     // 搜索上报 10:搜索到结果，11:未搜索到结果，12:接口异常
//     sendLog({
//       event_type: 'click',
//       event_name: 122,
//       event_data: {
//         key_words: keyword.value,
//         status: getSearchReportStatus(res)
//       }
//     })
//     if (res.song.length) {
//       const songvipsort = toSongvipSort(res.song)
//       setSearchSongList(songvipsort)
//       resultData.value.song = resultData.value.song.concat(songvipsort)
//       pagination.song ++
//     }
//     isRequest.value = false
//     isInit.value = false
//     sendLog({
//       event_type: '10000~50000',
//       event_name: 10081,
//       event_data: {
//         str1: '快速点歌',
//         str2: '搜索',
//         str3: '点击搜索',
//         str4: 'click',
//       },
//     })
//   }
// }

const fetchData = async () => {
  try {
    const res = await search(keyword.value, page)
    console.log(res)
    resultData.value = {
      singer: [
        ...resultData.value.singer,
        ...res.singer
      ],
      song: [
        ...resultData.value.song,
        ...res.song
      ],
    }
    
    page = page + 1
  } catch (error) {
    console.log('fast-order fetchData error', error)
  } finally {
    isRequest.value = false
    isInit.value = false
  }
}

const getSearchResult = async () => {
  if (isRequest.value) {
    return
  }
  isRequest.value = true
  // searchHandler[curSearchTab.value].call()
  fetchData()
}

const handleSwitchSearchTab = (tab) => {
  curSearchTab.value = tab
  // isInit.value = true
  if (!resultData.value[tab].length) {
    // searchHandler[tab].call()
    fetchData()
  }
}

const handleSwitchPageTab = (tab) => {
  currPageTab.value = tab

  if(tab == 'singer'){
    sendLog({
        event_type: '10000~50000',
        event_name: 6007,
        event_data: {
            str1: '欢唱页',
            str2: '快速点歌',
            str3: '歌手点歌',
            str4: 'click'
        },
    })
  }
}

const handleClickSinger = ({singer, singerhead, singerid}, type) => {
  if (type === 'search') {
    sendLog({
      event_type: '10000~50000',
      event_name: 10103,
      event_data: {
        str1: '快速点歌',
        str2: '搜索结果',
        str3: '点击任意歌手',
        str4: 'click',
      },
    })
  }

  sendLog({
    event_type: '10000~50000',
    event_name: 6007,
    event_data: {
        str1: '欢唱页',
        str2: '快速点歌',
        str3: '歌手区域',
        str4: 'click',
        str5: singerid
    }
  })
  singerData.value = {
    singerid,
    name: singer,
    image: singerhead,
  }
  pageViewName.value = 'singerDetail'
}

const handlechangePageViewName = () => {
  singerData.value = {
    singerid: '',
    name: '',
    image: '',
  }
  pageViewName.value = 'searchTabPage'
}

const handleClose = () => {
  emit('close')
}

watch(keyword, (k) => {
  if (k) {
    resultData.value = {
      singer: [],
      song: []
    }
    pagination = {
      singer: 1,
      song: 1
    }
    page = 1
    getSearchResult()
    setSearchCacheList(k)
  }
})
</script>

<style lang="stylus" scoped>
.fast-order-page
  width 100%
  height 100vh
  position relative
  display flex
  flex-direction column
  .search-result
    height 92%!important
  @media screen and (max-width: 1950px) and (min-width: 1700px) and (max-height: 720px) and (min-height: 650px)
    height calc(100vh * 1.4)
  @media screen and (max-width 1200px)
    height 1080px
  &-title
    color rgba(255, 255, 255, 0.8)
    font-size 40px
    margin 71px 0 53px
    width 200px
    height 48px
    line-height 48px
    font-weight 600
  &-back
    color rgba(255, 255, 255, 0.8)
    font-size 32px
    display flex
    align-items center
    margin 64px 0 84px 48px
    width 200px
    height 48px
    svg
      width 36px
      height 36px
      margin-right 30px
    @media screen and (max-width 1200px) and (min-height 1200px)
      height 30px
      font-size 26px
      margin 51px 0 51px 48px
      img
        width 26px
        height 26px
        margin-right 38px
  .close-side
    position absolute
    top 58px
    right 48px
    width 32px
    height 32px
    svg
      width 100%
      height 100%
    @media screen and (max-width 1200px) and (min-height 1200px)
      top 48px
  .tab
    width 100%
    height 100px
    margin-top 22px
    margin-bottom 40px
    border-bottom 2px solid rgba(255,255,255,0.1)
    display flex
    align-items center
    justify-content center
    &-item
      display flex
      justify-content center
      align-items center
      width 173px
      height 100px
      font-size 32px
      color rgba(255, 255, 255, 0.40)
      &.active
        color #dbae6a
        border-bottom 2px solid #DBAE6A
      &:nth-child(1)
        margin-right 136px
    &-right
      margin-right 128px !important
  .singer-order
    width 202px
    height 90px
    color #fff
    opacity 0.8
    font-size 28px
    display flex
    align-items center
    justify-content center
    position absolute
    top 43px
    right 158px
    background #383A3E
    border-radius 14px
    img
      width 29.6px
      height 28.8px
      margin-right 9px
  &-main
    width 100%
    flex 1
    padding 0
    display flex
    flex-direction column
    @media screen and (max-width 1200px)
      display flex
      flex-direction column
      align-items center
    .guess-song-list
      margin 0 !important
      padding 48px !important
      min-height calc(100vh - 276px) !important
      ::v-deep .section-container-header
        padding-bottom 10px
        .section-container-header-title
          font-size 28px
          font-weight 400
          @media screen and (max-width 1200px)
            font-size 24px
      ::v-deep .sec-gusse-sing-list
        grid-template-columns repeat(1, 704px) !important
        overflow-y scroll
        scroll-behavior smooth
        height 711px
        .sec-gusse-sing-list-item
          width 704px !important
          .order-btn
            width 160px !important
          .author
            max-width 160px
            overflow hidden
            white-space nowrap
            text-overflow ellipsis
            @media screen and (max-width 1200px)
              max-width 160px
          .right
            margin-right 0 !important
    ::v-deep .search-history
      margin-top 48px
      padding 0 48px
      .search-history-title
        height 32px
        span
          opacity 0.4
          font-size 28px
          margin-right 12px
        img
          // width 36px
          // height 36px
          opacity 0.6
        @media screen and (max-width 1200px)
          span
            font-size 24px
      .search-history-content
        margin-top 26px
        max-height 600px
        overflow-y scroll
        @media screen and (max-width 1200px)
          max-height 400px
        .search-history-content-item
          background rgba(56, 58, 62, 0.50)
          margin-right 24px
          margin-bottom 24px
          span
            color rgba(255, 255, 255, 0.6)
            margin-right 24px
          &-d-img
            opacity 0.7
          &-s-img
            display none
    .song-list
      width 100%
      box-sizing border-box
      padding 0
      margin-top 20px
      flex 1
      ::-webkit-scrollbar
        display none
      // ::-webkit-scrollbar-track
      //   background #1E1F21
      ::v-deep .song-item
        height 141px
        margin 0 48px
        padding 0
        .right
          margin-right 0
        .name
          color rgba(255, 255, 255, 0.70)
          .name-txt
            max-width 600px
        .ordered
          .name
            color #dbae6a
      @media screen and (max-width 1200px)
        width calc(100vw - 120px) !important
        padding 0 20px
        ::v-deep .song-item
          padding 0
          .name
            font-size 26px
          .desc
            font-size 20px
          .order-btn
            width 160px
            height 64px
            font-size 22px
      .empty
        display flex
        flex-direction column
        justify-content center
        align-items center
        margin-top 30px
        font-size 28px
        color rgba(255, 255, 255, 0.40)
        text-align center
        svg
          width 80px
          height 80px
          margin-bottom 40px
        p
          height 32px
          line-height 32px
        @media screen and (max-width 1200px) and (min-height 1200px)
          margin-top 14vh
          font-size 22px
          img
            width 72px
            height 72px
      .hint
        text-align center
        color #555555
      .singer-list
        margin 0 auto
        display grid
        grid-template-columns repeat(4, 140px)
        justify-content space-between
        padding-top 12px
        padding-left 48px
        padding-right 48px
        ::v-deep .singer-item
          width 140px
          margin-bottom 40px
          font-size 24px
          margin-right 45px
          &:nth-child(4n)
            margin-right 0
          .singer-item-cover
            width 140px
            height 140px
            margin-bottom 20px
          p
            width 140px
      .singer-list-padding
        padding-left 48px !important
        padding-right 48px !important
        padding-bottom 150px !important
        @media screen and (max-width 1280px) and (max-height 720px)
          height calc(100vh - 25vw)!important
.theme-themeLight
  .tab
    border-bottom-color rgba(29, 29, 31, 0.1)!important
    .tab-item
      color rgba(29, 29, 31, 0.5)!important
      &.active
        color: rgba(122, 83, 231, 1)!important
        border-bottom-color rgba(122, 83, 231, 1)!important
  .fast-order-page-back, .empty
    color #1D1D1FE5!important
</style>
