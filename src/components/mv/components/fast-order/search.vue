<template>
  <div class="fast-search" :class="[isSearch && 'pos-ab']">
    <div class="fast-search-main" :class="[isSearch && 'width_480']" @click="handleToSearch">
      <img class="fast-search-icon" :src="imgs[themeClass].icon"/>
      <!-- 、歌手 -->
      <input
        type="text"
        placeholder="搜索歌曲、歌手"
        v-model="keyword"
        class="fast-search-input"
        :disabled="!isSearch"
        ref="searchInput"
        maxlength="20"
        autocapitalize="off"
        @keydown="handleSearchKeydown($event)"
        @mousedown="disableDoubleClickSelection"
      >
      <div
        v-show="keyword"
        class="clear"
        @click.stop="keyword = ''"
      >
        <img v-show="themeClass === 'themeLight'" src="@/assets/close.png" />
        <img v-show="themeClass !== 'themeLight'" src="@/assets/close_dark.png" />
      </div>
      <div v-show="!isSearch" class="fast-search-mask"></div>
    </div>
    <div v-if="isSearch" class="fast-search-btn" @click="handleSearch('btn')">
      搜索
    </div>
  </div>
</template>

<script>
import { ref, toRefs, computed } from 'vue'
import { useStore } from 'vuex'
import Toast from '@/utils/toast'
import { sendLog } from "@/directives/v-log/log";

export default {
  name: 'SearchBar',
  props: {
    isSearch: Boolean,
  },
  setup(props, { emit }) {
    const store = useStore()
    let keyword = ref('')
    const searchInput = ref(null)
    const { isSearch } = toRefs(props)

    const imgs = {
      themeDark: {
        icon: require('@/assets/fast-search-dark.png'),
      },
      themeLight: {
        icon: require('@/assets/fast-search-light.png'),
      },
      themeSystem: {
        icon: require('@/assets/fast-search-dark.png'),
      },
    }

    const themeClass = computed(() => store.state.themeClass)

    const handleToSearch = () => {
      if (isSearch.value) return

      emit('go-search')
      setTimeout(() => {
        searchInput.value.focus()
      }, 0)
    }

    const handleSearch = (type) => {
      if (!keyword.value || !keyword.value.trim()) {
        Toast('请输入搜索内容')
        emit('search', '')
        return
      }
      emit('search', keyword.value)
      searchInput.value.blur()

      if(type == 'btn'){
        sendLog({
          event_type: '10000~50000',
          event_name: 6007,
          event_data: {
            str1: '欢唱页',
            str2: '搜索结果',
            str3: '点击搜索',
            str4: 'click',
          }
        })
      }
    }

    const handleSearchKeydown = (e) => {
      if (e.keyCode == 13) {
        handleSearch()
      }
    }

    const changeInput = (e, isForceSearch) => {
      keyword.value = e
      if (isForceSearch) return
      handleSearch()
    }

    const disableDoubleClickSelection = (event) => {
      if (event.detail > 1) {
        event.preventDefault();
      }
    }
    
    return {
      keyword,
      searchInput,
      handleToSearch,
      handleSearch,
      handleSearchKeydown,
      changeInput,
      imgs,
      themeClass,
      disableDoubleClickSelection
    }
  }
}
</script>

<style lang="stylus" scoped>
  .fast-search
    display flex
    align-items center
    justify-content space-between
    width 704px
    margin-left 48px
    &-main
      display flex
      align-items center
      position relative
      width 704px
      height 88px
      border 2px solid rgba(255, 255, 255, 0.20)
      border-radius 4px
    .clear
      width 30px
      position absolute
      right 20px
      top 50%
      margin-top -15px
    &-icon
      width 36px
      height 36px
      margin 0 28px
    &-input
      flex 1
      height 40px
      display flex
      align-items center
      font-size 28px
      color rgba(255, 255, 255, 0.6)
      overflow hidden
      margin-right 45px
    &-mask
      position absolute
      top 0
      right 0
      left 0
      bottom 0
      width 100%
      height 100%
    &-btn
      width 200px
      height 88px
      line-height 88px
      border-radius 4px
      backdrop-filter blur(100px)
      background: #E3AB5D;
      color: rgba(0, 0, 0, 0.80);
      text-align center
      font-size 28px
      font-weight 400
      margin-left 20px
    .width_480
      width 480px
    @media screen and (max-width 1200px)
      margin-left 0
      justify-content center
      &-main
        width 482px
      &-icon
        width 26px
        height auto
      &-input, &-btn
        font-size: 22px;
      .width_480
        width 482px
.theme-themeLight
  .fast-search-main
    border-color rgba(29, 29, 31, 0.2)!important
  .fast-search-btn
    background #7A53E7!important
    color #FFFFFFCC!important
  input
    color #1D1D1FE5
</style>