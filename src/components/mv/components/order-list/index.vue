<template>
  <div class="side-order-list" v-if="pageViewName === 'orderTabPage'">
    <div class="header">
      <div class="tab">
        <div
          class="tab-item"
          v-for="(tab, index) in tabList"
          :key="index"
          :class="{ actived: curTab.name === tab.name }"
          @click="handleChangeTab(tab)"
        >
          {{ tab.text }}
        </div>
      </div>
    </div>
    <div class="close-side" @click="$emit('close')">
      <svg v-show="themeClass === 'themeLight'" width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g opacity="0.4">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M3.74219 26.3708L5.62781 28.2564L15.9987 17.8855L26.3698 28.2566L28.2554 26.371L17.8843 15.9999L28.2552 5.62903L26.3696 3.74341L15.9987 14.1143L5.628 3.7436L3.74238 5.62922L14.1131 15.9999L3.74219 26.3708Z" fill="#1D1D1F" style="fill:#1D1D1F;fill:color(display-p3 0.1137 0.1137 0.1216);fill-opacity:1;"/>
        </g>
      </svg>
      <svg v-show="themeClass === 'themeDark'" width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g opacity="0.4">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M3.74219 26.3708L5.62781 28.2564L15.9987 17.8855L26.3698 28.2566L28.2554 26.371L17.8843 15.9999L28.2552 5.62903L26.3696 3.74341L15.9987 14.1143L5.628 3.7436L3.74238 5.62922L14.1131 15.9999L3.74219 26.3708Z" fill="white" style="fill:white;fill-opacity:1;"/>
        </g>
      </svg>
    </div>
    <component
      :is="currentComponent"
      @singer-click="handleClickSinger"
      from="mvOrder"
    ></component>
  </div>
  <SingerDetail
    v-if="pageViewName === 'singerDetail'"
    :singerData="singerData"
    @close="$emit('close')"
    @back="handlechangePageViewName"
  />
</template>

<script setup>
import { ref, defineEmits, computed } from 'vue'
import { useStore } from 'vuex'
import AlreadySongList from '@/components/song-list/already/index.vue'
import OrderSongList from '@/components/song-list/order/index.vue'
import SingerDetail from './../singer-detail/index.vue'
import { sendLog } from '@/directives/v-log/log'

const emit = defineEmits(['close'])


const currentComponent = computed(() => {
  return curTab.value.name === 'ordered' ? OrderSongList : AlreadySongList;
});

const store = useStore()
const orderedListNum = computed(() => store.state.orderedList.length)
const alreadyListNum = computed(() => store.state.alreadyList.length)
const tabList = computed(() => {
  return [{
    name: 'ordered',
    text: `已点(${orderedListNum.value > 99 ? '99' : orderedListNum.value})`,
  }, {
    name: 'already',
    text: `已唱(${alreadyListNum.value > 99 ? '99' : alreadyListNum.value})`,
  }]
})
let curTab = ref(tabList.value[0])

let pageViewName = ref('orderTabPage')
let singerData = ref({
  singerid: '',
  name: '',
  image: '',
}) // 侧边栏 - 歌手详情 - 歌手数据

const themeClass = computed(() => store.state.themeClass)

const handleChangeTab = (tab) => {
  if (curTab.value.name === tab.name) return
  curTab.value = tab
  
  // if (tab.text === '已唱') {
  //   sendLog({
  //     event_type: '10000~50000',
  //     event_name: 10061,
  //     event_data: {
  //       str1: '已点',
  //       str2: '已唱',
  //       str3: '进入已唱',
  //       str4: 'click',
  //     },
  //   })
  // }
  if (tab.name === 'already') {
    sendLog({
        event_type: '10000~50000',
        event_name: 6007,
        event_data: {
        str1: '欢唱页',
        str2: '已点',
        str3: '已唱tab',
        str4: 'click',
        },
    })

    sendLog({
      event_type: '10000~50000',
      event_name: 6012,
      event_data: {
      str1: '通用',
      str2: '已点/已唱弹窗',
      str3: '已唱tab',
      str4: 'click',
      },
    })
  }else{
    sendLog({
      event_type: '10000~50000',
      event_name: 6007,
      event_data: {
      str1: '欢唱页',
      str2: '已点',
      str3: '已点tab',
      str4: 'click',
      },
    })

    sendLog({
      event_type: '10000~50000',
      event_name: 6012,
      event_data: {
      str1: '通用',
      str2: '已点/已唱弹窗',
      str3: '已点tab',
      str4: 'click',
      },
    })
  }
}

const handleClickSinger = ({singer, singerhead, singerid}) => {
  singerData.value = {
    singerid,
    name: singer,
    image: singerhead,
  }
  pageViewName.value = 'singerDetail'
}

const handlechangePageViewName = () => {
  singerData.value = {
    singerid: '',
    name: '',
    image: '',
  }
  pageViewName.value = 'orderTabPage'
}

</script>
<style lang="stylus" scoped>
.side-order-list
  width 100%
  height 100vh
  padding 0
  position relative

  ::v-deep .song-list
    .tip
      bottom -50px
  @media screen and (max-width: 1950px) and (min-width: 1700px) and (max-height: 720px) and (min-height: 650px)
    height 100%
  .empty
    margin-top 375px !important
    padding-top 0 !important
  ::v-deep .song-list
    height calc(100% - 220px) !important
  .close-side
    position absolute
    top 53px
    right 48px
    width 32px
    height 32px
    img
      width 100%
      height 100%
  .header
    width 100%
    height 100px
    display flex
    justify-content space-between
    align-items center
    border-bottom 2px solid rgba(255,255,255,0.1)
    margin-top 22px
    margin-bottom 40px
    .tab
      width 100%
      height 100px
      display flex
      align-items center
      justify-content center
      &-item
        display flex
        justify-content center
        align-items center
        width 173px
        height 100px
        font-size 32px
        color rgba(255, 255, 255, 0.40)
        &.actived
          color #dbae6a
          border-bottom 2px solid #DBAE6A
        &:nth-child(1)
          margin-right 136px
  @media screen and (max-width 1200px) and (min-height 1200px)
    .header
      height 92px
      margin-top 20px
      margin-bottom 12px
      .tab
        height 92px
        &-item
          height 92px
          font-size 26px
          // &:nth-child(1)
          //   margin-right 340px
    .close-side
      top 48px
      right 48px
    ::v-deep .song-list
      height 948px
      padding 0 60px
.theme-themeLight
  .header
    border-bottom-color rgba(29, 29, 31, 0.1)!important
    .tab-item
      color rgba(29, 29, 31, 0.5)!important
      &.actived
        color: rgba(122, 83, 231, 1)!important
        border-bottom-color rgba(122, 83, 231, 1)!important
</style>
