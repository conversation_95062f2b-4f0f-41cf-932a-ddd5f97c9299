<template>
  <div class="mv-user-vip">
    <div class="mv-user-vip-back">
      <div class="left">
        我的特权
      </div>
      <div
        class="close"
        @click="$emit('close')"
      >
        <svg v-show="themeClass === 'themeLight'" width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g opacity="0.4">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M3.74219 26.3708L5.62781 28.2564L15.9987 17.8855L26.3698 28.2566L28.2554 26.371L17.8843 15.9999L28.2552 5.62903L26.3696 3.74341L15.9987 14.1143L5.628 3.7436L3.74238 5.62922L14.1131 15.9999L3.74219 26.3708Z" fill="#1D1D1F" style="fill:#1D1D1F;fill:color(display-p3 0.1137 0.1137 0.1216);fill-opacity:1;"/>
          </g>
        </svg>
        <svg v-show="themeClass === 'themeDark'" width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g opacity="0.4">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M3.74219 26.3708L5.62781 28.2564L15.9987 17.8855L26.3698 28.2566L28.2554 26.371L17.8843 15.9999L28.2552 5.62903L26.3696 3.74341L15.9987 14.1143L5.628 3.7436L3.74238 5.62922L14.1131 15.9999L3.74219 26.3708Z" fill="white" style="fill:white;fill-opacity:1;"/>
          </g>
        </svg>
      </div>
    </div>
    <MineVip v-if="isLogin" from="mv" />
    <div v-else class="mv-user-vip-unlogin">
      <h3>登录查看特权</h3>
      <p>微信扫码，记录唱过的歌</p>
      <div class="qrcode">
        <img v-if="!needReload" :src="qrCodeURL">
        <div v-else class="net-error" @click="getLoginQrcode('reload')">
          <svg :class="isRequest && 'active'" width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g opacity="0.4" clip-path="url(#clip0_1270_101510)">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M50.9798 10.5088C49.0366 10.1743 47.0387 10 45 10C25.67 10 10 25.67 10 45C10 56.1164 15.1825 66.0224 23.2632 72.4337L27.1902 70.3766C19.2129 64.7677 14 55.4926 14 45C14 27.8792 27.8792 14 45 14C45.4133 14 45.8247 14.0081 46.2341 14.0241L50.9798 10.5088ZM62.6381 19.5035C70.7122 25.0996 76 34.4323 76 45C76 62.1208 62.1208 76 45 76C44.4595 76 43.9222 75.9862 43.3885 75.9588L38.697 79.434C40.7416 79.8058 42.8481 80 45 80C64.33 80 80 64.33 80 45C80 33.8105 74.7491 23.8474 66.577 17.4403L62.6381 19.5035Z" fill="black" style="fill:black;fill-opacity:1;"/>
            <path d="M45.0424 80L48.3127 72.3843C48.3712 72.2479 48.4006 72.1796 48.4134 72.1425C48.7162 71.2627 47.8202 70.4473 46.9728 70.8315C46.9371 70.8477 46.8719 70.8833 46.7416 70.9545L37.0398 76.2512C36.3988 76.6012 36 77.2732 36 78.0035C36 78.5837 36.5272 78.9725 37.0924 79.103C39.6336 79.6899 42.2806 80 45 80C45.0141 80 45.0283 80 45.0424 80Z" fill="black" style="fill:black;fill-opacity:1;"/>
            <path d="M44.5523 10.0028L41.2832 17.6158C41.2246 17.7522 41.1953 17.8204 41.1825 17.8575C40.8796 18.7373 41.7757 19.5527 42.623 19.1685C42.6588 19.1523 42.7239 19.1167 42.8543 19.0455L52.5561 13.7488C53.1971 13.3988 53.5959 12.7268 53.5959 11.9965C53.5959 11.3276 52.9615 10.9029 52.3073 10.7639C49.9505 10.2634 47.506 10 45 10C44.8505 10 44.7013 10.0009 44.5523 10.0028Z" fill="black" style="fill:black;fill-opacity:1;"/>
            </g>
            <defs>
            <clipPath id="clip0_1270_101510">
            <rect width="90" height="90" fill="white" style="fill:white;fill-opacity:1;"/>
            </clipPath>
            </defs>
          </svg>
          <p>网络异常</p>
          <p>点击刷新二维码</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onBeforeMount, watch, nextTick } from 'vue'
import { useStore } from 'vuex'
import { getCarplayInfo } from '@/service/carplay-info'
import useQRCode from '@/composables/useQRCode'
import get from 'lodash/get'
import MineVip from '@/components/vip/index.vue'
import { vipLogFrom } from '@/constants/index'
import { sendLog } from "@/directives/v-log/log";

const store = useStore()
const isLogin = computed(() => !!store.state.userInfo.unionid)
const { getQRCodeURL } = useQRCode()
let needReload = ref(false)
let isRequest = ref(false)

let qrCodeURL = ref('https://qncweb.ktvsky.com/20211130/vadd/82b806b8bf560e49df69162835879c65.png')

const themeClass = computed(() => store.state.themeClass)
const net_status = computed(() => store.state.base.net_status);

const getLoginQrcode = async (payload) => {
  // if(!isLogin.value){
    sendLog({
      event_type: '10000~50000',
      event_name: 6007,
      event_data: {
        str1: '欢唱页',
        str2: '我的特权',
        str3: '登录码',
        str4: 'show',
      }
    })

    sendLog({
      event_type: '10000~50000',
      event_name: 1021,
      event_data: {
        str1: 'MV 页面',
        str2: '我的',
        str3: '登录弹窗',
        str4: 'show',
        str5: 1,
        str9: '车机端',
        str10: '1833'
      }
    })
  // }
  // 检查请求状态和网络状态
  if (isRequest.value || !net_status.value && payload !== 'reload') return;

  isRequest.value = true
  await nextTick()
  try {
    const { data } = await getCarplayInfo(payload === 'reload')
    isRequest.value = false
    if (get(data, 'login_qr', '')) {
      needReload.value = false
      const loginUrl = `${data.pay_qr}&log=${vipLogFrom.get('mv-我的-未登录')}`
      const qrCodeData = await getQRCodeURL(loginUrl)
      if (qrCodeData) {
        qrCodeURL.value = qrCodeData
      }
      return
    }

    // 未获取到的情况
    needReload.value = true
  } catch (error) {
    console.error('获取登录二维码失败:', error);
    needReload.value = true;
  } finally {
    isRequest.value = false;
  }
}

onBeforeMount(getLoginQrcode)

watch(net_status, async (val) => {
  if (!val) {
    qrCodeURL.value = ''
    needReload.value = true
  } else {
    getLoginQrcode()
  }
}, { immediate: true, deep: true })
</script>

<style lang="stylus" scoped>
.mv-user-vip
  width 100%
  &-back
    display flex
    justify-content space-between
    align-items center
    margin 22px 48px 22px 50px
    .left
      display flex
      align-items center
      height 120px
      color: rgba(255, 255, 255, 0.80);
      font-size: 32px;
    .close
      width 32px
      height 32px
    @media screen and (max-width 1200px) and (min-height 1200px)
      margin 3px 48px 3px 60px
      .left
        font-size 26px
  &-unlogin
    display flex
    flex-direction column
    align-items center
    padding-top 171px
    h3
      color: rgba(255, 255, 255, 0.80);
      font-size: 44px;
      font-weight: 700;
    p
      color: rgba(255, 255, 255, 0.40);
      font-size: 24px;
    .qrcode
      width: 266px;
      height: 266px;
      border-radius 10px
      background #fff
      margin-top 48px
      img
        width 246px
        height 246px
        margin 10px
.net-error
  width 100%!important
  height 100%!important
.theme-themeLight
  .left, .mv-user-vip-unlogin h3
    color #1D1D1FE5
  .mv-user-vip-unlogin p
    color #1D1D1F80
</style>
