<template>
  <div class="mv-quality-side">
    <div class="mv-quality-side-title">
      <div class="left">画质选择</div>
      <div
        class="close"
        @click="$emit('close')"
      >
        <svg v-show="themeClass === 'themeLight'" width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g opacity="0.4">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M3.74219 26.3708L5.62781 28.2564L15.9987 17.8855L26.3698 28.2566L28.2554 26.371L17.8843 15.9999L28.2552 5.62903L26.3696 3.74341L15.9987 14.1143L5.628 3.7436L3.74238 5.62922L14.1131 15.9999L3.74219 26.3708Z" fill="#1D1D1F" style="fill:#1D1D1F;fill:color(display-p3 0.1137 0.1137 0.1216);fill-opacity:1;"/>
          </g>
        </svg>
        <svg v-show="themeClass === 'themeDark'" width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g opacity="0.4">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M3.74219 26.3708L5.62781 28.2564L15.9987 17.8855L26.3698 28.2566L28.2554 26.371L17.8843 15.9999L28.2552 5.62903L26.3696 3.74341L15.9987 14.1143L5.628 3.7436L3.74238 5.62922L14.1131 15.9999L3.74219 26.3708Z" fill="white" style="fill:white;fill-opacity:1;"/>
          </g>
        </svg>
      </div>
    </div>
    <div class="mv-quality-side-main">
      <div
        class="mv-quality-side-main-item"
        v-for="(quality, index) in qualityList"
        :key="index"
        @click="handleChoose(quality)"
        :class="playingMvQuality == quality  && 'mv-quality-side-main-item-active'"
      >
        <p class="title">{{ quality }}</p>
        <p class="des">
          {{ qualityLog[quality][1] }}
          <img v-if="quality === '1080'" src="https://qncweb.ktvsky.com/20230830/vadd/c6f52232a92ac7d2273b244a5d6daf05.png">
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useStore } from 'vuex'
import useVip from '@/composables/useVip'
import useQuality from '@/composables/useQuality'
import { sendLog } from '@/directives/v-log/log'
import eventBus from '@/utils/event-bus'

const store = useStore()
const { showVipQrcode } = useVip()
const { useMvQuality } = useQuality()

const isLogin = computed(() => !!store.state.userInfo.unionid)
const isVip = computed(() => !!store.state.vipInfo.end_time)
const playingMvQuality = computed(() => store.state.playingMvQuality)
const videoPlayer = computed(() => store.state.videoPlayerHistory)
const qualityList = computed(() => store.state.availableQualities)

const qualityLog = {
  480: [10090, '标清'],
  720: [10091, '高清'],
  1080: [10092, '1080p']
}

const themeClass = computed(() => store.state.themeClass)

const handleChoose = (quality) => {
  if (playingMvQuality.value === quality) return
  
  sendLog({
    event_type: '10000~50000',
    event_name: 6007,
    event_data: {
      str1: '欢唱页',
      str2: '画质选择',
      str3: `${quality.value}P`,
      str4: 'click'
    }
  })
   if (quality === '1080'){
    if(!isLogin.value){
      showVipQrcode({
        mType: 'mv页画质vip弹窗',
        log: 'mv-画质1080P-未登录'
      })
      sendLog({
        event_type: '10000~50000',
        event_name: 6012,
        event_data: {
          str1: '通用',
          str2: '画质弹窗',
          str3: 'VIP画质弹窗展示',
          str4: 'show'
        }
      })

      sendLog({
        event_type: '10000~50000',
        event_name: 1021,
        event_data: {
          str1: 'MV 页面',
          str2: '画质-1080P',
          str3: '触发 VIP 弹窗',
          str4: 'click',
          str5: 1,
          str7: videoPlayer.value.songItem.songid,
          str9: '手机端',
          str10: '1829'
        }
      })
      return
    }

    if(!isVip.value){
      showVipQrcode({
        mType: 'mv页画质vip弹窗',
        log: 'mv-画质1080P-已登录'
      })
      sendLog({
        event_type: '10000~50000',
        event_name: 6012,
        event_data: {
          str1: '通用',
          str2: '画质弹窗',
          str3: 'VIP画质弹窗展示',
          str4: 'show'
        }
      })

      sendLog({
        event_type: '10000~50000',
        event_name: 1021,
        event_data: {
          str1: 'MV 页面',
          str2: '画质-1080P',
          str3: '触发 VIP 弹窗',
          str4: 'click',
          str5: 2,
          str7: videoPlayer.value.songItem.songid,
          str9: '手机端',
          str10: '1830'
        }
      })
      return
    }
   }
  // if (quality === '1080' && !isLogin.value) {
  //   showVipQrcode({
  //     mType: 'mv页画质vip弹窗',
  //     log: '画质切换-1080'
  //   })

  //   sendLog({
  //     event_type: '10000~50000',
  //     event_name: 6012,
  //     event_data: {
  //       str1: '通用',
  //       str2: '画质弹窗',
  //       str3: 'VIP画质弹窗展示',
  //       str4: 'show'
  //     }
  //   })

  //   return
  // }
  // if (quality === '1080' && !isVip.value) {
  //   showVipQrcode({
  //     mType: 'mv页画质vip弹窗',
  //     log: '画质切换-1080'
  //   })
  //   sendLog({
  //     event_type: '10000~50000',
  //     event_name: 6012,
  //     event_data: {
  //       str1: '通用',
  //       str2: '画质弹窗',
  //       str3: 'VIP画质弹窗展示',
  //       str4: 'show'
  //     }
  //   })
  //   return
  // }
  eventBus.emit('video-quality-change')
  useMvQuality(quality)
  sendLog({
    event_type: 'custom',
    event_name: 1070,
    event_data: {
      str1: '歌曲清晰度埋点',
      str2: videoPlayer.value.songItem.songid,
      str3: videoPlayer.value.songItem.music_name,
      str4: videoPlayer.value.songItem.singer,
      str5: `${quality}P`, // 画质
      str6: '2' // 上报时机
    }
  })
  if (qualityLog[quality]) {
    sendLog({
      event_type: '10000~50000',
      event_name: qualityLog[quality][0],
      event_data: {
        str1: '画质选择',
        str2: '选择区',
        str3: qualityLog[quality][1],
        str4: 'click',
      },
    })
  }
}
</script>

<style lang="stylus" scoped>
.mv-quality-side
  width 100%
  height 100vh
  padding 0 48px
  position relative
  &-title
    display flex
    justify-content space-between
    align-items center
    margin 22px 0 60px
    .left
      display flex
      align-items center
      height 120px
      color: rgba(255, 255, 255, 0.80);
      font-size: 32px;
    .close
      width 32px
      height 32px
  &-main
    width 100%
    height auto
    display flex
    flex-direction column
    align-items center
    &-item
      width 704px
      height 180px
      margin-bottom 36px
      display flex
      flex-direction column
      align-items center
      justify-content center
      border-radius 10px
      background: linear-gradient(94deg, rgba(56, 58, 62, 0.90) 0.37%, rgba(56, 58, 62, 0.50) 98.8%);
      position relative
      .title
        color rgba(255, 255, 255, 0.8)
        font-size 56px
        font-weight 500
        height 66px
        margin-bottom 12px
      .des
        color rgba(255, 255, 255, 0.5)
        font-size 28px
        display flex
        align-items center
        justify-content center
        font-weight 400
        img
          width 56px
          height 28px
          margin-left 12px
      &-active
        border: 4px solid #E3AB5D;
        background: rgba(228, 172, 94, 0.10);
      &-unactive
        display none
        .title
          color rgba(255, 255, 255, 0.10) !important
        .des
          color rgba(255, 255, 255, 0.10) !important
    @media screen and (max-width 1200px) and (min-height 1200px)
      flex-direction row
      margin-top 16px
      &-item
        width 340px
        height 200px
        margin 0
        .title
          font-size 44px
        .des
          font-size 22px
        &:nth-child(2)
          margin 0 30px
.theme-themeLight
  .mv-quality-side-title
    .left
      color #1D1D1FE5
  .mv-quality-side-main-item
    background #FFFFFF
    .title
      color #1D1D1FE5
    .des
      color: #1D1D1F80;
    &-active
      border-color #7A53E7
      background: #6341C31A;
</style>