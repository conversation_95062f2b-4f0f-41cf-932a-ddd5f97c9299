<template>
  <div id="mvVue">
    <div class="page mv mv-no-scroll">
      <MvSideBar :pos="isShowMvSideBar">
        <OrderList
          v-if="mvSlideBarItem === '已点'"
          @close="handleCloseMvSideBar"
        />
        <ChooseQuality
          v-if="mvSlideBarItem === '画质'"
          @close="handleCloseMvSideBar"
        />
        <AdjVolume
          v-if="mvSlideBarItem === '调音'"
          @close="handleCloseMvSideBar"
        />
        <FastOrderPage
          v-if="mvSlideBarItem === '快速点歌'"
          @close="handleCloseMvSideBar"
        />
        <UserVip
          v-if="mvSlideBarItem === '我的特权'"
          @close="handleCloseMvSideBar"
        />
      </MvSideBar>
      <VideoControlLayer
        @click="handleControlVideoPlayerTopMenu"
        v-show="isShowVideoPlayerPlugins"
      >
        <MvBack />
        <VideoControl
          v-if="!mvIsHide"
          :paused="videoPaused"
          :shortMode="isShowMvSideBar"
          :enabledAudioTrackId="videoPlayer.enabledAudioTrack.id"
          :audioTrackMap="videoPlayer.audioTrackMap"
          :supportsOrg="videoPlayer.supportsOrg"
          :isRecording="isRecording"
          @switchAudioTrack="handleSwitchAudioTrack"
          @videoControlPlay="handleVideoControlResume"
          @videoControlPause="handleVideoControlPause"
          @videoControlReplay="handleVideoControlReplay"
          @videoControlNext="handleVideoControlNext"
        ></VideoControl>
        <VideoBothSidesMenu
          v-if="!isShowMvSideBar"
          :headerimg="avatar"
          :enabledAudioTrackId="videoPlayer.enabledAudioTrack.id"
          :audioTrackMap="videoPlayer.audioTrackMap"
          :isRecording="isRecording"
          @switchAudioTrack="handleSwitchAudioTrack"
          @show-side-bar="handleOpenSideBar"
        />
      </VideoControlLayer>
      <VideoPlayer
        :autoplay="initAutoPlay"
        :startPosition="startPosition"
        @onVideoClick="handleControlVideoPlayerTopMenu"
        poster="https://qncweb.ktvsky.com/20211216/vadd/b1e6cb49a76d753245ec0a5dd71356dc.png"
        :src="videoPlayer.songItem.hls"
        :song="videoPlayer.songItem"
        :token="videoPlayer.songItem.token"
        :tokenExp="videoPlayer.songItem.tokenExp"
        @onAudioTracksSwitched="handleOnAudioTracksSwitched"
        @play="handleVideoPlay"
        @ended="handleVideoEnded"
        @error="handleVideoError"
        @canplay="handleCanPlay"
        @next="handleVideoControlNext"
        @onDownloadStart="handleDownloadStart"
        @onDownloadProgress="handleDownloadProgress"
        @onDownloadDone="handleDownloadDone"
        @onDownloadError="handleDownloadError"
        @showControl="handleControlVideoPlayerTopMenu"
      ></VideoPlayer>
      <component
        :is="lrcComponent"
        @on-lrc-click="handleControlVideoPlayerTopMenu"
        v-show="!isMvMode"
        browser-type="landscape"
        :lrc-data="ircListData"
        :lrc-empty="videoPlayer.songItemLrcEmpty"
        :singer-name="singerName"
        :song-name="songName"
        :current-iyric-index="currIrcIndex"
        :paused="videoPaused"
        :current-play-time="currentPlayTime"
        :live-update-lrc="!mvIsHide"
        :pos="isShowMvSideBar"
      />
      <MobileOrderModal v-show="!isShowVideoPlayerPlugins" />
      <MvControlGuide
        v-show="!isShowMvSideBar && showMvControlGuide"
        @closeControlGuide="handleCloseControlGuide"
        @show-side-bar="handleOpenSideBar"
      />

      <van-dialog :show="show" class="mode-dialog">
        <h2>为了您的驾驶安全，自动为您切换为歌词模式</h2>
        <template v-slot:footer>
          <div class="confirm-button" @click="handleConfirm">我知道了</div>
          <div
            class="checkbox"
            :class="isAgree && 'active'"
            @click="toggleCheckbox"
          >
            <span v-show="!isAgree"></span>
            <svg v-show="isAgree" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12ZM24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0C18.6274 0 24 5.37258 24 12ZM11.7063 15.7568L18.6536 9.75682L17.3464 8.24318L11.0445 13.6857L7.64425 10.8214L6.35575 12.351L10.4084 15.7648L11.0607 16.3143L11.7063 15.7568Z" fill="#DBAE6A" style="fill:#DBAE6A;fill:color(display-p3 0.8583 0.6809 0.4149);fill-opacity:1;"/>
            </svg>
            不再提示
          </div>
        </template>
      </van-dialog>
    </div>
    <!-- <button
      @click="handleVideoControlNext('ended')"
      :style="{
        position: 'fixed',
        bottom: '20px',  // 修改位置以避免可能的遮挡
        right: '20px',
        background: 'orange',
        zIndex: 10000,
        width: '150px',  // 调整尺寸以适应页面设计
        height: '50px',
        fontSize: '16px',  // 增加字体大小以提高可读性
        color: 'white'  // 设置字体颜色为白色以增强对比
      }"
    >结束视频</button> -->
    <!-- <button @click="stressTest" style="background:skyblue;position: fixed; top:100px; right:20px; z-index:9999">
      压力测试(100次)
    </button> -->
  </div>
</template>

<script>
import { ref, onMounted, watch, computed, nextTick, onUnmounted } from 'vue';
import { useStore } from 'vuex';
import { useRoute } from 'vue-router';
import VideoPlayer from '@/components/video-player/index.vue';
import VideoControlLayer from '@/components/video-player/plugins/index.vue';
import VideoControl from '@/components/video-player/plugins/control.vue';
import MobileOrderModal from './components/mobile-order/index.vue'
import MvControlGuide from './components/control-guide/index.vue'
import MvSideBar from './components/side-bar/index.vue'
import MvBack from '@/components/btn/mv-back.vue'
import VideoBothSidesMenu from '@/components/video-player/plugins/both-side.vue'
import OrderList from './components/order-list/index.vue'
import ChooseQuality from './components/choose-quality/index.vue'
import AdjVolume from './components/adj-volume/index.vue'
import FastOrderPage from './components/fast-order/index.vue'
import UserVip from './components/vip/index.vue'
import eventBus from '@/utils/event-bus';
import useOrder from '@/composables/useOrder';
import useAlready from '@/composables/useAlready';
import useM3u8 from '@/composables/useM3u8';
import useLoading from '@/composables/useLoading';
import useLoginValid from '@/composables/useLoginValid';
import useVip from '@/composables/useVip';
import useMvMode from '@/composables/useMvMode';
import Toast from '@/utils/toast'
import { Dialog } from 'vant'
import { sendLog, sendSongLog } from '@/directives/v-log/log';
import debounce from 'lodash/debounce'
import LrcList from '@/components/lrc/index.vue'
import { NatsCommandRouteMap } from '@/composables/useNats'
import {
  TSMediaInstance,
  TSMicrophoneInstance,
  TSVehicleInstance,
  TSBaseInfoInstance,
  TSVoiceInstance,
  TSConfigInstance
} from '@/packages/TSJsbridge';
import store2 from 'store2'
import { format } from 'date-fns'
import formatStr from '@/constants/index'
import getComponentLrcData from './utils'
import useDownload from '@/composables/useDownload';

let lastCallTimestamp = 0;

export default {
  name: 'mv',
  components: {
    MvBack,
    VideoPlayer,
    VideoControlLayer,
    VideoControl,
    MvSideBar,
    OrderList,
    ChooseQuality,
    AdjVolume,
    FastOrderPage,
    UserVip,
    VideoBothSidesMenu,
    MobileOrderModal,
    MvControlGuide,
    LrcList,
    [Dialog.Component.name]: Dialog.Component,
  },
  setup() {
    const store = useStore();
    const route = useRoute();
    const { addSong: addOrderSong, playNext, orderedListNumber } = useOrder();
    const { addSong: addAlreadySong } = useAlready();
    const { setCurrSongToken, addCurrSongM3u8 } = useM3u8();
    const { hideLoading } = useLoading();
    const { isLogin } = useLoginValid()
    const { showVipQrcode, isVipUser, freeVipNumber } = useVip()
    const { isMvMode } = useMvMode()
    const { getIsLocalSong, handleDownloadProgress, handleDownloadDone, handleDownloadError, handleDownloadStart } = useDownload();

    let videoPlayerHlsInstance = null;
    let isShowVideoPlayerPlugins = ref(false);
    let isShowVideoPlayerPluginsTimer = ref(0);
    let videoPaused = computed(() => store.state.videoPaused);
    
    const songName = computed(() => videoPlayer.value.songItem.music_name)
    const singerName = computed(() => videoPlayer.value.songItem.singer)
    const isRecording = computed(() => store.state.isRecording)
    const isSingStatus = computed(() => store.state.isSingStatus)
    const isAlreadyEnterMv = computed(() => store.state.videoInitAutoPlay)
    let isCanPlay = ref(false);
    let startPosition = ref(0);
    let initAutoPlay = ref(true);

    let currIrcIndex = computed(() => store.state.currIrcIndex)
    const ircListData = computed(() => {
      return getComponentLrcData(videoPlayer.value.songItemLrc)
    })
    let songStart = format(Date.now(), formatStr)
    let currVideoTime = ref(0)

    let isShowMvSideBar = ref(0) // 是否显示mv侧边栏 0：不显示 1：右侧显示 2：左侧显示 3: 底部显示
    let mvSlideBarItem = ref('') // 侧边栏组件名称
    const showMvControlGuide = ref(false)
    let currentPlayTime = ref(-1)
    let modeSwitchClickTag = false // 模式切换副作用标识

    const lrcComponent = ref('LrcList')

    const avatar = computed(() => store.state.userInfo.avatar)
    const playingMvQuality = computed(() => store.state.playingMvQuality)
    const carSpeed = computed(() => store.state.carMonitor.carSpeed)
    const carGear = computed(() => store.state.carMonitor.carGear)
    const carKey = computed(() => store.state.carMonitor.carKey)
    const notShowAgain = computed(() => store.state.carMonitor.notShowAgain)
    const paused = computed(() => store.state.videoPaused)

    const appStatus = computed(() => store.state.base.appStatus)

    const show = ref(false)
    const isAgree = ref(false)

    const handleChangeMvSideBar = (val) => {
      isShowMvSideBar.value = val ? val : 0
    }
    
    // const { isScreen, closeScreen } = useScreen()

    /**
     * name - 侧边栏显示的组件 - 快速点歌/调音/气氛/已点/画质
     * pos - 显示的侧边栏位置 - left or right
     */
    const handleOpenSideBar = ({ name, pos }) => {
      console.log({ name, pos }, 'handleOpenSideBar')
      isShowVideoPlayerPlugins.value = false
      mvSlideBarItem.value = name

      const posData = {
        left: 2,
        right: 1,
        bottom: 3,
      }
      handleChangeMvSideBar(posData[pos])

      switch (name) {
        case '我的特权':
          if(pos === 'right'){
            sendLog({
              event_type: '10000~50000',
              event_name: 6007,
              event_data: {
                str1: '欢唱页',
                str2: '播控侧边栏',
                str3: '我的右',
                str4: 'click',
              },
            })
          }else{
            sendLog({
              event_type: '10000~50000',
              event_name: 6007,
              event_data: {
                str1: '欢唱页',
                str2: '播控侧边栏',
                str3: '我的左',
                str4: 'click',
              },
            })
          }
        break
        case '快速点歌':
          if(pos === 'right'){
            sendLog({
              event_type: '10000~50000',
              event_name: 6007,
              event_data: {
                str1: '欢唱页',
                str2: '播控侧边栏',
                str3: '快速点歌右',
                str4: 'click',
              },
            })
          }else{
            sendLog({
              event_type: '10000~50000',
              event_name: 6007,
              event_data: {
                str1: '欢唱页',
                str2: '播控侧边栏',
                str3: '快速点歌左',
                str4: 'click',
              },
            })
          }
        break
        case '调音':
          if(pos === 'right'){
            sendLog({
              event_type: '10000~50000',
              event_name: 6007,
              event_data: {
                str1: '欢唱页',
                str2: '播控侧边栏',
                str3: '调音右',
                str4: 'click',
              },
            })
          }else{
            sendLog({
              event_type: '10000~50000',
              event_name: 6007,
              event_data: {
                str1: '欢唱页',
                str2: '播控侧边栏',
                str3: '调音左',
                str4: 'click',
              },
            })
          }
        break
        case '画质':
          if(pos === 'right'){
            sendLog({
              event_type: '10000~50000',
              event_name: 6007,
              event_data: {
                str1: '欢唱页',
                str2: '播控侧边栏',
                str3: '画质右',
                str4: 'click',
              },
            })
          }else{
            sendLog({
              event_type: '10000~50000',
              event_name: 6007,
              event_data: {
                str1: '欢唱页',
                str2: '播控侧边栏',
                str3: '画质左',
                str4: 'click',
              },
            })
          }
        break
        case '已点':
          if(pos === 'right'){
            sendLog({
              event_type: '10000~50000',
              event_name: 6007,
              event_data: {
                str1: '欢唱页',
                str2: '播控侧边栏',
                str3: '已点右',
                str4: 'click',
              },
            })
          }else{
            sendLog({
              event_type: '10000~50000',
              event_name: 6007,
              event_data: {
                str1: '欢唱页',
                str2: '播控侧边栏',
                str3: '已点左',
                str4: 'click',
              },
            })
          }
        break
      }
    }

    const handleCloseMvSideBar = () => {
      if(isShowMvSideBar.value == 1){
        sendLog({
          event_type: '10000~50000',
          event_name: 6007,
          event_data: {
            str1: '欢唱页',
            str2: 'mv空白处+右侧抽屉右上角关闭',
            str3: '系列行为-点击',
            str4: 'click',
          },
        })
      }

      if (mvSlideBarItem.value === '快速点歌') {
        sendLog({
          event_type: '10000~50000',
          event_name: 10089,
          event_data: {
            str1: '搜索',
            str2: '关闭',
            str3: '点击关闭',
            str4: 'click',
          },
        })
      }
      handleChangeMvSideBar(0)
      mvSlideBarItem.value = ''
      TSBaseInfoInstance.controlMVScaling(2)
    }

    const handleCloseControlGuide = () => {
      showMvControlGuide.value = false
      TSConfigInstance.setPreference('showMvControlGuide', 1)
    }

    const mvIsHide = computed(() => {
      return store.state.mvIsHide
    })

    const orderPosition = computed(() => route.params.position); //在当前mv页选择已选列表时不会触发
    const vedioPlayerRecovery = computed(
      () => store.state.videoPlayerHistory.recovery
    );
    const videoPlayer = computed(() => store.state.videoPlayerHistory);
    // 临时demo入口
    // const currSongName = computed(
    //   () => store.state.orderedList[0]?.music_name || false
    // );
    const currSongIsVip = computed(
      () => store.state.orderedList[0]?.is_vip || false
    );
    const nextSongIsVip = computed(
      () => store.state.orderedList[1]?.is_vip || false
    );
    
    const videoVolume = computed(() => store.state.videoVolume);
    const setting = computed(() => store.state.setting);

    const currSongIsLocal = ref(false)
    const nextSongIsLocal = ref(false)

    // onBeforeRouteLeave 位置要靠前
    const recordLastSongCurrentTime = () => {
      if (TSMediaInstance) {
        const currentPosition = TSMediaInstance.getCurrentPosition()
        console.log(
          'record last song currentTime:',
          currentPosition
        );
        store.commit(
          'SAVE_VIDEO_PLAYER_HISTORY_CURRENT_TIME',
          currentPosition
        );
      }
    };

    const handleBack = () => {
      recordLastSongCurrentTime();
    };

    const showVipQrcodeModal = (value) => {
      store.commit('UPDATE_MV_ISHIDE', true);
      if(value == 'next'){
        if(!isLogin.value){
          showVipQrcode({
            mType: 'mv页切歌vip弹窗',
            log: 'mv-切歌-未登录'
          });

          sendLog({
            event_type: '10000~50000',
            event_name: 1021,
            event_data: {
              str1: 'MV 页面',
              str2: '切歌',
              str3: '触发 VIP 弹窗',
              str4: 'click',
              str5: 1,
              str7: videoPlayer.value.songItem.songid,
              str9: '手机端',
              str10: '1831'
            }
          })
        }else{
          showVipQrcode({
            mType: 'mv页切歌vip弹窗',
            log: 'mv-切歌-已登录'
          });

          sendLog({
            event_type: '10000~50000',
            event_name: 1021,
            event_data: {
              str1: 'MV 页面',
              str2: '切歌',
              str3: '触发 VIP 弹窗',
              str4: 'click',
              str5: 2,
              str7: videoPlayer.value.songItem.songid,
              str9: '手机端',
              str10: '1832'
            }
          })
        }
      }else{
        showVipQrcode();
      }
      
    };

    const handleOnAudioTracksSwitched = (audioTrackId) => {
      // if (!isCanPlay.value) return
      store.commit('SAVE_VIDEO_PLAYER_HISTORY_ENABLED_AUDIO_TRACK', {
        id: audioTrackId,
      });
    };

    const handleCanPlay = () => {
      isCanPlay.value = true;

      TSMediaInstance.selectTrack(videoPlayer.value.enabledAudioTrack.id);

      isShowVideoPlayerPlugins.value = true;
      //初始化设置默认历史音量
      if (videoVolume.value) {
        onEffectAdjustTunerVolumeChange('stream', videoVolume.value);
      }
    };

    const handleSwitchAudioTrack = (audioTrack) => {
      console.log('handleSwitchAudioTrack', audioTrack, videoPlayer.value);

      let audioTrackPayload = audioTrack || {
        id: videoPlayer.value.enabledAudioTrack.id === 1 ? 2 : 1
      };
      console.log('handleSwitchAudioTrack audioTrackPayload', audioTrackPayload);

      if (!videoPlayer.value.supportsOrg) {
        Toast('抱歉，当前歌曲缺少原唱资源');
        if (audioTrack?.from === 'voice') {
          TSVoiceInstance.sendTtsMessage(-1, 1001, '抱歉，当前缺少原唱资源');
        }
        return;
      }

      if (audioTrack?.from === 'voice') {
        TSVoiceInstance.sendTtsMessage(-1, 1001, '好的');
      }
      console.log('handleSwitchAudioTrack switch-audio-track', audioTrackPayload);
      eventBus.emit('switch-audio-track', audioTrackPayload);
      sendLog({
        event_type: '10000~50000',
        event_name: 10049,
        event_data: {
          str1: 'MV',
          str2: '播控',
          str3: '原伴唱',
          str4: 'click',
        },
      });

      sendLog({
        event_type: '10000~50000',
        event_name: 6007,
        event_data: {
          str1: '欢唱页',
          str2: '播控浮层',
          // str3: videoPlayer.value.enabledAudioTrack.id === 1 ? '原唱' : '伴唱',
          str3: '原唱伴唱',
          str5: videoPlayer.value.songItem.songid,
          str6: !isVipUser.value ? 2 : 1
        },
      })
    };

    const handleSwitchSettingMode = () => {
      if (isMvMode.value) {
        modeSwitchClickTag = true
      }
      store.dispatch('mvMode/saveMvMode', {
        ...setting.value,
        mode: isMvMode.value ? '歌词' : 'mv',
      })
    }

    // 行车模式 watch
    const handleCarModeChange = (isSafe) => {
      console.log('isSafe', isSafe)

      console.log('handleCarModeChange 1')

      if (isMvMode.value) {
        modeSwitchClickTag = true;
      }

      console.log('handleCarModeChange 2')

      store.dispatch('mvMode/saveMvMode', {
        ...setting.value,
        mode: isSafe ? '歌词' : 'mv',
      });
      handleCloseMvSideBar()

      console.log('handleCarModeChange 3')

      if (mvIsHide.value) { 
        return;
      }

      console.log('handleCarModeChange 4')

      if (!notShowAgain.value && isSafe && isMvMode.value) {

        console.log('handleCarModeChange 5')

        handleVideoControlPause();
        show.value = true;
      } else if (!isSafe && !isMvMode.value) {

        console.log('handleCarModeChange 6')

        if (show.value) {

          console.log('handleCarModeChange 7')

          show.value = false
          handleVideoControlResume()
        }
        Toast('为了您拥有更好的K歌体验，自动为您切换为MV模式');
      }

      store.dispatch('mvMode/saveMvMode', {
        ...setting.value,
        mode: isSafe ? '歌词' : 'mv',
      });
    };
    

    watch(carSpeed, (val) => {
      handleCarModeChange(Number(val) > 12);
    });

    watch(carGear, (val) => {
      // 倒车模式
      if (Number(val) === 2) {
        handleVideoControlPause();
        return
      }
      handleCarModeChange(Number(val) === 4);
    });

    watch(carKey, (val) => {
      if (orderedListNumber.value == 0) {
        Toast('暂无更多已点歌曲')
        return
      }
      console.log('carKey', val)
      const videoControlActions = {
        'playPause': () => {
          if (paused.value) {
            handleVideoControlResume();
          } else {
            handleVideoControlPause();
          }
        },
        'play': handleVideoControlResume,
        'pause': handleVideoControlPause,
        'next': handleVideoControlNext,
        'replay': handleVideoControlReplay,
      };

      const action = videoControlActions[val];
      if (action) {
        action();
        store.dispatch('carMonitor/setCarKey', '')
      }
    });

    const handleVideoControlResume = async () => {
      if (!checkAppStatus()) return
      
      sendLog({
        event_type: '10000~50000',
        event_name: 6007,
        event_data: {
          str1: '欢唱页',
          str2: '播控浮层',
          str3: '播放暂停',
          str4: 'click',
          str5: videoPlayer.value.songItem.songid,
          str6: !isVipUser.value ? 2 : 1
        },
      })

      currSongIsLocal.value = await getIsLocalSong(videoPlayer.value.songItem)
      console.log('resume');
      //重播和播放时检查当前登录状态
      if (currSongIsVip.value && !currSongIsLocal.value) {
        if (!isLogin.value) {
          showVipQrcodeModal();
          return;
        }
        if (!isVipUser.value && freeVipNumber.value >= 3) {
          showVipQrcodeModal();
          return;
        }
      }
      await getIsLocalSong(videoPlayer.value.songItem)
      eventBus.emit('video-control-resume');
    }

    const handleVideoControlPlay = (isLocalSong = currSongIsLocal.value) => {
      console.log('play');
      if (!checkAudioFocus() || !checkAppStatus()) return
      sendLog({
        event_type: 'click',
        event_name: 102,
        event_data: {
          type: 1,
          song_id: videoPlayer.value.songItem.songid,
          song_name: videoPlayer.value.songItem.music_name,
          singer: videoPlayer.value.songItem.singer,
        },
      });
      //重播和播放时检查当前登录状态
      if (currSongIsVip.value && !isLocalSong) {
        if (!isLogin.value) {
          showVipQrcodeModal();
          return;
        }
        if (!isVipUser.value && freeVipNumber.value >= 3) {
          showVipQrcodeModal();
          return;
        }
      }
      eventBus.emit('video-control-play', videoPlayer.value.songItem, isLocalSong);
    };
    const handleVideoControlPause = () => {
      if (!checkAppStatus()) return
      sendLog({
        event_type: 'click',
        event_name: 103,
        event_data: {
          type: 1,
          song_id: videoPlayer.value.songItem.songid,
          song_name: videoPlayer.value.songItem.music_name,
          singer: videoPlayer.value.songItem.singer,
        },
      });
      console.log('pause');
      eventBus.emit('video-control-pause');
    };

    const handleVideoControlReplay = debounce(async () => {
      console.log('handleVideoControlReplay');
      if (!checkAudioFocus() || !checkAppStatus()) return

      sendLog({
        event_type: '10000~50000',
        event_name: 6007,
        event_data: {
          str1: '欢唱页',
          str2: '播控浮层',
          str3: '重唱',
          str4: 'click',
          str5: videoPlayer.value.songItem.songid,
          str6: !isVipUser.value ? 2 : 1
        },
      })

      sendLog({
        event_type: 'click',
        event_name: 104,
        event_data: {
          type: 1,
          song_id: videoPlayer.value.songItem.songid,
          song_name: videoPlayer.value.songItem.music_name,
          singer: videoPlayer.value.songItem.singer,
        },
      });
      currSongIsLocal.value = await getIsLocalSong(videoPlayer.value.songItem)
      console.log('replay', currSongIsLocal.value );

      //重播和播放时检查当前登录状态
      if (currSongIsVip.value && !currSongIsLocal.value) {
        if (!isLogin.value) {
          showVipQrcodeModal();
          return;
        }
        if (!isVipUser.value && freeVipNumber.value >= 3) {
          showVipQrcodeModal();
          return;
        }
      }
      await handleInitIrcIndex()
      eventBus.emit('video-control-replay');
      eventBus.emit('irc-control-replay');

      let play_time = ''
      if (videoPlayerHlsInstance && videoPlayerHlsInstance.media) {
        play_time = Math.round(videoPlayerHlsInstance.media.currentTime)
      } else {
        play_time = currVideoTime.value
      }
      sendSongLog({
        end_type: 1,
        song_id: videoPlayer.value.songItem.songid,
        song_name: videoPlayer.value.songItem.music_name,
        singer: videoPlayer.value.songItem.singer,
        start_time: songStart, // 格式："2020-07-27 14:02:20"
        end_time: format(Date.now(), formatStr),
        play_time,
        is_vip: videoPlayer.value.songItem.is_vip
      })
      songStart = format(Date.now(), formatStr)
      
    }, 1000, { leading: true, trailing: false });

    const handleVideoPlay = () => {
      console.log('handleVideoPlay')
      // if (!checkAudioFocus() || !checkAppStatus()) return
      
      // showLoading()
      // 埋点107
      sendLog({
        event_type: 'custom',
        event_name: 107,
        event_data: {
          type: 1,
          song_id: videoPlayer.value.songItem.songid,
          song_name: videoPlayer.value.songItem.music_name,
          singer: videoPlayer.value.songItem.singer,
        },
      });
      sendLog({
        event_type: 'custom',
        event_name: 1070,
        event_data: {
          str1: '歌曲清晰度埋点',
          str2: videoPlayer.value.songItem.songid,
          str3: videoPlayer.value.songItem.music_name,
          str4: videoPlayer.value.songItem.singer,
          str5: `${playingMvQuality.value}P`, // 画质
          str6: '1', // 上报时机
        },
      })
    };

    const checkAudioFocus = ()=>{
      if ((!isAlreadyEnterMv.value || !isSingStatus.value) && mvIsHide.value) {
        if (orderedListNumber.value > 0) {
          Toast('尚未进入欢唱页K歌，暂无法执行该操作')
        }
        return
      }
      return TSBaseInfoInstance.requestAudioFocus()
    }

    const checkAppStatus = () => {
      return appStatus.value
    }

    const handleControlVideoPlayerTopMenu = (payload) => {
      sendLog({
        event_type: '10000~50000',
        event_name: isShowVideoPlayerPlugins.value ? 10048 : 10047,
        event_data: {
          str1: 'MV',
          str2: '画面区',
          str3: isShowVideoPlayerPlugins.value ? '收回播控' : '唤起播控',
          str4: 'click',
        },
      })
      if (payload === true) {
        clearTimeout(isShowVideoPlayerPluginsTimer.value);
        isShowVideoPlayerPluginsTimer.value = setTimeout(() => {
          isShowVideoPlayerPlugins.value = false;
        }, 6 * 1000);
      }
      isShowVideoPlayerPlugins.value = payload === true ? true : !isShowVideoPlayerPlugins.value;
    };

    const handleVideoControlNext = debounce(async (from) => {
      console.log('handleVideoControlNext 开始执行', from);

      if (orderedListNumber.value <= 0) {
        Toast('暂无更多已点歌曲')
        return
      }

      if (!checkAudioFocus() || !checkAppStatus()) {
        console.log('音频焦点或应用状态检查失败，返回');
        return;
      }

      // VEC-872-begin
      store.commit('RESET_VIDEO_PLAYER_HLS');
      console.log('重置视频播放器 HLS');

      startPosition.value = 0;
      console.log('设置起始位置为 0');

      let isUnablePlay = false;
      nextSongIsLocal.value = !!await getIsLocalSong(store.state.orderedList[1]);
      console.log('下一首歌曲是否本地:', nextSongIsLocal.value);

      sendLog({
        event_type: '10000~50000',
        event_name: 10057,
        event_data: {
          str1: 'MV',
          str2: '播控',
          str3: '下一首',
          str4: 'click',
        },
      });
      console.log('发送日志: 下一首');
      if(!mvIsHide.value){
        sendLog({
          event_type: '10000~50000',
          event_name: 6007,
          event_data: {
            str1: '欢唱页',
            str2: '播控浮层',
            str3: '切歌',
            str4: 'click',
          },
        })
      }

      let play_time = '';
      if (videoPlayerHlsInstance && videoPlayerHlsInstance.media) {
        play_time = Math.round(videoPlayerHlsInstance.media.currentTime);
        console.log('获取当前播放时间:', play_time);
      } else {
        play_time = currVideoTime.value;
        console.log('获取当前视频时间:', play_time);
      }

      sendSongLog({
        end_type: 2,
        song_id: videoPlayer.value.songItem.songid,
        song_name: videoPlayer.value.songItem.music_name,
        singer: videoPlayer.value.songItem.singer,
        start_time: songStart, // 格式："2020-07-27 14:02:20"
        end_time: format(Date.now(), formatStr),
        play_time,
        is_vip: videoPlayer.value.songItem.is_vip
      });
      console.log('发送歌曲日志');

      songStart = format(Date.now(), formatStr);
      console.log('更新歌曲开始时间:', songStart);

      if (!initAutoPlay.value) {
        initAutoPlay.value = true;
        console.log('初始化自动播放');
      }

      // TODO 这个位置设计的不好
      // 切歌前校验 没有播放vip权限依然切歌到下一首但不播放
      if ((!isLogin.value || !isVipUser.value)) {
        console.log('用户未登录或不是 VIP 用户');
        if (nextSongIsVip.value && !nextSongIsLocal.value) {
          console.log('下一首歌曲是 VIP 且不是本地歌曲');
          isUnablePlay = true;
          showVipQrcodeModal('next');
          console.log('显示 VIP 二维码弹窗');
          
          addAlreadySong(store.state.orderedList[0]);
          console.log('添加已播放歌曲:', store.state.orderedList[0]);

          initAutoPlay.value = false;
          console.log('禁用自动播放');

          await store.commit('UPDATE_MV_INIT_AUTOPLAY', 0);
          console.log('更新 MV 初始自动播放状态为 0');

          await store.commit('RESET_VIDEO_PLAYER');
          console.log('重置视频播放器');

          await store.commit('UPDATE_MV_ISHIDE', true);
          console.log('隐藏 MV');

          TSMediaInstance.pause();
          console.log('暂停媒体实例');

          store.dispatch('setVideoPaused', true);
          console.log('handleVideoControlNext setVideoPaused true')
        } else {
          TSMediaInstance.stop();
          console.log('停止媒体实例');

          addAlreadySong(store.state.orderedList[0]);
          console.log('添加已播放歌曲:', store.state.orderedList[0]);
        }
      } else {
        TSMediaInstance.stop();
        console.log('停止媒体实例');

        addAlreadySong(store.state.orderedList[0]);
        console.log('添加已播放歌曲:', store.state.orderedList[0]);
      }

      await nextTick();
      console.log('等待 nextTick');

      playNext(videoPlayer.value.songItem, async() => {
        console.log('播放下一首歌曲');
        if (from === 'ended') {
          console.log('从 ended 触发');
          await store.commit('SAVE_VIDEO_PLAYER_HISTORY_SONG_ITEM', store.state.orderedList[0]);
          console.log('保存视频播放器历史歌曲项');

          handleVideoControlReplay();
          console.log('处理视频控制重播');
        } else {
          store.commit('UPDATE_MV_ISHIDE', true);
          console.log('隐藏 MV');

          store.commit('RESET_VIDEO_PLAYER');
          console.log('重置视频播放器');

          Toast('暂无更多已点歌曲');
          console.log('显示提示: 暂无更多已点歌曲');

          store.commit('UPDATE_MV_INIT_AUTOPLAY', 0);
          console.log('更新 MV 初始自动播放状态为 0');

          setTimeout(() => {
            TSMediaInstance.stop();
            console.log('停止媒体实例');
          }, 1000);
        }
      }, from, isUnablePlay);

      eventBus.emit('irc-control-next');
      console.log('触发 irc-control-next 事件');

      handleInitIrcIndex();
      console.log('初始化 IRC 索引');

    }, 1000, {
      leading: true,
      trailing: false
    });

    const handleVideoVolume = () => {
      // console.log(context);
      Toast('此功能暂未完成移植');
    };
    const handleShowOrdered = () => {
      eventBus.emit('show-order-song-control-popup');
      sendLog({
        event_type: '10000~50000',
        event_name: 10052,
        event_data: {
          str1: 'MV',
          str2: '播控',
          str3: '进入已点',
          str4: 'click',
        },
      })
    };
    const handleVideoEnded = async () => {
      await store.dispatch('download/stopDownload');
      nextSongIsLocal.value = !!await getIsLocalSong(store.state.orderedList[1])
      sendLog({
        event_type: 'custom',
        event_name: 101,
        event_data: {
          type: 1,
          song_id: videoPlayer.value.songItem.songid,
          song_name: videoPlayer.value.songItem.music_name,
          singer: videoPlayer.value.songItem.singer,
        },
      });

      let play_time = ''
      if (videoPlayerHlsInstance && videoPlayerHlsInstance.media) {
        play_time = Math.round(videoPlayerHlsInstance.media.currentTime)
      } else {
        play_time = currVideoTime.value
      }
      sendSongLog({
        end_type: 1,
        song_id: videoPlayer.value.songItem.songid,
        song_name: videoPlayer.value.songItem.music_name,
        singer: videoPlayer.value.songItem.singer,
        start_time: songStart, // 格式："2020-07-27 14:02:20"
        end_time: format(Date.now(), formatStr),
        play_time,
        is_vip: videoPlayer.value.songItem.is_vip
      })
      songStart = format(Date.now(), formatStr)
      
      handleVideoControlNext('ended')
    };

    /**
     * error 失败详情
     * type 失败类型
     */
    const handleVideoError = (error) => {
      // TSBanmaBridgeInstance.sendVideoState(VideoState.ERROR);
      hideLoading()
      console.log('handleVideoError')
      handleRetryToken()
      store.commit('UPDATE_MV_ISHIDE', true);

      store.commit('UPDATE_MV_INIT_AUTOPLAY', 0)
      Toast(`${videoPlayer.value.songItem.music_name}播放失败，请稍后重试`)
    };

    const handleRetryToken = (error) => {
      console.log('retry token');
      const ts = setCurrSongToken();
      // ts > 0 时上报
    };

    watch(mvIsHide, async (val) => {
      if (!val) {
        store.commit('UPDATE_MV_IS_OPENED', true)
        store.commit('UPDATE_IS_SING_STATUS', true)
      } else {
        handleCloseMvSideBar()
      }
    })

    const toggleCheckbox = () => {
      isAgree.value = !isAgree.value
    }

    const handleConfirm = () => {
      show.value = false
      handleVideoControlResume();
      store.commit('carMonitor/SET_DIALOG_SHOWN', isAgree.value);
      sendLog({
        event_type: '10000~50000',
        event_name: 30116,
        event_data: {
          str1: '歌词模式弹窗',
          str2: '我知道了',
          str3: 'click',
        },
      })
    }

    watch(isShowVideoPlayerPlugins, (val) => {
      const res = store2('isShowMvSideBarTag')
      if( res == 1 ){
        sendLog({
          event_type: '10000~50000',
          event_name: 6007,
          event_data: {
            str1: '欢唱页',
            str2: '右侧抽屉展示-mv空白处',
            str3: '点击',
            str4: 'click'
          },
        })
      }
      // 开启状态
      if (val) {
        if (isShowVideoPlayerPluginsTimer.value) {
          clearTimeout(isShowVideoPlayerPluginsTimer.value);
        }
        isShowVideoPlayerPluginsTimer.value = setTimeout(() => {
          isShowVideoPlayerPlugins.value = false;
        }, 6 * 1000);

        sendLog({
          event_type: '10000~50000',
          event_name: 6007,
          event_data: {
            str1: '欢唱页',
            str2: '画面区',
            str3: '唤起播控',
            str4: 'click'
          },
        })
      }else{
        sendLog({
          event_type: '10000~50000',
          event_name: 6007,
          event_data: {
            str1: '欢唱页',
            str2: '画面区',
            str3: '收回播控',
            str4: 'click'
          },
        })
      }
    });

    watch(vedioPlayerRecovery, (val) => {
      if (val) {
        store.commit('UPDATE_VIDEO_PLAYER_RECOVERY', false);
      }
    });

    // 歌曲播放日志上报 时长统计
    watch(videoPaused, (val) => {
      if (!val) {
        songStart = format(Date.now(), formatStr)
      }
    })

    watch(appStatus, (val) => {
      if (!val) {
        isShowVideoPlayerPlugins.value = false;
      }
    })

    const mediaVolume = computed(() => store.state.mediaVolume) //记忆多媒体音量
    watch(videoPaused,(val)=>{
      const storageMediaVolume =mediaVolume.value  ?mediaVolume.value  : store2.get('storageMediaVolume')
      if (!val && typeof storageMediaVolume === 'number' && TSVehicleInstance) {
        store.commit('UPDATE_MV_VIDEO_MEDIA', storageMediaVolume);
        TSVehicleInstance.setMediaVolume(storageMediaVolume);
      }
    })
    
    const handleMicrophoneVideoControlNext = () => {
      console.log('mv handleMicrophoneVideoControlNext', orderedListNumber.value)
      if (orderedListNumber.value == 0) {
        Toast('暂无更多已点歌曲')
        return
      }
      
      if (document.visibilityState !== 'visible' || !appStatus.value) {
        console.log('handleMicrophoneVideoControlNext 不在前台，操作被拦截')
        return
      }
      if (!checkAudioFocus()) {
        return
      }
      handleVideoControlNext('microphone')
    }

    const handleNatsPlayerControl = (payload) => {
      if (orderedListNumber.value == 0) {
        Toast('暂无更多已点歌曲')
        return
      }
      if (!checkAudioFocus()) {
        return
      }
      store.commit('UPDATE_CONTROL_FROM_TYPE', 3)
      switch (payload.name) {
        case NatsCommandRouteMap.ORDER_NEXT:
          handleVideoControlNext()
          break
        case NatsCommandRouteMap.ORDER_REPLAY:
          handleVideoControlReplay(3)
          sendLog({
            event_type: '10000~50000',
            event_name: 6010,
            event_data: {
              str1: '小程序',
              str2: '小程序',
              str3: '重播',
              str4: 'click',
              str5: videoPlayer.value.songItem.songid
            }
          })
          break
        case NatsCommandRouteMap.ORDER_PLAY_TOGGLE:
          sendLog({
            event_type: '10000~50000',
            event_name: 6010,
            event_data: {
              str1: '小程序',
              str2: '小程序',
              str3: '播放/暂停',
              str4: 'click',
              str5: videoPlayer.value.songItem.songid
            },
          })
          if (videoPaused.value) {
            handleVideoControlResume(3)
          } else {
            handleVideoControlPause(3)
          }
          break
        case NatsCommandRouteMap.ORDER_AUDIO_TRACK_TOGGLE:
          handleSwitchAudioTrack()
          sendLog({
            event_type: '10000~50000',
            event_name: 6010,
            event_data: {
              str1: '小程序',
              str2: '小程序',
              str3: '原唱/伴唱',
              str4: 'click',
              str5: videoPlayer.value.songItem.songid
            },
          })
          break
        default:
          break
      }
    }

    const onEffectAdjustTunerVolumeChange = (type, val) => {
      console.log(type, val)
      switch(type) {
        case 'media':
          if (TSVehicleInstance) {
            TSVehicleInstance.setMediaVolume(val);
          }
          break;
        case 'stream':
          if (TSMediaInstance) {
            TSMediaInstance.setStreamVolume(val);
          }
          break;
        case 'mic':
          if (TSMicrophoneInstance) {
            TSMicrophoneInstance.setMicVolume(val);
          }
          break;
        case 'echo':
          if (TSMicrophoneInstance) {
            TSMicrophoneInstance.setReverbVolume(val);
          }
          break;
      }
    };

    onUnmounted(() => {
      console.log('mv back');
      handleBack();
      hideLoading();
      eventBus.off('handle-video-play', handleVideoControlPlay)
      eventBus.off('handle-video-pause', handleVideoControlPause)
      eventBus.off('handle-video-next', handleVideoControlNext)
      eventBus.off('handle-video-toggle-track', handleSwitchAudioTrack)
      eventBus.off('effect-adjust-tuner-change', onEffectAdjustTunerVolumeChange);

      eventBus.off('ts-microphone-control-next', handleMicrophoneVideoControlNext)
      eventBus.off('nats-player-control', handleNatsPlayerControl)
      eventBus.off('show-plugins', handleControlVideoPlayerTopMenu)
    });
    // onBeforeRouteLeave 位置要靠前

    onMounted(() => {
      eventBus.on('handle-video-play', handleVideoControlPlay)
      eventBus.on('handle-video-pause', handleVideoControlPause)
      eventBus.on('handle-video-next', handleVideoControlNext)
      eventBus.on('handle-video-toggle-track', handleSwitchAudioTrack)
      eventBus.on('ts-microphone-control-next', handleMicrophoneVideoControlNext)
      eventBus.on('effect-adjust-tuner-change', onEffectAdjustTunerVolumeChange);
      eventBus.on('nats-player-control', handleNatsPlayerControl)
      eventBus.on('show-plugins', handleControlVideoPlayerTopMenu)
      if (!videoPlayer.value.songItem.songid) {
        store.commit('UPDATE_MV_ISHIDE', true);
        return;
      }
    });

    onMounted(async () => {
      try {
        console.log('1218',TSConfigInstance.getPreference('showMvControlGuide'))
        showMvControlGuide.value = !TSConfigInstance.getPreference('showMvControlGuide')
      } catch (error) {
        console.error('Error getting showMvControlGuide preference:', error);
      }

      if (orderPosition.value === 'recovery') {
        startPosition.value = videoPlayer.value.currentTime;
      }
      if (TSVehicleInstance) {
        try {
          const storageMediaVolume = store2('storageMediaVolume') || 50;
          console.log('TSVehicleInstance', storageMediaVolume)
          store.commit('UPDATE_MV_VIDEO_MEDIA', storageMediaVolume);
        } catch (error) {
          console.error('Error updating MV video media:', error);
        }
      }

      try {
        currSongIsLocal.value = !!await getIsLocalSong(store.state.orderedList[0])
        nextSongIsLocal.value = !!await getIsLocalSong(store.state.orderedList[1])
      } catch (error) {
        console.error('Error checking if songs are local:', error);
      }

    });

    const handleInitIrcIndex = () => {
      store.commit('UPDATE_CURR_IYRIC_INDEX', -1)
    }

    // // 临时添加测试代码
    // let counter = 0
    // const stressTest = async () => {
    //   await new Promise(resolve => setTimeout(resolve, 3000)); // 等待100毫秒
    //   Toast('开始压力测试')
    //   for(let i=0; i<300; i++) {
    //     sendSongLog({
    //       end_type: 1,
    //       song_id: videoPlayer.value.songItem.songid,
    //       song_name: videoPlayer.value.songItem.music_name,
    //       singer: videoPlayer.value.songItem.singer,
    //       start_time: songStart, // 格式："2020-07-27 14:02:20"
    //       end_time: format(Date.now(), formatStr),
    //       is_vip: videoPlayer.value.songItem.is_vip,
    //       play_time: 0,
    //     })
    //     // sendSongLog({
    //     //   end_type: 1,
    //     //   song_id: 'test'+counter++,
    //     //   // ...其他参数
    //     // })
    //   }
    // }

    return {
      isMvMode,
      mvIsHide,
      ircListData,
      currIrcIndex,
      isShowVideoPlayerPluginsTimer,
      isShowVideoPlayerPlugins,
      initAutoPlay,
      videoPaused,
      videoPlayer,
      startPosition,
      currSongIsVip,
      nextSongIsVip,
      isShowMvSideBar,
      avatar,
      mvSlideBarItem,
      showMvControlGuide,
      lrcComponent,
      songName,
      singerName,
      currentPlayTime,
      show,
      isAgree,
      isRecording,
      handleSwitchAudioTrack,
      handleOnAudioTracksSwitched,
      handleVideoControlPlay,
      handleVideoControlPause,
      handleVideoPlay,
      handleVideoControlReplay,
      handleControlVideoPlayerTopMenu,
      handleVideoControlNext,
      handleVideoVolume,
      handleShowOrdered,
      handleVideoEnded,
      handleVideoError,
      handleRetryToken,
      handleCanPlay,
      handleChangeMvSideBar,
      handleOpenSideBar,
      handleCloseMvSideBar,
      handleCloseControlGuide,
      handleVideoControlResume,
      handleSwitchSettingMode,
      toggleCheckbox,
      handleConfirm,
      handleDownloadProgress,
      handleDownloadDone,
      handleDownloadError,
      handleDownloadStart,
      // stressTest,
    };
  },
};
</script>

<style lang="stylus" scoped>
.page {
  position: relative;
  height: 100vh;
  display: flex;
  padding: 0 !important;
  background: transparent;
}
::v-deep .mode-dialog
  width 920px
  h2
    padding 60px 100px 50px !important
    color: rgba(255, 255, 255, 0.80)
    font-size 36px
    text-align center
  .confirm-button
    width: 300px;
    height: 80px;
    line-height: 80px;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.10);
    backdrop-filter: blur(100px);
    margin 0 auto 30px
    color: rgba(255, 255, 255, 0.80);
    font-size: 28px;
    text-align center
  .checkbox
    display flex
    justify-content center
    align-items center
    color: rgba(255, 255, 255, 0.50);
    margin 20px 0
    font-size: 24px;
    span
      display block
      width 24px
      height 24px
      border 2px solid rgba(255, 255, 255, 0.50)
      border-radius: 50%;
      margin-right 10px
      position relative
      top 2px
      svg
        width 24px
        height 24px
    svg
      margin-right 10px
      position relative
      top 2px
    &.active
      span
        border none
.secondary-screen-grade
  position: fixed;
  bottom: 100px;
  left: 0;
  right: 0;
  margin: 0 auto;
  z-index: 11;
.mv-no-scroll
  overflow-y hidden
</style>
