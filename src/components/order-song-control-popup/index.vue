<template>
  <ControlPopup ref="root">
    <template #control-popup-plugin>
      <div class="order-song-control-panel">
        <div class="header">
          <div class="tab">
            <div
              class="tab-item"
              v-for="(tab, index) in tabList"
              :key="index"
              :class="{ actived: curTab.name === tab.name }"
              @click="handleChangeTab(tab)"
            >
              {{ tab.text }}
            </div>
          </div>
          <div class="close" @click="handleClose">
            <svg v-show="themeClass === 'themeLight'" width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g opacity="0.4">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M3.74219 26.3708L5.62781 28.2564L15.9987 17.8855L26.3698 28.2566L28.2554 26.371L17.8843 15.9999L28.2552 5.62903L26.3696 3.74341L15.9987 14.1143L5.628 3.7436L3.74238 5.62922L14.1131 15.9999L3.74219 26.3708Z" fill="#1D1D1F" style="fill:#1D1D1F;fill:color(display-p3 0.1137 0.1137 0.1216);fill-opacity:1;"/>
              </g>
            </svg>
            <svg v-show="themeClass === 'themeDark'" width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g opacity="0.4">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M3.74219 26.3708L5.62781 28.2564L15.9987 17.8855L26.3698 28.2566L28.2554 26.371L17.8843 15.9999L28.2552 5.62903L26.3696 3.74341L15.9987 14.1143L5.628 3.7436L3.74238 5.62922L14.1131 15.9999L3.74219 26.3708Z" fill="white" style="fill:white;fill-opacity:1;"/>
              </g>
            </svg>
          </div>
        </div>
        <component
          :is="currentComponent"
          @singer-click="handleClickSinger"
        ></component>
      </div>
    </template>
  </ControlPopup>
</template>
<script setup>
import { ref, onMounted, onBeforeUnmount, computed, watch } from 'vue'
import { useStore } from 'vuex'
import { useRouter, useRoute } from 'vue-router'
import eventBus from '@/utils/event-bus'
import ControlPopup from '@/components/control-popup/index.vue'
import AlreadySongList from '@/components/song-list/already/index.vue'
import OrderSongList from '@/components/song-list/order/index.vue'
import { sendLog } from '@/directives/v-log/log'

const store = useStore()
const router = useRouter()
const route = useRoute()

const orderedListNum = computed(() => store.state.orderedList.length)
const alreadyListNum = computed(() => store.state.alreadyList.length)
const tabList = computed(() => {
  return [
    {
      name: 'ordered',
      text: `已点(${orderedListNum.value > 99 ? '99' : orderedListNum.value})`,
    },
    {
      name: 'already',
      text: `已唱(${alreadyListNum.value > 99 ? '99' : alreadyListNum.value})`,
    },
  ]
})

let curTab = ref(tabList.value[0])

const currentComponent = computed(() => {
  return curTab.value.name === 'ordered' ? OrderSongList : AlreadySongList;
});

const themeClass = computed(() => store.state.themeClass)

const handleChangeTab = (tab) => {
  if (curTab.value.name === tab.name) return
  curTab.value = tab

  if (tab.text === '已唱') {
    sendLog({
      event_type: '10000~50000',
      event_name: 10061,
      event_data: {
        str1: '已点',
        str2: '已唱',
        str3: '进入已唱',
        str4: 'click',
      },
    })
  }
}

const root = ref(null)

const handleShow = () => {
  console.log('1114', handleShow)
  root.value.show()
}

const handleClose = () => {
  sendLog({
    event_type: '10000~50000',
    event_name: 6012,
    event_data: {
      str1: '通用',
      str2: '已点/已唱弹窗',
      str3: '关闭弹窗',
      str4: 'click',
      str5: 1
    },
  })
  root.value.close()
}

const handleClickSinger = ({ singer, singerhead, singerid }) => {
  router.push({
    name: 'songList',
    query: {
      name: singer,
      image: singerhead,
      singerid: singerid,
    },
  })
  root.value.close()
}

onMounted(() => {
  eventBus.on('show-order-song-control-popup', handleShow)
  eventBus.on('close-order-song-control-popup', handleClose)
})

onBeforeUnmount(() => {
  eventBus.off('show-order-song-control-popup', handleShow)
  eventBus.off('close-order-song-control-popup', handleClose)
})

watch(route, val => {
  handleClose()
})

</script>
<style lang="stylus" scoped>
.order-song-control-panel
  padding 30px
  width 1000px
  height 800px
  background #1E1F21
  overflow hidden
  // box-shadow 0px 20px 80px 0px rgba(0, 0, 0, 0.5)
  border-radius 14px
  padding 0 !important
  @media screen and (max-width 1200px)
    zoom 0.8
  @media screen and (max-width: 1950px) and (min-width: 1700px) and (max-height: 720px) and (min-height: 650px)
    zoom 0.8
  .header
    width 100%
    height auto
    padding 0
    border-bottom 2px solid rgba(255, 255, 255, 0.10)
    position relative
    margin-bottom 16px
    .close
      position absolute
      top 5px
      right 10px
      width 90px
      height 90px
      display flex
      align-items center
      justify-content center
      svg
        width 34px
        height 34px
    .tab
      width 100%
      height 100px
      display flex
      align-items center
      justify-content center
      &-item
        display flex
        justify-content center
        align-items center
        width 173px
        height 100px
        font-size 32px
        color rgba(255, 255, 255, 0.40)
        &.actived
          color #dbae6a
          border-bottom 2px solid #DBAE6A
        &:nth-child(1)
          margin-right 136px
</style>
