import get from 'lodash/get'

const data = [
    ['00:26.584', '对这个世界如果', [183, 172, 230, 344, 184, 578, 125]],
    ['00:28.472', '你有太多的抱怨', [169, 165, 165, 159, 190, 389, 182]],
    ['00:30.051', '跌倒了', [380, 222, 335]],
    ['00:31.185', '就不敢继续往前走', [179, 179, 175, 173, 156, 236, 319, 387]],
    ['00:33.049', '为什么', [318, 207, 289]],
    ['00:34.127', '人要这么的脆弱堕落', [196, 195, 193, 175, 156, 159, 340, 357, 514]],
    ['00:37.174', '请你打开电视看看', [373, 172, 174, 209, 193, 188, 154, 87]],
    ['00:38.871', '多少人', [226, 224, 203]],
    ['00:39.651', '为生命在努力勇敢的', [189, 169, 224, 168, 163, 159, 397, 167, 103]],
    ['00:41.651', '走下去', [342, 177 , 299]],
    ['00:42.718', '我们是不是该知足', [357, 207, 186, 172, 168, 164, 352, 249,]],
    ['00:45.366', '珍惜一切', [179, 296, 334, 257]],
    ['00:46.527', '就算没有拥有', [158, 199,198 ,192, 332, 612]],
    ['00:49.783', '还记得你说', [377, 350, 391,193, 224]],
    ['00:51.415', '家是唯一的城堡', [380, 380, 176, 180, 213, 367, 202]],
    ['00:53.470', '随着稻香河流继续奔跑', [348, 216,348, 305, 409,187, 176, 199, 373, 159]],
    ['00:56.379', '微微笑', [375, 348, 179]],
    ['00:57.487', '小时候的梦我知道', [197, 178, 175, 170, 179, 200, 435, 617]],
    ['01:01.499', '不要哭', [375, 348, 179]],
    ['01:02.566', '让萤火虫带着你逃跑', [238, 380, 325, 391,195, 193, 219, 311, 190]],
    ['01:05.173', '乡间的歌谣永远的依靠', [372, 194, 354, 349, 379, 193, 173, 200, 348, 181]],
    ['01:08.106', '回家吧', [367, 181, 328]],
    ['01:09.270', '回到最初的美好', [170, 172, 314, 175, 652, 321, 695]],
    ['01:36.694', '不要这么容易', [173, 159 , 418, 320, 185, 111]],
    ['01:38.139', '就想放弃', [171, 175, 159, 334]],
    ['01:38.978', '就像我说的', [181, 175, 184, 310, 190]],
    ['01:40.479', '追不到的梦想', [166, 177, 195, 184, 172, 95]],
    ['01:41.544', '换个梦不就得了', [159, 152, 155, 148, 147, 167, 245]],
    ['01:43.395', '为自己的人生鲜艳上色', [179, 178, 167, 167, 228, 272, 198, 165, 170, 177]],
    ['01:45.478', '先把爱涂上喜欢的颜色', [324, 178, 167, 175, 176, 188, 164, 167, 186, 111]],
    ['01:48.537', '笑一个吧', [254, 159, 162, 156]],
    ['01:49.464', '功成名就不是目的', [160, 179, 179, 173, 172, 183, 388, 317]],
    ['01:51.476', '让自己快乐快乐', [154, 163, 219, 181, 158, 163, 79]],
    ['01:52.723', '这才叫做意义', [157, 174, 163, 163, 142, 207]],
    ['01:54.369', '童年的纸飞机', [162, 172, 247, 361, 174, 213]],
    ['01:55.858', '现在终于飞回我手里', [336, 168, 169, 191,159, 213, 381, 370, 471]],
    ['02:00.051', '所谓的那快乐', [157, 186, 157, 223, 378, 187]],
    ['02:01.511', '赤脚在田里', [176, 371, 351, 187, 95]],
    ['02:02.773', '追蜻蜓追到累了', [171, 170, 168, 162, 242, 370, 152]],
    ['02:04.435', '偷摘水果', [226, 301, 383, 78]],
    ['02:05.532', '被蜜蜂给叮到怕了', [182, 190, 210, 174, 169, 185, 332, 255]],
    ['02:07.524', '谁在偷笑呢', [196, 195, 163, 346, 284]],
    ['02:09.074', '我靠着稻草人吹着风', [172, 163, 198, 178, 154, 358, 201, 159, 117]],
    ['02:11.030', '唱着歌睡着了', [183, 189, 370, 196, 175, 119]],
    ['02:12.482', '哦哦', [368, 168]],
    ['02:13.274', '午后吉它在虫鸣中', [176, 173, 160, 195, 174, 165, 174, 104]],
    ['02:14.677', '更清脆', [176, 164, 122]],
    ['02:15.376', '哦哦', [383, 155]],
    ['02:16.156', '阳光洒在路上', [197, 173, 193, 198, 170, 92]],
    ['02:17.259', '就不怕心碎', [178, 179, 185, 169, 139]],
    ['02:18.869', '珍惜一切', [179, 390, 185, 138]],
    ['02:20.177', '就算没有拥有', [172, 197, 206, 191, 357, 391]],
    ['02:23.480', '还记得你说', [362, 343, 364, 179, 191]],
    ['02:25.083', '家是唯一的城堡', [377, 383, 178, 205, 188, 347, 164]],
    ['02:27.127', '随着稻香河流继续奔跑', [369, 189, 350, 333, 402, 180, 198, 205, 358, 170]],
    ['02:30.060', '微微笑', [344, 181, 272]],
    ['02:31.187', '小时候的梦我知道', [175, 183, 172, 238, 372, 253, 335, 460]],
    ['02:35.187', '不要哭', [333, 392, 137]],
    ['02:36.240', '让萤火虫带着你逃跑', [208, 371, 335, 375, 208, 180, 223, 352, 138]],
    ['02:38.846', '乡间的歌谣永远的依靠', [337, 227, 334, 368, 393, 174, 182, 229, 335, 151]],
    ['02:41.774', '回家吧', [325, 208, 285]],
    ['02:42.921', '回到最初的美好', [178, 172, 327, 202, 619, 325, 403]],
    ['02:47.132', '还记得', [222, 143, 165]],
    ['02:47.890', '你说家是唯一的城堡', [222, 165, 353, 482, 195, 220, 204, 349, 179]],
    ['02:50.562', '随着稻香河流继续奔跑', [310, 229, 339, 351, 395, 177, 170, 199, 383, 163]],
    ['02:53.465', '微微笑', [312,195, 254]],
    ['02:54.572', '小时候的梦我知道', [189, 185, 329, 188, 176, 349, 363, 426]],
    ['02:58.607', '不要哭', [350, 349, 106]],
    ['02:59.706', '让萤火虫带着你逃跑', [177, 310, 374, 425, 187, 172, 218, 343, 90]],
    ['03:02.266', '乡间的歌谣永远的依靠', [357, 196, 352, 365, 425, 151, 170, 183, 345, 57]],
    ['03:05.162', '回家吧', [385, 202, 64]],
    ['03:06.285', '回到最初的美好', [188, 201, 371, 180, 557, 370, 557]],
]

const convertT = (s) => {
    let arr = s.split(':')
    return Number(arr[0])*60 + Number(arr[1])
}
let ircList = data.map(v => {
    return {
        t: convertT(v[0]),
        irc: v[1],
        textT:get(v, '[2]', [])
    }
})

export default ircList