<template>
  <div class="irc" @click="$emit('on-irc-click')">
    <div class="irc-top-box"></div>
    <div id="refIrc" class="irc-out">
      <div class="irc-list" :style="ircTop">
        <div class="start-point active irc-list-item">{{loadPoint}}</div>
        <div
          class="irc-list-item"
          v-for="(item, index) in ircData"
          :key="index"
        >
          <p
            :class="{
              'active': currT == index,
              'active-scan': currT == index,
              'active-pause': currT == index && paused
            }"
            :style="{
              'animation-duration': animationStyle
            }"
          >
            {{item.irc}}
          </p>
        </div>
      </div>
    </div>
    <div class="irc-bottom">
      <img src="https://qncweb.ktvsky.com/20220119/vadd/aea975fdf8f7718ca240a8b39260e0af.png" alt="">
    </div>
    <div class="irc-bottom-box"></div>
  </div>
</template>

<script>
import { ref, toRefs, watch, onMounted, onUnmounted, onBeforeUnmount } from 'vue'
import { checkLandscapeOrPortrait } from '@/utils/device'
import eventBus from '@/utils/event-bus'

export default {
  name: 'IrcList',
  props: {
    ircData: Array,
    currT: Number,
    paused: {
      type: Boolean,
      default: false
    },
  },
  setup(props) {
    const { ircData, currT } = toRefs(props)
    let ircTop = ref({
      top: 0
    })
    const loadPoint = ref('● ● ●') // 后续处理 播放3秒前递减
    let animationStyle = ref('2s') // 'scan 2s linear'
    const centerPoint = checkLandscapeOrPortrait() === 'landscape' ? 1 : 3
    let goBackTimer = null // 返回到当前播放歌词位置

    const setIrcTop = (val) => {
      let top = val > centerPoint ? `-${(val - centerPoint) * 153}px` : 0
      // 歌词播放完毕6s后歌词回归首位
      if ((val + 1) === ircData.value.length) {
        setTimeout(() => {
          ircTop.value = {
            top: 0
          }
        }, 8000)
      }
      ircTop.value = {
        top: top
      }
    }

    const setAnimationStyle = (val) => {
      // console.log(ircData.value[val].textT)
      const animationTime = ircData.value[val].textT.reduce((total, num) => {
        return total + num
      }, 0)
      animationStyle.value = `${animationTime/1000}s`
      // console.log(animationStyle.value)
    }

    watch(currT, (val) => {
      console.log(val)
      setIrcTop(val)
      setAnimationStyle(val)
    })

    const handleScroll = (e) => {
      // e.preventDefault()
      // 用户手动操作3秒后回归当前播放位
      if (goBackTimer) clearTimeout(goBackTimer)

      goBackTimer = setTimeout(() => {
        e.target.scrollTop = 0
      },3000)
    }

    const handleControlIrcReplay = () => {
      ircTop.value = {
        top: 0
      }
    }

    const attachIrcPlayerEvents = () => {
      eventBus.on('video-control-replay', handleControlIrcReplay)
    }

    const detachIrcPlayerEvents = () => {
      eventBus.off('video-control-replay', handleControlIrcReplay)
    }

    onMounted(() => {
      const refIrc = document.getElementById('refIrc')
      refIrc.addEventListener('scroll', handleScroll, false)
      attachIrcPlayerEvents()
    })
    onUnmounted(() => {
      detachIrcPlayerEvents()
    })
    onBeforeUnmount(() => {
      const refIrc = document.getElementById('refIrc')
      refIrc.removeEventListener('scroll', handleScroll)
    })

    return {
      ircTop,
      loadPoint,
      animationStyle,
      handleScroll,
    }
  }

}
</script>

<style lang="stylus" scoped>
.irc
  width 100vw
  height 100vh
  color rgba(238, 238, 238, 0.5)
  font-size 70px
  overflow-y auto
  position relative
  display flex
  justify-content center
  background url(https://qncweb.ktvsky.com/20220330/vadd/bf7d61c52afedd90f353b0f61688e65d.png) no-repeat
  background-size 100% 100%
  background-position center
  @media screen and (max-width: 1200px)
    // box-shadow 0 -480px 300px -200px rgba(5, 10, 30, 1) inset
    // box-shadow 0 200px 300px -200px rgba(5, 10, 30, 1) inset
    background url(https://qncweb.ktvsky.com/20211231/vadd/1c1b177a739ef3dea65007878175502f.png) no-repeat
  &-out
    width 100vw
    height 810px
    overflow-y auto
    position relative
    margin-top 120px
    @media screen and (max-width: 1200px)
      height 1580px
  &-list
    width 100%
    height auto
    z-index 0
    position absolute
    left 0
    transition-property all
    transition-duration .5s
    transition-timing-function ease-in-out
    &-item
      text-align center
      padding 24px 0
    .active
      color rgba(238, 238, 238, 1)
      font-size 100px
      font-weight 700
      @media screen and (max-width: 1200px)
        font-size 96px
    .active-scan
      background rgba(238, 238, 238, 0.86) -webkit-linear-gradient(left, rgba(124, 193, 255, 1), rgba(124, 193, 255, 1)) no-repeat 0 0
      -webkit-text-fill-color transparent
      -webkit-background-clip text
      background-size 0 100%
      animation scan 2s linear
    .active-load
      background-size 100% 100%
    .active-pause
      animation-play-state paused !important
  &-top-box
    width 100%
    height 260px
    z-index 3
    position absolute
    top 0
    left 0
    background linear-gradient(to top, rgba(29, 16, 50, 0), rgba(18, 15, 44, 1) 55%)
    @media screen and (max-width: 1200px)
      height 400px
  &-bottom-box
    width 100%
    height 260px
    z-index 3
    position absolute
    bottom 0
    left 0
    background linear-gradient(to bottom, rgba(29, 16, 50, 0),#18062C 80%)
    @media screen and (max-width: 1200px)
      height 400px
      background linear-gradient(to bottom, rgba(29, 16, 50, 0), rgba(9, 11, 33, 1), rgba(5, 10, 30, 1))
  &-bottom
    position absolute
    bottom 50px
    display flex !important
    flex-direction column
    justify-content center
    align-items center
    z-index 9
    @media screen and (max-width: 1200px)
      bottom 120px
    img
      width 300px
    p
      color #999999
      font-size 30px
      margin-top 40px
  @keyframes scan
    0%
      background-size 0 100%
    100%
      background-size 100% 100%
</style>
