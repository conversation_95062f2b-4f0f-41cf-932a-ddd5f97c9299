<template>
  <div
    class="lrc"
    @click="$emit('on-lrc-click')"
    :class="{
      'pos-left': pos == 2,
      'pos-right': pos == 1,
    }"
  >
    <div class="lrc-background"></div>
    <div
      id="refLrc"
      class="lrc-out"
      :class="currentIyricIndex < 1 && 'init'"
    >
      <div
        class="lrc-list"
      >
        <div v-if="currentIyricIndex < 0">
          <p class="lrc-list-songname">{{ songName }}</p>
          <p class="lrc-list-singername">{{ singerName }}</p>
          <div v-if="!lrcEmpty" class="start-point">
            <span v-for="(point, i) in loadPoint" :key="i">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
                fill="none"
                version="1.1"
                width="30"
                height="30"
                viewBox="0 0 30 30"
              >
                <g style="mix-blend-mode: passthrough">
                  <ellipse
                    cx="15"
                    cy="15"
                    rx="15"
                    ry="15"
                    fill="#D8D8D8"
                    fill-opacity="1"
                  />
                </g>
              </svg>
            </span>
          </div>
        </div>
        <div v-if="lrcData.length">
          <div
            v-if="currentIyricIndex >= 0"
            class="lrc-list-item"
          >
            <p :class="currentIyricIndex === 0 && 'lrc-list-item-p'">
              <span>
                {{ lrcItems.up }}
              </span>
            </p>
            <p
              :class="currentIyricIndex !== 0 && 'lrc-list-item-p'"
            >
              <span>
                {{ lrcItems.center }}
              </span>
            </p>
            <p>
              <span>
                {{ lrcItems.down }}
              </span>
            </p>
          </div>
        </div>
        <div v-if="lrcEmpty" class="lrc-list-nodata">暂无匹配歌词</div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, toRefs, watch, computed, defineProps, onMounted, onUnmounted, onBeforeUnmount } from 'vue'
import eventBus from '@/utils/event-bus'
import { get } from 'lodash'
import { sendLog } from "@/directives/v-log/log";

const props = defineProps({
  lrcData: Array,
  lrcEmpty: {
    type: Boolean,
    default: true,
  },
  songName: String,
  singerName: String,
  currentIyricIndex: {
    type: Number,
    default: -1,
  },
  paused: {
    type: Boolean,
    default: false,
  },
  currentPlayTime: {
    type: Number,
    default: 0,
  },
  browserType: {
    type: String,
    default: 'landscape'
  },
  pos: {
    type: Number,
    default: 0, // 0 不显示 1 右侧显示 2 左侧显示
  }
})

const { lrcData, currentIyricIndex, currentPlayTime } = toRefs(props)

const lrcItems = computed(() => {
  let res = {
    up: '',
    down: '',
    center: ''
  };

  if (lrcData.value.length) {
    if (currentIyricIndex.value === 0) {
      res.up = lrcData.value[0].irc.join('').replace(/,/g, '');
      res.center = lrcData.value[1].irc.join('').replace(/,/g, '');
      res.down = lrcData.value[2].irc.join('').replace(/,/g, '');
    } else if (currentIyricIndex.value > 0) {
      res.up = get(lrcData.value, `[${currentIyricIndex.value - 1}].irc`, ['']).join('').replace(/,/g, '');
      res.center = lrcData.value[currentIyricIndex.value].irc.join('').replace(/,/g, '');
      res.down = get(lrcData.value, `[${currentIyricIndex.value + 1}].irc`, ['']).join('').replace(/,/g, '');
    }
  }

  return res;
});

const loadPoint = ref(['●', '●', '●'])
let goBackTimer = null
let reducePointTimer = null

const handleInit = () => {
  console.log('lrc handleInit')
  loadPoint.value = ['●', '●', '●']
  clearTimeout(goBackTimer)
  clearInterval(reducePointTimer)
  goBackTimer = null
  reducePointTimer = null
}

watch(currentIyricIndex, (val) => {
  if (val === -1) {
    handleInit()
  }
})

const reducePoint = (t) => {
  if (reducePointTimer) return
  reducePointTimer = setInterval(() => {
    if (loadPoint.value.length === 0) {
      clearInterval(reducePointTimer)
      reducePointTimer = null
      return
    }
    if (loadPoint.value.length === 1) return
    loadPoint.value.pop()
  }, t)
}

watch(currentPlayTime, (val) => {
  const firstLrc = parseInt(lrcData.value[0].t)
  if (val > 0 && parseInt(val) === 0) {
    if (firstLrc <= 1) {
      reducePoint(300)
    } else {
      if (firstLrc < 4.7) reducePoint(((firstLrc - 0.2) * 1000) / 3)
    }
  }
  if (parseInt(val + 4.5) === firstLrc) reducePoint(1500)
})

const handleScroll = (e) => {
  clearTimeout(goBackTimer)
  goBackTimer = setTimeout(() => {
    e.target.scrollTop = 0
  }, 3000)
}

const handleControlIrcReplay = () => {
  console.log('handleControlIrcReplay')
  handleInit()
}

const handleControlIrcNext = () => {
  console.log('handleControlIrcNext')
  handleInit()
}

const handleControlIrcEnd = () => {
  console.log('handleControlIrcEnd')
  handleInit()
}

const attachIrcPlayerEvents = () => {
  eventBus.on('irc-control-replay', handleControlIrcReplay)
  eventBus.on('irc-control-next', handleControlIrcNext)
  eventBus.on('irc-control-end', handleControlIrcEnd)
}

const detachIrcPlayerEvents = () => {
  eventBus.off('irc-control-replay', handleControlIrcReplay)
  eventBus.off('irc-control-next', handleControlIrcNext)
  eventBus.off('irc-control-end', handleControlIrcEnd)
}

onMounted(() => {
  const refLrc = document.getElementById('refLrc')
  refLrc.addEventListener('scroll', handleScroll, false)
  attachIrcPlayerEvents()

  sendLog({
    event_type: '10000~50000',
    event_name: 6007,
    event_data: {
      str1: '欢唱页',
      str2: '歌词模式',
      str3: '歌词模式展示',
      str4: 'show',
    },
  })
})

onUnmounted(() => {
  detachIrcPlayerEvents()
})

onBeforeUnmount(() => {
  const refLrc = document.getElementById('refLrc')
  refLrc.removeEventListener('scroll', handleScroll)
})

</script>
<style lang="stylus" scoped>
.lrc
  width 100vw
  height 100vh
  color rgba(238, 238, 238, 0.4)
  font-size 70px
  overflow-y auto
  position relative
  display flex
  justify-content center
  align-items center
  background url('https://qncweb.ktvsky.com/20250219/vadd/091c9b87f72a7deb232a71d9c4610b06.png')
  background-size 100% 100%
  background-position center
  &-out
    width 100vw
    height 100vh
    overflow-y auto
    position relative
    margin-top 0px
    display flex
    justify-content center
    align-items center
  &-list
    width 100%
    height 500px
    z-index 0
    position relative
    transition-property all
    transition-duration 0.5s
    transition-timing-function ease-in-out
    touch-action none
    display flex
    flex-direction column
    justify-content center
    &-singername
      text-align center
      color rgba(238, 238, 238, 0.8)
      font-size 40px
      padding-bottom 20px
    &-songname
      text-align center
      color rgba(238, 238, 238, 0.8)
      font-size 100px
      padding 80px 0 30px
      @media screen and (max-width 1200px) and (min-height 1421px)
        padding 500px 0 50px
      @media screen and (max-width 1181px) and (min-height 1200px)
        padding 360px 0 30px
      @media screen and (max-width 1181px) and (max-height 1199px)
        padding 240px 0 30px !important
    &-nodata
      height 100px
      margin-bottom 120px
      font-size 46px
      padding-top 24px
      text-align center
    &-item
      text-align center
      padding 12px 0
      p
        width 100%
        overflow hidden
      span
        display inline-block
        width 100%
        white-space: normal; /* 允许换行 */
      &-p
        overflow hidden
        width 100%
        white-space nowrap
        color rgba(124, 193, 255, 1) !important
        font-size 96px
        font-weight 700
      
    .start-point
      height 100px
      margin-bottom 120px
      text-align center
      display flex
      justify-content center
      align-items center
      span
        padding 0 15px
    .active
      color rgba(238, 238, 238, 1)
      font-size 96px
      font-weight 700
      transition font-size 0.4s linear
      @media screen and (max-width 1200px)
        font-size 96px
    .active-scan
      background rgba(238, 238, 238, 0.86) -webkit-linear-gradient(left, rgba(124, 193, 255, 1), rgba(124, 193, 255, 1)) no-repeat 0 0
      -webkit-text-fill-color transparent
      -webkit-background-clip text
      background-size 0 100%
      animation scan 2s linear
      animation-fill-mode forwards
    .active-load
      background-size 100% 100%
    .active-pause
      animation-play-state paused !important
    .animate-fade-in
      animation fade-in 1.2s cubic-bezier(0.39, 0.575, 0.565, 1) both
  &-bottom
    position absolute
    bottom 50px
    display flex !important
    flex-direction column
    justify-content center
    align-items center
    z-index 9
    opacity 0.7
    @media screen and (max-width 1200px) and (min-height 1421px)
      bottom 120px
    @media screen and (max-width 1180px) and (min-height 1200px)
      bottom 50px
    img
      width 300px
    p
      color #999999
      font-size 30px
      margin-top 40px
  @keyframes scan
    0%
      background-size 0 100%
    100%
      background-size 100% 100%
  @keyframes fade-in
    0%
      opacity 0.2
    100%
      opacity 1
  &-background
    position absolute
    top 0
    left 0
    right 0
    bottom 0
    z-index 0
    video
      width 100vw
      height 100vh

.pos-left
  padding-left 43vw
  .lrc-background
    padding-left 43vw

  @media screen and (max-width: 1950px) and (min-width: 1700px) and (max-height: 720px) and (min-height: 650px)
    padding-left 30vw
    .lrc-background
      padding-left 30vw
  
  .lrc-list
    position relative
    p
      font-size 50px
      @media screen and (max-width: 1950px) and (min-width: 1700px) and (max-height: 720px) and (min-height: 650px)
        font-size 60px

    .lrc-list-item-p
      font-size 80px
      @media screen and (max-width: 1950px) and (min-width: 1700px) and (max-height: 720px) and (min-height: 650px)
        font-size 90px

  .lrc-out
    height 100vh !important
    display flex
    align-items center
      
.pos-right
  padding-right 43vw
  .lrc-background
    padding-right 43vw

  @media screen and (max-width: 1950px) and (min-width: 1700px) and (max-height: 720px) and (min-height: 650px)
    padding-right 30vw
    .lrc-background
      padding-right 30vw
    
  .lrc-list
    position relative
    p
      font-size 50px
      @media screen and (max-width: 1950px) and (min-width: 1700px) and (max-height: 720px) and (min-height: 650px)
        font-size 60px

    .lrc-list-item-p
      font-size 80px
      @media screen and (max-width: 1950px) and (min-width: 1700px) and (max-height: 720px) and (min-height: 650px)
        font-size 90px

  .lrc-out
    height 100vh !important
    display flex
    align-items center
</style>