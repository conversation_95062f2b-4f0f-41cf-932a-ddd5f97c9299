<template>
  <div>
    <div
      v-if="isShowBottomBar"
      class="bottom-bar"
      @click="handleClickBottombar"
    >

      <div class="left">
        <div class="bottom-bar-btn">
          <img
            v-show="paused"
            class="bottom-bar-img margin_26"
            @click.stop="handleVideoPlay"
            :src="imgs[themeClass].play"
          />
          
          <img
            v-show="!paused"
            class="bottom-bar-img"
            @click.stop="handleVideoPause"
            :src="imgs[themeClass].pause"
          />
        </div>
        <div class="bottom-bar-btn">
          <img
            class="bottom-bar-img"
            :src="imgs[themeClass].next"
            @click.stop="handleVideoNext"
            alt=""
          />
        </div>
      </div>
      <div class="bottom-bar-lyc theme-color">
        <div class="bottom-bar-lyc-item">
          <p class="bottom-bar-lyc-item-p" v-if="!lycListNum || currIrcIndex === -1">{{ lycName }}</p>
          <p v-else class="bottom-bar-lyc-item-p" ref="lyricP">
            <span
              v-for="(text, ind) in lycTxt.irc"
              :key="ind + lycTxt.t"
              :ref="el => { if (el) lyricSpans[ind] = el }"
            >
              {{ text.trim() }}
              {{ text.includes(' ') ? '&nbsp;' : '' }}
            </span>
          </p>
        </div>
      </div>
    </div>
    <OperationSus 
      v-if="isShowOperationSus"
      :class="botPos"
      @close="handleCloseOperationSus"
    />
  </div>
</template>

<script setup>
import { computed, ref, watch, nextTick } from 'vue'
import { useStore } from 'vuex'
import { useRoute } from 'vue-router'
import eventBus from '@/utils/event-bus'
import useVip from '@/composables/useVip'
import useSongItem from '@/composables/useSongItem'
import useDownload from '@/composables/useDownload';
import OperationSus from '@/components/operation'
import Toast from '@/utils/toast'
import getComponentLrcData from '@/components/mv/utils'
import { sendLog } from '@/directives/v-log/log'
import { debounce } from 'lodash'

const store = useStore()
const route = useRoute()
const { showVipQrcode, isVipUser } = useVip()
const { orderSong } = useSongItem()
const { getIsLocalSong } = useDownload();

const mvIsHide = computed(() => store.state.mvIsHide)
const isAlreadyEnterMv = computed(() => store.state.videoInitAutoPlay)
const paused = computed(() => store.state.videoPaused)
const videoPlayerHistory = computed(() => store.state.videoPlayerHistory)
const orderedList = computed(() => store.state.orderedList)
const orderedListNum = computed(() => orderedList.value.length)
const orderedSongIdMap = computed(() => store.state.orderedSongIdMap)
const isSingStatus = computed(() => store.state.isSingStatus)

const isShowBottomBar = computed(() => !!(mvIsHide.value && isSingStatus.value && orderedListNum.value));
const isShowOperationSus = ref(true)

const lyricP = ref(null);
const lyricSpans = ref([]);

const botPos = computed(() => {
  if (isShowBottomBar.value) {
    if (route.name === 'search') return 'bot-pos-search'
    if (route.name === 'singer') return 'bot-pos-singer'
    return 'bot-pos'
  }
  return ''
})

const lycList = computed(() => {
  return getComponentLrcData(store.state.videoPlayerHistory.songItemLrc)
})
const lycListNum = computed(() => lycList.value.length)
const currIrcIndex = computed(() => store.state.currIrcIndex)
const lycTxt = computed(() => {
  if (lycListNum.value) {
    if (currIrcIndex.value) {
      return lycList.value[currIrcIndex.value]
    }
    return lycList.value[0]
  }
  return []
})

const lycName = computed(() => {
  if (orderedListNum.value) {
    if (currIrcIndex.value === -1 || !lycListNum.value) {
      return `${orderedList.value[0].music_name} - ${orderedList.value[0].singer}`
    }
  }
  return ''
})


const imgs = {
  themeDark: {
    play: require('@/assets/bottom-play-d.png'),
    pause: require('@/assets/bottom-pause-d.png'),
    next: require('@/assets/bottom-next-d.png'),
  },
  themeLight: {
    play: require('@/assets/bottom-play.png'),
    pause: require('@/assets/bottom-pause.png'),
    next: require('@/assets/bottom-next.png'),
  },
  themeSystem: {
    play: require('@/assets/bottom-play-d.png'),
    pause: require('@/assets/bottom-pause-d.png'),
    next: require('@/assets/bottom-next-d.png'),
  },
}

const themeClass = computed(() => store.state.themeClass)

const checkMvIsPlaying = async () => {
  if (!isVipUser.value) {
    const isLocal = await getIsLocalSong(orderedList.value[0])
    if (orderedList.value[0].is_vip && !isLocal) {
      showVipQrcode()
      return false
    }
  }
  if (!videoPlayerHistory.value.songItem.songid) {
    if (
      Object.keys(orderedSongIdMap.value).length
    ) {
      orderSong(orderedList.value[0], {
        position: 'recovery',
        isPushOrdered: false,
      })
      return true
    }
    Toast('请点播歌曲')
    return false
  }
  return true
}

const handleVideoPlay = async () => {
  const canPlay = await checkMvIsPlaying()
  if (canPlay) {
    eventBus.emit('video-control-resume')
  }
  sendLog({
    event_type: '10000~50000',
    event_name: 10096,
    event_data: {
      str1: '任意页',
      str2: '歌词条',
      str3: '播放',
      str4: 'click',
    },
  })

  sendLog({
    event_type: '10000~50000',
    event_name: 6012,
    event_data: {
      str1: '通用',
      str2: '歌词条',
      str3: '播放/暂停',
      str4: 'click',
      str5: videoPlayerHistory.value.songItem.songid,
      str6: !isVipUser.value ? 2 : 1
    },
  })
}

const handleVideoPause = () => {
  checkMvIsPlaying()
  eventBus.emit('handle-video-pause')
  sendLog({
    event_type: '10000~50000',
    event_name: 10096,
    event_data: {
      str1: '任意页',
      str2: '歌词条',
      str3: '暂停',
      str4: 'click',
    },
  })

  sendLog({
    event_type: '10000~50000',
    event_name: 6012,
    event_data: {
      str1: '通用',
      str2: '歌词条',
      str3: '播放/暂停',
      str4: 'click',
      str5: videoPlayerHistory.value.songItem.songid,
      str6: !isVipUser.value ? 2 : 1
    },
  })
}

const handleVideoNext = debounce(() => {
  eventBus.emit('handle-video-next', 'bottom-bar')
  sendLog({
    event_type: '10000~50000',
    event_name: 10097,
    event_data: {
      str1: '任意页',
      str2: '歌词条',
      str3: '切歌',
      str4: 'click',
    },
  })

  sendLog({
    event_type: '10000~50000',
    event_name: 6002,
    event_data: {
      str1: '通用',
      str2: '歌词条',
      str3: '切歌',
      str4: 'click',
      str5: videoPlayerHistory.value.songItem.songid,
      str6: !isVipUser.value ? 2 : 1
    },
  })
}, 1000, { leading: true, trailing: false })

const handleCloseOperationSus = () => {
  isShowOperationSus.value = false
}

const handleClickBottombar = () => {
  sendLog({
    event_type: '10000~50000',
    event_name: 6012,
    event_data: {
      str1: '通用',
      str2: '歌词条',
      str3: '点击进入 MV',
      str4: 'click',
      str5: videoPlayerHistory.value.songItem.songid,
      str6: !isVipUser.value ? 2 : 1
    },
  })
  store.commit('UPDATE_MV_ISHIDE', false)
}

watch(isShowBottomBar, (val) => {
  if (val) {
    sendLog({
      event_type: '10000~50000',
      event_name: 10095,
      event_data: {
        str1: '任意页',
        str2: '歌词条',
        str3: '展示',
        str4: 'show',
      },
    })

    sendLog({
      event_type: '10000~50000',
      event_name: 6012,
      event_data: {
        str1: '通用',
        str2: '歌词条',
        str3: '展示歌词条',
        str4: 'show',
      },
    })
  }
})

watch(currIrcIndex, async (newIndex) => {
  const currentLyric = lycList.value[newIndex];

  if (currentLyric.irc.length > 50) {
    const delayTime = currentLyric.delayTime[currentLyric.delayTime.length - 2];

    setTimeout(() => {
      const lastSpan = lyricSpans.value[lyricSpans.value.length - 1];
      lastSpan.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest',
        inline: 'start'
      });
    }, delayTime);
  }
}, {
  deep: true,
  immediate: true,
});

</script>

<style lang="stylus" scoped>
.bottom-bar
  width 100%
  height 110px
  display flex
  flex-direction row
  align-items center
  padding 0 286px 0
  position fixed
  bottom 0
  left 0
  z-index 9
  background: #20242b
  .left
    position absolute
    left 42px
    display flex
  @media screen and (max-width 1200px)
    padding-right 100px
  &-btn
    width 110px
    height 110px
    display flex
    align-items center
    justify-content center
    transition-property all
    transition-duration .5s
    transition-timing-function ease-in-out
    &:nth-child(3)
      left 176px
  &-img
    width 44px
    height 44px
  &-lyc
    flex 1
    height 100%
    display flex
    align-items center
    justify-content center
    font-size 40px
    opacity 0.8
    @media screen and (max-width 1200px)
      font-size 32px
    .active-scan
      background rgba(255, 255, 255, 0.4) -webkit-linear-gradient(left, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.8)) no-repeat 0 0
      -webkit-text-fill-color transparent
      -webkit-background-clip text
      background-size 0 100%
      animation scan 2s linear
      animation-fill-mode forwards
    .active-load
      background-size 100% 100%
    .active-pause
      animation-play-state paused !important
    .animate-fade-in
      animation fade-in 1.2s cubic-bezier(0.39, 0.575, 0.565, 1) both
    p
      font-weight 400
    &-item
      width 100%
      height 100%
      display flex
      align-items center
      justify-content center
      &-p
        max-width 80vw !important
        height 100%
        line-height 110px
        display flex
        align-items center
        flex-wrap nowrap
        color #fff !important
        overflow-x scroll !important
        overflow-y hidden !important
        white-space nowrap !important
        @media screen and (max-width 1200px)
          max-width 80vw
      span
        font-weight 400
        height 42px
        line-height 42px
  .margin_26
    margin 0 26px
  .margin_left_20
    margin-left 20px
  @keyframes scan
    0%
      background-size 0 100%
    100%
      background-size 100% 100%
  @keyframes fade-in
    0%
      opacity 0.2
    100%
      opacity 1
.bot-pos
  bottom 118px !important
.bot-pos-search
  bottom 140px !important
.bot-pos-singer
  bottom 110px !important
.theme-themeLight
  .bottom-bar-lyc-item-p
    color rgba(29, 29, 31, 1) !important

</style>