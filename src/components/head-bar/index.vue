<template>
  <div class="header">
    <div class="header-back">
      <!-- <img
        @click="handleBack"
        :src="imgs[themeClass].back"
      /> -->
      <div class="back" @click="handleBack">
        <svg v-if="themeClass === 'themeLight'" width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M19.6759 3.70701L20.383 2.99991L18.9688 1.58569L18.2617 2.2928L1.29109 19.2634L0.583984 19.9705L1.29109 20.6776L18.2617 37.6481L18.9688 38.3552L20.383 36.941L19.6759 36.2339L4.44185 20.9999H37V18.9999H4.38297L19.6759 3.70701Z" fill="#1D1D1F" style="fill:#1D1D1F;fill:color(display-p3 0.1137 0.1137 0.1216);fill-opacity:1;"/>
        </svg>

        <svg v-else width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M19.6759 3.70714L20.383 3.00003L18.9688 1.58582L18.2617 2.29292L1.29109 19.2635L0.583984 19.9706L1.29109 20.6777L18.2617 37.6483L18.9688 38.3554L20.383 36.9412L19.6759 36.234L4.44185 21H37V19H4.38297L19.6759 3.70714Z" fill="white" style="fill:white;fill-opacity:1;"/>
        </svg>
      </div>
    </div>
    <span>
      {{ title }}
    </span>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { defineProps, defineEmits } from 'vue'

const props = defineProps({
  title: String,
})

const emit = defineEmits(['back'])

const router = useRouter()
const store = useStore()

const imgs = {
  themeDark: {
    back: 'https://qncweb.ktvsky.com/20231207/vadd/72ff0b114ee2cb3153ce901af19bc813.png',
  },
  themeLight: {
    back: 'https://qncweb.ktvsky.com/20240221/vadd/7691c0a91bc7f951374f15c175f8d69a.png',
  },
  themeSystem: {
    back: 'https://qncweb.ktvsky.com/20231207/vadd/72ff0b114ee2cb3153ce901af19bc813.png',
  },
}

const themeClass = computed(() => store.state.themeClass)

const handleBack = () => {
  store.dispatch('getCarplayInfo') // 返回上一页时更新下用户状态
  router.back()
  emit('back')
  store.commit('base/SET_IS_ROUTER_BACK', true)
}
</script>

<style lang="stylus" scoped>
.header
  width 100vw
  padding 36px 80px
  background none
  display flex
  align-items center
  position fixed
  top 0
  left 0
  z-index 6
  @media screen and (max-width 1200px)
    padding 0px 60px 0px 48px
    height 140px
  &-back
    width fit-content
    height 90px
    display flex
    align-items center
    justify-content center
    .back
      width 40px
      height 40px
      margin-right 60px
    svg
      width 40px
      height 40px
  span
    color rgba(255, 255, 255, 1)
    font-size 32px
    font-weight 300
.theme-themeLight
  span
    color #1D1D1F
</style>

