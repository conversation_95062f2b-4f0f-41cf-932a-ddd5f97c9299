<template>
  <div class="backbtn" @click="handleBack">
    <img class="backbtn-icon" src="https://qncweb.ktvsky.com/20210922/vadd/53133eda4c466cf1365ec9c00069a85e.png"/>
  </div>
</template>

<script>
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'

export default {
  name: 'MvBackBtn',
  setup(props, { emit }) {
    const router = useRouter()
    const store = useStore()
    const handleBack = () => {
      emit('back')
      store.commit('UPDATE_MV_ISHIDE', true)
      // router.back()
    }
    return {
      handleBack,
    }
  }
}
</script>

<style lang="stylus" scoped>
.backbtn
  position absolute
  top 35px
  left 80px
  z-index 12
  width 140px
  height 80px
  display flex
  justify-content center
  align-items center
  background rgba(30, 31, 33, 0.8)
  border-radius 20px
  &-icon
    width 23px
    height 40px
</style>
