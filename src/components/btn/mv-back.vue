<template>
  <div class="backbtn" @click="handleBack">
    <!-- <img class="backbtn-icon" src="https://qncweb.ktvsky.com/20231207/vadd/72ff0b114ee2cb3153ce901af19bc813.png"/> -->
    <svg class="backbtn-icon" width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fill-rule="evenodd" clip-rule="evenodd" d="M19.6739 3.70714L20.381 3.00003L18.9668 1.58582L18.2597 2.29292L1.28914 19.2635L0.582031 19.9706L1.28914 20.6777L18.2597 37.6483L18.9668 38.3554L20.381 36.9412L19.6739 36.234L4.4399 21H36.9981V19H4.38102L19.6739 3.70714Z" fill="white" style="fill:white;fill-opacity:1;"/>
    </svg>
  </div>
</template>

<script setup>
import { useRoute } from 'vue-router';
import { computed } from 'vue';
import { useStore } from 'vuex';
import { TSNativeInstance } from '@/packages/TSJsbridge';
import { sendLog } from "@/directives/v-log/log";

// 使用 Vue Router
const route = useRoute();
// 使用 Vuex Store
const store = useStore();

// 计算属性，获取 mvIsHide 的状态
const mvIsHide = computed(() => store.state.mvIsHide);

// 处理返回按钮的逻辑
const handleBack = async (payload) => {
  try {
    sendLog({
      event_type: '10000~50000',
      event_name: 6007,
      event_data: {
        str1: '欢唱页',
        str2: '播控浮层',
        str3: '返回首页',
        str4: 'click'
      },
    })

    console.log('handleBack: Start handling back button'); // 新增的console信息

    const isMvHidden = mvIsHide.value;
    console.log('handleBack: Current mvIsHide status', isMvHidden); // 新增的console信息

    // 如果当前路由是 home 且 mvIsHide 为 true，调用退出方法
    if (route.name === 'home' && isMvHidden) {
      console.log('handleBack: Current route is home and mvIsHide is true, calling exit method'); // 新增的console信息
      TSNativeInstance.exit();
      return;
    }

    // 提交 mutation 更新 mvIsHide 状态
    console.log('handleBack: Updating mvIsHide status to true'); // 新增的console信息
    store.commit('UPDATE_MV_ISHIDE', true);

    console.log('handleBack: Back button handling completed'); // 新增的console信息
  } catch (error) {
    console.log('handleBack error', error, payload);
  }
};
</script>

<style lang="stylus" scoped>
.backbtn
  position absolute
  top 35px
  left 70px !important
  z-index 12
  width 130px
  height 80px
  display flex
  justify-content center
  align-items center
  background rgba(30, 31, 33, 0.8)
  border-radius 20px
  @media screen and (min-aspect-ratio: 1920/720) and (max-aspect-ratio: 1792/660)
    zoom 0.8
    top 100 !important
  &-icon
    width 40px
    height auto
</style>
