<template>
  <div
    ref="root"
    class="loadmore"
    :style="{
      height: calcHeight
    }"
  >
  <!-- @scroll="handleScroll" -->
    <slot name="default"></slot>
    <div ref="triggerTarget" class="loadmore-trigger"></div>
  </div>
</template>
<script>
import { ref, onBeforeUnmount, onMounted, computed, toRefs, nextTick } from 'vue'

export default {
  name: '<PERSON>adMore',
  props: {
    safeAreaHeight: {
      type: String,
      default: '13.6991vw',
    }
  },
  setup(props, { emit }) {
    
    // const route = useRoute()
    const { safeAreaHeight } = toRefs(props)

    let root = ref(null)
    let triggerTarget = ref(null)
    let observer = ref({})

    const calcHeight = computed(() => {
      if (safeAreaHeight.value.includes('calc')) {
        return safeAreaHeight.value
      }
      return `calc(100vh - ${safeAreaHeight.value})`
    })

    const options = {
      root: null,
      rootMargin: '0px',
      threshold: 0.5
    }
    const init = () => {
      options.root = root.value
      observer.value = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            console.log('load-more')
            emit('load-more')
          }
        });
      }, options);
      observer.value.observe(triggerTarget.value)
    }

    const handleScroll = () => {
      const scrollTop = root.value.scrollTop;
      const scrollHeight = root.value.scrollHeight;
      const clientHeight = root.value.clientHeight;

      // 当滚动到接近底部时触发加载更多
      if (scrollTop + clientHeight >= scrollHeight - 40) {
        console.log('load-more triggered');
        emit('load-more');
      }
    };

    const destory = () => {
      observer.value = null
      // root.value.removeEventListener('scroll', handleScroll)
    }

    onMounted(async () => {
      await nextTick()
      // root.value.addEventListener('scroll', handleScroll) // 也可以换成监听
      init()
    })

    onBeforeUnmount(destory)

    return {
      root,
      triggerTarget,
      calcHeight,
      // handleScroll,
    }
  },
}
</script>
<style lang="stylus" scoped>
#triggerTarget
  height 10px
.loadmore
  overflow-y: scroll
  padding-bottom 110px!important
  &::-webkit-scrollbar
    width 8px
  &::-webkit-scrollbar-thumb
    width 8px
    border-radius: 4px
    background: #303030
  &::-webkit-scrollbar-track
    background: #000000
  &-trigger
    height 10px
</style>