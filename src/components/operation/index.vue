<template>
  <div
    v-show="isShowOperationSus && isYSTipAccept && !isVip"
    class="operation-sus"
    :class="[route_page === 'search' && 'operation-sus-search', route_page === 'singer' && 'operation-sus-singer']"
    @click="handleShowVip"
  >
    <div @click.stop="$emit('close')" class="operation-sus-close"></div>
    <img v-show="!['search', 'singer'].includes(route_page)" class="operation-sus-icon" src="https://qncweb.ktvsky.com/20241122/other/1861781b2bcb80a8e2f70c3350cb3312.png">
  </div>
</template>

<script>
import { computed, ref, watch, onMounted } from 'vue'
import { useStore } from 'vuex'
import { useRoute } from 'vue-router'
import useVip from '@/composables/useVip'
import { sendLog } from '@/directives/v-log/log'

export default {
  name: 'OperationSus',
  setup() {
    const store = useStore()
    const route = useRoute()
    const { showVipQrcode } = useVip()

    // 搜索页和歌星页的来源埋点 - 车机端
    const frObj = {
      search: 1849,
      singer: 1767,
    }

    const route_page = computed(() => route.name)
    const userInfo = computed(() => store.state.userInfo)
    const isLogin = computed(() => !!userInfo.value.unionid)
    const mvIsHide = computed(() => store.state.mvIsHide)
    const isYSTipAccept = computed(() => store.state.storageYSTipAccept)
    const vipInfo = computed(() => store.state.vipInfo)
    const isVip = computed(() => !!vipInfo.value.end_time)
    const isShowOperationSus = ref(true)

    const handleShowVip = () => {
      sendLog({
        event_type: 'click',
        event_name: 1737
      })
      // 此埋点先不区分页面 产品提供的埋点方案暂时没区分
      showVipQrcode({
        log: frObj[route.name] ? `${route.name}-底部运营位` : '首页-底部运营位',
        isLogin: isLogin.value,
        fr: frObj[route.name] ? frObj[route.name] : 1849
      })

      sendLog({
        event_type: '10000~50000',
        event_name: 1021,
        event_data: {
          str1: '搜索',
          str2: '底部搜索结果运营位',
          str3: '',
          str4: 'show',
          str5: 1,
          str9: '车机端',
          str10: isLogin.value ? 1849 : 1848
        }
      })

      sendLog({
        event_type: 'show',
        event_name: 1738
      })

      sendLog({
        event_type: '10000~50000',
        event_name: 6012,
        event_data: {
          str1: '通用',
          str2: '底部运营位',
          str3: '点击',
          str4: 'click',
        },
      })
    }

    watch(mvIsHide, (val) => {
      isShowOperationSus.value = val
    })

    onMounted(()=>{
      sendLog({
        event_type: '10000~50000',
        event_name: 6012,
        event_data: {
          str1: '通用',
          str2: '底部运营位',
          str3: '展示',
          str4: 'show',
        },
      })
    })

    return {
      route_page,
      isShowOperationSus,
      handleShowVip,
      isYSTipAccept,
      isVip
    }
  }
}
</script>

<style lang="stylus" scoped>
.operation-sus
  width 800px
  height 90px
  display flex
  align-items center
  justify-content flex-end
  position fixed
  bottom 50px
  left calc(50vw - 400px)
  background url(https://qncweb.ktvsky.com/20241122/other/91f162704d82d7abdc1f5bcee0e60cf8.png) no-repeat
  background-size 100% 100%
  background-position center
  z-index 0
  &-icon {
    position absolute
    width 106px
    height auto
    top -40px
    left 80px
  }
  &-close
    width 80px
    height 80px
    margin-right 20px
.operation-sus-search
  width 740px
  height 116px
  background url(https://qncweb.ktvsky.com/20231213/vadd/037524b0c62adf8b6782e420936c1492.png) no-repeat
  background-size 100% 100%
  z-index 10
.operation-sus-singer
  width 766px
  height 141px
  background url(https://qncweb.ktvsky.com/20231213/vadd/072ae7f281a657a5691b52c7b7c90d9a.png) no-repeat
  background-size 100% 100%
</style>