<template>
  <div class="service-licence">
    <div>版本号：v{{finalVn}}</div>
    <div>
      <a @click="handleGoLicence">
        备案号：{{ serviceLicenceInfo.serviceLicence }}
      </a>
    </div>
  </div>
</template>

<script setup>
import { onMounted, computed } from 'vue';
import { useStore } from 'vuex';
import { getLicence } from '@/service/base';
import config from '@/config'
import { TSBaseInfoInstance, TSNativeInstance } from '@/packages/TSJsbridge'
import { sendLog } from '@/directives/v-log/log'

const store = useStore();
const { vn } = config;

const serviceLicenceInfo = computed(() => store.state.serviceLicenceInfo);

const finalVn = computed(() => {
  // 版本号规则：安卓版本号-前端版本号（01开始）
  const _vn = TSNativeInstance.getParams('_vn') || '1.07';
  return _vn.replace(/v/g, '') + '-' + vn;
});

const getAndSaveLicenceInfo = async () => {
  const { data = {} } = await getLicence();
  store.commit('SAVE_SERVICE_INFO', data);
};

onMounted(() => {
  if (!serviceLicenceInfo.value.serviceUrl) {
    getAndSaveLicenceInfo();
  }
});

const handleGoLicence = () => {
  sendLog({
    event_type: '10000~50000',
    event_name: 6009,
    event_data: {
      str1: '设置页',
      str2: '备案号',
      str3: '点击备案号',
      str4: 'click'
    }
  })

  TSBaseInfoInstance.goToExternalPage(serviceLicenceInfo.value.serviceUrl);
};
</script>

<style lang="stylus" scoped>
.service-licence
  font-size: 28px;
  color: rgba(255, 255, 255, 0.40);
  text-align center
  margin 200px 0
  a
    margin 0 auto
    padding-right 28px
    background url('https://qncweb.ktvsky.com/20240103/other/7cc982caaff9791d87656fa3bbab7a92.svg') no-repeat right 4px
    background-size 28px 28px
.theme-themeLight
  .service-licence a
    background url('https://qncweb.ktvsky.com/20240305/vadd/a1be2103de31f225006acf5038ae031c.png') no-repeat right 4px
    background-size 28px 28px
</style>
