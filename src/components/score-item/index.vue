<template>
    <div class="song">
      <div class="song-item" :class="className">
        <div class="left">
            <div class="name">
              <span class="name-txt">{{ songItem.music_name }} </span>
            </div>
            <div class="desc">
              <template v-if="songItem.singer">
                <span
                  class="author"
                  :class="singerCanClick && 'clickable'"
                  @click="handleClickSinger"
                >{{ songItem.singer }}</span>
                <span class="divider">|</span>
              </template>
              <span class="album">{{ songItem.flag && songItem.flag.toString() }}</span>
              <img v-if="songItem.is_vip" class="song-block-vip" src="https://qncweb.ktvsky.com/20231206/vadd/56dc0bc6477bf2c7a6c4fcdc8360804e.png" alt="">
              <img v-if="false" class="song-block-score" src="https://qncweb.ktvsky.com/20240205/other/151412e04f9df9e6bbc1a781ddd6ab7d.png" />
              <span  class="sing-cnt" v-if="songItem.sing_cnt">演唱过 <span>{{ songItem.sing_cnt }}</span> 次</span>
            </div>
        </div>
        <div class="right">{{ songItem.level }} <span>{{ songItem.score }}</span></div>
      </div>
    </div>
</template>

<script>
import { computed, inject, toRefs, ref } from 'vue'
import { useStore } from 'vuex'
import { sendLog } from '@/directives/v-log/log'
import { useRouter, useRoute } from 'vue-router'
import split from 'lodash/split'

export default {
  name: 'SongItem',
  props: {
    className: {
      type: String,
      default: ''
    },
    songItem: Object,
    index: Number,
    startPosition: {
      type: Number,
      default: 0,
    },
    logFrom: {
      type:Object,
      default:() => {}
    },
    ponitActionLog: {
      type:Object,
      default:() => {}
    },
    singerEnable: { // mv页情况下，歌手名点击是否可以使用
      type: Boolean,
      default: true,
    },
    renderType: {
      default: 'list', // list:列表形态 block：块状形态
      type: String,
    },
    isStickButton: {
      default: false,
      type: Boolean,
    }
  },
  emits: ['singer-click'],
  setup(props, { emit }) {
    const router = useRouter()
    const route = useRoute()
    const { songItem, ponitActionLog, singerEnable } = toRefs(props)
    const store = useStore()
    const orderedSongIdMap = inject('orderedSongIdMap')
    const userInfo = computed(() => store.state.userInfo)
    const vipInfo = computed(() => store.state.vipInfo)
    const isOrdered = computed(() => {
      return orderedSongIdMap.value[songItem.value.songid]
    })
    const mvIsHide = computed(() => store.state.mvIsHide);
    const singerCanClick = computed(() => (mvIsHide.value && !route.query.singerid) || (!mvIsHide.value && singerEnable.value))

    const isLocalSong = ref(false)

    const handleClickSinger = (e) => {
      if (!singerCanClick.value) { return; }
      e.stopPropagation();
      sendLog({
        event_type: '10000~50000',
        event_name: 10108,
        event_data: {
          str1: '任意点歌页',
          str2: '歌曲列表',
          str3: '点击任意歌手',
          str4: 'click',
        },
      })
      if (mvIsHide.value) {
        router.push({
          name: 'songList',
          query: {
            name: split(songItem.value.singer, ',')[0],
            image: songItem.value.singer_head,
            singerid: songItem.value.singerid,
          },
        })
      } else {
        emit('singer-click', {
          singer: split(songItem.value.singer, ',')[0],
          singerhead: songItem.value.singer_head,
          singerid: songItem.value.singerid,
        })
        sendLog({
          event_type: '10000~50000',
          event_name: 10109,
          event_data: {
            str1: 'MV点歌页',
            str2: '歌曲列表',
            str3: '点击任意歌手',
            str4: 'click',
          },
        })
      }
    }
    
    return {
      isOrdered,
      userInfo,
      vipInfo,
      handleClickSinger,
      singerCanClick,
      isLocalSong,
    }
  },
}
</script>

<style lang="stylus" scoped>
.song
  height fit-content
  &-item
    display flex
    align-items center
    justify-content space-between
    height 157px
    color #ffffff
    border-bottom 1px solid rgba(255, 255, 255, 0.18)
    // border-radius 10px
    color rgba(255,255,255,0.7)
    .left
      flex 1
      display flex
      flex-direction column
    .name
      font-size 32px
      color rgba(255,255,255,0.7)
      margin-bottom 15px
      display flex
      align-items center
      &-txt
        display -webkit-box
        -webkit-box-orient vertical
        -webkit-line-clamp 2
        overflow hidden
        word-break break-all
        max-width 812px
        @media screen and (max-width 1200px)
          max-width 690px
          font-size 26px
    .desc
      display flex
      align-items center
      height 33px
      font-size 24px
      color rgba(255,255,255,0.4)
      @media screen and (max-width 1200px)
        font-size 20px
      .clickable
        position relative
        padding-right 26px
        &::after
          content ""
          position absolute
          right 0
          top 50%
          margin-top -10px
          width 22px
          height 22px
          background url('https://qncweb.ktvsky.com/20231212/vadd/70dbe52816b882ae1f6871b3a509f375.png') no-repeat
          background-size 100% 100%
      .divider
        margin -4px 16px 0 16px
      .sing-cnt
        margin-left 16px
        span
          color #DBAE6A
      .author
        max-width 320px
        overflow hidden
        white-space nowrap
        text-overflow ellipsis
        @media screen and (max-width 1200px)
          max-width 160px
    .ordered
      .name
        color rgba(219, 174, 106, 1) !important
      .desc
        color rgba(219, 174, 106, 1) !important
      .clickable::after
        content ""
        background-image url('https://qncweb.ktvsky.com/20231212/vadd/4a5a96b4a7e0b0ae7f364679f5f69417.png')
    .right
      span
        color rgba(255, 255, 255, 0.4)
</style>
