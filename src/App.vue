<template>
  <div class="app-main" :class="[themeClass]">
    <router-view v-show="mvIsHide" v-slot="{ Component }">
      <keep-alive>
        <component
          :key="$route.meta.key"
          v-if="$route.meta.keepAlive"
          :is="Component"
        />
      </keep-alive>
    </router-view>
    <router-view v-show="mvIsHide" v-if="!$route.meta.keepAlive" />
    <SearchBarTips />
    <BottomBar />
    <MvPageComponent v-show="!mvIsHide" />
    <VantStyle></VantStyle>
    <OrderSongControlPopup></OrderSongControlPopup>
    <!-- <LimitModal v-if="isShowLimitModal" @exit="handleExit" :limitParams="limitParams" /> -->
    <Offline :curRoute="$route" />
    <div id="preload-container"></div>
  </div>
</template>

<script>
import {
  onBeforeMount,
  watch,
  computed,
  provide,
  onMounted,
  onBeforeUnmount,
  ref,
  nextTick,
  defineAsyncComponent,
} from 'vue';
import { useStore } from 'vuex';
import { useRouter, useRoute } from 'vue-router';
import useM3u8 from '@/composables/useM3u8';
import useActivity from '@/composables/useActivity';
import useNats from '@/composables/useNats';
import {
  getOrderedList,
  getAlreadyList,
  getSearchCache,
  getSearchSong,
  setVipExpireTag,
  getVipExpireTag,
  setOrderedList,
} from '@/utils/historyCache';
import { Dialog } from 'vant';
import Toast from '@/utils/toast'
import { sendLog } from '@/directives/v-log/log';
import { setUid } from '@/utils/bl';
import { format, isWithinInterval, parseISO, addDays } from 'date-fns';
import formatStr from '@/constants/index';
import get from 'lodash/get';
import store2 from 'store2';
import { initNatsClient } from '@/packages/nats';
import useGlobalEvent from '@/composables/useGlobalEvent';
import useForceLogin from '@/composables/useForceLogin';
import { 
  TSMediaInstance,
  TSWebEventInstance,
  TSMicrophoneInstance,
  TSBaseInfoInstance,
  TSVehicleInstance,
  TSNativeInstance,
  TSVoiceInstance,
} from '@/packages/TSJsbridge';
import eventBus from '@/utils/event-bus'
import { checkConfig } from '@/service/base'
import { resolutionRatio } from '@/service/user'
import DownloadStatus from '@/components/download-status/index.vue'
import useLoading from '@/composables/useLoading';
import { getCarplayInfo } from '@/service/carplay-info'
import useQuality from '@/composables/useQuality'
import { isVcGreaterThanOrEqual } from '@/utils/device'
import useTimeUpdate from '@/composables/useTimeUpdate'
import MvPageComponent from '@/components/mv/index.vue'
import SearchBarTips from '@/components/search-bar/tips.vue'
import useOrder from '@/composables/useOrder';
import { getLicence } from '@/service/base';
import { cacheImagesAsBase64 } from '@/utils/cacheImages';
import { cacheImages } from '@/constants'

// const backgroundImages = {
//   vipLoginedBg: 'https://qncweb.ktvsky.com/20250102/other/fc4319604cda43efbd23db39838a2e74.png',
//   vipBg: 'https://qncweb.ktvsky.com/20241225/other/e9074e972a7118553a3fc903f4aa30cc.png',
// }

let Timer = null

export default {
  name: 'App',
  components: {
    OrderSongControlPopup: defineAsyncComponent(() => import('@/components/order-song-control-popup/index.vue')),
    MvPageComponent,
    SearchBarTips,
    BottomBar: defineAsyncComponent(() => import('@/components/bottom-bar/index.vue')),
    LimitModal: defineAsyncComponent(() => import('@/components/teleport/limit-toast/index.vue')),
    Offline: defineAsyncComponent(() => import('@/components/offline/index.vue')),
  },
  setup() {
    let appLoading = ref(false);
    const store = useStore();
    const router = useRouter();
    const route = useRoute();
    const { addCurrSongM3u8 } = useM3u8();
    const { showLoading, hideLoading } = useLoading();
    const {
      // showActivityModal,
      showActivityVip,
      showVipExpire,
      // showActivitySignIn,
    } = useActivity();
    const { showForceLogin } = useForceLogin();
    const { handleReceiveMessageFromNats } = useNats();
    const { installGlobalClickHandler, uninstallGlobalClickHandler } = useGlobalEvent()
    const { handleTimeupdate }  = useTimeUpdate()
    const { orderSong } = useOrder();

    const setting = computed(() => store.state.setting)
    const isLogin = computed(() => !!userInfo.value.unionid);
    const userInfo = computed(() => store.state.userInfo);
    const vipInfo = computed(() => store.state.vipInfo);
    const macAddress = computed(() => store.state.system.systemInfo.mac)
    const natsConfig = computed(() => store.state.base.nats);
    const icon = computed(() => store.state.base.icon);
    const launch_img = computed(() => store.state.base.launch_img);
    const net_status = computed(() => store.state.base.net_status);
    const needLoading = computed(() => store.state.base.needLoading);
    const videoPaused = computed(() => store.state.videoPaused);
    const appStatus = computed(() => store.state.base.appStatus)

    let checkIsWatchLoginSendVip = ref(false);

    const orderedSongIdMap = computed(() => store.state.orderedSongIdMap);
    provide('orderedSongIdMap', orderedSongIdMap);
    const curPlayingSongid = computed(
      () => store.state.videoPlayerHistory.songItem.songid
    );
    provide('playingSongId', curPlayingSongid);

    const videoPlayer = computed(() => store.state.videoPlayerHistory);
    const mvIsHide = computed(() => store.state.mvIsHide);
    const currentTask = computed(() => store.state.download.currentTask);
    const themeClass = computed(() => store.state.themeClass)
    const orderedList = computed(() => store.state.orderedList)

    const resolution = computed(() => store.state.carplayInfo.resolution)
    const isVip = computed(() => !!store.state.vipInfo.end_time)
    const isYSTipAccept = computed(() => store.state.storageYSTipAccept)
    const isExpired = computed(() => store.state.vipInfo.expire)
    let appStartTime = Date.now();
    let isShowLimitModal = ref(false)
    let limitParams = ref({})
    const natsInstance = ref(null)

    const loading = ref(false)

    const initApp = async () => {
      TSWebEventInstance.on('handleTimeupdate', handleTimeupdate);
      await store.dispatch('system/getSystemInfo');

      store.dispatch('getSetting');

      store.dispatch('base/getBaseInfo');
      store.dispatch('base/getConfig');
      store.dispatch('abTest/setABTestVersion')
      store.dispatch('base/reportSession')
      // 初始化特斯拉top50常唱数据
      store.dispatch('oftenSing/initTop50List')

      // 登录时更新云端画质到本地(非vip云端画质还为1080时，降级为720)
      let qualityNew = !isVip.value && resolution.value === '1080' ? '720' : resolution.value
      if (!qualityNew) qualityNew = '480' // 云端检测是否是新用户未设置过画质，新用户设置480
      store.commit('CHANGE_PLAYING_MV_QUALITY', qualityNew)
      store.dispatch('saveSetting', {
        ...setting.value,
        quality: qualityNew
      })
      if (qualityNew !== resolution.value  && userInfo.value.unionid) {
        resolutionRatio({ unionid: userInfo.value.unionid, quality: qualityNew})
      }

      // 启动上报
      sendLog({
        event_type: 'custom',
        event_name: 241,
        event_data: {
          _car_language: navigator.language,
          _car_time: Date.now(),
          memory_max: get(performance, 'memory.jsHeapSizeLimit', 0),
          memory_used: get(performance, 'memory.usedJSHeapSize', 0),
        },
      });
      // app打开上报
      sendLog({
        event_type: 'custom',
        event_name: 221,
        event_data: {
          start_time: format(appStartTime, formatStr),
        },
      });
      appLoading.value = true;
      store.commit('UPDATE_APP_START_TIME', appStartTime);
      // 查看运营活动
      store.dispatch('setActivityInfo');
      setTimeout(() => {
        console.log('拉取一次是否麦克风连接')
        TSMicrophoneInstance.hasThunderMic()
      }, 3000)

      setTimeout(() => {
        const skeleton = document.getElementById('Skeleton');
        if (skeleton) {
          skeleton.parentNode.removeChild(skeleton);
        }
      }, 5000)

      const { data = {} } = await getLicence();
      store.commit('SAVE_SERVICE_INFO', data);
    };

    const onUnload = () => {
      if (appStartTime) {
        const unloadTime = Date.now();
        const stayTime = Math.round((unloadTime - appStartTime) / 1000);
        sendLog({
          event_type: 'custom',
          event_name: 222,
          event_data: {
            start_time: format(appStartTime, formatStr),
            end_time: format(unloadTime, formatStr),
            stay_time: stayTime,
          },
        });
      }
    };

    // 监听网络状态
    const onNetChange = (status) => {
      // console.log('onNetChange', status)
      // Toast('当前网络状态差，请检查网络状态');
      // store.commit('base/SET_NET_STATUS', status.type === 'online');
    };

    const showExpireModal = () => {
      // 过期会员提醒 - 检测到登录用户会员过期时弹运营弹框 - 每天只弹一次
      if (vipInfo.value.expire && getVipExpireTag()) {
        setVipExpireTag()
        showVipExpire({
          closeEvent: () => {
            store.dispatch('getCarplayInfo')
          },
          onCancel: () => {
            store.dispatch('getCarplayInfo')
          },
        })
        sendLog({
          event_type: 'show',
          event_name: 1752,
        })
      }
    }

    watch(isYSTipAccept, (val) => {
      if (val) {
        showExpireModal()
      }
    })

    // 添加themeClass监听
    watch(themeClass, (newVal, oldVal) => {
      console.log('themeClass changed:', oldVal, '->', newVal)
    })

    watch(isLogin, async (val) => {
      if (val) {
        store.dispatch('download/checkAutoDownload', !!vipInfo.value.end_time)
      } else {
        if (currentTask.value.songid && currentTask.value.is_vip) {
          await store.dispatch('download/stopDownload')
          store.dispatch('download/checkAutoDownload', !!vipInfo.value.end_time)
        }
      }
      
      if (!val) {
        // const quality = store.state.availableQualities
        const playingMvQuality = store.state.playingMvQuality
        const availableQualities = store.state.availableQualities
        let finalQuality = ''
        
        if (playingMvQuality === '1080') {
          // 找到 '1080' 的索引
          const qualityKeys = Object.keys(availableQualities);
          const currentIndex = qualityKeys.findIndex(key => availableQualities[key] === playingMvQuality);

          // 如果当前质量不是最低质量，降级到下一个低质量
          if (currentIndex > 0) {
            finalQuality = availableQualities[qualityKeys[currentIndex - 1]];
          } else {
            // 如果已经是最低质量，保持当前质量
            finalQuality = availableQualities[qualityKeys[currentIndex]];
          }

          store.commit('CHANGE_PLAYING_MV_QUALITY', finalQuality);
          console.log('getDefaultQualityM3U8', finalQuality)
          store.dispatch('saveSetting', {
            ...setting.value,
            finalQuality,
          })
          TSMediaInstance.setResolution(finalQuality)
        }
        // TSMediaInstance.setResolution(quality)
        // store.dispatch('saveSetting', {
        //   ...setting.value,
        //   quality,
        // })

        Dialog.confirm({
          className: 'global-force-login',
          confirmButtonText: '我知道了',
          showCancelButton: false,
          title: '监测到您的账号在当前车机已下线',
          beforeClose: () => {
            showForceLogin()
            return true
          },
        }).then(() => {
          router.push({
            name: 'home',
          });
        });
        setUid();
        // 退出登录时重置常唱数据
        store.dispatch('oftenSing/resetOftenSingList')
        TSBaseInfoInstance.notifyLoginInfoStatus(null)
      } else {
        sendLog({
          event_type: '10000~50000',
          event_name: 6002,
          event_data: {
            str1: '通用',
            str2: '顶部运营',
            str3: '点击',
            str4: 'show',
            str5: 1,
            str6:isVip.value ? 2 : isExpired.value ? 4 : 1
          }
        })

        if (isYSTipAccept.value) {
          showExpireModal()
        }

        // 登录时更新常唱数据
        store.dispatch('oftenSing/initOftenSingList', userInfo.value.unionid)
        TSBaseInfoInstance.notifyLoginInfoStatus({
          unionid: userInfo.value.unionid
        })
      }

      // 检测到已登录时正常开启活动领取状态检测
      setTimeout(() => {
        checkIsWatchLoginSendVip.value = val;
      }, 500);
    });

    // 已登录用户初始uid赋值
    watch(
      userInfo,
      (val) => {
        if (val.unionid) {
          setUid(val.unionid);
        }
      },
      {
        deep: true,
        immediate: true,
      }
    );

   const handleNatsConfigChange = async (val) => {
      if (val) {
        const { eip } = val;
        if (eip && macAddress.value) {
          natsInstance.value = await initNatsClient({ ...val, mac: macAddress.value }, handleReceiveMessageFromNats);
          console.log('handleNatsConfigChange', natsInstance.value)
        }
      }
    };

    // 监听natsConfig的变化
    watch(
      natsConfig,
      handleNatsConfigChange,
      {
        deep: true,
        immediate: true,
      }
    );

    // 监听net_status的变化
    watch(
      net_status,
      async (val) => {
        loading.value = false
        if (val) {
          handleNatsConfigChange(natsConfig.value);

          try {
            if (!videoPlayer.value.songItemLrc?.length && videoPlayer.value.songItem.lrc != 0) {
              await addCurrSongM3u8();
            }
          } catch (error) {
            console.log('app.vue net_status',error);
          }
        }
      },
      {
        deep: true,
      }
    );

    watch(
      () => orderedList.value[0]?.songid,
      async (newSongId, oldSongId) => {
        if (newSongId !== oldSongId) {
          console.log('🎵 已点列表首曲变化', { 
            oldSongId, 
            newSongId,
            orderedList: orderedList.value 
          });

          // 如果已点列表第一个的 songid 有更新，才执行 addCurrSongM3u8
          console.log('⏳ 开始获取当前歌曲的可用画质...');
          const availableQualities = await addCurrSongM3u8({
            availableQualities: orderedList.value[0].availableQualities
          });
          console.log('✅ 获取到可用画质', availableQualities);

          console.log('⏳ 提交更新所有已点列表项的画质信息', {
            songid: newSongId,
            availableQualities
          });
          await store.commit('UPDATE_ALL_ORDERED_LIST_ITEMS_BY_SONGID', {
            songid: newSongId,
            availableQualities: availableQualities
          });
          console.log('✅ 已更新store中的画质信息', orderedList.value);

          console.log('⏳ 持久化存储已点列表');
          setOrderedList(orderedList.value);
          console.log('💾 已点列表存储完成', orderedList.value);
        }
      }
    );

    watch(mvIsHide, (v) => {
      console.log('watch: mvIsHide changed to', v); // 新增的console信息

      // 进入过mv页面后默认初始化自动播放
      if (!v) {
        console.log('watch: mvIsHide is false, initializing auto play if needed'); // 新增的console信息
        if (!store.state.videoInitAutoPlay) {
          store.commit('UPDATE_MV_INIT_AUTOPLAY', 1);
          console.log('watch: Updated MV_INIT_AUTOPLAY to 1'); // 新增的console信息
        }
        console.log('watch: Notifying web page change from mv to', route.name); // 新增的console信息
        TSWebEventInstance.onWebPageChanged('mv', route.name)
      } else {
        console.log('watch: mvIsHide is true, delaying web page change notification'); // 新增的console信息
        console.log('watch: Notifying web page change from', route.name, 'to mv'); // 新增的console信息
        TSWebEventInstance.onWebPageChanged(route.name, 'mv')
      }

      console.log('watch: Hiding loading'); // 新增的console信息
      hideLoading();

      const hasVideo = !TSMediaInstance.getIsPlayingFinish();
      console.log('hasVideo 0416', hasVideo)
      store.commit('UPDATE_HAS_VIDEO', hasVideo)
    });

    watch(icon, (val) => {
      const icons = get(val, 'schedule', [])
      console.log('ICON DATA:', icons)
      let logoData = icons.length ? icons[0] : { id: 0 }
      if (icons.length) {
        const currentDate = new Date()
        icons.find(v => {
          if (v.id == 0) return false
          if (isWithinInterval(currentDate, { start: parseISO(v.start, 'yyyyMMdd'), end: addDays(parseISO(v.end, 'yyyyMMdd'), 1)})) {
            logoData = v
            return true
          }
            return false

        })
      }
      setTimeout(() => {
        TSBaseInfoInstance.notifyAppLogo(logoData)
        TSBaseInfoInstance.notifyAppLauncherImg(launch_img.value)
      }, 3000)
    }, {
      deep: true
    })

    watch(isVip, async (val) => {
      const isPlaying = TSMediaInstance.getIsPlaying()
      const songItem = videoPlayer.value.songItem
      if (!val && isPlaying && songItem.is_vip) {
        const isLocal = await store.dispatch('download/getIsLocalSong', songItem)
        if (!isLocal) {
          console.log('watch isVip', '视频停止播放')
          eventBus.emit('video-control-stop')
          store.commit('UPDATE_MV_INIT_AUTOPLAY', 0);
          store.dispatch('download/stopDownload')
        }
      }
    })

    watch(needLoading, async (val) => {
      if (val) {
        await nextTick();
        const mvSide = document.querySelector('.mv-side');
        const mvUnlogin = document.querySelector('.mv-user-vip-unlogin')
        if (mvUnlogin) {
          return
        }
        if (!mvIsHide.value && mvSide) {
          const pos = mvSide.dataset.pos;
          if (pos === '0') return
          showLoading(pos === '1' ? 'right' : 'left');
        } else {
          showLoading();
        }
      } else {
        hideLoading();
      }
    });

    onBeforeMount(initApp);

    // 强制登录
    const forceLoginLogic = async () => {
      sendLog({
        event_type: '10000~50000',
        event_name: 6012,
        event_data: {
          str1: '通用',
          str2: '过期会员弹窗',
          str3: '过期会员弹窗展示',
          str4: 'show',
          str5: isLogin.value ? 2 : 1
        },
      })
      
      await store.dispatch('getCarplayInfo')
      setTimeout(() => {
        showForceLogin()
      }, 600)
    }

    const openLimitModal = (params) => {
      isShowLimitModal.value = true
      limitParams.value = params
    }

    const handleExit = () => {
      TSNativeInstance.exit()
    }

    const onMicphoneNotification = (type, action) => {
      console.log('onMicphoneNotification', type, action)
      TSMicrophoneInstance.handleMicphoneNotification({ type, action })
    }

    const onMicphoneDongleEvent = (type, arg1, arg2) => {
      console.log('onMicphoneDongleEvent', type, arg1, arg2)
      TSMicrophoneInstance.handleMicphoneDongleEvent({ type, arg1, arg2 })
    }

    const onAudioEffectParams = async (type,json) => {
      console.log('onAudioEffectParams', json)
      TSMicrophoneInstance.handleAudioEffectParams(type,json)
    }

    const onCarSpeed = (number) => {
      console.log('onCarSpeed', number)
      store.dispatch('carMonitor/setCarSpeed', number)
    }

    // 1 P  2 R 3 N  4 D
    const onCarGear = (number) => {
      console.log('onCarGear', number)
      store.dispatch('carMonitor/setCarGear', number)
    }

    // 1 playpause  2 play 3 Pause 4 next 5 replay
    const onCarKey = (key) => {
      try {
        console.log('onCarKey', key)
        store.dispatch('carMonitor/setCarKey', key)

        if (mvIsHide.value) return
        if (['playPause', 'play', 'pause'].includes(key)) {
          eventBus.emit('show-plugins', true)
        }
      } catch (error) {
        console.log('onCarKey error', error)
      }
    }

    const onChangeRoute = () => {
      let name = 'home'
      // if (['home', 'search'].includes(val)) name = val
      router.push({
        name,
      })
    }

    const onMediaVolume = (val) => {
      const maxMediaVolume = store2.get('maxMediaVolume')
      store.commit('UPDATE_MV_VIDEO_MEDIA', Math.floor(parseInt(val) / maxMediaVolume * 100));
      console.log('onMediaVolume', val, maxMediaVolume, Math.floor(parseInt(val) / maxMediaVolume * 100))
    }

    const onCheckInput = async (status) => {
      await nextTick()
      const inputElements = document.querySelectorAll('input')
      if (inputElements) {
        console.log('onCheckInput', inputElements)
        setTimeout(() => {
          inputElements.forEach(inputElement => {
            inputElement.blur()
          })
        }, 0)
      }
    }
    
    // 公版默认配置 {}
    const onDefaultCarModels = () => {
      let m = {
      }
      return JSON.stringify(m)
    }

    const onUiModeChanged = async (type) => {
      console.log('onUiModeChanged', type)
      const theme = type == 1 ? 'themeDark' : 'themeLight'
      // 切换主题
      await store.commit('SET_THEME', theme)
      await nextTick()
      const htmlElement = document.querySelector('html');
      // 先移除所有主题类
      htmlElement.classList.remove(...Array.from(htmlElement.classList).filter(cls => cls.startsWith('theme-')));
      // 添加新的主题类
      htmlElement.classList.add(`theme-${theme}`);
      console.log('onUiModeChanged after', type)
    }

    const onIsScreen = () => {
    }

    const onNetworkChanged = (val) => {
      // 版本限制
      if (isVcGreaterThanOrEqual(100030)) {
        store.commit('base/SET_NET_STATUS', val == 1);
        console.log('onNetworkChanged', val == 1 ? '有网络' : '无网络')
      }
      
      if (val == 1) {
        // 有网络
        store.dispatch('download/checkAutoDownload', !!vipInfo.value.end_time)
      } else {
        // 无网络
        store.dispatch('download/stopDownload')
      }
    }

    // local server change - 预留
    const onCommServerFound = () => {

    }

    const onSystemKeyWords = async (data) => {
      console.log('onSystemKeyWords', data);
      try {
        const info = JSON.parse(data);
        const params = {
          vin: info.vin || '',
          mac_id: info.mac_id || '',
          androidid: info.androidid || '',
          sys_func: info.sys_func || '',
          car_sdk: info.car_sdk || '',
          car_name: info.car_name || '',
        };

        let bol = true;

        // 当mac_id为020000000000时，不调用checkConfig方法，并且bol为false
        if (params.mac_id === '020000000000') {
          bol = false;
        } else {
          const res = await checkConfig(params);
          if (res && res.data && res.data.id) {
            if (res.data.id.charAt(6) === '1') {
              bol = false;
            }
          } else {
            bol = false;
          }
        }

        if (bol) {
          openLimitModal(params);
        }
      } catch (error) {
        console.log('onSystemKeyWords error', error);
      }
    };

    const onShowToast = (val) => {
      val && Toast(val);
    }

    const onSystemMute = (val) => {
      console.log('onSystemMute', val, val == 1)
      store.commit('UPDATE_SYSTEM_MUTED', val == 1)
    }

    const onDeleteFile = (songId) => {
      console.log('onDeleteFile', songId)
      store.commit('UPDATE_SONG_DOWNLOAD_STATE', {
        songid: songId,
        downloadProgress: 0,
        downloadState: DownloadStatus.PROCESSING,
        src: '',
        isDeleted: true,
      });
    }

    const fetchCarplayInfo = () => {
      store.dispatch('getCarplayInfo');
    }

    const onBackPressed = () => {
      try {
        console.log('onBackPressed', 'mvIsHide.value:', mvIsHide.value);
        const controlPopup = document.querySelector('.control-popup-plugin')
        const thunderModal = document.querySelector('.thunder-modal-container')
        const dialog = document.querySelector('.van-dialog')
        const mvVue = document.querySelector('#mvVue')
        const isMvHidden = mvVue ? mvVue.style.display === 'none' : true
        console.log('onBackPressed', 'isMvHidden:', isMvHidden, 'mvIsHide:', mvIsHide.value, 'route:', route.name, 'controlPopup:', !!controlPopup, 'thunderModal:', !!thunderModal, 'dialog:', !!dialog);
        
        if (dialog && dialog.style.display !== 'none'){
          Dialog.close()
          console.log('onBackPressed dialog');
          return true
        }

        if (controlPopup && controlPopup.style.display !== 'none') {
          eventBus.emit('close-order-song-control-popup')
          console.log('close-order-song-control-popup');
          return true
        }

        if (thunderModal && thunderModal.style.display !== 'none') {
          eventBus.emit('close-thunder-modal')
          console.log('close-thunder-modal');
          return true
        }

        if (!isMvHidden || !mvIsHide.value) {
          store.commit('UPDATE_MV_ISHIDE', true)
          return true
        }
        console.log('onBackPressed back before');
        if (route.name === 'home' && isMvHidden) {
          TSNativeInstance.exit()
          return true
        }
        console.log('onBackPressed back after');
        router.back()
        store.commit('base/SET_IS_ROUTER_BACK', true)
        return true
      } catch (error) {
        console.log('onBackPressed error', error);
      }
    }


    const onAppStatus = (status)=>{
      console.log('onAppStatus 入口 status:', status, '类型:', typeof status); // 新增入口日志
      if (typeof status !== 'number') {
        console.log('onAppStatus 收到非数字状态:', status);
        return;
      }

      console.log(' 提交应用状态变更:', !(status == 1 || status == 3), '原始状态码:', status);
      store.commit('base/SET_APP_STATUS', !(status == 1 || status == 3))
      console.log('触发播放状态检查');
      onCheckPlayStatus(status)
      console.log('触发输入检查');
      onCheckInput(status)

      nextTick(() => {
        if (status == 0 || status == 2) {
          console.log('onAppStatus 0411:', status, '当前MV可见状态:', !mvIsHide.value, '当前页面:', store2.get('currentPage'));
          const mvIsVisible = !mvIsHide.value
          if (mvIsVisible && store2.get('currentPage') != 'mv') {
            console.log('满足隐藏条件，隐藏MV页面 - mvIsVisible:', mvIsVisible, 'currentPage:', store2.get('currentPage'));
            store.commit('UPDATE_MV_ISHIDE', true)
            console.log('MV隐藏状态已提交，当前mvIsHide:', true);
          }
        }
      })
    }
    
    const onCheckPlayStatus = async (status) => {
      console.log('onCheckPlayStatus 入口 status:', status, '类型:', typeof status);
      if (status === 0 || status === 2) {
        console.log('处理非活跃状态:', status, '车型:', store.state.system.car_name, 'nextTickCheck:', nextTickCheck);
        
        // // V31新增判断
        // if (nextTickCheck == 1 && store.state.system.car_name === '192V35') {
        //   console.log('B07 车型 nextTickCheck 异常，触发错误处理');
        //   handleNextTickError()
        //   return
        // }

        console.log('清理 exitTimer', exitTimer ? '存在定时器' : '无定时器');
        exitTimer && clearTimeout(exitTimer);
        exitTimer = null;
        nextTickCheck = 0;

        const updateStatus = () => {
          console.log('开始更新播放状态...');
          const isPlaying = TSMediaInstance.getIsPlaying()
          const hasVideo = !TSMediaInstance.getIsPlayingFinish();
          console.log('播放状态 - 正在播放:', isPlaying, '存在视频:', hasVideo);

          console.log('提交视频暂停状态:', !isPlaying);
          store.dispatch('setVideoPaused', !isPlaying);
          console.log('更新MV自动播放状态:', hasVideo ? 1 : 0);
          store.commit('UPDATE_MV_INIT_AUTOPLAY', hasVideo ? 1 : 0);
          console.log('更新演唱状态:', hasVideo);
          store.commit('UPDATE_IS_SING_STATUS', hasVideo);
          console.log('状态更新完成');
        };

        console.log('启动延迟状态更新');
        setTimeout(async () => {
          console.log('执行 nextTick');
          await nextTick();
          updateStatus();
        }, 1000);
      } else {
        console.log('应用处于活跃状态:', status);
      }
    }

    let mTimeCount = 0;
    let nextTickCheck = 0;

    const handleNextTickError = async () => {
      console.log('处理 nextTick 错误 - 应用状态:', appStatus.value, '网络状态:', net_status.value);
      if (appStatus.value) {
        console.log('尝试重新加载应用...');
        if (!mvIsHide.value) {
          console.log('发送页面变更通知:', route.name, '-> mv');
          await store.commit('UPDATE_MV_ISHIDE', true)
          TSWebEventInstance.onWebPageChanged(route.name, 'mv');
        }
        if (net_status.value) {
          const hasControlPopup = !!document.querySelector('.control-popup-plugin')
          console.log('网络可用，检测到控制弹窗:', hasControlPopup);
          if (hasControlPopup) {
            console.log('设置 webvieActiveYidian 标记');
            localStorage.setItem('webvieActiveYidian', true)
          }
          console.log('强制刷新页面');
          window.location.reload();
        } else {
          console.warn('无网络连接，显示提示对话框');
          TSNativeInstance.showDialog('当前无网络连接，请连接网络后重试')
        }
      } else if (store.state.system.car_name !== '192V35') {// 过滤B07-1,比较容易触发
        console.log('非 B07-1 车型，执行退出');
        TSNativeInstance.exit();
      }
    };

    let exitTimer = null
    const onWebViewActive = async () => {
      console.log('onWebViewActive 入口 mTimeCount:', mTimeCount, '应用状态:', appStatus.value);
      
      if (appStatus.value) {
        console.log('应用活跃，重置计数器');
        mTimeCount = 0
      } else {
        mTimeCount++
        console.log('计数器递增:', mTimeCount);
        if (mTimeCount >= 30) {
          console.log('计数器超过阈值，启动退出流程');
          mTimeCount = 0
          if (exitTimer){
            console.log('清除现有退出定时器');
            clearTimeout(exitTimer)
          }
          exitTimer = setTimeout(() => {
            console.log('定时器触发，执行退出');
            TSNativeInstance.exit()
          }, 1000)
          return true
        }
      }

      if (nextTickCheck === 1) {
        console.log('onWebViewActive nextTick error');
        handleNextTickError();
        return true;
      }
      console.log('onWebViewActive nextTick start');
      nextTickCheck = 1;
      await nextTick();
      nextTickCheck = 0;
      console.log('onWebViewActive nextTick end');
      return true;
    };

    const checkIsEnterMv = () => {
      const hasVideo = !TSMediaInstance.getIsPlayingFinish();
      if (!orderedList.value.length) {
        Toast('暂无更多已点歌曲')
        TSVoiceInstance.sendTtsMessage(-1, 1001, '暂无更多已点歌曲');
        return true;
      }
      if (!hasVideo) {
        Toast('尚未进入欢唱页K歌，暂无法执行该操作')
        TSVoiceInstance.sendTtsMessage(-1, 1001, '尚未进入欢唱页K歌，暂无法执行该操作');
        return true;
      }
      console.log('checkIsEnterMv', TSBaseInfoInstance.requestAudioFocus())
      return !TSBaseInfoInstance.requestAudioFocus()
    };

    // // 检查已点歌曲数量
    // const checkOrderedList = () => {
    //   if (!orderedList.value.length) {
    //     Toast('暂无更多已点歌曲')
    //     TSVoiceInstance.sendTtsMessage(0, 1001, '暂无更多已点歌曲');
    //     return true;
    //   }
    //   return false;
    // };

    const COMMANDS = {
      MEDIA_PLAY_FROM_SEARCH: 'CONTROLLER_COMMAND_MEDIA_PLAY_FROM_SEARCH',
      MEDIA_KEY_EVENT: 'CONTROLLER_COMMAND_MEDIA_KEY_EVENT',
      MEDIA_PLAY_PAUSE: 'CONTROLLER_COMMAND_MEDIA_PLAY_PAUSE',
      MEDIA_PLAY: 'CONTROLLER_COMMAND_MEDIA_PLAY',
      MEDIA_PLAY_AUTO: 'CONTROLLER_COMMAND_MEDIA_PLAY_AUTO',
      MEDIA_PAUSE: 'CONTROLLER_COMMAND_MEDIA_PAUSE',
      MEDIA_SKIP_TO_NEXT: 'CONTROLLER_COMMAND_MEDIA_SKIP_TO_NEXT',
      MEDIA_SKIP_TO_PREVIOUS: 'CONTROLLER_COMMAND_MEDIA_SKIP_TO_PREVIOUS',
      MEDIA_CUSTOM: 'CONTROLLER_COMMAND_MEDIA_CUSTOM',
      MEDIA_REPLAY: 'CONTROLLER_COMMAND_MEDIA_REPLAY', // 新增重唱命令
    };

    const createMediaCommands = () => {
      const hasVideo = !TSMediaInstance.getIsPlayingFinish();

      const mediaCommands = {
        [COMMANDS.MEDIA_PLAY_FROM_SEARCH]: async (payload) => {
          console.log(`Search for song: ${payload}`);
          try {
            const { song, singer } = JSON.parse(payload);
            const keyword = (song || '') + (singer || '');

            console.log('Song:', song);
            console.log('Singer:', singer);
            if (!song && !singer) {
              return;
            }
            const routeName = route.name;
            const query = { keyword, from: 'voiceControl' };

            await store.commit('UPDATE_MV_ISHIDE', true);

            if (routeName === 'search' && route.query.keyword === keyword) {
              eventBus.emit('voiceSearchSame')
              return
            }

            router[routeName === 'search' ? 'replace' : 'push']({ name: 'search', query });

          } catch (error) {
            console.error('Error processing payload:', error);
          }
        },
        [COMMANDS.MEDIA_PLAY]: async () => {
          console.log('Play media', '是否有资源在播放：', hasVideo);
          if (checkIsEnterMv()) return;
          
          if (!videoPaused.value) {
            console.log('Play media 已经正在播放歌曲了');
            // await store.commit('UPDATE_MV_ISHIDE', false);
            return;
          }
          hasVideo ? eventBus.emit('video-control-resume', 'command') : orderSong(orderedList.value[0], 0);

          if (mvIsHide.value) return
          eventBus.emit('show-plugins', true)
        },
        [COMMANDS.MEDIA_PLAY_AUTO]: async () => {
          mediaCommands[COMMANDS.MEDIA_PLAY]();
        },
        [COMMANDS.MEDIA_PAUSE]: () => {
          if (checkIsEnterMv()) return;
          
          console.log('Pause media');
          eventBus.emit('video-control-pause');
          if (mvIsHide.value) return
          eventBus.emit('show-plugins', true)
        },
        [COMMANDS.MEDIA_SKIP_TO_NEXT]: () => {
          if (!orderedList.value.length) {
            Toast('暂无更多已点歌曲')
            return
          }
          if (checkIsEnterMv()) return;
          
          console.log('Next media');
          eventBus.emit('video-control-next');
        },
        [COMMANDS.MEDIA_SKIP_TO_PREVIOUS]: () => {
          if (checkIsEnterMv()) return;
          
          console.log('Replay media');
          eventBus.emit('video-control-replay');
        },
        [COMMANDS.MEDIA_REPLAY]: () => { // 新增重唱命令的处理
          if (checkIsEnterMv()) return;
          
          console.log('Replay media');
          eventBus.emit('video-control-replay');
        },
        [COMMANDS.MEDIA_PLAY_PAUSE]: async () => {
          console.log('Toggle play/pause media');
          if (checkIsEnterMv()) return;
          

          if (hasVideo) {
            videoPaused.value ? eventBus.emit('video-control-resume', 'command') : eventBus.emit('video-control-pause', 'command');
          } else {
            mediaCommands[COMMANDS.MEDIA_PLAY](); // 这里可以直接调用
          }

          if (mvIsHide.value) return
          eventBus.emit('show-plugins', true)
        },
        [COMMANDS.MEDIA_CUSTOM]: (payload) => {
          if (checkIsEnterMv()) return;
          console.log('Toggle org/acc media', payload);
          const { mode, value } = JSON.parse(payload);

          if (mode === 'original') {
            const targetTrackId = value === 'off' ? 2 : 1;
            
            console.log(value, videoPlayer.value)
            if (!videoPlayer.value.supportsOrg) {
              TSVoiceInstance.sendTtsMessage(-1, 1001, '抱歉，当前缺少原唱资源');
              return
            }
            
            if (targetTrackId === videoPlayer.value.enabledAudioTrack.id) {
              TSVoiceInstance.sendTtsMessage(-1, 990);
              return
            }

            eventBus.emit('handle-video-toggle-track', {
              id: targetTrackId,
              from: 'voice',
            });
          }
        }
      };

      return mediaCommands; // 返回 mediaCommands
    };
    const handleSoft = (val) => {
      if (!val) {
        console.log('handleSoft', val, '搜索页设置失去焦点')
        eventBus.emit('inputOutFocus');
        return;
      }
    }
    const handleVoice = (command, payload) => {
      try {
        console.log('handleVoice', command, payload)
        if (command !== COMMANDS.MEDIA_PLAY_FROM_SEARCH
            && command !== COMMANDS.MEDIA_KEY_EVENT
            && command !== COMMANDS.MEDIA_PLAY_PAUSE
            && command !== COMMANDS.MEDIA_PLAY
            && command !== COMMANDS.MEDIA_PLAY_AUTO
            && command !== COMMANDS.MEDIA_PAUSE
            && command !== COMMANDS.MEDIA_SKIP_TO_NEXT
            && command !== COMMANDS.MEDIA_SKIP_TO_PREVIOUS
            && command !== COMMANDS.MEDIA_CUSTOM
            && command !== COMMANDS.MEDIA_REPLAY) {
          TSVoiceInstance.sendTtsMessage(0, '', '');
          return;
        }
        if (!isYSTipAccept.value) {
          if (command !== COMMANDS.MEDIA_KEY_EVENT) {
            TSVoiceInstance.sendTtsMessage(-1, 1001, '需要先通过隐私协议后才可以哦');
          }
          return
        }

        // 方控
        if (command === COMMANDS.MEDIA_KEY_EVENT) {
          handleMediaKeyEvent(payload);
          return; // 结束函数
        }

        if (command !== COMMANDS.MEDIA_PLAY_FROM_SEARCH) {
          if (!appStatus.value) {
            if (command === COMMANDS.MEDIA_PLAY_AUTO) {
              TSVoiceInstance.sendTtsMessage(-1, 1001, '语音还不支持该操作哦，试试手动操作吧。');
            } else {
              TSVoiceInstance.sendTtsMessage(0, '', '');
            }
            return;
          }
          if (checkIsEnterMv()) return;

          // 如果命令是切换原伴唱的不执行
          if (command !== COMMANDS.MEDIA_CUSTOM) {
            if (command === COMMANDS.MEDIA_SKIP_TO_NEXT && orderedList.value.length == 1) {
              TSVoiceInstance.sendTtsMessage(-1, 1001, '没有下一个资源');
            } else {
              TSVoiceInstance.sendTtsMessage(-1, 1001, '好的');
            }
          }
        }

        const mediaCommands = createMediaCommands(); // 创建 mediaCommands

        if (mediaCommands[command]) {
          mediaCommands[command](payload);
        }
      } catch (error) {
        console.log('handleVoice error:', error);
      }
    };

    const handleMediaKeyEvent = (payload) => {
      console.log(COMMANDS.MEDIA_KEY_EVENT, payload);
      if (checkIsEnterMv()) {
        return
      }
      const params = payload;

      if (['85', '126', '127', '86', '87', '88'].includes(params)) {
        TSVoiceInstance.sendTtsMessage(0, '', '');
        const mediaCommands = createMediaCommands();

        switch (params) {
          case '85': mediaCommands[COMMANDS.MEDIA_PLAY_PAUSE](); break; // 播放/暂停
          case '126': mediaCommands[COMMANDS.MEDIA_PLAY](); break; // 播放
          case '127':
          case '86': mediaCommands[COMMANDS.MEDIA_PAUSE](); break; // 暂停
          case '87': mediaCommands[COMMANDS.MEDIA_SKIP_TO_NEXT](); break; // 下一首
          case '88': mediaCommands[COMMANDS.MEDIA_SKIP_TO_PREVIOUS](); break; // 重唱
        }
      }
    };

    const attachEvents = () => {
      eventBus.on('getCarplayInfo', fetchCarplayInfo)

      TSWebEventInstance.on('handleMicphoneNotification', onMicphoneNotification)
      TSWebEventInstance.on('handleMicphoneDongleEvent', onMicphoneDongleEvent)
      TSWebEventInstance.on('handleAudioEffectParams', onAudioEffectParams)
      TSWebEventInstance.on('handleSpeed', onCarSpeed)
      TSWebEventInstance.on('handleGear', onCarGear)
      TSWebEventInstance.on('handleKey', onCarKey)
      TSWebEventInstance.on('handleChangeRoute', onChangeRoute)
      TSWebEventInstance.on('handleMediaVolume', onMediaVolume)
      TSWebEventInstance.on('handleDisplaysVisibilityChanged', onIsScreen)
      TSWebEventInstance.on('handleCommServerFound', onCommServerFound)
      TSWebEventInstance.on('handleDefaultCarModels', onDefaultCarModels)
      TSWebEventInstance.on('handleNetworkChanged', onNetworkChanged)
      TSWebEventInstance.on('handleSystemKeyWords', onSystemKeyWords)
      TSWebEventInstance.on('handleUiModeChanged', onUiModeChanged)
      TSWebEventInstance.on('handleShowToast', onShowToast)
      TSWebEventInstance.on('handleSystemMute', onSystemMute)
      TSWebEventInstance.on('handleAppStatus',onAppStatus)
      TSWebEventInstance.on('handleDeleteFile',onDeleteFile) // 超过4G自动触发删除
      TSWebEventInstance.on('handleBackPressed', onBackPressed);
      TSWebEventInstance.on('handleWebViewActive', onWebViewActive);
      TSWebEventInstance.on('handleVoice', handleVoice);
      TSWebEventInstance.on('handleSoft', handleSoft);

      // 全局异常捕获器
      window.onerror = function(message, source, lineno, colno, error) {
        console.error('javaScript onerror: ' + message + ' at ' + source + ':' + lineno + ':' + colno + ':' + error);
        return true;
      };

      // 捕获全局未处理的 Promise 拒绝
      window.addEventListener('unhandledrejection', function (event) {
        console.error('javaScript onerror promise rejection:', event.reason);
        if (event.reason.type === 'timeout') {
          console.log('Unhandled promise rejection event: timeout');
          Dialog({
            title: '提示',
            className: 'global-force-login net-error-dialog',
            confirmButtonText: '我知道了',
            showCancelButton: false,
            message: '当前网络状态不佳，请检查网络', // 歌曲下载出现了点问题，请重新加载
            zIndex: 10000,
            transition: false // 禁用动画效果
          })
          .then(() => {
            console.log('Dialog confirmed');
            store.commit('base/SET_NET_LOADING', false);
          });
          if (event.reason.name === 'ChunkLoadError') {
            console.log('Unhandled promise rejection event: ChunkLoadError');
            console.log('ChunkLoadError detected, replacing route');
            router.replace(router.history?.pending?.fullPath);
          }
        }
        event.preventDefault();
      });

      window.addEventListener('error', (event) => {
        // 完整输出错误对象的所有可用信息
        console.error('Error event:', {
          message: event.message,
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          error: event.error,
          stack: event.error?.stack // 尝试获取调用栈
        });
      });
    }

    onMounted(async () => {
      await store.dispatch('system/getCarName', TSVehicleInstance.getCarName())
      const _vc = TSNativeInstance.getParams('_vc') || 0
      console.log('app_vc=', _vc)
      if (_vc > 100023 && isYSTipAccept.value) {
        showLoading()
      }
      store.dispatch('base/fetchMallInfo')
      forceLoginLogic()
      let timeout = 2000;

      setTimeout(async () => {
        hideLoading()
        store.dispatch('updateMicrophones', { reset: true })
        store.dispatch('storage/getSpaceInfo')
        //初始化挂载本地历史已点已唱
        let orderedList = await getOrderedList();
        let alreadyList = await getAlreadyList();
        let searchCache = getSearchCache();
        let searchSong = getSearchSong();
        if (orderedList.length) {
          store.commit('SAVE_ORDERED_LIST', orderedList);
          // 此处只挂载歌曲id 歌曲不在此处挂载 车机上的浏览器和实际浏览器在- mv自动播放表现会不一致
          store.commit('SHIFT_SONG_ORDERED_LIST');
          store.dispatch('download/checkAutoDownload', !!vipInfo.value.end_time)

          store2.remove('orderedData')
        }
        if (alreadyList.length) {
          store.commit('SAVE_ALREADY_LIST', alreadyList.filter(item => !!item?.songid));

          store2.remove('alreadyData')
        }
        if (searchCache.length) {
          store.dispatch('search/updateSearchCache', searchCache)
        }
        if (searchSong.length) {
          store.dispatch('search/initSearchSong', searchSong)
        }
        
      }, timeout);

      const htmlElement = document.querySelector('html');
      // 先移除所有主题类
      htmlElement.classList.remove(...Array.from(htmlElement.classList).filter(cls => cls.startsWith('theme-')));
      // 添加新的主题类
      htmlElement.classList.add(`theme-${themeClass.value}`);

      if (localStorage.getItem('webvieActiveYidian')) {
        setTimeout(() => {
          eventBus.emit('show-order-song-control-popup');
        }, 3000)
        localStorage.setItem('webvieActiveYidian', '')
      }
    });

    const onVisibilitychange = async () => {
      if (!document.hidden) {
        getCarplayInfo()
      } else {
        const inputElement = document.querySelector('input')
        if (inputElement) {
          inputElement.blur()
        }
      }
    }

    const onRefresh = async () => {
      try {
        if (!mvIsHide.value) {
          loading.value = false
          return
        }
        await router.replace({ path: route.path, query: { ...route.query, t: Date.now() } });
        loading.value = false
      } catch {
        loading.value = false
      }
    }

    onMounted(() => {
      window.addEventListener('beforeunload', onUnload);
      window.addEventListener('online', onNetChange)
      window.addEventListener('offline', onNetChange);
      installGlobalClickHandler()
      document.addEventListener('visibilitychange', onVisibilitychange)
      attachEvents()
      TSBaseInfoInstance.getConfigurationUIMode()
      cacheImagesAsBase64(cacheImages)
    });
    onBeforeUnmount(() => {
      window.removeEventListener('beforeunload', onUnload);
      window.removeEventListener('online', onNetChange)
      window.removeEventListener('offline', onNetChange);
      uninstallGlobalClickHandler()
      document.removeEventListener('visibilitychange', onVisibilitychange)
      eventBus.off('getCarplayInfo', fetchCarplayInfo)
    });

    return {
      mvIsHide,
      isShowLimitModal,
      handleExit,
      themeClass,
      limitParams,
      net_status,
      onRefresh,
      loading,
      handleVoice,
      handleSoft,
    };
  },
};
</script>

<style lang="stylus">
.app-main
  position relative
.vld-background
  display none
[aria-label="loading"]
  touch-action none
#preload-container
  opacity 0
  position relative
  z-index -1
</style>
