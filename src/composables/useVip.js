import { ref, inject, computed, watch } from 'vue'
// import { useStore } from 'vuex'
import store from '@/store'
import { sendLog } from '@/directives/v-log/log'
import get from "lodash/get"

export default function useVip() {
  // const store = useStore()
  const $vipQrcode = inject('$vipQrcode')
  const vipQrcodeInstance = ref(null)

  const isLogin = computed(() => !!store.state.userInfo.unionid)

  const isVipUser = computed(() => {
    if (store.state.vipInfo?.end_time) {
      const vipEndDate = new Date(store.state.vipInfo.end_time).getTime()
      const now = Date.now()
      return now <= vipEndDate
    }
    return false
  })

  const showVipQrcode = (obj) => {
    if (vipQrcodeInstance.value) {
      return
    }
    let songData = get(obj, 'songid', false) ? {
      song_id: obj.songid
    } : {}
    sendLog({
      event_type: 'show',
      event_name: 256,
      event_data: {
        source: get(obj, 'src', ''), // 新增来源场景字段
        ...songData,
      }
    })
    vipQrcodeInstance.value = $vipQrcode.show({
      ...obj,
      onHide: () => {
        vipQrcodeInstance.value = null
        store.dispatch('stopCheckLoginStatus')
      }
    })
  }

  const handleIsLoginChange = (val) => {
    if (val && vipQrcodeInstance.value) {
      vipQrcodeInstance.value.hide()
    }
  }

  const handleIsVipChange = (val) => {
    if (val && vipQrcodeInstance.value) {
      vipQrcodeInstance.value.hide()
    }
  }

  watch(isLogin, handleIsLoginChange)

  watch(isVipUser, handleIsVipChange)

  return {
    isLogin,
    isVipUser,
    showVipQrcode,
  }
}