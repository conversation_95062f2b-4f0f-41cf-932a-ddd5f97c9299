import { computed } from 'vue'
import store from '@/store'
import useSongItem from './useSongItem'
import useM3u8 from './useM3u8'
import useDownload from './useDownload'
import { sendLog } from '@/directives/v-log/log'
import { get } from 'lodash'
import { setOrderedList, setAlreadyList } from '@/utils/historyCache'
import { TSMediaInstance } from '@/packages/TSJsbridge';
import { DownloadStatus } from '@/utils/download'
import eventBus from '@/utils/event-bus'
import { nanoid } from 'nanoid'

export default function useOrder() {
  // const store = useStore()
  const { orderSong: normalOrderSong } = useSongItem()
  const { getIsLocalSong, checkAutoDownload } = useDownload()

  const orderedList = computed(() => {
    const list = store.state.orderedList;
    list.forEach((item, index) => {
      if (!item._i)
        item._i = 'k_' + nanoid();
        item._index = index;
    });
    return list;
  });
  
  const alreadyList = computed(() => {
    const list = store.state.alreadyList;
    list.forEach((item, index) => {
      if (!item._i)
        item._i = 'k_' + nanoid();
        item._index = index;
    });
    return list;
  });

  const orderedListNumber = computed(() => orderedList.value.length)
  const orderedSongIdMap = computed(() => store.state.orderedSongIdMap)
  const currPlayingSong = computed(() => store.state.videoPlayerHistory.songItem)

  const isSingStatus = computed(() => store.state.isSingStatus)
  const initControlPlay = computed(() => store.state.videoInitAutoPlay)

  // 检查已点列表是否为 “空” 逻辑：length为0 或 仅包括自己
  const playNext = async (playingSongTemp, notHasCallback, from = 'control', isUnablePlay) => {
    const playingSong = orderedList.value[0]
    await store.dispatch('download/stopDownload');

    let nextSong = orderedList.value[1]

    if (!nextSong) {
      console.log('切歌: 无下一首')
      if (from !== 'ended') {
        deleteSong(0)
        setOrderedList()
        setAlreadyList()
      }
      if (typeof notHasCallback === 'function') {
        notHasCallback.call()
      }
      return
    }

    const nextSongIsLocal = await getIsLocalSong(nextSong)

    await store.commit('DELETE_SONG_ORDER_LIST', 0)

    sendLog({
      event_type: 'click',
      event_name: 105,
      event_data: {
        type: 1,
        song_id: nextSong.songid,
        song_name: nextSong.music_name,
        singer: nextSong.singer
      }
    })
    if (from == 'microphone') {
      sendLog({
        event_type: 'click',
        event_name: 106,
        event_data: {
          type: 2,
          song_id: nextSong.songid,
          song_name: nextSong.music_name,
          singer: nextSong.singer
        }
      })
    }
    await store.commit('SAVE_VIDEO_PLAYER_HISTORY_SONG_ITEM', nextSong)

    if (!isUnablePlay) {
      console.log('切歌: 播放下一首', nextSong)
      // 播下一首
      // store.commit('SAVE_VIDEO_PLAYER_HISTORY_SONG_ITEM', nextSong)
      if (nextSong.songid) {
        TSMediaInstance.playNext(store.state.videoPlayerHistory.songItem, nextSongIsLocal)
      } else {
        if (store.state.videoPlayerHistory.songItem.src) {
          TSMediaInstance.playNext(store.state.videoPlayerHistory.songItem, nextSongIsLocal)
        } else {
          console.log('order next', '无歌曲src')
        }
      }
    }
    //需更新下一首歌曲orderedSongIdMap 后续可考虑数据转换Map
    const isAddSongIdMap = (nextSong.songid === orderedList.value[0].songid) || !Object.prototype.hasOwnProperty.call(orderedSongIdMap.value, nextSong.songid)
    if (isAddSongIdMap) {
      store.commit('SAVE_ORDERED_SONGIDMAP', nextSong.songid)
    }

    // 首先查找 playingSong 在数组中的索引
    const alreadyIndex = alreadyList.value.findIndex((item) => item.songid === playingSong.songid);

    // 如果 playingSong 已经在数组中，将其移动到第一位
    if (alreadyIndex !== -1) {
      // 使用 splice 方法从数组中移除 playingSong，并将其插入到数组的开始位置
      alreadyList.value.splice(alreadyIndex, 1);
      alreadyList.value.unshift(playingSong);
    }

    // 然后，无论 playingSong 是否已经在数组中，都保存更新后的数组
    await store.commit('SAVE_ALREADY_LIST', alreadyList.value);

    // 如果 setAlreadyList 是用来更新组件状态的函数，确保它在 commit 之后调用
    setAlreadyList();

    setOrderedList()
    checkAutoDownload();
  }

  // 已点列表中点歌
  const orderSong = async (songItem, index, isAlreadyStick = false) => {
    try {
      console.log('开始点歌流程')
      const songIsLocal = await getIsLocalSong(songItem)
      console.log('歌曲是否本地:', songIsLocal)
  
      if (!songIsLocal && store.state.download.currentTask.songid === songItem.songid) {
        console.log('停止下载歌曲')
        await store.dispatch('download/stopDownload')
      }
  
      // 已点列表切歌时更新【已点列表切歌】状态标识，供歌曲播放上报使用 (点正播放歌曲不进行上报)
      if (index !== 0) {
        console.log('更新已点列表切歌状态标识')
        store.dispatch('songLog/updateCutSongTag')
      }
  
      let posData = {}
      // 点击当前播放歌曲继续播放，点击其他从零播放
      if (index === 0) {
        console.log('点击当前播放歌曲继续播放')
        posData = { position: 'recovery' }
      }
      if (index !== 0 || !initControlPlay.value) {
        console.log('点击其他从零播放')
        posData.useFirstSongAutoPlay = true
      }
      if (!isSingStatus.value) {
        console.log('更新播放状态')
        posData.useFirstSongAutoPlay = true
      }
  
      // 播放
      normalOrderSong(songItem, {
        isPushOrdered: false,
        enabledMvShow: true,
        isAlreadyStick: isAlreadyStick,
        beforeImmediate: async () => {
          console.log('开始立即点歌流程')
          if (index > 0) {
            if (index > 1) {
              console.log('先置顶(放到第二位)')
              await stickSongToTop(index, true)
            }
            const shiftSong = get(orderedList, 'value.0', null)
            if (shiftSong) {
              console.log('将歌曲移至已点列表')
              store.commit('PUSH_SONG_TO_ALREADY_LIST', shiftSong)
            }
            console.log('删除已点列表中的歌曲')
            store.commit('DELETE_SONG_ORDER_LIST', 0)
            setOrderedList()
            setAlreadyList()
          }
        },
        afterOrder: async () => {
          console.log('更新当前歌词索引')
          await store.commit('UPDATE_CURR_IYRIC_INDEX', -1)
          console.log('触发irc-control-next事件')
          eventBus.emit('irc-control-next')
          // 首次进入mv页 更新演唱状态
          if (!isSingStatus.value) {
            console.log('更新演唱状态')
            store.commit('UPDATE_IS_SING_STATUS', true)
            store.dispatch('searchTips/updateIsShowSingTips', false)
          }
          console.log('设置已点列表')
          await setOrderedList()
          if (isAlreadyStick) {
            console.log('已点列表已置顶，跳过自动下载检查')
            return
          }
          console.log('检查自动下载')
          checkAutoDownload()
        },
        ...posData // 点击当前播放歌曲继续播放，点击其他从零播放
      })
    } catch (error) {
      console.log('useSongItem orderSong error', error)
    }
  }

  // 置顶
  const stickSongToTop = async(index, isFromOrder) => {
    await store.dispatch('download/stopDownload');
    await store.commit('download/RESET_CURRENT_TASK');

    const _index = (typeof index === 'undefined' || index === -999)
      ? orderedList.value.length - 1
      : index

    await store.commit('STICK_SONG_TO_TOP_ORDERED_LIST', _index)

    if(!isFromOrder) {
      checkAutoDownload()
    }
  }

  // 删除
  const deleteSong = async (index) => {
    const currentSong = orderedList.value[index]
    // 下载中的歌曲被切歌，进入已唱列表，记忆下载进度，再次回到已点会继续下载。
    if (currentSong.downloadState === DownloadStatus.PROCESSING) {
      await store.dispatch('download/stopDownload');
    }

    store.commit('DELETE_SONG_ORDER_LIST', index)
    Promise.all([
      setOrderedList()
    ]).then(() => {
      checkAutoDownload();
    })
  }

  const addSong = (songItem, option) => {
    store.commit('PUSH_SONG_TO_ORDERED_LIST', { song: songItem, from: get(option, 'from', {}) })
  }

  return {
    playNext,
    addSong,
    orderSong,
    stickSongToTop,
    deleteSong,
    orderedList,
    orderedListNumber,
  }
}
