import { computed } from 'vue'
import { useStore } from 'vuex'
import useSongItem from './useSongItem'
import useOrder from './useOrder'
import { setOrderedList, setAlreadyList } from '@/utils/historyCache'
import { nanoid } from 'nanoid'

export default function useAlready() {
  const store = useStore()
  const { orderSong: normalOrderSong } = useSongItem()
  const { stickSongToTop: stickSongToOrderedTop, orderedListNumber } = useOrder()
  const isLogin= computed(() => !!store.state.userInfo.unionid)
  const mvIsHide = computed(() => store.state.mvIsHide)
  const alreadyList = computed(() => {
    const list = store.state.alreadyList;
    list.forEach((item, index) => {
      if (!item._i)
        item._i = 'k_' + nanoid();
        item._index = index;
    });
    return list;
  });

  const addSong = async (songItem) => {
    await store.commit('PUSH_SONG_TO_ALREADY_LIST', songItem)
    console.log('alreadyList', alreadyList.value)
  }

  // 已唱列表点歌
  const orderSong = (songItem, afterCallback, isAlreadyStick = false) => {
    let isImmediate = false
    if (orderedListNumber.value === 0) {
      isImmediate = true
    }
    normalOrderSong(songItem, {
      immediate: isImmediate,
      isAlreadyStick: isAlreadyStick,
      from: {
        song_list_source: !mvIsHide.value && !isLogin.value ? 22 : !mvIsHide.value && isLogin.value ? 23 : isLogin.value ? 33 : 4
      },
      afterOrder: () => {
        if (typeof afterCallback === 'function') {
          afterCallback.call(null, {
            orderedListNumber: orderedListNumber.value,
            immediate: isImmediate
          })
        }
      }
    })
    setOrderedList()
    // 已唱列表中删除该歌曲 VEC-869
    // deleteSong(index)
  }

  // 置顶
  const stickSongToTop = (songItem) => {
    // 添加到已点
    orderSong(songItem, () => {
      stickSongToOrderedTop(-999)
    }, true)
  }

  // 删除
  const deleteSong = (index) => {
    store.commit('DELETE_SONG_ALREADY_LIST', index)
    setAlreadyList()
  }

  return {
    orderSong,
    stickSongToTop,
    deleteSong,
    alreadyList,
    addSong,
  }
}
