import { createApp, h } from 'vue';
import LoadingIcon from '@/components/loading/icon.vue';

export default function useLoading() {
  let loadingInstance = null;

  const showLoading = (position = '', isDarkTheme = false) => {
    if (loadingInstance) {
      hideLoading();
    }

    const LoadingComponent = {
      render() {
        return h('div', {
          style: {
            position: 'fixed',
            top: 0,
            left: position === 'right' ? '57vw' : 0,
            width: position ? '43vw' : '100vw',
            height: '100vh',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 10000,
          },
        }, [
          h(LoadingIcon, {
            isDark: isDarkTheme,
          })
        ]);
      },
    };

    loadingInstance = createApp(LoadingComponent).mount(document.createElement('div'));
    document.body.appendChild(loadingInstance.$el);
  };

  const hideLoading = () => {
    if (loadingInstance) {
      document.body.removeChild(loadingInstance.$el);
      loadingInstance = null;
    }
  };

  return {
    showLoading,
    hideLoading,
  };
}
