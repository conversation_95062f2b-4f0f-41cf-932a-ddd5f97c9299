import { onMounted, onUnmounted, computed } from 'vue';
import eventBus from '@/utils/event-bus';
import { TSMediaInstance, TSWebEventInstance, TSBaseInfoInstance } from '@/packages/TSJsbridge';
import Toast from '@/utils/toast'
import store from '@/store'
import useOrder from '@/composables/useOrder';
import { DownloadStatus } from '@/utils/download';

export default function useVideoPlayer(song, emit) {
  const isAlreadyEnterMv = computed(() => store.state.videoInitAutoPlay)
  const isSingStatus = computed(() => store.state.isSingStatus)
  const mvIsHide = computed(() => store.state.mvIsHide)
  const { orderedListNumber } = useOrder();

  const checkAudioFocus = ()=>{
    if ((!isAlreadyEnterMv.value || !isSingStatus.value) && mvIsHide.value) {
      if (orderedListNumber.value > 0) {
        Toast('尚未进入欢唱页K歌，暂无法执行该操作')
      }
      return
    }
    return TSBaseInfoInstance.requestAudioFocus()
  }

  // 视频音轨切换事件
  const onVideoEventsAudioTrackSwitched = (id) => {
    console.log('AUDIO_TRACK_SWITCHED', id);
    emit('onAudioTracksSwitched', id)
  }

  // 错误处理
  const onVideoEventsError = (data) => {
    emit('error', data);
    store.dispatch('setVideoPaused', true);
  }

  // 音轨切换
  const handleSwitchAudioTrack = (audioTrack) => {
    console.log(
      'video-player handleSwitchAudioTrack',
      JSON.stringify(audioTrack),
    );
    TSMediaInstance.selectTrack(audioTrack.id);
    store.commit('SAVE_VIDEO_PLAYER_HISTORY_ENABLED_AUDIO_TRACK', audioTrack)
  };

  // 播放
  const handleControlVideoPlay = (s, { isLocalSong = false } = {}) => {
    if (!checkAudioFocus()) return

    console.log('video-control-play', s && s.music_name, isLocalSong ? '本地播放' : '边下边播');
    if (isLocalSong) {
      TSMediaInstance.play(s);
      return
    }
    TSMediaInstance.startPlay(s);
  };

  const handleControlVideoResume = () => {
    if (!checkAudioFocus()) return
    console.log('handleControlVideoResume')
    
    const result = TSMediaInstance.resume();
    if (result.resultCode !== 0 && result.resultCode !== 3) {
      console.log('handleControlVideoResume failed')
      handleControlVideoReplay()
    } else {
      emit('play')
    }
  }

  // 暂停
  const handleControlVideoPause = () => {
    TSMediaInstance.pause();
  }

  const handleControlVideoStop = () => {
    console.log('handleControlVideoStop')
    TSMediaInstance.stop();
  }

  // 重播
  const handleControlVideoReplay = () => {
    console.log('handleControlVideoReplay')
    if (!checkAudioFocus()) return
    store.commit('UPDATE_CURR_IYRIC_INDEX', -1)
    TSMediaInstance.replay(store.state.videoPlayerHistory.songItem || song.value);
  }

  // video canplay事件
  const handleCanPlay = () => {
    emit('canplay')
  }

  // video ended事件
  const handleVideoEnded = () => {
    console.log('handleVideoEnded')
    emit('ended')
    store.dispatch('setVideoPaused', true);
  }

  // video 切换画质下一首生效
  const handleChangeQualityTag = () => {
    Toast('画质切换后，下一首歌曲生效')
  }

  const handleControlVideoNext = () => {
    emit('next', 'useVideoPlayer')
  }

  const attachVideoPlayerEvents = () => {
    eventBus.on('switch-audio-track', handleSwitchAudioTrack)
    eventBus.on('video-control-play', handleControlVideoPlay)
    eventBus.on('video-control-resume', handleControlVideoResume)
    eventBus.on('video-control-pause', handleControlVideoPause)
    eventBus.on('video-control-replay', handleControlVideoReplay)
    eventBus.on('video-control-next', handleControlVideoNext)
    eventBus.on('video-quality-change', handleChangeQualityTag)
    eventBus.on('video-control-stop', handleControlVideoStop)

    eventBus.on('handleDownloadStart', handleDownloadStart);
    eventBus.on('updateDownloadProgress', handleUpdateDownloadProgress);
    eventBus.on('handleDownloaded', handleDownloaded);
    eventBus.on('handleDownloadFail', handleDownloadFail);

    TSWebEventInstance.on('handlePlayStart', () => {
      store.dispatch('setVideoPaused', false);
      // store.commit('UPDATE_MV_INIT_AUTOPLAY', 1);
      store.commit('UPDATE_IS_SING_STATUS', true)
      emit('play');

      // // 检查已点列表的第一首歌曲的 downloadState 是否为 4，如果是则改为 999
      // const orderedList = store.state.orderedList;
      // if (orderedList && orderedList.length > 0 && orderedList[0].downloadState === DownloadStatus.FAILED) {
      //   console.log('v35 handlePlayStart 已点第一首下载失败的状态更新为999', orderedList[0].music_name)
      //   store.commit('UPDATE_SONG_DOWNLOAD_STATE', { songid: orderedList[0].songid, downloadState: 999 });
      // }
    });
    TSWebEventInstance.on('handlePlayPause', async () => {
      // await nextTick()
      if (store.state.base.appStatus) {
        store.dispatch('setVideoPaused', true);
      }
      emit('pause');
    });
    TSWebEventInstance.on('handlePlayStop', () => {
      store.dispatch('setVideoPaused', true);
      emit('pause');
    });
    TSWebEventInstance.on('handlePlayError', onVideoEventsError);
    TSWebEventInstance.on('handlePlayCompletion', handleVideoEnded);
    TSWebEventInstance.on('handlePlayPrepared', handleCanPlay);
    TSWebEventInstance.on('handleSelectTrack', onVideoEventsAudioTrackSwitched);
    // TSWebEventInstance.on('handleTimeupdate', handleTimeupdate);
  };

  const detachVideoPlayerEvents = () => {
    console.log('detach-video-player-events')
    eventBus.off('switch-audio-track', handleSwitchAudioTrack)
    eventBus.off('video-control-play', handleControlVideoPlay)
    eventBus.off('video-control-pause', handleControlVideoPause)
    eventBus.off('video-control-replay', handleControlVideoReplay)
    eventBus.off('video-control-resume', handleControlVideoResume)
    eventBus.off('video-control-stop', handleControlVideoStop)

    eventBus.off('handleDownloadStart', handleDownloadStart);
    eventBus.off('updateDownloadProgress', handleUpdateDownloadProgress);
    eventBus.off('handleDownloaded', handleDownloaded);
    eventBus.off('handleDownloadFail', handleDownloadFail);
    eventBus.off('video-quality-change', handleChangeQualityTag)
  }

  const handleDownloadStart = ({
    songId,
    mediaFileName,
    tmpFilePath,
    fileLength,
  }) => {
    emit('on-download-start', {
      songId,
      mediaFileName,
      tmpFilePath,
      fileLength,
    });
  };
  const handleUpdateDownloadProgress = ({ songId, progress }) => {
    // console.log('歌曲下载进度', songId, progress + '%')
    // if (song.value.songid !== songId) return;
    emit('on-download-progress', { songId, progress });
  };
  const handleDownloaded = ({ songId, path, fileName, fileLength }) => {
    console.log('下载完成', songId, path, fileName, fileLength);
    emit('on-download-done', { songId, path, fileName, fileLength });
  };
  const handleDownloadFail = ({ songId, errorCode, msg }) => {
    console.log('下载失败', songId, errorCode, msg);
    emit('on-download-error', { songId, errorCode, msg });
  };

  onMounted(() => {
    attachVideoPlayerEvents()
  })
  onUnmounted(() => {
    console.log('onUnmounted: VideoPlayer')
    detachVideoPlayerEvents()
  })

  return {
  }
}