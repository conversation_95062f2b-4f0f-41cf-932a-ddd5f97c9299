import { computed } from 'vue'
import { useStore } from 'vuex'
import get from 'lodash/get'
import { TSMediaInstance } from '@/packages/TSJsbridge';
import useSetting from './useSetting'
import { resolutionRatio } from '@/service/user'
import useVip from './useVip'

export default function useQuality() {
  const store = useStore()
  const { setting } = useSetting()
  const { isVipUser } = useVip()
  const unionid = computed(() => store.state.userInfo.unionid)
  const playingSongItem = computed(() => store.state.videoPlayerHistory.songItem)

  // const getDefaultQualityM3U8 = (quality) => {
  //   const m3u8 = get(playingSongItem.value, 'm3u8', {})

  //   if (quality === '1080' && (!isVipUser.value || !m3u8['1080'])) {
  //     const qualitySupportList = ['720', '480'] // 720优先
  //     const res = qualitySupportList.find((qualitySupport) => {
  //       return get(m3u8, qualitySupport, '')
  //     })
  //     store.commit('CHANGE_PLAYING_MV_QUALITY', res)
  //     return res
  //   }

  //   if (quality === '720' && !m3u8['720']) {
  //     store.commit('CHANGE_PLAYING_MV_QUALITY', '480')
  //     return '480'
  //   }
  //   store.commit('CHANGE_PLAYING_MV_QUALITY', quality)
  //   return quality
  // }

  // 及时选择正播放歌曲的画质
  const useMvQuality = (val) => {
    // 关闭无1080画质副作用操作
    store.commit('UPDATE_LOWERBEFOREQ', '')
    console.log('useMvQuality:', val)
    TSMediaInstance.setResolution(val)
    // 同步当前正播放歌曲的画质选项
    store.commit('CHANGE_PLAYING_MV_QUALITY', val)
    let hls =  get(playingSongItem.value, `m3u8.${val}`, '')
    if (hls) {
      store.commit('CHANGE_PLAYING_MV_QUALITY_RESOURCE', hls)
    }

    // 切换画质时同步本地数据
    store.dispatch('saveSetting', {
      ...setting.value,
      quality: val
    })
    // // 同步服务器云端画质
    if (unionid.value) {
      resolutionRatio({ unionid: unionid.value, quality: val})
    }
  }

  return {
    useMvQuality,
    // getDefaultQualityM3U8,
  }
}