import { ref, computed } from 'vue';
import { useStore } from 'vuex';
import getComponentLrcData from '@/components/mv/utils'

export default function useTimeUpdate() {
  const store = useStore();

  const ircListData = computed(() => {
    return getComponentLrcData(store.state.videoPlayerHistory.songItemLrc);
  });
  const isMvMode = computed(() => store.state.mvMode.mvMode.mode === 'mv');
  const currVideoTime = ref(0);
  const currentPlayTime = ref(-1);
  const videoPaused = computed(() => store.state.videoPaused);

  const handleTimeupdate = (payload) => {
    try {
      if (videoPaused.value) {
        console.log('handleTimeupdate videoPaused')
        store.dispatch('setVideoPaused', false);
      }
      // store.commit('UPDATE_MV_INIT_AUTOPLAY', 1);
      store.commit('UPDATE_IS_SING_STATUS', true);
  
      // 计算当前时间
      const t = payload / 1000;
      currVideoTime.value = t;
  
      // 如果非 MV 模式且 ircListData 有数据
      if (!isMvMode.value && ircListData.value.length) {
        ircTimeCompare(t);
        return;
      }
  
      // 如果 ircListData 为空
      if (!ircListData.value.length) {
        console.log('ircListData 为空，提前返回');
        return;
      }
  
      // 调用 ircTimeCompare 进行比较
      ircTimeCompare(t, absCompare);
    } catch (error) {
      console.error('handleTimeupdate 发生错误:', error);
      // 可以根据需要处理错误，例如显示错误提示或恢复状态
      // store.dispatch('setVideoPaused', true); // 恢复暂停状态
      // errorMessage.value = '处理视频时间更新时出错';
    }
  };

  const absCompare = (n1, n2, p, max) => {
    return Math.abs(Math.floor(n1 * p) - Math.floor(n2 * p)) <= max * p;
  };

  const easyCompare = (n1, n2, p) => {
    return Math.floor(n1 * p) < Math.floor(n2 * p);
  };

  // mv、歌词播放时间同步
  const ircTimeCompare = (t, func) => {
    // 释放歌曲首次时间同步
    if (t < 0.3 || t <= ircListData.value[0].t) {
      currentPlayTime.value = t;
    }
    if (t >= ircListData.value[ircListData.value.length - 1].t) {
      store.commit('UPDATE_CURR_IYRIC_INDEX', ircListData.value.length - 1);
      return;
    }
    // 更新歌词位置状态
    ircListData.value.find((v, i, arr) => {
      let compareRes = false;
      if (typeof func === 'function') {
        compareRes = func(v.t, t, 10, 0.5); // 临近值实时对比
      } else {
        compareRes = easyCompare(v.t, t, 10) && easyCompare(t, arr[i + 1].t, 10); // 区间模糊对比
      }
      if (compareRes) {
        store.commit('UPDATE_CURR_IYRIC_INDEX', i);
        return true;
      }
      return false;
    });
  };

  return {
    handleTimeupdate,
  };
}