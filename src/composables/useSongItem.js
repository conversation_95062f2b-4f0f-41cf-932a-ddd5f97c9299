import { sendLog } from '@/directives/v-log/log'
import store from '@/store'
import { DownloadStatus } from '@/utils/download'
import eventBus from '@/utils/event-bus'
import { setAlreadyList, setOrderedList } from '@/utils/historyCache'
import Toast from '@/utils/toast'
import { get } from 'lodash'
import { computed, inject } from 'vue'
import useDownload from './useDownload'
import useLoginValid from './useLoginValid'
import useVip from './useVip'
// let time = 1

export default function useSongItem() {
  const withTimeoutHandling = inject('withTimeoutHandling');

  const { showLoginQrcode, isLogin } = useLoginValid()
  const { showVipQrcode, isVipUser } = useVip()

  const { getIsLocalSong, checkAutoDownload } = useDownload()

  const orderedList = computed(() => store.state.orderedList)
  const orderedListNumber = computed(() => store.state.orderedList.length)
  const paused = computed(() => store.state.videoPaused)

  const validSong = ({ hls, is_vip, songid }, _option={}) => {
    console.log(_option)
    // log数据 - 传递log打点到移动端支付服务
    let logData = {}
    switch (get(_option, 'from.song_list_source', '')) {
      case 2:
        logData.log = '搜索-会员歌曲-未登录'
        break;
      case 16:
        logData.log = '搜索-会员歌曲-已登录'
        break;
      case 10:
        logData.log = '搜索-会员歌曲'
        break;
      case 3:
        logData.log = '歌单-歌单名称-已登录'
        break;
      case 15:
        logData.log = '歌单-歌单名称-未登录'
        break;
      case 4:
        logData.log = '全局-已唱-未登录'
        break;
      case 33:
        logData.log = '全局-已唱-已登录'
        break;
      case 5:
        logData.log = '常唱列表-已登录'
        break;
      case 11:
        logData.log = '常唱列表-未登录'
        break;
      case 6:
      case 12:
        logData.log = '歌星-会员歌曲-已登录'
        break;
      case 17:
        logData.log = '歌星-会员歌曲-未登录'
        break;
      case 9:
        logData.log = '歌名-会员歌曲-未登录'
        break;
      case 14:
        logData.log = '歌名-会员歌曲-已登录'
        break;
      case 18:
        logData.log = '搜索-猜你会唱-已登录'
        break;
      case 19:
        logData.log = '搜索-猜你会唱-未登录'
        break;
      case 20:
        logData.log = 'mv-已点-未登录'
        break;
      case 21:
        logData.log = 'mv-已点-已登录'
        break;
      case 22:
        logData.log = 'mv-已唱-未登录'
        break;
      case 23:
        logData.log = 'mv-已唱-已登录'
        break;
      case 24:
        logData.log = 'mv搜索-歌曲-未登录'
        break;
      case 25:
        logData.log = 'mv搜索-歌曲-已登录'
        break;
      case 26:
        logData.log = 'mv搜索-歌手-未登录'
        break;
      case 27:
        logData.log = 'mv搜索-歌手-已登录'
        break;
      case 28:
        logData.log = 'mv-画质1080P-未登录'
        break;
      case 29:
        logData.log = 'mv-画质1080P-已登录'
        break;
      case 30:
        logData.log = 'mv-我的-未登录'
        break;
      case 31:
        logData.log = '我的入口-头像'
        break;
      case 32:
        logData.log = '我的-唱过的歌'
        break;
      case 34:
        logData.log = 'mv-猜你会唱-未登录'
        break;
      case 35:
        logData.log = 'mv-猜你会唱-已登录'
        break;
      case 36:
        logData.log = '歌手详情页-未登录'
        break;
      case 37:
        logData.log = '歌手详情页-已登录'
        break;
      case 13:
        logData.log = '长城车主尊享歌单'
        break;
      default:
        break;
    }
    // vip歌曲
    if (is_vip) {
      if (_option.from === 'searchBar' && !paused.value) return true
      if (!isLogin.value) {
        showLoginQrcode({
          songid,
          ...logData,
        })
        return false
      }
      if (!isVipUser.value) {
        showVipQrcode({
          songid,
          ...logData,
        })

        sendLog({
          event_type: '10000~50000',
          event_name: 6012,
          event_data: {
            str1: '通用',
            str2: '画质弹窗',
            str3: 'VIP画质弹窗展示',
            str4: 'show'
          }
        })
        return false
      }
    }
    return true
  }

  const orderOption = {
    position: '',
    isPushOrdered: true,
    immediate: true,
    from: {}, // 点歌来源 - 打点使用
    ponitActionLog: {}, // 点歌的点位信息 - 10000 ~ 50000 打点使用
    enabledMvShow: false, // 启用MV页，默认关闭 - 当开启时自动打开mv页；immediate同为true时才生效
    useFirstSongAutoPlay: false, // 点播的第一首歌曲是否启用自动播放，默认关闭
    isAlreadyStick: false, // 是否是已唱列表置顶歌曲
    beforePushOrdered: () => {
      if (orderedListNumber.value >= 99) {
        Toast('您已经点了很多歌曲啦，快去演唱吧')
        return false
      }
      return true
    },
    beforeImmediate: () => {
      // console.log('beforeImmediate')
    },
    afterOrder: () => {
      // console.log('afterOrder')
      setOrderedList()
      setAlreadyList()
    }
  }

  const orderSong = async (song, option) => {
    try {
      console.log('开始点歌流程', JSON.stringify(song))
      let _song = { ...song }
      const _option = { ...orderOption, ...option }
  
      const isLocalSong = await withTimeoutHandling(getIsLocalSong(_song))
      console.log(
        '点歌:', _song.music_name, 
        '是否本地', isLocalSong, 
        '是否加入已点:', !!option?.isPushOrdered, 
        '立即点歌', !!_option?.immediate,
        'useFirstSongAutoPlay', option.useFirstSongAutoPlay
      )
  
      updateSongDownloadState(_song, isLocalSong)
  
      if (!isLocalSong && !validSong(_song, _option)) {
        console.log('歌曲不合法，停止点歌流程')
        return
      }
  
      if (_option.isPushOrdered) {
        await handlePushOrdered(_song, _option, isLocalSong)
      }
  
      if (_option.immediate) {
        await handleImmediateOrder(_song, _option)
      }
  
      _option.afterOrder.call()
  
      if (_option.immediate && store.state.mvIsHide && _option.enabledMvShow) {
        await handleMvShow()
      }
  
      if (_option.useFirstSongAutoPlay) {
        await handleFirstSongAutoPlay(_song, isLocalSong)
      }
  
      if (!_option.isAlreadyStick) {
        checkAutoDownload()
      }
    } catch (error) {
      console.log('useSongItem orderSong error', error)
    }
  }
  
  const updateSongDownloadState = async (song, isLocalSong) => {
    if (isLocalSong) {
      console.log('更新歌曲下载状态为已存储')
      song = {
        ...song,
        downloadProgress: 100,
        downloadState: DownloadStatus.SUCCESS,
      }
      await withTimeoutHandling(store.commit('UPDATE_SONG_DOWNLOAD_STATE', {
        songid: song.songid,
        downloadProgress: 100,
        downloadState: DownloadStatus.SUCCESS,
        src: song.src
      }))
    } else {
      song = {
        ...song,
        downloadState: song.downloadState || DownloadStatus.UNSTORAGE,
        downloadProgress: song.downloadProgress || 0,
      }
      console.log('更新歌曲下载状态为未存储',)
      await withTimeoutHandling(store.commit('UPDATE_SONG_DOWNLOAD_STATE', {
        songid: song.songid,
        downloadState: song.downloadState || DownloadStatus.UNSTORAGE,
        downloadProgress: song.downloadProgress || 0,
        src: ''
      }))
    }
  }
  
  const handlePushOrdered = async (song, option, isLocalSong) => {
    console.log('开始加入已点列表流程')
    const isExistOrdered = orderedList.value.some(orderedSong => orderedSong.songid === song.songid)
  
    if (isExistOrdered && !isLocalSong) {
      console.log('云端歌曲不可重复加入已点')
      Toast('云端歌曲不可重复加入已点')
      return
    }
  
    const isConditionMet = option.beforePushOrdered.call()
    if (!isConditionMet) {
      console.log('beforePushOrdered条件不满足，停止点歌流程')
      return
    }
  
    if (orderedListNumber.value === 0 || isLocalSong || !isExistOrdered) {
      console.log('歌曲已加入已点列表')
      Toast('歌曲已加入已点列表')
      store.dispatch('addSongToOrderedList', {
        ...song,
        downloadState: isLocalSong ? DownloadStatus.SUCCESS : undefined,
        downloadProgress: isLocalSong ? 100 : undefined,
      })
    } else {
      console.log('歌曲已加入已点列表')
      Toast('歌曲已加入已点列表')
    }
  }
  
  const handleImmediateOrder = async (song, option) => {
    console.log('开始立即点歌流程')
    option.beforeImmediate.call()
    await withTimeoutHandling(store.commit('SAVE_VIDEO_PLAYER_HISTORY_SONG_ITEM', song))
    if (option.position !== 'recovery') {
      console.log('更新视频播放器恢复状态为true')
      await withTimeoutHandling(store.commit('UPDATE_VIDEO_PLAYER_RECOVERY', true))
    }
  }
  
  const handleMvShow = async () => {
    try {
      console.log('进入欢唱页')
      await withTimeoutHandling(store.commit('UPDATE_MV_ISHIDE', false))
    } catch (error) {
      console.log('进入欢唱页 error', error)
    }
  }
  
  const handleFirstSongAutoPlay = async (song, isLocalSong) => {
    console.log('开始播放视频')
    eventBus.emit('handle-video-play', { isLocalSong })
    if (!isLocalSong) {
      console.log('停止下载歌曲')
      await withTimeoutHandling(store.dispatch('download/stopDownload'))
    }
  }

  return {
    validSong,
    orderSong,
  }
}