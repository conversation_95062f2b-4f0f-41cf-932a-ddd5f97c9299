import { useStore } from 'vuex'
import { inject } from 'vue'
import { useRouter } from 'vue-router'
import {
  useActivityAlert,
  useActivityMic,
  useActivityVip,
  useVipExpire,
  useActivityRecommendSong,
  // useActivitySignIn,
  // useZeroBuyVip,
  useActivityKSong
} from '@/components/modal/global/activity-modal/create'

export default function useActivity() {
  const store = useStore()
  const router = useRouter()
  const $activityModal = inject('$activityModal')

  const showActivityModal = (data) => {
    $activityModal.show(data)
  }

  const showActivityAlert = (text) => {
    const $activityAlert = useActivityAlert()
    $activityAlert.show(text)
  }

  const showActivityMic = () => {
    const $activityMic = useActivityMic()
    $activityMic.show()
  }

  const showActivityVip = () => {
    const $activityVip = useActivityVip()
    $activityVip.show({
      onHide: () => {
        store.dispatch('stopCheckLoginStatus')
      }
    })
  }

  const showVipExpire = (data) => {
    const $vipExpire = useVipExpire()
    $vipExpire.show(data)
  }

  const showActivityRecommendSong = (data) => {
    const $useActivityRecommendSong = useActivityRecommendSong()
    $useActivityRecommendSong.show(data)
  }

  // const showActivitySignIn = () => {
  //   const $activitySignIn = useActivitySignIn({
  //     router,
  //   })
  //   $activitySignIn.show()
  // }

  // const showActivityZerobuyVip = (unionid) => {
  //   store.dispatch('zerobuy/startCheckZerobuyStatus', unionid)
  //   const $activityZerobuyVip = useZeroBuyVip()
  //   $activityZerobuyVip.show()
  // }

  const showActivityKSong = () => {
    const $activityKSong = useActivityKSong()
    $activityKSong.show()
  }

  return {
    showActivityModal,
    showActivityAlert,
    showActivityMic,
    showActivityVip,
    showVipExpire,
    showActivityRecommendSong,
    // showActivitySignIn,
    // showActivityZerobuyVip,
    showActivityKSong
  }
}