import { computed, ref } from 'vue';
import store from '@/store';
import get from 'lodash/get';
import useSetting from './useSetting';
import { differenceInMilliseconds } from 'date-fns';
import { getSongM3U8, getSongToken, getSongLrc } from '@/service/song';
import axios from 'axios';

let cancelSource = null

export default function useM3u8() {
  const { setting } = useSetting();
  const orderedList = computed(() => store.state.orderedList);
  const isVip = computed(() => !!store.state.vipInfo.end_time)
  const oldServerTime = computed(() => store.state.videoPlayerHistory.songItem.serverTime);
  const tokenExp = computed(() => store.state.videoPlayerHistory.songItem.tokenExp);

  // 添加当前歌曲的m3u8资源
  const addCurrSongM3u8 = async ({ availableQualities = [] }) => {
    console.log('开始处理歌曲m3u8资源，当前歌曲:', orderedList.value[0]?.music_name, 'ID:', orderedList.value[0]?.songid);
    store.commit('RESET_CURR_SONG_LRC');
    store.commit('UPDATE_CURR_IYRIC_INDEX', -1);
    const song = orderedList.value[0];
    if (!song) return; // 如果没有歌曲，直接返回

    // 管理请求取消令牌
    const source = axios.CancelToken.source();
    if (cancelSource) {
      cancelSource.cancel('请求被取消');
    }
    cancelSource = source;

    try {
      // 获取并更新歌词
      const songLrc = await getSongLrc(song.songid, song.lrc, { cancelToken: source.token });
      store.commit('UPDATE_CURR_SONG_LRC', { lrc: get(songLrc, 'lrc.json', []) });
      console.log(`歌词获取成功，共${get(songLrc, 'lrc.json', []).length}行歌词`);

      // 请求并更新可用画质
      if (!availableQualities.length) {
        console.log('开始请求M3U8画质信息...');
        const { is_m3u8 = {} } = await getSongM3U8(song.songid, { cancelToken: source.token });
        availableQualities = Object.keys(is_m3u8).filter(quality => is_m3u8[quality] === 1);
        console.log(`${song.music_name} 可用的画质:`, availableQualities);
      }
      store.commit('UPDATE_SONG_AVAILABLE_QUALITIES', availableQualities);
      console.log('已提交可用画质列表到store:', availableQualities);

      // 确定并更新当前播放画质
      let quality = 
        availableQualities.includes(setting.value.quality)
        ? setting.value.quality
        : availableQualities[availableQualities.length - 1];

      // 如果用户不是VIP且当前选择的画质是1080p，则降级
      if (!isVip.value && quality === '1080') {
        // 优先降级到720p
        if (availableQualities.includes('720')) {
          quality = '720';
          console.log('用户不是VIP，1080p画质降级为720p');
        } else if (availableQualities.includes('480')) {
          // 如果没有720p，则降级到480p
          quality = '480';
          console.log('用户不是VIP，1080p画质降级为480p');
        }
      }

      store.commit('CHANGE_PLAYING_MV_QUALITY', quality);
      console.log('当前在播的画质:', quality, '设置的画质', setting.value.quality);

      console.log('最终确定的播放画质:', quality, '可用画质列表:', availableQualities);
      return availableQualities;
    } catch (error) {
      if (axios.isCancel(error)) {
        console.log('请求被取消');
      } else {
        console.error('添加当前歌曲的m3u8资源失败:', error);
        store.commit('CHANGE_PLAYING_MV_QUALITY', 480); // 默认画质
      }
    }
  };

  // 设置当前歌曲的 token
  const setCurrSongToken = async () => {
    try {
      const songData = await getSongToken();
      const currServerTime = get(songData, 'serverTime', 0);

      // 与 exp 对比前后服务器时间的差值
      const ts =
        differenceInMilliseconds(new Date(currServerTime), new Date(oldServerTime.value)) <
        1000 * tokenExp.value;

      store.commit('UPDATE_CURR_SONG_TOKEN', {
        token: get(songData, 'token', ''),
        tokenExp: get(songData, 'tokenExp', 5 * 60),
        serverTime: currServerTime,
      });

      return ts;
    } catch (error) {
      console.error('设置当前歌曲的 token 失败:', error);
      return false;
    }
  };

  return {
    addCurrSongM3u8,
    setCurrSongToken,
  };
}