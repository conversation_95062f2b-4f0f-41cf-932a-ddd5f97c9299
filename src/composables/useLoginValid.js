// import { useRouter } from 'vue-router'
// import { useStore } from 'vuex'
import { sendLog } from '@/directives/v-log/log'
import { computed, inject, ref, watch } from 'vue'
import { useStore } from 'vuex'

export default function useLoginValid() {
  const store = useStore()
  const $loginQrcode = inject('$loginQrcode')
  // 统一改为vipLogin的模块
  // const $vipLoginQrcode = inject('$vipQrcode')
  // 改回只login
  const $loginConfirm = inject('$loginConfirm')
  const loginQrcodeInstance = ref(null)
  const loginConfirmInstance = ref(null)

  const isLogin = computed(() => !!store.state.userInfo.unionid)

  const showLoginQrcode = (obj) => {
    console.log('开始显示登录二维码');
    
    // 此处需求变更 - 改回之前的login
    loginQrcodeInstance.value = $loginQrcode.show({
      ...obj,
      onHide: () => {
        console.log('登录二维码弹窗隐藏');
        loginQrcodeInstance.value = null;
        store.dispatch('stopCheckLoginStatus');
        console.log('停止检查登录状态');
      }
    });
  
    console.log('登录二维码弹窗已显示');
  }

  const showLoginConfirm = (props) => {
    loginConfirmInstance.value = $loginConfirm.show(props)
  }

  const checkUserLoginStatus = () => {
    if (isLogin.value) {
      return true
    }
    showLoginQrcode()
    return false
  }

  const handleIsLoginChange = (val) => {
    // console.log('用户登录状态改变', val, !!loginQrcodeInstance.value);
    if (val && loginQrcodeInstance.value) {
      loginQrcodeInstance.value.hide()
      sendLog({
        event_type: '10000~50000',
        event_name: 10098,
        event_data: {
          str1: '任意页',
          str2: '登录弹窗',
          str3: '关闭弹窗',
          str4: 'click',
        },
      })

      sendLog({
        event_type: '10000~50000',
        event_name: 6012,
        event_data: {
          str1: '通用',
          str2: '扫码登录弹窗',
          str3: '弹窗展示',
          str4: 'show'
        },
      })
    }
  }

  watch(isLogin, handleIsLoginChange)

  return {
    isLogin,
    showLoginQrcode,
    showLoginConfirm,
    checkUserLoginStatus,
  }
}