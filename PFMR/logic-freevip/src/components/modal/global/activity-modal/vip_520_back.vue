<template>
  <CommonModal ref="root">
    <!-- 520活动 -->
    <div class="activity-modal-vip">
      <img @click="handleCloseModal" class="activity-modal-vip-close" src="https://qncweb.ktvsky.com/20230517/vadd/74aefe68369d7ca036db1d7fc07b1434.png"/>
      <img class="activity-modal-vip-top-horn" src="https://qncweb.ktvsky.com/20230517/vadd/f416fc3fe9667f2d787e09da2ebde0e5.png"/>
      <div class="activity-modal-vip-title">
        <span>520为爱放价</span>
        <span>年卡立减</span>
        <img src="https://qncweb.ktvsky.com/20230517/vadd/b55105c930bf610b87f32681fb4d0dce.png" alt="">
        <span>元</span>
        <img class="activity-modal-vip-title-love-icon" src="https://qncweb.ktvsky.com/20230517/vadd/bbf88644a80e70eccfc3844233fae338.png" alt="">
      </div>
      <p class="activity-modal-vip-p"><span>开通VIP</span>享专属优质曲库，高清MV等特权</p>
      <div class="activity-modal-vip-content">
        <span>每日仅需</span>
        <img src="https://qncweb.ktvsky.com/20230517/vadd/b4688fcdec27a59e6cf2ee7446403cab.png" alt="">
        <span>元</span>
      </div>
      <div class="activity-modal-vip-bottom">
        <div class="activity-modal-vip-code">
          <img :src="qrCodeURL" alt="">
          <span>微信扫码 立享优惠</span>
        </div>
      </div>
    </div>
  </CommonModal>
</template>
<script>
import { computed, ref, onBeforeMount } from 'vue'
import store from '@/store'
import CommonModal from '@/components/modal/common/component.vue'
import { format } from 'date-fns'
import { getCarplayInfo } from '@/service/carplay-info'
import useQRCode from '@/composables/useQRCode'
import { Toast } from 'vant'
import get from 'lodash/get'
import { sendLog } from '@/directives/v-log/log'

export default {
  name: 'ActivityVipModal',
  components: {
    CommonModal,
  },
  props: {
    closeEvent: {
      type: Function,
      default: () => {
      }
    },
  },
  setup(props) {
    const root = ref(null)
    const appStartTime = computed(() => store.state.appStartTime)

    const { getQRCodeURL } = useQRCode()
    let qrCodeURL = ref('https://qncweb.ktvsky.com/20211130/vadd/82b806b8bf560e49df69162835879c65.png')

    const getVipQrcode = async () => {
      const { data } = await getCarplayInfo()
      if (get(data, 'pay_qr', '')) {
        const qrCodeData = await getQRCodeURL(`${data.pay_qr}&fr=520`)
        if (qrCodeData) {
          qrCodeURL.value = qrCodeData
        }
        return
      }
      Toast('未获取到登录二维码')
    }

    const handleCloseModal = () => {
      const now = Date.now()
      props.closeEvent.call()
      sendLog({
        event_type: 'show',
        event_name: 1723,
        event_data: {
          start_time: format(appStartTime.value, 'yyyy-MM-dd HH:mm:ss'),
          end_time: format(now, 'yyyy-MM-dd HH:mm:ss'),
          stay_time: Math.round((now - appStartTime.value) / 1000),
          key_words: '销售VIP'
        }
      })
      root.value.hide()
    }

    onBeforeMount(getVipQrcode)

    return {
      root,
      qrCodeURL,
      handleCloseModal,
    }
  }
}
</script>
<style lang="stylus" scoped>
.activity-modal-vip
  padding-top 122px
  position relative
  width 1000px
  height 730px
  background url('https://qncweb.ktvsky.com/20230517/vadd/517e7856bcbbe51d0aa43114095a32b2.png') no-repeat
  background-size 100% 100%
  background-position center
  color rgba(255, 51, 100, 1)
  display flex
  flex-direction column
  align-items center
  &-close
    width 90px
    height 90px
    position absolute
    top 30px
    left 50px
  &-top-horn
    width 283px
    height 283px
    position absolute
    top -133px
    left 392px
  &-title
    margin-bottom 20px
    font-size 52px
    height 60px
    display flex
    flex-direction row
    align-items center
    justify-content center
    position relative
    span
      height 60px
      font-weight 700
      &:nth-child(1)
        width 302px
        margin-right 20px
      &:nth-child(2)
        width 208px
      &:nth-child(4)
        width 55px
    img
      width 90px
      height auto
      margin 0 10px
    &-love-icon
      width 53px !important
      height auto
      position absolute
      top -20px
      right -60px
  &-p
    width 768px
    height 53px
    background rgba(255, 51, 100, 1)
    font-size 28px
    font-weight 400
    text-align center
    line-height 53px
    letter-spacing 6px
    color rgba(255, 255, 255, 0.8)
    background url('https://qncweb.ktvsky.com/20230518/vadd/993d1c70326241e12e6e8a6741b3350a.png') no-repeat
    background-size 100% 100%
    background-position center
    span
      letter-spacing 6px
      margin-right 32px
  &-content
    margin-top 7px
    margin-left -30px
    font-size 28px
    height 200px
    display flex
    flex-direction row
    align-items flex-end
    justify-content center
    span
      height 60px
      color rgba(255, 51, 100, 1)
      height 39px
      line-height 50px
      font-weight 400
      &:nth-child(1)
        width auto
      &:nth-child(3)
        width auto
    img
      width 337px
      height auto
      margin 0 14px 0 18px
  &-bottom
    width 100%
    height 193px
    display flex
    justify-content center
    position absolute
    bottom 20px
    left 0
  &-code
    width 162px
    height 193px
    background #fff
    border-radius 8px
    border 2px solid rgba(255, 52, 82, 0.5)
    display flex
    flex-direction column
    align-items center
    img
      width 150px
      height 150px
      margin-top 6px
    span
      height 21px
      line-height 21px
      font-weight 400
      font-size 15px
      color rgba(0, 0, 0, 0.8)
      margin-top 8px
</style>
