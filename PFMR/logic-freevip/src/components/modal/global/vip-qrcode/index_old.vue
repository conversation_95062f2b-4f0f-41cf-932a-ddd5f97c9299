<template>
  <CommonModal ref="root" :onCancel="handleCancel">
    <div class="vip-modal-content">
      <img @click="handleCloseModal" class="close" src="https://qncweb.ktvsky.com/20231226/vadd/b92b46946f7ce7be7a885f8b27846292.png"/>
      <img class="vip-top-img" src="https://qncweb.ktvsky.com/20230901/vadd/081fccee1059d5da84b21fd154479354.png" alt="">
      <div class="title">
        <img src="https://qncweb.ktvsky.com/20231226/vadd/a4e8232a41055a42361d438ecb297d65.png" alt="">
      </div>
      <div class="qrcode">
        <img :src="qrCodeURL" />
        <!-- <div class="qrcode-txt">扫码立减 199 元！</div> -->
      </div>
      <!-- <div class="tip" v-if="isLogin">
        <span>
          <img src="https://qncweb.ktvsky.com/20220308/vadd/baff20ce1752c049fe211f12685bc6df.png" alt="">
          微信扫码支付
        </span>
      </div>
      <div class="tip" v-else>
        <span>
          <img src="https://qncweb.ktvsky.com/20220308/vadd/baff20ce1752c049fe211f12685bc6df.png" alt="">
          微信扫码
        </span>
        即可登录账号/开通会员
      </div> -->
    </div>
  </CommonModal>
</template>
<script>
import { onBeforeMount, ref, toRefs, computed } from 'vue'
import store from '@/store'
import CommonModal from '@/components/modal/common/component.vue'
import { getCarplayInfo } from '@/service/carplay-info'
import useQRCode from '@/composables/useQRCode'
import Toast from '@/utils/toast'
import get from 'lodash/get'
import { vipLogFrom } from '@/constants/index'
import { sendLog } from '@/directives/v-log/log'

export default {
  name: 'VipModalQRcode',
  components: {
    CommonModal,
  },
  props: {
    songid: {
      type: Number,
      default: 0 // 当通过点歌打开弹框时增加二维码增加songid参数，埋点使用
    },
    log: {
      type: String,
      default: ''
    },
  },
  setup(props) {
    const { songid, log } = toRefs(props)
    const isLogin = computed(() => !!store.state.userInfo.unionid)
    const { getQRCodeURL } = useQRCode()
    const root = ref(null)
    let qrCodeURL = ref('https://qncweb.ktvsky.com/20211130/vadd/82b806b8bf560e49df69162835879c65.png')

    const getVipQrcode = async () => {
      // console.log('vipQRcode:', songid.value)
      const { data } = await getCarplayInfo()
      if (get(data, 'pay_qr', '')) {
        const qrCodeData = await getQRCodeURL(`${data.pay_qr}&songid=${songid.value ? songid.value : ''}&log=${vipLogFrom.get(log.value ? log.value : '其他')}`)
        if (qrCodeData) {
          qrCodeURL.value = qrCodeData
        }
        return
      }
      Toast('未获取到登录二维码')
    }

    const handleCloseModal = () => {
      // 手动关闭时统一做一次用户信息更新 处理用户在个人中心模块-前往开通时，关闭弹窗刷新用户会员信息
      store.dispatch('getCarplayInfo')
      root.value.hide()
      sendLog({
        event_type: '10000~50000',
        event_name: 10094,
        event_data: {
          str1: '任意页',
          str2: 'VIP弹窗',
          str3: '关闭弹窗',
          str4: 'click',
        },
      })
    }

    const handleCancel = () => {
      // 点击其他区域关闭弹窗时，也去刷一次
      store.dispatch('getCarplayInfo')
      sendLog({
        event_type: '10000~50000',
        event_name: 10094,
        event_data: {
          str1: '任意页',
          str2: 'VIP弹窗',
          str3: '关闭弹窗',
          str4: 'click',
        },
      })
    }

    onBeforeMount(getVipQrcode)

    return {
      qrCodeURL,
      root,
      isLogin,
      handleCancel,
      handleCloseModal,
    }
  }
}
</script>
<style lang="stylus" scoped>
.vip-modal
  &-content
    position relative
    width 1000px
    height 740px
    padding-top 0 !important
    border-radius 10px
    background linear-gradient(0deg, #1E1F21 0%, #1E1F21 100%), linear-gradient(168deg, #4D4843 -82.51%, #17181B 138.48%)
    font-size 24px
    color rgba(255, 255, 255, 1)
    display flex
    flex-direction column
    align-items center
    @media screen and (max-width 1200px)
      zoom 0.8
    .close
      position absolute
      top 30px
      right 30px
      left unset!important
      width 40px
      height 40px
    .vip-top-img
      width 242px
      height 138px
      position absolute
      top -70px
      left 379px
    .title
      margin 120px 0 47px
      width 1000px
      height 309px
      display flex
      justify-content center
      img
        width 700px
        height 309px
    .qrcode
      width 190px
      height 190px
      display flex
      justify-content center
      align-items center
      background #ffffff
      border-radius 10px
      position relative
      img
        width 174px
        height 174px
      &-txt
        width 226px
        height 49px
        position absolute
        bottom -42px
        left -18px
        background linear-gradient(90deg, #FF3D3D 0%, #FF881A 100%)
        font-size 24px
        color #fff
        text-align center
        line-height 49px
        border-radius 30px
        padding-left 10px
    .tip
      font-size 32px
      text-align center
      display flex
      flex-direction column
      align-items center
      span
        display flex
        align-items center
      img
        width 45px
        height 45px
        margin-right 10px
</style>
