# 免费VIP产品逻辑文档

**文档版本**: 2025.7.02  
**项目**: Tesla KTV  
**功能**: 免费送VIP逻辑  

## 1. 产品概述

### 1.1 功能定义
免费送VIP是为非VIP用户提供的限时体验功能，允许用户在不开通VIP的情况下，免费点唱VIP歌曲，每日提供3次免费点唱机会。

### 1.2 核心价值
- **用户体验提升**: 降低VIP歌曲的体验门槛
- **转化引导**: 通过免费体验引导用户开通正式VIP
- **用户留存**: 增加非VIP用户的使用粘性

## 2. 业务规则

### 2.1 免费次数规则
- **每日免费次数**: 3次
- **次数重置**: 每日重置（服务端控制）
- **次数扣减**: 成功点唱VIP歌曲后自动扣减1次
- **次数增加**: 服务端自动处理，前端仅展示

### 2.2 用户状态判断
```
用户状态优先级：
1. 未登录用户 → 引导登录
2. VIP用户 → 正常使用所有功能
3. 过期VIP用户 → 引导续费
4. 普通用户 + 有免费次数 → 允许点唱VIP歌曲
5. 普通用户 + 无免费次数 → 引导开通VIP
```

### 2.3 触发场景
- **点歌操作**: 点击VIP歌曲时进行判断
- **播放控制**: 播放VIP歌曲时验证权限
- **切歌操作**: 切换到下一首VIP歌曲时验证

## 3. 用户交互流程

### 3.1 免费次数展示
**展示位置**: 
- 搜索栏下方提示区域
- 头部导航栏信息区域

**展示逻辑**:
```
IF 用户未登录:
    显示 "登录即可享千元特权，好礼送不停！"
ELSE IF 用户是VIP:
    显示 "尊敬的VIP用户，拉满状态开启您狂欢时刻！"
ELSE IF 用户是过期VIP:
    显示 "权益重磅升级，邀您回归！续费低至¥0.2/天"
ELSE IF 免费次数 >= 3:
    显示 "每日更新曲库，紧跟实时热点，解锁VIP，海量歌曲免费唱！"
ELSE:
    显示 "VIP歌曲免费点唱次数: X/3"
```

### 3.2 点歌流程
```
用户点击VIP歌曲
    ↓
判断用户登录状态
    ↓
IF 未登录:
    显示登录二维码
ELSE IF 已登录:
    判断VIP状态
        ↓
    IF 是VIP用户:
        直接点歌成功
    ELSE IF 有免费次数:
        点歌成功，扣减1次免费次数
    ELSE:
        显示VIP开通弹窗
```

### 3.3 弹窗策略
**VIP开通弹窗触发条件**:
- 非VIP用户点击VIP歌曲且免费次数为0
- 播放VIP歌曲时权限验证失败
- 切换到下一首VIP歌曲时权限不足

**弹窗类型**:
- 登录用户: 显示VIP套餐选择弹窗
- 未登录用户: 显示登录引导弹窗

## 4. 数据字段

### 4.1 核心字段
- `freeVipNumber`: 已使用的免费VIP次数
- `remainingFreeOrders`: 剩余免费次数 (计算值: 3 - freeVipNumber)

### 4.2 状态计算
```javascript
// 是否显示免费次数信息
showFreeOrderInfo = !isExpired && !isShowClimax && freeVipNumber < 3

// 剩余免费次数
remainingFreeOrders = 3 - freeVipNumber

// 是否允许免费点唱VIP歌曲
canUseFreeVip = isLogin && !isVip && freeVipNumber < 3
```

## 5. 异常处理

### 5.1 网络异常
- 免费次数获取失败时，默认显示0次剩余
- 点歌失败时，不扣减免费次数

### 5.2 状态同步
- 登录状态变化时，重新获取免费次数
- VIP状态变化时，隐藏相关弹窗

## 6. 埋点统计

### 6.1 关键事件
- 免费次数展示: 记录展示时机和用户状态
- 免费点歌成功: 记录使用次数和歌曲信息
- VIP弹窗展示: 记录触发场景和用户类型
- 转化成功: 记录从免费体验到VIP开通的转化

### 6.2 数据分析维度
- 免费次数使用率
- 免费用户转VIP转化率
- 不同触发场景的转化效果

## 7. 产品优化建议

### 7.1 短期优化
- 增加免费次数用完后的引导文案优化
- 优化VIP弹窗的视觉设计和转化路径

### 7.2 长期规划
- 考虑根据用户行为动态调整免费次数
- 增加免费次数的获取途径（如分享、邀请等）
- 建立更精细化的用户分层和权益体系
