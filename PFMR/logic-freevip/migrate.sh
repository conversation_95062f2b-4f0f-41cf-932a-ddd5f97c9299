#!/bin/bash

# 免费送VIP功能迁移脚本
# 使用方法: ./migrate.sh <目标项目路径>

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查参数
if [ $# -eq 0 ]; then
    echo -e "${RED}错误: 请提供目标项目路径${NC}"
    echo "使用方法: ./migrate.sh <目标项目路径>"
    exit 1
fi

TARGET_PATH="$1"

# 检查目标路径是否存在
if [ ! -d "$TARGET_PATH" ]; then
    echo -e "${RED}错误: 目标路径不存在: $TARGET_PATH${NC}"
    exit 1
fi

echo -e "${BLUE}=== 免费送VIP功能迁移脚本 ===${NC}"
echo -e "${YELLOW}目标路径: $TARGET_PATH${NC}"
echo ""

# 函数: 检查文件是否存在
check_file_exists() {
    local file_path="$1"
    if [ -f "$file_path" ]; then
        echo -e "${GREEN}✓${NC} 文件存在: $file_path"
        return 0
    else
        echo -e "${RED}✗${NC} 文件不存在: $file_path"
        return 1
    fi
}

# 函数: 备份文件
backup_file() {
    local file_path="$1"
    if [ -f "$file_path" ]; then
        cp "$file_path" "$file_path.backup.$(date +%Y%m%d_%H%M%S)"
        echo -e "${YELLOW}已备份:${NC} $file_path"
    fi
}

# 函数: 复制文件
copy_file() {
    local src="$1"
    local dest="$2"
    local dest_dir=$(dirname "$dest")
    
    # 创建目标目录
    mkdir -p "$dest_dir"
    
    # 备份目标文件（如果存在）
    backup_file "$dest"
    
    # 复制文件
    cp "$src" "$dest"
    echo -e "${GREEN}已复制:${NC} $src -> $dest"
}

echo -e "${BLUE}1. 检查目标项目结构...${NC}"

# 检查关键目录
REQUIRED_DIRS=(
    "$TARGET_PATH/src"
    "$TARGET_PATH/src/store"
    "$TARGET_PATH/src/service"
    "$TARGET_PATH/src/components"
    "$TARGET_PATH/src/composables"
)

for dir in "${REQUIRED_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        echo -e "${GREEN}✓${NC} 目录存在: $dir"
    else
        echo -e "${RED}✗${NC} 目录不存在: $dir"
        echo -e "${YELLOW}正在创建目录...${NC}"
        mkdir -p "$dir"
    fi
done

echo ""
echo -e "${BLUE}2. 复制核心文件...${NC}"

# 复制服务接口文件
echo -e "${YELLOW}复制服务接口文件...${NC}"
copy_file "src/service/user.js" "$TARGET_PATH/src/service/user.js"
copy_file "src/service/vip.js" "$TARGET_PATH/src/service/vip.js"

# 复制业务逻辑文件
echo -e "${YELLOW}复制业务逻辑文件...${NC}"
copy_file "src/composables/useSongItem.js" "$TARGET_PATH/src/composables/useSongItem.js"
copy_file "src/composables/useVip.js" "$TARGET_PATH/src/composables/useVip.js"

# 复制Store文件
echo -e "${YELLOW}复制Store文件...${NC}"
copy_file "src/store/index.js" "$TARGET_PATH/src/store/index.js"
mkdir -p "$TARGET_PATH/src/store/modules"
copy_file "src/store/modules/act.js" "$TARGET_PATH/src/store/modules/act.js"
copy_file "src/store/modules/vipAddSong.js" "$TARGET_PATH/src/store/modules/vipAddSong.js"
copy_file "src/store/modules/climax.js" "$TARGET_PATH/src/store/modules/climax.js"

echo ""
echo -e "${BLUE}3. 复制UI组件...${NC}"

# 复制组件文件
echo -e "${YELLOW}复制组件文件...${NC}"

# 创建组件目录结构
mkdir -p "$TARGET_PATH/src/components/modal/global/vip-qrcode"
mkdir -p "$TARGET_PATH/src/components/modal/global/activity-modal"
mkdir -p "$TARGET_PATH/src/components/search-bar"
mkdir -p "$TARGET_PATH/src/components/head-bar"
mkdir -p "$TARGET_PATH/src/components/free-singing"
mkdir -p "$TARGET_PATH/src/components/openvip-hint"
mkdir -p "$TARGET_PATH/src/components/vip"
mkdir -p "$TARGET_PATH/src/components/mv/components/vip"
mkdir -p "$TARGET_PATH/src/components/climax-modal"

# 复制组件文件
cp -r src/components/modal/global/vip-qrcode/* "$TARGET_PATH/src/components/modal/global/vip-qrcode/"
cp -r src/components/modal/global/activity-modal/* "$TARGET_PATH/src/components/modal/global/activity-modal/"
cp -r src/components/search-bar/* "$TARGET_PATH/src/components/search-bar/"
cp -r src/components/head-bar/* "$TARGET_PATH/src/components/head-bar/"
cp -r src/components/free-singing/* "$TARGET_PATH/src/components/free-singing/"
cp -r src/components/openvip-hint/* "$TARGET_PATH/src/components/openvip-hint/"
cp -r src/components/vip/* "$TARGET_PATH/src/components/vip/"
cp -r src/components/mv/components/vip/* "$TARGET_PATH/src/components/mv/components/vip/"
cp -r src/components/climax-modal/* "$TARGET_PATH/src/components/climax-modal/"

# 复制MV主组件和App.vue
copy_file "src/components/mv/index.vue" "$TARGET_PATH/src/components/mv/index.vue"
copy_file "src/App.vue" "$TARGET_PATH/src/App.vue"

# 复制页面文件
mkdir -p "$TARGET_PATH/src/pages"
copy_file "src/pages/index.vue" "$TARGET_PATH/src/pages/index.vue"

echo ""
echo -e "${BLUE}4. 复制文档...${NC}"

# 复制文档文件
copy_file "免费vip的产品逻辑文档2025.7.02.md" "$TARGET_PATH/免费vip的产品逻辑文档2025.7.02.md"
copy_file "免费送vip的业务架构代码设计文档2025.7.02.code.md" "$TARGET_PATH/免费送vip的业务架构代码设计文档2025.7.02.code.md"
copy_file "emitcode.md" "$TARGET_PATH/emitcode.md"
copy_file "README.md" "$TARGET_PATH/README_freevip.md"

echo ""
echo -e "${GREEN}=== 迁移完成 ===${NC}"
echo ""
echo -e "${YELLOW}接下来请执行以下步骤:${NC}"
echo "1. 阅读目标项目中的 emitcode.md 文档"
echo "2. 根据文档检查和适配代码"
echo "3. 测试免费VIP功能是否正常工作"
echo "4. 检查所有备份文件，确认迁移结果"
echo ""
echo -e "${BLUE}备份文件位置:${NC} 所有被覆盖的文件都已备份为 .backup.时间戳 格式"
echo ""
echo -e "${GREEN}祝您迁移顺利！${NC} 🎉"
