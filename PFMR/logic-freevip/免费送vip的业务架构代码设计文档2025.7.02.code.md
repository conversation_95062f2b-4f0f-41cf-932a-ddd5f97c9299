# 免费送VIP业务架构代码设计文档

**文档版本**: 2025.7.02  
**项目**: Tesla KTV  
**技术栈**: Vue 3 + Vuex + JavaScript  

## 1. 架构概览

### 1.1 核心模块
```
免费VIP系统
├── 状态管理 (Vuex Store)
├── 业务逻辑层 (Composables)
├── UI组件层 (Components)
├── 服务接口层 (Services)
└── 工具函数层 (Utils)
```

### 1.2 数据流向
```
服务端API → Store状态 → 组件响应式数据 → UI展示 → 用户交互 → 业务逻辑 → API调用
```

## 2. 状态管理设计

### 2.1 Store状态定义
**文件**: `src/store/index.js`

```javascript
// 核心状态字段
state: {
  freeVipNumber: 0,        // 已使用的免费VIP次数
  userInfo: {              // 用户信息
    unionid: '',
    userType: 1
  },
  vipInfo: {               // VIP信息
    end_time: '',
    expire: false
  }
}
```

### 2.2 Mutations
```javascript
// 保存免费VIP次数
SAVE_FREE_VIP_COUNT(state, freeVipNumber) {
  state.freeVipNumber = freeVipNumber;
}
```

### 2.3 Actions
```javascript
// 获取用户信息时同步免费VIP次数
async getCarplayInfo({ commit }) {
  const { data } = await getCarplayInfo();
  // 处理用户信息和VIP信息
  // 免费次数通过单独接口获取
}
```

## 3. 服务接口层

### 3.1 免费VIP相关接口
**文件**: `src/service/user.js`

```javascript
// 获取用户免费VIP次数
export async function getFreeVip({ unionid }) {
  const res = await http.get(`/stb/v2/free_vip?unionid=${unionid}`);
  return res;
}
```

### 3.2 VIP相关接口
**文件**: `src/service/vip.js`

```javascript
// VIP套餐二维码获取
export async function getVipPkgQr({ unionid, pkg_id, fr }) {
  const res = await httpV2.get('/stb/v2/vip/qr', {
    params: { unionid, pkg_id, fr }
  });
  return res.data;
}
```

## 4. 业务逻辑层

### 4.1 点歌权限验证
**文件**: `src/composables/useSongItem.js`

```javascript
// 核心验证逻辑
const validSongVip = (song, option) => {
  const { is_vip } = song;
  
  if (is_vip) {
    if (isLogin.value) {
      // 已登录用户逻辑
      if (!isVipUser.value && !freeVipNumber.value) {
        // 非VIP且无免费次数 → 显示VIP弹窗
        showVipQrcode({
          fromType: 'vip-expired',
          isLogin: true,
          fr: option.fr || 1873
        });
        return false;
      }
    } else {
      // 未登录用户 → 显示VIP弹窗
      showVipQrcode({
        fromType: 'vip-expired',
        isLogin: false,
        fr: 1756
      });
      return false;
    }
  }
  return true;
};
```

### 4.2 VIP弹窗管理
**文件**: `src/composables/useVip.js`

```javascript
export default function useVip() {
  const showVipQrcode = (obj) => {
    // 防重复弹窗
    if (vipQrcodeInstance.value) return;
    
    // 埋点统计
    sendLog({
      event_type: 'show',
      event_name: 256,
      event_data: {
        source: get(obj, 'src', ''),
        song_id: obj.songid || ''
      }
    });
    
    // 显示弹窗
    vipQrcodeInstance.value = $vipQrcode.show({
      ...obj,
      onHide: () => {
        vipQrcodeInstance.value = null;
        obj?.onHide?.();
      }
    });
  };
}
```

## 5. UI组件设计

### 5.1 免费次数提示组件
**文件**: `src/components/search-bar/index-version3.vue`

```vue
<template>
  <div class="times-info" @click="handleClickTimes">
    <!-- 未登录状态 -->
    <template v-if="!isLogin">
      <span class="light">登录</span>即可享千元特权，好礼送不停！
    </template>
    
    <!-- VIP用户 -->
    <template v-else-if="isVip">
      尊敬的VIP用户，拉满状态开启您狂欢时刻！
    </template>
    
    <!-- 过期VIP -->
    <template v-else-if="isExpire">
      权益重磅升级，邀您回归！<span class="light">续费低至¥0.2/天</span>
    </template>
    
    <!-- 免费次数用完 -->
    <template v-else-if="freeVipNumber >= 3">
      每日更新曲库，紧跟实时热点，<span class="light">解锁</span>VIP，海量歌曲免费唱！
    </template>
    
    <!-- 显示剩余免费次数 -->
    <template v-else>
      <p class="free-order">
        VIP歌曲免费点唱次数:
        <span class="free-order-zero">{{3 - freeVipNumber}}/3</span>
      </p>
    </template>
  </div>
</template>
```

### 5.2 VIP弹窗组件
**文件**: `src/components/modal/global/vip-qrcode/loggedIn.vue`

```vue
<template>
  <CommonModal ref="root" :onCancel="handleCancel">
    <div class="vip-loggedin-modal-content">
      <!-- VIP套餐选择 -->
      <div class="vip-packages">
        <div v-for="item in packages" :key="item.id" 
             class="vip-packages-item"
             :class="item.id === selectItem.id && 'active'"
             @click="handleClickItem(item)">
          <div class="days">{{ item.title }}</div>
          <div class="price">¥{{ formatValue(item.fee) }}</div>
        </div>
      </div>
      
      <!-- 支付二维码 -->
      <div class="pay-qrcode">
        <img :src="qrCodeURL" />
      </div>
    </div>
  </CommonModal>
</template>
```

### 5.3 免费次数用尽弹窗
**文件**: `src/components/modal/global/activity-modal/freeSongNoTime.vue`

```vue
<template>
  <CommonModal ref="root">
    <div class="activity-nofree-vip">
      <div class="activity-nofree-vip-code">
        <img :src="qrCodeURL" alt="">
        <span>微信扫码开通</span>
      </div>
    </div>
  </CommonModal>
</template>
```

## 6. 关键业务流程

### 6.1 应用启动流程
```javascript
// App.vue
const setFreeVip = async () => {
  const res = await getFreeVip({
    unionid: userInfo.value.unionid,
  });
  store.commit("SAVE_FREE_VIP_COUNT", res.data.free_vip_count);
};

// 监听登录状态变化
watch(userInfo, (newVal) => {
  if (newVal.unionid) {
    setFreeVip(); // 登录后获取免费次数
  }
});
```

### 6.2 点歌验证流程
```javascript
// 点歌前验证
const orderSong = (song, option) => {
  // 1. VIP歌曲权限验证
  if (!validSongVip(song, option)) {
    return false;
  }
  
  // 2. 执行点歌逻辑
  if (song.is_vip && !isVipUser.value) {
    // 非VIP用户点VIP歌曲，加入特殊队列
    store.dispatch('vipAddSong/addSong', song);
  }
  
  // 3. 正常点歌流程
  // ...
};
```

### 6.3 播放验证流程
```javascript
// mv/index.vue
const validSongVip = () => {
  if (videoPlayer.value.songItem.is_vip) {
    if (isLogin.value && !isVipUser.value && !freeVipNumber.value) {
      handleVideoControlPause();
      showVipQrcode({
        fromType: "vip-expired",
        isLogin: true,
        fr: 1873,
      });
      return false;
    }
  }
  return true;
};
```

## 7. 数据计算逻辑

### 7.1 响应式计算属性
```javascript
// 剩余免费次数
const remainingFreeOrders = computed(() => 3 - freeVipNumber.value);

// 是否显示免费次数信息
const showFreeOrderInfo = computed(() => 
  !isExpire.value && !isShowClimax.value && freeVipNumber.value < 3
);

// 是否可以使用免费VIP
const canUseFreeVip = computed(() => 
  isLogin.value && !isVipUser.value && freeVipNumber.value < 3
);
```

## 8. 错误处理与边界情况

### 8.1 网络异常处理
```javascript
const setFreeVip = async () => {
  try {
    const res = await getFreeVip({ unionid: userInfo.value.unionid });
    store.commit("SAVE_FREE_VIP_COUNT", res.data.free_vip_count);
  } catch (error) {
    console.error('获取免费VIP次数失败:', error);
    // 默认设置为0，确保UI正常显示
    store.commit("SAVE_FREE_VIP_COUNT", 0);
  }
};
```

### 8.2 状态同步处理
```javascript
// 监听VIP状态变化，自动隐藏弹窗
watch(isVipUser, (val) => {
  if (val && vipQrcodeInstance.value) {
    vipQrcodeInstance.value.hide();
  }
});
```

## 9. 性能优化

### 9.1 防重复请求
```javascript
let freeVipLoading = false;

const setFreeVip = async () => {
  if (freeVipLoading) return;
  freeVipLoading = true;
  
  try {
    // API调用
  } finally {
    freeVipLoading = false;
  }
};
```

### 9.2 组件懒加载
```javascript
// 弹窗组件按需加载
const VipModal = defineAsyncComponent(() => 
  import('@/components/modal/global/vip-qrcode/loggedIn.vue')
);
```

## 10. 测试要点

### 10.1 单元测试
- 免费次数计算逻辑
- 权限验证函数
- 状态变更mutations

### 10.2 集成测试
- 完整点歌流程
- 弹窗显示逻辑
- 状态同步机制

### 10.3 边界测试
- 网络异常情况
- 并发操作处理
- 状态不一致场景
