# 免费送VIP逻辑迁移指导文档

**文档版本**: 2025.7.02  
**项目**: Tesla KTV  
**用途**: 跨渠道分支迁移免费VIP功能  

## 1. 文件结构概览

```
logic-freevip/
├── 免费vip的产品逻辑文档2025.7.02.md          # 产品逻辑文档
├── 免费送vip的业务架构代码设计文档2025.7.02.code.md  # 技术架构文档
├── emitcode.md                                # 本迁移指导文档
└── src/
    ├── App.vue                               # 应用入口文件
    ├── composables/                          # 业务逻辑层
    │   ├── useSongItem.js                   # 点歌权限验证逻辑
    │   └── useVip.js                        # VIP弹窗管理逻辑
    ├── service/                             # 服务接口层
    │   ├── user.js                          # 用户相关接口
    │   └── vip.js                           # VIP相关接口
    ├── store/                               # 状态管理
    │   ├── index.js                         # 主store文件
    │   └── modules/                         # store模块
    │       ├── act.js                       # 活动相关状态
    │       ├── climax.js                    # 快唱相关状态
    │       └── vipAddSong.js               # VIP点歌队列状态
    ├── components/                          # UI组件
    │   ├── climax-modal/                    # 快唱弹窗组件
    │   ├── free-singing/                    # 免费唱歌组件
    │   ├── head-bar/                        # 头部导航栏
    │   ├── modal/global/                    # 全局弹窗组件
    │   │   ├── activity-modal/              # 活动弹窗
    │   │   └── vip-qrcode/                  # VIP二维码弹窗
    │   ├── mv/                              # MV播放相关
    │   │   ├── index.vue                    # MV主组件
    │   │   └── components/vip/              # MV中的VIP组件
    │   ├── openvip-hint/                    # VIP开通提示
    │   ├── search-bar/                      # 搜索栏组件
    │   └── vip/                             # VIP相关组件
    └── pages/                               # 页面组件
        └── index.vue                        # VIP页面
```

## 2. 核心代码关键信息

### 2.1 状态管理 (Store)

**文件**: `src/store/index.js`

**关键字段**:
```javascript
state: {
  freeVipNumber: 0,        // 已使用的免费VIP次数 (核心字段)
  userInfo: {              // 用户信息
    unionid: '',           // 用户唯一标识
    userType: 1            // 用户类型
  },
  vipInfo: {               // VIP信息
    end_time: '',          // VIP到期时间
    expire: false          // 是否过期VIP
  }
}
```

**关键Mutations**:
```javascript
SAVE_FREE_VIP_COUNT(state, freeVipNumber) {
  state.freeVipNumber = freeVipNumber;
}
```

**迁移要点**:
- 确保目标分支的store中包含`freeVipNumber`字段
- 确保`SAVE_FREE_VIP_COUNT` mutation存在
- 检查用户信息和VIP信息的字段结构是否一致

### 2.2 服务接口层

**文件**: `src/service/user.js`

**关键接口**:
```javascript
// 获取用户免费VIP次数
export async function getFreeVip({ unionid }) {
  const res = await http.get(`/stb/v2/free_vip?unionid=${unionid}`);
  return res;
}
```

**文件**: `src/service/vip.js`

**关键接口**:
```javascript
// VIP套餐二维码获取
export async function getVipPkgQr({ unionid, pkg_id, fr }) {
  const res = await httpV2.get('/stb/v2/vip/qr', {
    params: { unionid, pkg_id, fr }
  });
  return res.data;
}
```

**迁移要点**:
- 检查目标分支的API接口路径是否一致
- 确认http和httpV2工具类的引入路径
- 验证接口参数和返回数据结构

### 2.3 业务逻辑层

**文件**: `src/composables/useSongItem.js`

**关键函数**:
```javascript
// 核心权限验证逻辑
const validSongVip = (song, option) => {
  const { is_vip } = song;
  
  if (is_vip) {
    if (isLogin.value) {
      if (!isVipUser.value && !freeVipNumber.value) {
        showVipQrcode({
          fromType: 'vip-expired',
          isLogin: true,
          fr: option.fr || 1873
        });
        return false;
      }
    } else {
      showVipQrcode({
        fromType: 'vip-expired',
        isLogin: false,
        fr: 1756
      });
      return false;
    }
  }
  return true;
};
```

**文件**: `src/composables/useVip.js`

**关键函数**:
```javascript
const showVipQrcode = (obj) => {
  // 防重复弹窗逻辑
  // 埋点统计逻辑
  // 弹窗显示逻辑
};
```

**迁移要点**:
- 确保目标分支有相同的composables结构
- 检查依赖的store状态和方法
- 验证埋点统计方法的兼容性

### 2.4 UI组件层

**关键组件列表**:

1. **搜索栏提示组件** (`src/components/search-bar/index-version3.vue`)
   - 显示免费次数: `{{3 - freeVipNumber}}/3`
   - 根据用户状态显示不同提示文案

2. **头部导航栏** (`src/components/head-bar/index.vue`)
   - 计算属性: `showFreeOrderInfo`, `remainingFreeOrders`
   - 免费次数显示逻辑

3. **VIP弹窗组件** (`src/components/modal/global/vip-qrcode/`)
   - `loggedIn.vue`: 已登录用户VIP弹窗
   - `index_old.vue`: 旧版VIP弹窗
   - `create.js`: 弹窗创建工具

4. **活动弹窗组件** (`src/components/modal/global/activity-modal/`)
   - `freeSongNoTime.vue`: 免费次数用尽弹窗
   - `vip.vue`: VIP活动弹窗

5. **MV播放组件** (`src/components/mv/index.vue`)
   - `validSongVip()`: 播放时权限验证
   - `validNextSong()`: 下一首歌权限验证

**迁移要点**:
- 检查组件的依赖关系和引入路径
- 确认样式文件和图片资源
- 验证组件间的事件通信机制

## 3. 关键业务流程

### 3.1 应用启动流程

**文件**: `src/App.vue`

**关键代码**:
```javascript
const setFreeVip = async () => {
  const res = await getFreeVip({
    unionid: userInfo.value.unionid,
  });
  store.commit("SAVE_FREE_VIP_COUNT", res.data.free_vip_count);
};

// 监听登录状态变化
watch(userInfo, (newVal) => {
  if (newVal.unionid) {
    setFreeVip(); // 登录后获取免费次数
  }
});
```

### 3.2 点歌验证流程

**触发位置**: 用户点击VIP歌曲时
**验证逻辑**: 
1. 检查登录状态
2. 检查VIP状态  
3. 检查免费次数
4. 决定是否显示弹窗

### 3.3 播放验证流程

**触发位置**: MV播放组件中
**验证时机**: 
1. 歌曲开始播放时
2. 切换下一首歌时

## 4. 迁移步骤指南

### 4.1 前置检查

1. **依赖检查**:
   - 确认目标分支的Vue版本和Vuex版本
   - 检查lodash、axios等工具库版本
   - 验证UI组件库(如Vant)版本兼容性

2. **API接口检查**:
   - 确认`/stb/v2/free_vip`接口在目标环境可用
   - 验证VIP相关接口的兼容性
   - 检查接口返回数据结构

3. **Store结构检查**:
   - 确认目标分支store的基础结构
   - 检查是否有冲突的字段名或方法名

### 4.2 核心迁移步骤

**步骤1: 状态管理迁移**
```bash
# 1. 在目标分支的store/index.js中添加freeVipNumber字段
# 2. 添加SAVE_FREE_VIP_COUNT mutation
# 3. 如果需要，迁移相关的actions
```

**步骤2: 服务接口迁移**
```bash
# 1. 将getFreeVip函数添加到目标分支的service/user.js
# 2. 确保VIP相关接口在service/vip.js中存在
# 3. 检查http工具类的引入路径
```

**步骤3: 业务逻辑迁移**
```bash
# 1. 迁移useSongItem.js中的权限验证逻辑
# 2. 迁移useVip.js中的弹窗管理逻辑
# 3. 适配目标分支的具体业务场景
```

**步骤4: UI组件迁移**
```bash
# 1. 按需迁移UI组件，优先迁移核心组件
# 2. 适配目标分支的组件结构和样式
# 3. 检查组件间的依赖关系
```

**步骤5: 应用入口迁移**
```bash
# 1. 在App.vue中添加免费次数获取逻辑
# 2. 添加登录状态监听
# 3. 确保在合适的时机调用setFreeVip函数
```

### 4.3 测试验证

1. **功能测试**:
   - 登录后免费次数正确显示
   - 点击VIP歌曲时权限验证正确
   - 免费次数用完后弹窗正确显示
   - VIP用户不受免费次数限制

2. **边界测试**:
   - 网络异常时的处理
   - 并发操作的处理
   - 状态不一致的处理

3. **兼容性测试**:
   - 不同用户状态下的表现
   - 不同设备和浏览器的兼容性

## 5. 常见问题和解决方案

### 5.1 API接口不兼容

**问题**: 目标分支的API接口路径或参数不同
**解决**:
- 检查目标分支的API文档
- 适配接口路径和参数
- 必要时创建适配层

### 5.2 Store结构差异

**问题**: 目标分支的store结构与源分支不同
**解决**:
- 分析目标分支的store结构
- 适配字段名和方法名
- 保持数据流的一致性

### 5.3 组件依赖问题

**问题**: 组件依赖的其他组件或工具在目标分支不存在
**解决**:
- 识别缺失的依赖
- 迁移必要的依赖组件
- 或寻找目标分支的替代方案

### 5.4 样式兼容问题

**问题**: 样式在目标分支显示异常
**解决**:
- 检查CSS预处理器版本
- 适配目标分支的样式规范
- 确保图片资源路径正确

## 6. 迁移检查清单

### 6.1 代码迁移检查

- [ ] Store状态字段已添加
- [ ] Mutations和Actions已迁移
- [ ] 服务接口已适配
- [ ] 业务逻辑已迁移
- [ ] UI组件已适配
- [ ] 应用入口逻辑已添加

### 6.2 功能验证检查

- [ ] 免费次数正确显示
- [ ] 点歌权限验证正常
- [ ] VIP弹窗正确显示
- [ ] 播放权限验证正常
- [ ] 状态同步正常

### 6.3 性能和稳定性检查

- [ ] 无内存泄漏
- [ ] 无重复请求
- [ ] 错误处理完善
- [ ] 埋点统计正常

## 7. 后续优化建议

1. **代码优化**:
   - 提取公共逻辑为工具函数
   - 优化组件的可复用性
   - 改进错误处理机制

2. **性能优化**:
   - 实现组件懒加载
   - 优化API请求频率
   - 添加缓存机制

3. **用户体验优化**:
   - 优化弹窗交互流程
   - 改进提示文案
   - 增加动画效果

4. **监控和分析**:
   - 完善埋点统计
   - 添加错误监控
   - 分析用户行为数据

## 8. 核心代码位置速查表

| 功能模块 | 文件路径 | 关键代码/方法 | 说明 |
|---------|----------|---------------|------|
| 免费次数状态 | `src/store/index.js` | `freeVipNumber: 0` | 核心状态字段 |
| 免费次数更新 | `src/store/index.js` | `SAVE_FREE_VIP_COUNT` | 状态更新方法 |
| 免费次数获取 | `src/service/user.js` | `getFreeVip()` | API接口调用 |
| 点歌权限验证 | `src/composables/useSongItem.js` | `validSongVip()` | 核心验证逻辑 |
| VIP弹窗管理 | `src/composables/useVip.js` | `showVipQrcode()` | 弹窗显示逻辑 |
| 播放权限验证 | `src/components/mv/index.vue` | `validSongVip()` | 播放时验证 |
| 次数显示UI | `src/components/search-bar/index-version3.vue` | 模板中的显示逻辑 | 用户界面展示 |
| 头部次数显示 | `src/components/head-bar/index.vue` | `showFreeOrderInfo` | 计算属性 |
| 应用启动逻辑 | `src/App.vue` | `setFreeVip()` | 初始化获取 |
| VIP弹窗组件 | `src/components/modal/global/vip-qrcode/` | 整个目录 | 弹窗UI组件 |

## 9. 重要注意事项

1. **数据一致性**: 确保免费次数的获取、显示、验证在各个组件中保持一致
2. **错误处理**: 网络请求失败时要有合适的降级处理
3. **性能考虑**: 避免频繁请求免费次数接口，合理使用缓存
4. **用户体验**: 状态变化时要及时更新UI，避免用户困惑
5. **埋点统计**: 确保关键操作都有对应的埋点记录

---

**注意**: 本文档基于当前分支的代码结构生成，在迁移到其他分支时，请根据目标分支的实际情况进行适配调整。建议在迁移前先在测试环境验证功能完整性。
