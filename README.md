# TeslaKtv

特斯拉车机定制版

### 文档

[排期文档](https://m2miovoqda.feishu.cn/wiki/wikcnDOrW5ihp0z54uRC4NItDgb?sheet=0mPOCW)
[需求文档](https://m2miovoqda.feishu.cn/wiki/wikcnvrM9oBcudcDxVFVmPnqGVM)
[测试用例](https://m2miovoqda.feishu.cn/wiki/wikcnaaBtwuioxTAH8XK9hfw3Nf)
[设计稿](https://lanhuapp.com/web/#/item?fid=dd82dc30-e6cf-4d3e-bc19-72b6d673bea2)
[接口文档](https://m2miovoqda.feishu.cn/wiki/wikcnBeAYTWV5TcF9xBSTvx2S3f#7bpjBQ)

### 环境配置

建议使用node 16.13.0（LTS)

```
yarn install
```

### 本地开发
```
yarn dev
```

### 测试环境构建
```
yarn stage
```

同组内测试环境流程一样，该项目已配置gitlab-ci，使用release/online分支提交会同步到测试环境

### 规则及修复
```
yarn lint
```

### 目录
* src
  * componens 应用内组件，供任何位置使用，如页面独有组件，请在页面所在目录`/components`内创建组件，如 `src/pages/classify/components`
    * btn
      * mv-back 返回按钮
      * order-control 已点悬浮按钮
    * control-popup 控制浮层
    * head-bar 页面顶部导航条
    * load-more 列表滚动加载组件
    * modal 弹窗组件
      * common 弹窗公共组件
      * login-confirm 登录确认弹窗
      * login-qrcode 登录二维码弹窗
    * nav-list 首页导航栏组件
    * order-song-control-popup 已点/已唱控制浮层
    * rank-list 首页歌单排行榜列表
    * search-bar 搜索组件
    * song-item 普通形态点歌组件
    * song-list 普通歌曲列表组件
    * style 全局样式组件
      * vant vant复写样式
    * video-player
      * index 播放器
      * plugins
        * control 播放控制组件 （重播、暂停/播放、切歌）
        * menu 播放器顶部菜单组件 （返回、原伴唱切换、
  * composables 组合式api
    * useAlready 已唱逻辑
    * useOrder 已点逻辑
    * useLoading loading逻辑
    * useLoginValid 登录校验逻辑
    * useSongItem 歌曲组件点歌逻辑，如独有的歌曲类型点歌逻辑建议新建逻辑，择情复用，如useAlready中的orderSong
  * pages
    * agreement 协议页面
    * classify 分类页面 由首页入口进入
    * index 首页
    * mine
      * index 设置页
      * profile 用户中心页
    * mv mv播放页面
    * search 搜索页面
    * singer 歌手页面
    * songlist 歌曲列表页
    * vip
      * exchange-list 兑换记录页
      * exchange 兑换页面
      * index vip开通
  * router
    * 路由
  * service
    * 网络资源请求
  * store
    * 全局状态
  * styles
    * global.styl 全局业务样式
    * init.css 抹平样式差异
  * utils
    * event-bus 事件总线
    * http axios简单封装
    * mac 生成虚拟mac地址 供接口鉴权
    * ua 当前宿主环境信息 供接口鉴权
  * App.vue
    * 全局信息
      * 获取mac地址
      * 获取用户设置信息（画质）
      * 获取当前宿主环境信息
      * provide
        * 已点歌曲记录map
        * 当前播放歌曲id
    * 全局弹出层逻辑
      * 登录
      * 已点、已唱
    * 全局任务逻辑
      * 轮询登录态